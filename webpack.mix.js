const mix = require('laravel-mix');
const path = require('path');

mix.options({
    terser: {
        extractComments: false,
    }
});

mix.setPublicPath('public');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.less('resources/assets/less/app.less', 'public/css')
   .copy('node_modules/sweetalert/dist/sweetalert.min.js', 'public/js/sweetalert.min.js')
   // .copy('node_modules/sweetalert/dist/sweetalert.css', 'public/css/sweetalert.css')
   .js('resources/assets/js/app.js', 'public/js')
   .vue()
   .webpackConfig({
        resolve: {
            modules: [
                path.resolve(__dirname, 'resources/assets/js/spark'),
                'node_modules'
            ],
            alias: {
                'vue$': 'vue/dist/vue.common.js'
            }
        },
        externals: {
            'jquery': 'jQuery'
        }
   });

mix.autoload({
    'jquery': ['$', 'window.jQuery', 'jQuery'],
});

/* Start Remark Theme */

mix.styles([
    // Site base styles
        'resources/assets/remark/global/css/bootstrap.css',
        'resources/assets/remark/global/css/bootstrap-extend.css',
        'resources/assets/remark/assets/css/site.css',
    ], 'public/css/styles.css')
    // Remark Skins
    .copy('resources/assets/remark/assets/skins', 'public/css/skins')
    .copy('resources/assets/tinymce/skins/content', 'public/js/skins/content')
    .styles('resources/assets/tinymce/skins/content/wavo/content.css', 'public/js/skins/content/wavo/content.min.css')
    .copy('resources/assets/tinymce/skins/ui', 'public/js/skins/ui')
    .styles('resources/assets/tinymce/skins/ui/wavo/content.css', 'public/js/skins/ui/wavo/content.min.css')
    .styles('resources/assets/tinymce/skins/ui/wavo/skin.css', 'public/js/skins/ui/wavo/skin.min.css')
    .copy('resources/assets/tinymce/skins/ui/wavo/fonts', 'public/js/fonts/vendor/tinymce/skins/ui/wavo')
    .styles('resources/assets/remark/assets/skins/black.css', 'public/css/skins/black.min.css')
    .styles('resources/assets/remark/assets/skins/blue.css', 'public/css/skins/blue.min.css')
    .styles('resources/assets/remark/assets/skins/brown.css', 'public/css/skins/brown.min.css')
    .styles('resources/assets/remark/assets/skins/cyan.css', 'public/css/skins/cyan.min.css')
    .styles('resources/assets/remark/assets/skins/green.css', 'public/css/skins/green.min.css')
    .styles('resources/assets/remark/assets/skins/grey.css', 'public/css/skins/grey.min.css')
    .styles('resources/assets/remark/assets/skins/indigo.css', 'public/css/skins/indigo.min.css')
    .styles('resources/assets/remark/assets/skins/orange.css', 'public/css/skins/orange.min.css')
    .styles('resources/assets/remark/assets/skins/pink.css', 'public/css/skins/pink.min.css')
    .styles('resources/assets/remark/assets/skins/purple.css', 'public/css/skins/purple.min.css')
    .styles('resources/assets/remark/assets/skins/red.css', 'public/css/skins/red.min.css')
    .styles('resources/assets/remark/assets/skins/teal.css', 'public/css/skins/teal.min.css')
    .styles('resources/assets/remark/assets/skins/wavo.css', 'public/css/skins/wavo.min.css')
    .styles('resources/assets/remark/assets/skins/yellow.css', 'public/css/skins/yellow.min.css')
    .copy('resources/assets/css/v-calendar', 'public/css/v-calendar')
    .styles('resources/assets/css/v-calendar/grey.css', 'public/css/v-calendar/grey.min.css')
    .styles('resources/assets/css/v-calendar/black.css', 'public/css/v-calendar/black.min.css')
    .styles('resources/assets/css/v-calendar/brown.css', 'public/css/v-calendar/brown.min.css')
    .styles('resources/assets/css/v-calendar/cyan.css', 'public/css/v-calendar/cyan.min.css')
    .styles('resources/assets/css/v-calendar/wavo.css', 'public/css/v-calendar/wavo.min.css')
    // Template plugins
    .styles([
        // 'resources/assets/remark/global/vendor/animsition/animsition.css',
        'resources/assets/remark/global/vendor/asscrollable/asScrollable.css',
        // 'resources/assets/remark/global/vendor/switchery/switchery.css',
        'resources/assets/remark/global/vendor/slidepanel/slidePanel.css',
        'resources/assets/remark/global/vendor/flag-icon-css/flag-icon.css',
        // Additional plugins that may be needed
        'resources/assets/remark/global/vendor/intro-js/introjs.css',
        'resources/assets/remark/global/vendor/chartist/chartist.css',
        'resources/assets/remark/global/vendor/chartist-plugin-tooltip/chartist-plugin-tooltip.css',
        'resources/assets/remark/global/vendor/aspieprogress/asPieProgress.css',
        // 'resources/assets/remark/global/vendor/jquery-selective/jquery-selective.css',
        'resources/assets/remark/global/vendor/bootstrap-datepicker/bootstrap-datepicker.css',
        'resources/assets/remark/global/vendor/bootstrap-select/bootstrap-select.css',
        'resources/assets/remark/global/vendor/select2/select2.css',
        'resources/assets/remark/global/vendor/jquery-wizard/jquery-wizard.css',
        'resources/assets/remark/global/vendor/typeahead-js/typeahead.min.css',
    ], 'public/css/plugins.css')
    // datatable styles
    .styles([
        'resources/assets/remark/global/vendor/datatables.net-bs4/dataTables.bootstrap4.css',
        'resources/assets/remark/global/vendor/datatables.net-fixedheader-bs4/dataTables.fixedheader.bootstrap4.css',
        'resources/assets/remark/global/vendor/datatables.net-fixedcolumns-bs4/dataTables.fixedcolumns.bootstrap4.css',
        'resources/assets/remark/global/vendor/datatables.net-rowgroup-bs4/dataTables.rowgroup.bootstrap4.css',
        'resources/assets/remark/global/vendor/datatables.net-scroller-bs4/dataTables.scroller.bootstrap4.css',
        'resources/assets/remark/global/vendor/datatables.net-select-bs4/dataTables.select.bootstrap4.css',
        'resources/assets/remark/global/vendor/datatables.net-responsive-bs4/dataTables.responsive.bootstrap4.css',
        'resources/assets/remark/global/vendor/datatables.net-buttons-bs4/dataTables.buttons.bootstrap4.css',
    ], 'public/css/datatables.css')
    // Template fonts
    .styles([
        'resources/assets/remark/global/fonts/brand-icons/brand-icons.css',
        'resources/assets/remark/global/fonts/web-icons/web-icons.css',
        'resources/assets/remark/global/fonts/font-awesome/font-awesome.css',
    ], 'public/fonts/fonts.css')
    // Custom CSS Overrides
    .styles('resources/assets/css/custom.css', 'public/css/custom.css')
    .copy('resources/assets/css/pages', 'public/css/pages')
    // Icon Fonts
    .copy('resources/assets/remark/global/fonts/brand-icons', 'public/fonts')
    .copy('resources/assets/remark/global/fonts/web-icons', 'public/fonts')
    .copy('resources/assets/remark/global/fonts/font-awesome', 'public/fonts')
    // Breakpoint script placed in head
    .scripts([
        'resources/assets/remark/global/vendor/breakpoints/breakpoints.js',
    ], 'public/js/breakpoints.js')
    // Responsive scripts for head
    .copy('resources/assets/remark/global/vendor/html5shiv/html5shiv.min.js',
        'public/js/html5shiv.min.js')
    .copy('resources/assets/remark/global/vendor/media-match/media.match.min.js',
        'public/js/media.match.min.js')
    .copy('resources/assets/remark/global/vendor/respond/respond.min.js',
        'public/js/respond.min.js')
    .copy('resources/assets/remark/global/vendor/datatables.net-sorting/datetime-moment.js',
        'public/js/datetime-moment.js')
    .copy('resources/assets/js/papaparse.min.js',
        'public/js/papaparse.min.js')
    // Template images
    .copy('resources/assets/remark/global/photos', 'public/img')
    // Core and plugin dependencies
    .scripts([
        'resources/assets/remark/global/vendor/babel-external-helpers/babel-external-helpers.js',
        'resources/assets/remark/global/vendor/jquery/jquery.js',
        'resources/assets/remark/global/vendor/popper-js/umd/popper.js',
        'resources/assets/remark/global/vendor/bootstrap/bootstrap.js',
        // 'resources/assets/remark/global/vendor/animsition/animsition.js',
        'resources/assets/remark/global/vendor/mousewheel/jquery.mousewheel.js',
        'resources/assets/remark/global/vendor/asscrollbar/jquery-asScrollbar.js',
        'resources/assets/remark/global/vendor/asscrollable/jquery-asScrollable.js',
        'resources/assets/remark/global/vendor/ashoverscroll/jquery-asHoverScroll.js',
    ], 'public/js/core.js')
    // Additional plugins that might be needed
    .scripts([
        'resources/assets/remark/global/vendor/intro-js/intro.js',
        // 'resources/assets/remark/global/vendor/screenfull/screenfull.js',
        // 'resources/assets/remark/global/vendor/slidepanel/jquery-slidePanel.js',
        // 'resources/assets/remark/global/vendor/chartist/chartist.js',
        // 'resources/assets/remark/global/vendor/chartist-plugin-tooltip/chartist-plugin-tooltip.js',
        'resources/assets/remark/global/vendor/aspieprogress/jquery-asPieProgress.js',
        'resources/assets/remark/global/vendor/matchheight/jquery.matchHeight-min.js',
        // 'resources/assets/remark/global/vendor/jquery-selective/jquery-selective.min.js',
        'resources/assets/remark/global/vendor/bootstrap-datepicker/bootstrap-datepicker.js',
        'resources/assets/remark/global/vendor/bootstrap-select/bootstrap-select.js',
        'resources/assets/remark/global/vendor/select2/select2.full.min.js',
        'resources/assets/remark/global/vendor/typeahead-js/typeahead.bundle.min.js',
        // 'resources/assets/remark/global/vendor/switchery/switchery.js',
    ], 'public/js/plugins.js')
    // datatables plugin
    .scripts([
        'resources/assets/remark/global/vendor/datatables.net/jquery.dataTables.js',
        'resources/assets/remark/global/vendor/datatables.net-bs4/dataTables.bootstrap4.js',
        'resources/assets/remark/global/vendor/datatables.net-fixedheader/dataTables.fixedHeader.js',
        'resources/assets/remark/global/vendor/datatables.net-fixedcolumns/dataTables.fixedColumns.js',
        'resources/assets/remark/global/vendor/datatables.net-rowgroup/dataTables.rowGroup.js',
        'resources/assets/remark/global/vendor/datatables.net-scroller/dataTables.scroller.js',
        'resources/assets/remark/global/vendor/datatables.net-responsive/dataTables.responsive.js',
        'resources/assets/remark/global/vendor/datatables.net-responsive-bs4/responsive.bootstrap4.js',
        'resources/assets/remark/global/vendor/datatables.net-buttons/dataTables.buttons.js',
        'resources/assets/remark/global/vendor/datatables.net-buttons/buttons.html5.js',
        'resources/assets/remark/global/vendor/datatables.net-buttons/buttons.flash.js',
        'resources/assets/remark/global/vendor/datatables.net-buttons/buttons.print.js',
        'resources/assets/remark/global/vendor/datatables.net-buttons/buttons.colVis.js',
        'resources/assets/remark/global/vendor/datatables.net-buttons-bs4/buttons.bootstrap4.js',
    ], 'public/js/datatables.js')
    // Template component scripts
    .scripts([
        'resources/assets/remark/global/js/Component.js',
        'resources/assets/remark/global/js/Plugin.js',
        'resources/assets/remark/global/js/Base.js',
        'resources/assets/remark/global/js/Config.js',
        'resources/assets/remark/assets/js/Section/Menubar.js',
        // 'resources/assets/remark/assets/js/Section/GridMenu.js',
        'resources/assets/remark/assets/js/Section/Sidebar.js',
        'resources/assets/remark/assets/js/Section/PageAside.js',
        'resources/assets/remark/assets/js/Plugin/menu.js',
        // 'resources/assets/remark/global/js/config/colors.js',
        // 'resources/assets/remark/assets/js/Site.js'
    ], 'public/js/template.js')
    // Template Config scripts
    .scripts([
        // 'resources/assets/remark/global/js/config/colors.js'
    ], 'public/js/config.js')
    // .scripts('resources/assets/remark/assets/js/Site.js','public/js/site.js')
    // Template plugin wrapper scripts
    .scripts([
        'resources/assets/remark/assets/js/Site.js',
        'resources/assets/remark/global/js/Plugin/asscrollable.js',
        'resources/assets/remark/global/js/Plugin/slidepanel.js',
        // 'resources/assets/remark/global/js/Plugin/switchery.js',
        'resources/assets/remark/global/js/Plugin/matchheight.js',
        'resources/assets/remark/global/js/Plugin/aspieprogress.js',
        'resources/assets/remark/global/js/Plugin/datatables.js',
        'resources/assets/remark/global/js/Plugin/bootstrap-datepicker.js',
        'resources/assets/remark/global/js/Plugin/bootstrap-select.js',
        'resources/assets/remark/global/js/Plugin/select2.js',
        'resources/assets/remark/global/js/Plugin/input-group-file.js',
        'resources/assets/remark/global/js/Plugin/material.js',
    ], 'public/js/page.js');

if (mix.inProduction()) {
    mix.version();
    mix.disableNotifications();
}
