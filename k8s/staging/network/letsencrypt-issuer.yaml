apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: staging-letsencrypt-issuer
  namespace: wavo-staging
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: '<EMAIL>'
    preferredChain: ""
    privateKeySecretRef:
      name: staging-letsencrypt-issuer
    # Configure the challenge solvers.
    solvers:
      # An empty selector will 'match' all Certificate resources that
      # reference this Issuer.
      - selector: {}
        http01:
          ingress:
            class: ingress-nginx
