apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: staging-letsencrypt-staging-issuer
  namespace: wavo-staging
spec:
  acme:
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    email: '<EMAIL>'
    preferredChain: ""
    privateKeySecretRef:
      name: staging-letsencrypt-staging-issuer
    # Configure the challenge solvers.
    solvers:
      # An empty selector will 'match' all Certificate resources that
      # reference this Issuer.
#      - selector: {}
      - http01:
          ingress:
            class: ingress-nginx
