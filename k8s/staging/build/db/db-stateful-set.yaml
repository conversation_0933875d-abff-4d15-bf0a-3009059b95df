apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: staging-db-stateful-set
  namespace: wavo-staging
spec:
  replicas: 1
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      component: staging-db
  serviceName: "staging-db-cluster-ip-service"
  template:
    metadata:
      labels:
        component: staging-db
    spec:
      terminationGracePeriodSeconds: 10
      tolerations:
        - key: cloud.google.com/gke-preemptible
          operator: Equal
          value: "true"
          effect: NoSchedule
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                - key: node-group
                  operator: In
                  values:
                    - "cheap"
                - key: node-group
                  operator: NotIn
                  values:
                    - "default"
                    - "redis"
                    - "browser"
                    - "browser120"
                    - "elastic"
                    - "emailengine"
      containers:
        - name: staging-db
          image: mysql:8.0.30
          ports:
            - containerPort: 3306
              name: staging-db
          envFrom:
            - secretRef:
                name: staging-mysql-secret
          volumeMounts:
            - name: mysql-persistent-storage
              mountPath: "/var/lib/mysql"
              subPath: "mysql-data"
            - name: config-volume
              mountPath: /etc/mysql/mysql.conf.d
#              mountPath: /etc/mysql/mysql.conf.d/mysqld.cnf
#          livenessProbe:
#            tcpSocket:
#              port: 3306
#            initialDelaySeconds: 15
#            periodSeconds: 15
#          readinessProbe:
#            tcpSocket:
#              port: 3306
#            initialDelaySeconds: 30
#            periodSeconds: 10
      volumes:
        - name: mysql-persistent-storage
          persistentVolumeClaim:
            claimName: staging-db8-persistent-volume-claim
        - name: config-volume
          configMap:
            name: staging-mysql-config-new
            optional: true
