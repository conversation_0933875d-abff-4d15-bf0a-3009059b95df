apiVersion: v1
items:
- apiVersion: cert-manager.io/v1alpha3
  kind: Issuer
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"Issuer","metadata":{"annotations":{},"name":"production-wildcard-letsencrypt-issuer","namespace":"wavo-production"},"spec":{"acme":{"email":"<EMAIL>","privateKeySecretRef":{"name":"production-wildcard-letsencrypt-issuer"},"server":"https://acme-v02.api.letsencrypt.org/directory","solvers":[{"dns01":{"clouddns":{"project":"wavo-225922","serviceAccountSecretRef":{"key":"key.json","name":"clouddns-sa"}}},"selector":{"matchLabels":{"use-clouddns-solver":"true"}}}]}}}
    creationTimestamp: "2022-01-26T20:56:39Z"
    generation: 1
    name: production-wildcard-letsencrypt-issuer
    namespace: wavo-production
    resourceVersion: "*********"
    uid: ********-d8a7-45ba-886c-e552461b232a
  spec:
    acme:
      email: <EMAIL>
      privateKeySecretRef:
        name: production-wildcard-letsencrypt-issuer
      server: https://acme-v02.api.letsencrypt.org/directory
      solvers:
      - dns01:
          clouddns:
            project: wavo-225922
            serviceAccountSecretRef:
              key: key.json
              name: clouddns-sa
        selector:
          matchLabels:
            use-clouddns-solver: "true"
  status:
    acme:
      lastRegisteredEmail: <EMAIL>
      uri: https://acme-v02.api.letsencrypt.org/acme/acct/********
    conditions:
    - lastTransitionTime: "2022-01-26T20:56:40Z"
      message: The ACME account was registered with the ACME server
      reason: ACMEAccountRegistered
      status: "True"
      type: Ready
- apiVersion: cert-manager.io/v1alpha3
  kind: Issuer
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"Issuer","metadata":{"annotations":{},"name":"staging-wildcard-letsencrypt-issuer","namespace":"wavo-staging"},"spec":{"acme":{"email":"<EMAIL>","privateKeySecretRef":{"name":"staging-wildcard-letsencrypt-issuer"},"server":"https://acme-v02.api.letsencrypt.org/directory","solvers":[{"dns01":{"clouddns":{"project":"wavo-225922","serviceAccountSecretRef":{"key":"key.json","name":"clouddns-sa"}}},"selector":{"matchLabels":{"use-clouddns-solver":"true"}}}]}}}
    creationTimestamp: "2022-01-26T20:57:49Z"
    generation: 1
    name: staging-wildcard-letsencrypt-issuer
    namespace: wavo-staging
    resourceVersion: "*********"
    uid: 4074030f-6ca7-4ab2-b6c9-e84d6ed88821
  spec:
    acme:
      email: <EMAIL>
      privateKeySecretRef:
        name: staging-wildcard-letsencrypt-issuer
      server: https://acme-v02.api.letsencrypt.org/directory
      solvers:
      - dns01:
          clouddns:
            project: wavo-225922
            serviceAccountSecretRef:
              key: key.json
              name: clouddns-sa
        selector:
          matchLabels:
            use-clouddns-solver: "true"
  status:
    acme:
      lastRegisteredEmail: <EMAIL>
      uri: https://acme-v02.api.letsencrypt.org/acme/acct/********
    conditions:
    - lastTransitionTime: "2022-01-26T20:57:50Z"
      message: The ACME account was registered with the ACME server
      reason: ACMEAccountRegistered
      status: "True"
      type: Ready
- apiVersion: cert-manager.io/v1alpha3
  kind: ClusterIssuer
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"ClusterIssuer","metadata":{"annotations":{},"name":"production-letsencrypt-issuer"},"spec":{"acme":{"email":"<EMAIL>","privateKeySecretRef":{"name":"production-letsencrypt-issuer"},"server":"https://acme-v02.api.letsencrypt.org/directory","solvers":[{"http01":{"ingress":{"class":"nginx"}},"selector":{}}]}}}
    creationTimestamp: "2022-01-26T20:56:26Z"
    generation: 1
    name: production-letsencrypt-issuer
    resourceVersion: "*********"
    uid: f8661e6f-8165-4660-9f2e-b8b1ab398f95
  spec:
    acme:
      email: <EMAIL>
      privateKeySecretRef:
        name: production-letsencrypt-issuer
      server: https://acme-v02.api.letsencrypt.org/directory
      solvers:
      - http01:
          ingress:
            class: nginx
        selector: {}
  status:
    acme:
      lastRegisteredEmail: <EMAIL>
      uri: https://acme-v02.api.letsencrypt.org/acme/acct/*********
    conditions:
    - lastTransitionTime: "2022-01-26T20:56:27Z"
      message: The ACME account was registered with the ACME server
      reason: ACMEAccountRegistered
      status: "True"
      type: Ready
- apiVersion: cert-manager.io/v1alpha3
  kind: ClusterIssuer
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"ClusterIssuer","metadata":{"annotations":{},"name":"staging-letsencrypt-issuer"},"spec":{"acme":{"email":"<EMAIL>","privateKeySecretRef":{"name":"staging-letsencrypt-issuer"},"server":"https://acme-v02.api.letsencrypt.org/directory","solvers":[{"http01":{"ingress":{"class":"nginx"}},"selector":{}}]}}}
    creationTimestamp: "2022-01-26T20:57:30Z"
    generation: 1
    name: staging-letsencrypt-issuer
    resourceVersion: "*********"
    uid: ff58bb80-5a00-482a-bd30-8ecd5848d575
  spec:
    acme:
      email: <EMAIL>
      privateKeySecretRef:
        name: staging-letsencrypt-issuer
      server: https://acme-v02.api.letsencrypt.org/directory
      solvers:
      - http01:
          ingress:
            class: nginx
        selector: {}
  status:
    acme:
      lastRegisteredEmail: <EMAIL>
      uri: https://acme-v02.api.letsencrypt.org/acme/acct/*********
    conditions:
    - lastTransitionTime: "2022-01-26T20:57:30Z"
      message: The ACME account was registered with the ACME server
      reason: ACMEAccountRegistered
      status: "True"
      type: Ready
- apiVersion: cert-manager.io/v1alpha3
  kind: Certificate
  metadata:
    creationTimestamp: "2022-01-26T21:09:38Z"
    generation: 8
    name: production-ingress-service-cert
    namespace: wavo-production
    ownerReferences:
    - apiVersion: extensions/v1beta1
      blockOwnerDeletion: true
      controller: true
      kind: Ingress
      name: production-ingress-service
      uid: 8088d702-b382-11e9-b7d3-42010a8e0102
    resourceVersion: "*********"
    uid: a653f205-8553-4ed7-87c9-b6a40733aeb3
  spec:
    dnsNames:
    - app2.askhuron.com
    - dash.reignmakers.io
    - outreach.sparkoutbound.com
    - app.wavo.co
    - app.brokergenerator.com
    - outbound.techpromarketing.com
    - prospecting.superhumansales.com
    - email.moreresultsmarketing.com
    - wavo.leadable.io
    - app.fivecontacts.com
    - send.emailmovers-group.com
    - leadfactory.whitelabelclub.com
    - email.automationwolf.com
    - emailogin.salesleadautomation.com
    - app.inboxmaps.com
    - outreach.soleadify.com
    - app.snaptiv.com
    - app.b2bleadflow.io
    - campaigns.leadroll.co
    - email.brainylabs.io
    - outbound.agencyleads.pro
    - email.leadfactury.com
    - campaigns.salezilla.io
    - campaigns.quantumga.com
    - app.boonsnap.com
    - app.tryupsurge.com
    - portal.leadfeed.pro
    - hub.growthsource.io
    - amazing.netskiel.com
    - dash.mailstrats.com
    - worldleaders.automatedsalesprospecting.com
    - campaigns.leadsoft.io
    - app.reachsupreme.com
    - email.ai-bees.io
    - app.leadforce360.com
    - outreach.fuzeiq.com
    - outreach.fuzeiq.com
    issuerRef:
      group: cert-manager.io
      kind: ClusterIssuer
      name: production-letsencrypt-issuer
    secretName: production-ingress-service-cert
  status:
    conditions:
    - lastTransitionTime: "2022-01-27T16:49:09Z"
      message: Certificate is up to date and has not expired
      reason: Ready
      status: "True"
      type: Ready
    notAfter: "2022-04-27T15:49:07Z"
    notBefore: "2022-01-27T15:49:08Z"
    renewalTime: "2022-03-28T15:49:07Z"
    revision: 1
- apiVersion: cert-manager.io/v1alpha3
  kind: Certificate
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"Certificate","label":{"use-clouddns-solver":"true"},"metadata":{"annotations":{},"name":"production-wildcard-cert","namespace":"wavo-production"},"spec":{"commonName":"*.wavo.co","dnsNames":["*.wavo.co"],"issuerRef":{"name":"production-wildcard-letsencrypt-issuer"},"secretName":"production-wildcard-cert-secret"}}
    creationTimestamp: "2022-01-26T20:56:47Z"
    generation: 1
    name: production-wildcard-cert
    namespace: wavo-production
    resourceVersion: "518176655"
    uid: f99bb25e-c6a1-4be2-b446-b380b3131de8
  spec:
    commonName: '*.wavo.co'
    dnsNames:
    - '*.wavo.co'
    issuerRef:
      name: production-wildcard-letsencrypt-issuer
    secretName: production-wildcard-cert-secret
  status:
    conditions:
    - lastTransitionTime: "2022-01-27T16:13:11Z"
      message: Certificate is up to date and has not expired
      reason: Ready
      status: "True"
      type: Ready
    notAfter: "2022-04-27T15:49:05Z"
    notBefore: "2022-01-27T15:49:06Z"
    renewalTime: "2022-03-28T15:49:05Z"
    revision: 1
- apiVersion: cert-manager.io/v1alpha3
  kind: Certificate
  metadata:
    creationTimestamp: "2022-01-26T21:03:15Z"
    generation: 2
    name: staging-ingress-service-cert
    namespace: wavo-staging
    ownerReferences:
    - apiVersion: extensions/v1beta1
      blockOwnerDeletion: true
      controller: true
      kind: Ingress
      name: staging-ingress-service
      uid: 5aaeebb3-ecb8-45e3-a9ed-3c2fc5c215a1
    resourceVersion: "518176520"
    uid: 9c0a3f30-8326-4cc0-ad37-50b4226a100e
  spec:
    dnsNames:
    - temp.trywavo.com
    issuerRef:
      group: cert-manager.io
      kind: ClusterIssuer
      name: staging-letsencrypt-issuer
    secretName: staging-ingress-service-cert
  status:
    conditions:
    - lastTransitionTime: "2022-01-26T21:05:45Z"
      message: Certificate is up to date and has not expired
      reason: Ready
      status: "True"
      type: Ready
    notAfter: "2022-04-26T20:05:43Z"
    notBefore: "2022-01-26T20:05:44Z"
    renewalTime: "2022-03-27T20:05:43Z"
- apiVersion: cert-manager.io/v1alpha3
  kind: Certificate
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"Certificate","label":{"use-clouddns-solver":"true"},"metadata":{"annotations":{},"name":"staging-wildcard-cert","namespace":"wavo-staging"},"spec":{"commonName":"*.trywavo.com","dnsNames":["*.trywavo.com"],"issuerRef":{"name":"staging-wildcard-letsencrypt-issuer"},"secretName":"staging-wildcard-cert-secret"}}
    creationTimestamp: "2022-01-26T20:57:56Z"
    generation: 1
    name: staging-wildcard-cert
    namespace: wavo-staging
    resourceVersion: "518176648"
    uid: f11b24b2-0245-4e25-bf62-e1a77f821270
  spec:
    commonName: '*.trywavo.com'
    dnsNames:
    - '*.trywavo.com'
    issuerRef:
      name: staging-wildcard-letsencrypt-issuer
    secretName: staging-wildcard-cert-secret
  status:
    conditions:
    - lastTransitionTime: "2022-01-27T16:13:11Z"
      message: Certificate is up to date and has not expired
      reason: Ready
      status: "True"
      type: Ready
    notAfter: "2022-04-27T15:49:05Z"
    notBefore: "2022-01-27T15:49:06Z"
    renewalTime: "2022-03-28T15:49:05Z"
    revision: 1
- apiVersion: cert-manager.io/v1alpha3
  kind: CertificateRequest
  metadata:
    annotations:
      cert-manager.io/certificate-name: production-ingress-service-cert
      cert-manager.io/certificate-revision: "1"
      cert-manager.io/private-key-secret-name: production-ingress-service-cert-7vl4d
    creationTimestamp: "2022-01-27T16:49:03Z"
    generateName: production-ingress-service-cert-
    generation: 1
    name: production-ingress-service-cert-gfn97
    namespace: wavo-production
    ownerReferences:
    - apiVersion: cert-manager.io/v1alpha2
      blockOwnerDeletion: true
      controller: true
      kind: Certificate
      name: production-ingress-service-cert
      uid: a653f205-8553-4ed7-87c9-b6a40733aeb3
    resourceVersion: "518176636"
    uid: b0f0b989-77ce-465a-97c1-de6e8efce5f6
  spec:
    csr: 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
    issuerRef:
      group: cert-manager.io
      kind: ClusterIssuer
      name: production-letsencrypt-issuer
  status:
    certificate: 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
    conditions:
    - lastTransitionTime: "2022-01-27T16:49:09Z"
      message: Certificate fetched from issuer successfully
      reason: Issued
      status: "True"
      type: Ready
- apiVersion: cert-manager.io/v1alpha3
  kind: CertificateRequest
  metadata:
    annotations:
      cert-manager.io/certificate-name: production-wildcard-cert
      cert-manager.io/certificate-revision: "1"
      cert-manager.io/private-key-secret-name: production-wildcard-cert-wcmjp
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"Certificate","label":{"use-clouddns-solver":"true"},"metadata":{"annotations":{},"name":"production-wildcard-cert","namespace":"wavo-production"},"spec":{"commonName":"*.wavo.co","dnsNames":["*.wavo.co"],"issuerRef":{"name":"production-wildcard-letsencrypt-issuer"},"secretName":"production-wildcard-cert-secret"}}
    creationTimestamp: "2022-01-27T16:49:05Z"
    generateName: production-wildcard-cert-
    generation: 1
    name: production-wildcard-cert-hkjgk
    namespace: wavo-production
    ownerReferences:
    - apiVersion: cert-manager.io/v1alpha2
      blockOwnerDeletion: true
      controller: true
      kind: Certificate
      name: production-wildcard-cert
      uid: f99bb25e-c6a1-4be2-b446-b380b3131de8
    resourceVersion: "518176622"
    uid: 658a8fea-ed49-47e2-add1-c3d53909a74a
  spec:
    csr: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0KTUlJQ2dEQ0NBV2dDQVFBd0ZERVNNQkFHQTFVRUF3d0pLaTUzWVhadkxtTnZNSUlCSWpBTkJna3Foa2lHOXcwQgpBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFyR203eGliN3lJM3JlWGg5eEZCWXVrbFB1ZXR1VXZZY0FzVW1xLzhFCkl1Q205MFpGdGF1YlJ2MTFIRHpodXBYbVRWR011RTJxa3M3RkIxeW9tOThmd1FscHZPcVZBNnFYY2YwdU5XdGoKMXFsVkprZkpteHVodVJOL3lEUk9yYXlKQ0piT3pvZFVlc21CRE4yVlU4V1o1YVhVZzBnV1dqKzgzUEVncFlEVwpRWTV5akdrSmppbndJY3dYNTZWZTlLaWNiS2V4bWlYRjlpcWRtWm5IVmltWm5KRjBibDdITTRsb1MvRzNIWTZrCjBLK2l2ZlFUTnFJcDhYZUlvU3FzbnZhdzFZeUpuamhwOUZBOEN1bnZOYkZqZWFmbnpTbHFuVThMdGxWZGZYemoKQkQvenBIWFlmYUFJNjZiaVRQbm95MVhwNUJkVVFTcWpYMG90S1g3RlNXa1VQd0lEQVFBQm9DY3dKUVlKS29aSQpodmNOQVFrT01SZ3dGakFVQmdOVkhSRUVEVEFMZ2drcUxuZGhkbTh1WTI4d0RRWUpLb1pJaHZjTkFRRUxCUUFECmdnRUJBSFBKd3pJSXoxR2E3UGtUVmFQaVpFem1nTWYyQlAwb0hrQzVIYkFRYjhkMWR6MFFvVTM5OWNBdmNmQ2YKMXpocFR3cXZpOHBPNmxSbW04cFh5akI4ZXVua3RCSi9ETWRIOEM3cGNqVytBcnpQMS9BVk13WHVGZkNPWGxONQo4TFFrT0FlNEY4eWJxU3plQ3FtVDkxVUNpMi81Mmx2VlNLV0VkT3VUdWNDQUZtN1Vzc3BodUdad3Zkay90S0QzCjdMeGNlNkZnclBFZXl3dkMxdGV4ZkRDeVdqLy84UXhpWlA0WkR4MGJlWmVBZ0FYdHpKQk44Y05MdGNMc0wrVm4KYXlHeFBRMy9QbHZnQjV1LzRUSW9lRnhRRHhNT1RmWUZlQ1NtVG5IWS82K2ltc3RtOXJVWWpVWjJuZkhwVUpFNwpGb0lqdkVqYjdveU1xVjhSZkd3TGlWUXljbUU9Ci0tLS0tRU5EIENFUlRJRklDQVRFIFJFUVVFU1QtLS0tLQo=
    issuerRef:
      name: production-wildcard-letsencrypt-issuer
  status:
    certificate: 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
    conditions:
    - lastTransitionTime: "2022-01-27T16:49:08Z"
      message: Certificate fetched from issuer successfully
      reason: Issued
      status: "True"
      type: Ready
- apiVersion: cert-manager.io/v1alpha3
  kind: CertificateRequest
  metadata:
    annotations:
      cert-manager.io/certificate-name: staging-ingress-service-cert
      cert-manager.io/private-key-secret-name: staging-ingress-service-cert
    creationTimestamp: "2022-01-26T21:05:16Z"
    generation: 1
    name: staging-ingress-service-cert-2081258468
    namespace: wavo-staging
    ownerReferences:
    - apiVersion: cert-manager.io/v1alpha2
      blockOwnerDeletion: true
      controller: true
      kind: Certificate
      name: staging-ingress-service-cert
      uid: 9c0a3f30-8326-4cc0-ad37-50b4226a100e
    resourceVersion: "517656285"
    uid: 12760c69-4872-48ad-9ad8-88b0679d0b08
  spec:
    csr: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0KTUlJQ2lqQ0NBWElDQVFBd0Z6RVZNQk1HQTFVRUNoTU1ZMlZ5ZEMxdFlXNWhaMlZ5TUlJQklqQU5CZ2txaGtpRwo5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBMHJhRnF4NXo5U001RCtqUlRJNmdzWUJMSDdFR09BR0UyOVM3CjBNbnJlN0tUSzBIOHBjRnc1T052Vlp5VmRJQlhvdDIxK2F0OWF3MmJHUjd6Rks4NGZSUmpOT3VhT2s2WkpZRkYKeFh2NkY1ZWVMU3lFS0tlbVUzc1VwWWhmVWt0TkdqVUxiTGxPUmRobVhxOE9tNGVUOU1GZEpXTnJ5bTd5bHBFWgpkYmVKenFEYUpGQTkvTnFiMG9NNllITDBJMXk2Y3pySTFtUXV5d2ZqMVBDVlZ1bTgrZDd0Ry94Y2xLRXFFYVYzCm1reExWdmVwT0J1LzVWQWNqT3FNNHNzNTZ6ZUhOMlZveDdQNHFqVm1QV3pxb01vQ0k4d0svQXNWcWpxaUFQb0EKaG84dFZ2SDNHdjcyTm13UHJLOERxemlUWFd1RFdPZHgxU3J2Njk1cHl0V01WVUxqUlFJREFRQUJvQzR3TEFZSgpLb1pJaHZjTkFRa09NUjh3SFRBYkJnTlZIUkVFRkRBU2doQjBaVzF3TG5SeWVYZGhkbTh1WTI5dE1BMEdDU3FHClNJYjNEUUVCQ3dVQUE0SUJBUUJDVEhPTyt3N3JhWkZ1VGFaZ0ZLV1VsV28vQnMrcHdaTDhMbmRQMjI2OTJ6TnAKM01JVWRoRjhHVU4vb25Sc1RVMUI1RTJlaktrcXRDSitvWXJ1OFJVREhrR3FJMHhXMHBObGU5NDloVE9PcEMzSAp5MHk5Q0RRUWRKRUFTVFJ3WlRYcDhiWVlrUWI1cUZ5Z3hoQnZ5amdSQnJncVIrMGZYSFU2VHdHbUNYRGMzQXN0ClpFVHJjT1AzcW1kY09CY29qTUxwRzl0all4Y1ZWQXRpbFk4Y3lWRXRyQUtkTkFrMDJ4RXE2YnlsQ0QzVGplZWoKUk95cjcvdDNBSzFNa0ZoYW1jeFRQN2JuRmRGRGlnUDZQTnhtT0ZEK25pRjBzTDVEMWhSWHpXVkRzYTZBNlZHQwpzUE5LMkVVSkZaNTZTUU1TQ1hQV1VsRUpFdDBUYzlNZFdmZ1BLeGpVCi0tLS0tRU5EIENFUlRJRklDQVRFIFJFUVVFU1QtLS0tLQo=
    issuerRef:
      group: cert-manager.io
      kind: ClusterIssuer
      name: staging-letsencrypt-issuer
  status:
    certificate: 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
    conditions:
    - lastTransitionTime: "2022-01-26T21:05:45Z"
      message: Certificate fetched from issuer successfully
      reason: Issued
      status: "True"
      type: Ready
- apiVersion: cert-manager.io/v1alpha3
  kind: CertificateRequest
  metadata:
    annotations:
      cert-manager.io/certificate-name: staging-wildcard-cert
      cert-manager.io/certificate-revision: "1"
      cert-manager.io/private-key-secret-name: staging-wildcard-cert-5nhnc
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"Certificate","label":{"use-clouddns-solver":"true"},"metadata":{"annotations":{},"name":"staging-wildcard-cert","namespace":"wavo-staging"},"spec":{"commonName":"*.trywavo.com","dnsNames":["*.trywavo.com"],"issuerRef":{"name":"staging-wildcard-letsencrypt-issuer"},"secretName":"staging-wildcard-cert-secret"}}
    creationTimestamp: "2022-01-27T16:49:04Z"
    generateName: staging-wildcard-cert-
    generation: 1
    name: staging-wildcard-cert-4rmhf
    namespace: wavo-staging
    ownerReferences:
    - apiVersion: cert-manager.io/v1alpha2
      blockOwnerDeletion: true
      controller: true
      kind: Certificate
      name: staging-wildcard-cert
      uid: f11b24b2-0245-4e25-bf62-e1a77f821270
    resourceVersion: "518176619"
    uid: 3ea34796-b582-4a88-9f7b-9a5ee551a616
  spec:
    csr: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0KTUlJQ2lEQ0NBWEFDQVFBd0dERVdNQlFHQTFVRUF3d05LaTUwY25sM1lYWnZMbU52YlRDQ0FTSXdEUVlKS29aSQpodmNOQVFFQkJRQURnZ0VQQURDQ0FRb0NnZ0VCQU5vVFRaWHVLQndDQ05EY1RkUU0xcXE2MjNWMnd5MFYySWpFCmhhSERCRktDY294aVNtbU54SUVaTXhjREpFUk9Cd1JIV2hUYnhpWllTSERRZU5tekxHYkg4VkdVL3BGaGxIN1cKcTZLNXpEMUVLRjA4RU5jb0NOazgvSHV3aE13TmMxTGZPZFozMUNGZU44MW85bWU3NjNzT1hwYm5MZ2NGS2l3bwpIdnNKQVJGeCtiZkI5OVFuTGU0QVFFUkZ3NFExTFkydzNzWXh6U2tHcUptc1R5ZXhMMFYvQXZxOWthWGd5bzhhCk1kMVphcXpmeVc2TjFTNThsUXBXcVdERnNmZm1qNTR5QTBmQlVsdHF6WVZ1T0VJWEVYbmNqcGFyeWVNQUphQSsKWHRzbzJzWGZYT3c5bW5yM1JkYk9qZHRMaU8zMzhlVWVMb0tERmw3ZjlqN3FHZjBZQlY4Q0F3RUFBYUFyTUNrRwpDU3FHU0liM0RRRUpEakVjTUJvd0dBWURWUjBSQkJFd0Q0SU5LaTUwY25sM1lYWnZMbU52YlRBTkJna3Foa2lHCjl3MEJBUXNGQUFPQ0FRRUFaU2FLWDlCWXY2dkZhNndWTlNHMi9EMVdlMFBndkxCUU1hMmJDSnV0VTdrSEpwM0MKVzVsZUgxVktVZjduR0d5aVcrMUFXTFNuNGRhdUJzeVhrRkJoUmZFR2Vkb3VqN2xIb3NUM2ZFYXRGK0pEUnJDMApjNHdSWS9MVkppSmw3QkQ0Z0dPaVJmSDhycWtzWXRMQTFWZU1BRSt0d0dYdnZMVWF5ZUJYQ3B2SFpydkM5ZDRLClZ0OHdHV1VsaTIvRGZUMlRkN0F0YVhDUWd3aC9MVVVKdlRGR1QrUE0wVTA3VHNUazJ4c3lNcTBUQTJ2TWZadzkKS3pNMlU4WmxJbXBQMnduUGZMQkFIWkpKZUtIYWIxekRDU29UU1NjbHY5a2g4RXBjWjcycTFqOWVwdU90WFdoUQp5WFdhYVp4VmdsUEQwVlEwbEprSGQ5RHBMd2pVOFBLUk1ub2NiZz09Ci0tLS0tRU5EIENFUlRJRklDQVRFIFJFUVVFU1QtLS0tLQo=
    issuerRef:
      name: staging-wildcard-letsencrypt-issuer
  status:
    certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUZJRENDQkFpZ0F3SUJBZ0lTQStTQ0Jpd210SGk2ZFZkVmQ5TW1PcVM5TUEwR0NTcUdTSWIzRFFFQkN3VUEKTURJeEN6QUpCZ05WQkFZVEFsVlRNUll3RkFZRFZRUUtFdzFNWlhRbmN5QkZibU55ZVhCME1Rc3dDUVlEVlFRRApFd0pTTXpBZUZ3MHlNakF4TWpjeE5UUTVNRFphRncweU1qQTBNamN4TlRRNU1EVmFNQmd4RmpBVUJnTlZCQU1NCkRTb3VkSEo1ZDJGMmJ5NWpiMjB3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRGEKRTAyVjdpZ2NBZ2pRM0UzVUROYXF1dHQxZHNNdEZkaUl4SVdod3dSU2duS01Za3BwamNTQkdUTVhBeVJFVGdjRQpSMW9VMjhZbVdFaHcwSGpac3l4bXgvRlJsUDZSWVpSKzFxdWl1Y3c5UkNoZFBCRFhLQWpaUFB4N3NJVE1EWE5TCjN6bldkOVFoWGpmTmFQWm51K3Q3RGw2VzV5NEhCU29zS0I3N0NRRVJjZm0zd2ZmVUp5M3VBRUJFUmNPRU5TMk4Kc043R01jMHBCcWlackU4bnNTOUZmd0w2dlpHbDRNcVBHakhkV1dxczM4bHVqZFV1ZkpVS1ZxbGd4YkgzNW8rZQpNZ05Id1ZKYmFzMkZiamhDRnhGNTNJNldxOG5qQUNXZ1BsN2JLTnJGMzF6c1BacDY5MFhXem8zYlM0anQ5L0hsCkhpNkNneFplMy9ZKzZobjlHQVZmQWdNQkFBR2pnZ0pJTUlJQ1JEQU9CZ05WSFE4QkFmOEVCQU1DQmFBd0hRWUQKVlIwbEJCWXdGQVlJS3dZQkJRVUhBd0VHQ0NzR0FRVUZCd01DTUF3R0ExVWRFd0VCL3dRQ01BQXdIUVlEVlIwTwpCQllFRk5OaklzdHU3UGtlMUlWQXRsTXRNamtjalFXd01COEdBMVVkSXdRWU1CYUFGQlF1c3hlM1dGYkxybEFKClFPWWZyNTJMRk1MR01GVUdDQ3NHQVFVRkJ3RUJCRWt3UnpBaEJnZ3JCZ0VGQlFjd0FZWVZhSFIwY0RvdkwzSXoKTG04dWJHVnVZM0l1YjNKbk1DSUdDQ3NHQVFVRkJ6QUNoaFpvZEhSd09pOHZjak11YVM1c1pXNWpjaTV2Y21jdgpNQmdHQTFVZEVRUVJNQStDRFNvdWRISjVkMkYyYnk1amIyMHdUQVlEVlIwZ0JFVXdRekFJQmdabmdRd0JBZ0V3Ck53WUxLd1lCQkFHQzN4TUJBUUV3S0RBbUJnZ3JCZ0VGQlFjQ0FSWWFhSFIwY0RvdkwyTndjeTVzWlhSelpXNWoKY25sd2RDNXZjbWN3Z2dFRUJnb3JCZ0VFQWRaNUFnUUNCSUgxQklIeUFQQUFkUUJCeU1xeDN5SkdTaERHb1RvSgpRb2RlVGpHTEd3UHI2MHZIYVBDUVlwWUc5Z0FBQVg2Y2NOT3VBQUFFQXdCR01FUUNJSDFGSmZrVzM0ZGVHeEovClZPQlJuMUJ2MjNHdTBkdXNlYlNUMVZKYW9DN1JBaUJjOHBLSy9qRTR4YkI2OEtsdnFDQVlIMHZpMittMDAySEwKdkVCZVdhZUVkUUIzQUVhbFZldDErcEVnTUxXaWlXbjA4MzBSTEVGMHZ2MUp1SVdyOHZ4dy9tMUhBQUFCZnB4dwowODRBQUFRREFFZ3dSZ0loQUprQkt4N0VheCtOayt2VmJYTy9mQldwUVdza1AwREp3ajhzU2kyYWF5c2xBaUVBCnBJcjVMV213VElLL0ZHdFJUcnBSeXR3Q05vb1JRZGQ5Qnp1V0xUbWRPNXN3RFFZSktvWklodmNOQVFFTEJRQUQKZ2dFQkFJd1AzNmFmYzk2aHk0R3ZjckMxVzhzZkdGNVVhYmhGcG9KTlQ3RjZxU1pQamtXRnpxTE5TTktkbG5JbApJN012cm1RcEZNUG9YaW9CTGhpbFN5YmZNUUVXMXdqVEppNEZTSmtQclMwdEZPdWkzanIvcE1hY1ExYWhvR0hUCkZnVkxnMmpjMUJEbWl0bEZyV2tPaDVvaXBsNUttcDhRTWFMMXExS0tGZlZKcWVXcGsvWUZ4YnkrSmRyWTRtLzgKTVB4MDZwMCtPNDMzTUJzQ2VSekduaWtTajZjN3dGbVo2dTNocCt3bG1GRnBYT3FkRjlvcjFkMzZHY0JoY2hHNApUZ3o5dlRWemtnUnkxdHRVREY5emFhKzM3Y2FTcHZGbXJjQ3c5aS9kZnN4WERlZ2RxWVJKZHVzRkxGbmIwcWV5Cm5xaHUzS3VONldUWlkwbWJPYkVGc21ZRHppQT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQotLS0tLUJFR0lOIENFUlRJRklDQVRFLS0tLS0KTUlJRkZqQ0NBdjZnQXdJQkFnSVJBSkVyQ0VyUERCaW5VL2JXTGlXblgxb3dEUVlKS29aSWh2Y05BUUVMQlFBdwpUekVMTUFrR0ExVUVCaE1DVlZNeEtUQW5CZ05WQkFvVElFbHVkR1Z5Ym1WMElGTmxZM1Z5YVhSNUlGSmxjMlZoCmNtTm9JRWR5YjNWd01SVXdFd1lEVlFRREV3eEpVMUpISUZKdmIzUWdXREV3SGhjTk1qQXdPVEEwTURBd01EQXcKV2hjTk1qVXdPVEUxTVRZd01EQXdXakF5TVFzd0NRWURWUVFHRXdKVlV6RVdNQlFHQTFVRUNoTU5UR1YwSjNNZwpSVzVqY25sd2RERUxNQWtHQTFVRUF4TUNVak13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUM3QWhVb3pQYWdsTk1QRXV5TlZaTEQrSUx4bWFaNlFvaW5YU2FxdFN1NXhVeXhyNDVyK1hYSW85Y1AKUjVRVVZUVlhqSjZvb2prWjlZSThRcWxPYnZVN3d5N2JqY0N3WFBOWk9PZnR6Mm53V2dzYnZzQ1VKQ1dIK2pkeApzeFBuSEt6aG0rL2I1RHRGVWtXV3FjRlR6alRJVXU2MXJ1MlAzbUJ3NHFWVXE3WnREcGVsUURScks5TzhadXRtCk5IejZhNHVQVnltWitEQVhYYnB5Yi91QnhhM1NobGc5RjhmbkNidnhLL2VHM01IYWNWM1VSdVBNclNYQmlMeGcKWjNWbXMvRVk5NkpjNWxQL09vaTJSNlgvRXhqcW1BbDNQNTFUK2M4QjVmV21jQmNVcjJPay81bXprNTNjVTZjRwova2lGSGFGcHJpVjF1eFBNVWdQMTdWR2hpOXNWQWdNQkFBR2pnZ0VJTUlJQkJEQU9CZ05WSFE4QkFmOEVCQU1DCkFZWXdIUVlEVlIwbEJCWXdGQVlJS3dZQkJRVUhBd0lHQ0NzR0FRVUZCd01CTUJJR0ExVWRFd0VCL3dRSU1BWUIKQWY4Q0FRQXdIUVlEVlIwT0JCWUVGQlF1c3hlM1dGYkxybEFKUU9ZZnI1MkxGTUxHTUI4R0ExVWRJd1FZTUJhQQpGSG0wV2VaN3R1WGtBWE9BQ0lqSUdsajI2WnR1TURJR0NDc0dBUVVGQndFQkJDWXdKREFpQmdnckJnRUZCUWN3CkFvWVdhSFIwY0RvdkwzZ3hMbWt1YkdWdVkzSXViM0puTHpBbkJnTlZIUjhFSURBZU1CeWdHcUFZaGhab2RIUncKT2k4dmVERXVZeTVzWlc1amNpNXZjbWN2TUNJR0ExVWRJQVFiTUJrd0NBWUdaNEVNQVFJQk1BMEdDeXNHQVFRQgpndDhUQVFFQk1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQ0FRQ0Z5azVIUHFQM2hVU0Z2TlZuZUxLWVk2MTFUUjZXClBUTmxjbFF0Z2FEcXcrMzRJTDlmekxkd0FMZHVPL1plbE43a0lKK203NHV5QStlaXRSWThrYzYwN1RrQzUzd2wKaWtmbVpXNC9SdlRaOE02VUsrNVV6aEs4akNkTHVNR1lMNkt2elhHUlNnaTN5TGdqZXdRdENQa0lWejZEMlFRegpDa2NoZUFtQ0o4TXF5SnU1emx6eVpNakF2bm5BVDQ1dFJBeGVrcnN1OTRzUTRlZ2RSQ25iV1NEdFk3a2grQkltCmxKTlhvQjFsQk1FS0lxNFFEVU9Yb1JnZmZ1RGdoamUxV3JHOU1MK0hiaXNxL3lGT0d3WEQ5UmlYOEY2c3c2VzQKYXZBdXZEc3p1ZTVMM3N6ODVLK0VDNFkvd0ZWRE52Wm80VFlYYW82WjBmK2xRS2MwdDhEUVl6azFPWFZ1OHJwMgp5Sk1DNmFsTGJCZk9EQUxadllIN243ZG8xQVpsczRJOWQxUDRqbmtEclFveEIzVXFROWhWbDNMRUtRNzN4RjFPCnlLNUdoRERYOG9WZkdLRjV1K2RlY0lzSDRZYVR3N21QM0dGeEpTcXYzKzBsVUZKb2k1TGM1ZGExNDlwOTBJZHMKaENFeHJvTDErN21yeUlrWFBlRk01VGdPOXIwcnZaYUJGT3ZWMnowZ3AzNVowK0w0V1BsYnVFak4vbHhQRmluKwpIbFVqcjhnUnNJM3FmSk9RRnkvOXJLSUpSMFkvOE9td3QvOG9UV2d5MW1kZUhtbWprN2oxbllzdkM5SlNRNlp2Ck1sZGxUVEtCM3poVGhWMStYV1lwNnJqZDVKVzF6YlZXRWtMTnhFN0dKVGhFVUczc3pnQlZHUDdwU1dUVVRzcVgKbkxSYndIT29xN2hId2c9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCi0tLS0tQkVHSU4gQ0VSVElGSUNBVEUtLS0tLQpNSUlGWURDQ0JFaWdBd0lCQWdJUVFBRjNJVGZVNlVLNDduYXFQR1FLdHpBTkJna3Foa2lHOXcwQkFRc0ZBREEvCk1TUXdJZ1lEVlFRS0V4dEVhV2RwZEdGc0lGTnBaMjVoZEhWeVpTQlVjblZ6ZENCRGJ5NHhGekFWQmdOVkJBTVQKRGtSVFZDQlNiMjkwSUVOQklGZ3pNQjRYRFRJeE1ERXlNREU1TVRRd00xb1hEVEkwTURrek1ERTRNVFF3TTFvdwpUekVMTUFrR0ExVUVCaE1DVlZNeEtUQW5CZ05WQkFvVElFbHVkR1Z5Ym1WMElGTmxZM1Z5YVhSNUlGSmxjMlZoCmNtTm9JRWR5YjNWd01SVXdFd1lEVlFRREV3eEpVMUpISUZKdmIzUWdXREV3Z2dJaU1BMEdDU3FHU0liM0RRRUIKQVFVQUE0SUNEd0F3Z2dJS0FvSUNBUUN0NkNSejlCUTM4NXVlSzFjb0hJZSszTGZmT0pDTWJqem1WNkI0OTNYQwpvdjcxYW03MkFFOG8yOTVvaG14RWs3YXhZLzBVRW11L0g5THFNWnNoZnRFelBMcEk5ZDE1MzdPNC94THhJWnBMCndZcUdjV2xLWm1ac2ozNDhjTCt0S1NJRzgrVEE1b0N1NGt1UHQ1bCtsQU9mMDBlWGZKbElJMVBvT0s1UENtK0QKTHRGSlY0eUFkTGJhTDlBNGpYc0RjQ0ViZGZJd1BQcVBydDNhWTZ2ckZrL0NqaEZMZnM4TDZQKzFkeTcwc250Swo0RXdTSlF4d2pRTXBvT0ZUSk93VDJlNFp2eEN6U293L2lhTmhVZDZzaHdlVTlHTng3QzdpYjF1WWdlR0pYRFI1CmJIYnZPNUJpZWViYnBKb3ZKc1hRRU9FTzN0a1FqaGI3dC9lbzk4ZmxBZ2VZanpZSWxlZmlONVlOTm5XZSt3NXkKc1IyYnZBUDVTUVhZZ2QwRnRDcldRZW1zQVhhVkNnL1kzOVc5RWg4MUx5Z1hiTktZd2FnSlpIZHVSemU2enF4WgpYbWlkZjNMV2ljVUdRU2srV1Q3ZEp2VWt5UkduV3FOTVFCOUdvWm0xcHpwUmJvWTdubjF5cHhJRmVGbnRQbEY0CkZRc0RqNDNRTHdXeVBudEtIRXR6QlJMOHh1cmdVQk44UTVOMHM4cDA1NDRmQVFqUU1OUmJjVGEwQjdyQk1EQmMKU0xlQ081aW1mV0NLb3FNcGdzeTZ2WU1FRzZLREEwR2gxZ1h4RzhLMjhLaDhoanRHcUVncWlOeDJtbmEvSDJxbApQUm1QNnpqelpON0lLdzBLS1AvMzIrSVZRdFFpMENkZDRYbitHT2R3aUsxTzV0bUxPc2JkSjFGdS83eGs5VE5EClR3SURBUUFCbzRJQlJqQ0NBVUl3RHdZRFZSMFRBUUgvQkFVd0F3RUIvekFPQmdOVkhROEJBZjhFQkFNQ0FRWXcKU3dZSUt3WUJCUVVIQVFFRVB6QTlNRHNHQ0NzR0FRVUZCekFDaGk5b2RIUndPaTh2WVhCd2N5NXBaR1Z1ZEhKMQpjM1F1WTI5dEwzSnZiM1J6TDJSemRISnZiM1JqWVhnekxuQTNZekFmQmdOVkhTTUVHREFXZ0JURXA3R2tleXh4Cit0dmhTNUIxLzhRVllJV0pFREJVQmdOVkhTQUVUVEJMTUFnR0JtZUJEQUVDQVRBL0Jnc3JCZ0VFQVlMZkV3RUIKQVRBd01DNEdDQ3NHQVFVRkJ3SUJGaUpvZEhSd09pOHZZM0J6TG5KdmIzUXRlREV1YkdWMGMyVnVZM0o1Y0hRdQpiM0puTUR3R0ExVWRId1ExTURNd01hQXZvQzJHSzJoMGRIQTZMeTlqY213dWFXUmxiblJ5ZFhOMExtTnZiUzlFClUxUlNUMDlVUTBGWU0wTlNUQzVqY213d0hRWURWUjBPQkJZRUZIbTBXZVo3dHVYa0FYT0FDSWpJR2xqMjZadHUKTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCQVFBS2N3QnNsbTcvRGxMUXJ0Mk01MW9HclMrbzQ0Ky95UW9ERlZEQwo1V3hDdTIrYjlMUlB3a1NJQ0hYTTZ3ZWJGR0p1ZU43c0o3bzVYUFdpb1c1V2xIQVFVN0c3NUsvUW9zTXJBZFNXCjlNVWdOVFA1MkdFMjRIR050TGkxcW9KRmxjRHlxU01vNTlhaHkyY0kycUJETEtvYmt4L0ozdldyYVYwVDlWdUcKV0NMS1RWWGtjR2R0d2xmRlJqbEJ6NHBZZzFodG1mNVg2RFlPOEE0anF2MklsOURqWEE2VVNiVzFGelhTTHI5TwpoZThZNElXUzZ3WTdiQ2tqQ1dEY1JRSk1FaGc3NmZzTzN0eEUrRmlZcnVxOVJVV2hpRjFteXY0UTZXK0N5QkZDCkRmdnA3T09HQU42ZEVPTTQrcVI5c2Rqb1NZS0VCcHNyNkd0UEFRdzRkeTc1M2VjNQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
    conditions:
    - lastTransitionTime: "2022-01-27T16:49:07Z"
      message: Certificate fetched from issuer successfully
      reason: Issued
      status: "True"
      type: Ready
kind: List
metadata:
  resourceVersion: ""
  selfLink: ""
