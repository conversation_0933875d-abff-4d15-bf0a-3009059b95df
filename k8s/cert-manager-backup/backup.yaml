apiVersion: v1
items:
- apiVersion: cert-manager.io/v1
  kind: Issuer
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"Issuer","metadata":{"annotations":{},"name":"production-wildcard-letsencrypt-issuer","namespace":"wavo-production"},"spec":{"acme":{"email":"<EMAIL>","privateKeySecretRef":{"name":"production-wildcard-letsencrypt-issuer"},"server":"https://acme-v02.api.letsencrypt.org/directory","solvers":[{"dns01":{"clouddns":{"project":"wavo-225922","serviceAccountSecretRef":{"key":"key.json","name":"clouddns-sa"}}},"selector":{"matchLabels":{"use-clouddns-solver":"true"}}}]}}}
    creationTimestamp: "2022-01-26T20:56:39Z"
    generation: 1
    name: production-wildcard-letsencrypt-issuer
    namespace: wavo-production
    resourceVersion: "*********"
    uid: ********-d8a7-45ba-886c-e552461b232a
  spec:
    acme:
      email: <EMAIL>
      preferredChain: ""
      privateKeySecretRef:
        name: production-wildcard-letsencrypt-issuer
      server: https://acme-v02.api.letsencrypt.org/directory
      solvers:
      - dns01:
          cloudDNS:
            project: wavo-225922
            serviceAccountSecretRef:
              key: key.json
              name: clouddns-sa
        selector:
          matchLabels:
            use-clouddns-solver: "true"
  status:
    acme:
      lastRegisteredEmail: <EMAIL>
      uri: https://acme-v02.api.letsencrypt.org/acme/acct/********
    conditions:
    - lastTransitionTime: "2022-01-26T20:56:40Z"
      message: The ACME account was registered with the ACME server
      reason: ACMEAccountRegistered
      status: "True"
      type: Ready
- apiVersion: cert-manager.io/v1
  kind: Issuer
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"Issuer","metadata":{"annotations":{},"name":"staging-wildcard-letsencrypt-issuer","namespace":"wavo-staging"},"spec":{"acme":{"email":"<EMAIL>","privateKeySecretRef":{"name":"staging-wildcard-letsencrypt-issuer"},"server":"https://acme-v02.api.letsencrypt.org/directory","solvers":[{"dns01":{"clouddns":{"project":"wavo-225922","serviceAccountSecretRef":{"key":"key.json","name":"clouddns-sa"}}},"selector":{"matchLabels":{"use-clouddns-solver":"true"}}}]}}}
    creationTimestamp: "2022-01-26T20:57:49Z"
    generation: 1
    name: staging-wildcard-letsencrypt-issuer
    namespace: wavo-staging
    resourceVersion: "*********"
    uid: 4074030f-6ca7-4ab2-b6c9-e84d6ed88821
  spec:
    acme:
      email: <EMAIL>
      preferredChain: ""
      privateKeySecretRef:
        name: staging-wildcard-letsencrypt-issuer
      server: https://acme-v02.api.letsencrypt.org/directory
      solvers:
      - dns01:
          cloudDNS:
            project: wavo-225922
            serviceAccountSecretRef:
              key: key.json
              name: clouddns-sa
        selector:
          matchLabels:
            use-clouddns-solver: "true"
  status:
    acme:
      lastRegisteredEmail: <EMAIL>
      uri: https://acme-v02.api.letsencrypt.org/acme/acct/********
    conditions:
    - lastTransitionTime: "2022-01-26T20:57:50Z"
      message: The ACME account was registered with the ACME server
      reason: ACMEAccountRegistered
      status: "True"
      type: Ready
- apiVersion: cert-manager.io/v1
  kind: ClusterIssuer
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"ClusterIssuer","metadata":{"annotations":{},"name":"production-letsencrypt-issuer"},"spec":{"acme":{"email":"<EMAIL>","privateKeySecretRef":{"name":"production-letsencrypt-issuer"},"server":"https://acme-v02.api.letsencrypt.org/directory","solvers":[{"http01":{"ingress":{"class":"nginx"}},"selector":{}}]}}}
    creationTimestamp: "2022-01-26T20:56:26Z"
    generation: 1
    name: production-letsencrypt-issuer
    resourceVersion: "*********"
    uid: f8661e6f-8165-4660-9f2e-b8b1ab398f95
  spec:
    acme:
      email: <EMAIL>
      preferredChain: ""
      privateKeySecretRef:
        name: production-letsencrypt-issuer
      server: https://acme-v02.api.letsencrypt.org/directory
      solvers:
      - http01:
          ingress:
            class: nginx
        selector: {}
  status:
    acme:
      lastRegisteredEmail: <EMAIL>
      uri: https://acme-v02.api.letsencrypt.org/acme/acct/*********
    conditions:
    - lastTransitionTime: "2022-01-26T20:56:27Z"
      message: The ACME account was registered with the ACME server
      reason: ACMEAccountRegistered
      status: "True"
      type: Ready
- apiVersion: cert-manager.io/v1
  kind: ClusterIssuer
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"ClusterIssuer","metadata":{"annotations":{},"name":"staging-letsencrypt-issuer"},"spec":{"acme":{"email":"<EMAIL>","privateKeySecretRef":{"name":"staging-letsencrypt-issuer"},"server":"https://acme-v02.api.letsencrypt.org/directory","solvers":[{"http01":{"ingress":{"class":"nginx"}},"selector":{}}]}}}
    creationTimestamp: "2022-01-26T20:57:30Z"
    generation: 1
    name: staging-letsencrypt-issuer
    resourceVersion: "*********"
    uid: ff58bb80-5a00-482a-bd30-8ecd5848d575
  spec:
    acme:
      email: <EMAIL>
      preferredChain: ""
      privateKeySecretRef:
        name: staging-letsencrypt-issuer
      server: https://acme-v02.api.letsencrypt.org/directory
      solvers:
      - http01:
          ingress:
            class: nginx
        selector: {}
  status:
    acme:
      lastRegisteredEmail: <EMAIL>
      uri: https://acme-v02.api.letsencrypt.org/acme/acct/*********
    conditions:
    - lastTransitionTime: "2022-01-26T20:57:30Z"
      message: The ACME account was registered with the ACME server
      reason: ACMEAccountRegistered
      status: "True"
      type: Ready
- apiVersion: cert-manager.io/v1
  kind: Certificate
  metadata:
    creationTimestamp: "2022-01-26T21:09:38Z"
    generation: 8
    name: production-ingress-service-cert
    namespace: wavo-production
    ownerReferences:
    - apiVersion: extensions/v1beta1
      blockOwnerDeletion: true
      controller: true
      kind: Ingress
      name: production-ingress-service
      uid: 8088d702-b382-11e9-b7d3-42010a8e0102
    resourceVersion: "*********"
    uid: a653f205-8553-4ed7-87c9-b6a40733aeb3
  spec:
    dnsNames:
    - app2.askhuron.com
    - dash.reignmakers.io
    - outreach.sparkoutbound.com
    - app.wavo.co
    - app.brokergenerator.com
    - outbound.techpromarketing.com
    - prospecting.superhumansales.com
    - email.moreresultsmarketing.com
    - wavo.leadable.io
    - app.fivecontacts.com
    - send.emailmovers-group.com
    - leadfactory.whitelabelclub.com
    - email.automationwolf.com
    - emailogin.salesleadautomation.com
    - app.inboxmaps.com
    - outreach.soleadify.com
    - app.snaptiv.com
    - app.b2bleadflow.io
    - campaigns.leadroll.co
    - email.brainylabs.io
    - outbound.agencyleads.pro
    - email.leadfactury.com
    - campaigns.salezilla.io
    - campaigns.quantumga.com
    - app.boonsnap.com
    - app.tryupsurge.com
    - portal.leadfeed.pro
    - hub.growthsource.io
    - amazing.netskiel.com
    - dash.mailstrats.com
    - worldleaders.automatedsalesprospecting.com
    - campaigns.leadsoft.io
    - app.reachsupreme.com
    - email.ai-bees.io
    - app.leadforce360.com
    - outreach.fuzeiq.com
    - outreach.fuzeiq.com
    issuerRef:
      group: cert-manager.io
      kind: ClusterIssuer
      name: production-letsencrypt-issuer
    secretName: production-ingress-service-cert
  status:
    conditions:
    - lastTransitionTime: "2022-01-27T16:49:09Z"
      message: Certificate is up to date and has not expired
      reason: Ready
      status: "True"
      type: Ready
    notAfter: "2022-04-27T15:49:07Z"
    notBefore: "2022-01-27T15:49:08Z"
    renewalTime: "2022-03-28T15:49:07Z"
    revision: 1
- apiVersion: cert-manager.io/v1
  kind: Certificate
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"Certificate","label":{"use-clouddns-solver":"true"},"metadata":{"annotations":{},"name":"production-wildcard-cert","namespace":"wavo-production"},"spec":{"commonName":"*.wavo.co","dnsNames":["*.wavo.co"],"issuerRef":{"name":"production-wildcard-letsencrypt-issuer"},"secretName":"production-wildcard-cert-secret"}}
    creationTimestamp: "2022-01-26T20:56:47Z"
    generation: 1
    name: production-wildcard-cert
    namespace: wavo-production
    resourceVersion: "518180712"
    uid: f99bb25e-c6a1-4be2-b446-b380b3131de8
  spec:
    commonName: '*.wavo.co'
    dnsNames:
    - '*.wavo.co'
    issuerRef:
      name: production-wildcard-letsencrypt-issuer
    secretName: production-wildcard-cert-secret
  status:
    conditions:
    - lastTransitionTime: "2022-01-27T16:13:11Z"
      message: Certificate is up to date and has not expired
      reason: Ready
      status: "True"
      type: Ready
    notAfter: "2022-04-27T15:49:05Z"
    notBefore: "2022-01-27T15:49:06Z"
    renewalTime: "2022-03-28T15:49:05Z"
    revision: 1
- apiVersion: cert-manager.io/v1
  kind: Certificate
  metadata:
    creationTimestamp: "2022-01-26T21:03:15Z"
    generation: 2
    name: staging-ingress-service-cert
    namespace: wavo-staging
    ownerReferences:
    - apiVersion: extensions/v1beta1
      blockOwnerDeletion: true
      controller: true
      kind: Ingress
      name: staging-ingress-service
      uid: 5aaeebb3-ecb8-45e3-a9ed-3c2fc5c215a1
    resourceVersion: "518180709"
    uid: 9c0a3f30-8326-4cc0-ad37-50b4226a100e
  spec:
    dnsNames:
    - temp.trywavo.com
    issuerRef:
      group: cert-manager.io
      kind: ClusterIssuer
      name: staging-letsencrypt-issuer
    secretName: staging-ingress-service-cert
  status:
    conditions:
    - lastTransitionTime: "2022-01-26T21:05:45Z"
      message: Certificate is up to date and has not expired
      reason: Ready
      status: "True"
      type: Ready
    notAfter: "2022-04-26T20:05:43Z"
    notBefore: "2022-01-26T20:05:44Z"
    renewalTime: "2022-03-27T20:05:43Z"
- apiVersion: cert-manager.io/v1
  kind: Certificate
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"cert-manager.io/v1alpha2","kind":"Certificate","label":{"use-clouddns-solver":"true"},"metadata":{"annotations":{},"name":"staging-wildcard-cert","namespace":"wavo-staging"},"spec":{"commonName":"*.trywavo.com","dnsNames":["*.trywavo.com"],"issuerRef":{"name":"staging-wildcard-letsencrypt-issuer"},"secretName":"staging-wildcard-cert-secret"}}
    creationTimestamp: "2022-01-26T20:57:56Z"
    generation: 1
    name: staging-wildcard-cert
    namespace: wavo-staging
    resourceVersion: "518180710"
    uid: f11b24b2-0245-4e25-bf62-e1a77f821270
  spec:
    commonName: '*.trywavo.com'
    dnsNames:
    - '*.trywavo.com'
    issuerRef:
      name: staging-wildcard-letsencrypt-issuer
    secretName: staging-wildcard-cert-secret
  status:
    conditions:
    - lastTransitionTime: "2022-01-27T16:13:11Z"
      message: Certificate is up to date and has not expired
      reason: Ready
      status: "True"
      type: Ready
    notAfter: "2022-04-27T15:49:05Z"
    notBefore: "2022-01-27T15:49:06Z"
    renewalTime: "2022-03-28T15:49:05Z"
    revision: 1
kind: List
metadata:
  resourceVersion: ""
  selfLink: ""
