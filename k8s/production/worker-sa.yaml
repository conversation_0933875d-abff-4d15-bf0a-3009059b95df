apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: production-worker-clusterrole
  namespace: wavo-production
rules:
  - apiGroups:
      - "networking.k8s.io"
    resources:
      - ingresses
    verbs:
      - get
      - list
      - watch
      - patch
  - apiGroups:
      - "networking.k8s.io"
    resources:
      - ingresses/status
    verbs:
      - update
  - apiGroups:
      - "cert-manager.io"
    resources:
      - certificates
    verbs:
      - get
      - list
      - delete
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: production-k8s-worker-role-binding
  namespace: wavo-production
subjects:
  - kind: ServiceAccount
    name: production-worker-sa
    namespace: wavo-production
roleRef:
  kind: ClusterRole
  name: production-worker-clusterrole
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: production-worker-sa
  namespace: wavo-production
