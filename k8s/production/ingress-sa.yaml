apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: production-nginx-ingress-clusterrole
  namespace: wavo-production
rules:
  - apiGroups:
      - "networking.k8s.io"
    resources:
      - ingresses
    verbs:
      - get
      - list
      - watch
      - patch
  - apiGroups:
      - "networking.k8s.io"
    resources:
      - ingresses/status
    verbs:
      - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: production-k8s-ingress-role-binding
  namespace: wavo-production
subjects:
  - kind: ServiceAccount
    name: production-ingress-sa
    namespace: wavo-production
roleRef:
  kind: ClusterRole
  name: production-nginx-ingress-clusterrole
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: production-ingress-sa
  namespace: wavo-production
