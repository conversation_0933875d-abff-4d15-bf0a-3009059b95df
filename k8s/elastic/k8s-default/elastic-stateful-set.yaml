apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: production-elastic-stateful-set
  namespace: wavo-production
spec:
  replicas: 1
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      component: production-elastic
  serviceName: "production-elastic-cluster-ip-service"
  template:
    metadata:
      labels:
        component: production-elastic
    spec:
      terminationGracePeriodSeconds: 10
      securityContext:
        fsGroup: 1000
      initContainers:
        - image: alpine:latest
          command: ["/sbin/sysctl", "-w", "vm.max_map_count=262144"]
          name: elasticsearch-init
          securityContext:
            privileged: true
      containers:
        - name: elasticsearch
          resources:
            requests:
              memory: 2000M
            limits:
              memory: 2000M
          securityContext:
            privileged: true
            runAsUser: 1000
            capabilities:
              add:
                - IPC_LOCK
                - SYS_RESOURCE
          image: docker.elastic.co/elasticsearch/elasticsearch:7.4.0
          env:
            - name: ES_JAVA_OPTS
              value: "-Xms1g -Xmx1g"
            - name: discovery.type
              value: single-node
          readinessProbe:
            tcpSocket:
              port: es-transport
            initialDelaySeconds: 60
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: es-transport
            initialDelaySeconds: 60
            periodSeconds: 60
          ports:
            - containerPort: 9200
              name: es-http
              protocol: TCP
            - containerPort: 9300
              name: es-transport
              protocol: TCP
          volumeMounts:
            - name: elastic-persistent-storage
              mountPath: "/usr/share/elasticsearch/data"
      volumes:
        - name: elastic-persistent-storage
          persistentVolumeClaim:
            claimName: production-elastic-persistent-volume-claim
