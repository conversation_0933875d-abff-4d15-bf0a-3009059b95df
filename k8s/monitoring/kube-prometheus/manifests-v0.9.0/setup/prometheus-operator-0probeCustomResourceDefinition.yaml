apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.4.1
  creationTimestamp: null
  name: probes.monitoring.coreos.com
spec:
  group: monitoring.coreos.com
  names:
    categories:
    - prometheus-operator
    kind: Probe
    listKind: ProbeList
    plural: probes
    singular: probe
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: Probe defines monitoring for a set of static targets or ingresses.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Specification of desired Ingress selection for target discovery by Prometheus.
            properties:
              authorization:
                description: Authorization section for this endpoint
                properties:
                  credentials:
                    description: The secret's key that contains the credentials of the request
                    properties:
                      key:
                        description: The key of the secret to select from.  Must be a valid secret key.
                        type: string
                      name:
                        description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                        type: string
                      optional:
                        description: Specify whether the Secret or its key must be defined
                        type: boolean
                    required:
                    - key
                    type: object
                  type:
                    description: Set the authentication type. Defaults to Bearer, Basic will cause an error
                    type: string
                type: object
              basicAuth:
                description: 'BasicAuth allow an endpoint to authenticate over basic authentication. More info: https://prometheus.io/docs/operating/configuration/#endpoint'
                properties:
                  password:
                    description: The secret in the service monitor namespace that contains the password for authentication.
                    properties:
                      key:
                        description: The key of the secret to select from.  Must be a valid secret key.
                        type: string
                      name:
                        description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                        type: string
                      optional:
                        description: Specify whether the Secret or its key must be defined
                        type: boolean
                    required:
                    - key
                    type: object
                  username:
                    description: The secret in the service monitor namespace that contains the username for authentication.
                    properties:
                      key:
                        description: The key of the secret to select from.  Must be a valid secret key.
                        type: string
                      name:
                        description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                        type: string
                      optional:
                        description: Specify whether the Secret or its key must be defined
                        type: boolean
                    required:
                    - key
                    type: object
                type: object
              bearerTokenSecret:
                description: Secret to mount to read bearer token for scraping targets. The secret needs to be in the same namespace as the probe and accessible by the Prometheus Operator.
                properties:
                  key:
                    description: The key of the secret to select from.  Must be a valid secret key.
                    type: string
                  name:
                    description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                    type: string
                  optional:
                    description: Specify whether the Secret or its key must be defined
                    type: boolean
                required:
                - key
                type: object
              interval:
                description: Interval at which targets are probed using the configured prober. If not specified Prometheus' global scrape interval is used.
                type: string
              jobName:
                description: The job name assigned to scraped metrics by default.
                type: string
              labelLimit:
                description: Per-scrape limit on number of labels that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.
                format: int64
                type: integer
              labelNameLengthLimit:
                description: Per-scrape limit on length of labels name that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.
                format: int64
                type: integer
              labelValueLengthLimit:
                description: Per-scrape limit on length of labels value that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.
                format: int64
                type: integer
              module:
                description: 'The module to use for probing specifying how to probe the target. Example module configuring in the blackbox exporter: https://github.com/prometheus/blackbox_exporter/blob/master/example.yml'
                type: string
              oauth2:
                description: OAuth2 for the URL. Only valid in Prometheus versions 2.27.0 and newer.
                properties:
                  clientId:
                    description: The secret or configmap containing the OAuth2 client id
                    properties:
                      configMap:
                        description: ConfigMap containing data to use for the targets.
                        properties:
                          key:
                            description: The key to select.
                            type: string
                          name:
                            description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                            type: string
                          optional:
                            description: Specify whether the ConfigMap or its key must be defined
                            type: boolean
                        required:
                        - key
                        type: object
                      secret:
                        description: Secret containing data to use for the targets.
                        properties:
                          key:
                            description: The key of the secret to select from.  Must be a valid secret key.
                            type: string
                          name:
                            description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                            type: string
                          optional:
                            description: Specify whether the Secret or its key must be defined
                            type: boolean
                        required:
                        - key
                        type: object
                    type: object
                  clientSecret:
                    description: The secret containing the OAuth2 client secret
                    properties:
                      key:
                        description: The key of the secret to select from.  Must be a valid secret key.
                        type: string
                      name:
                        description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                        type: string
                      optional:
                        description: Specify whether the Secret or its key must be defined
                        type: boolean
                    required:
                    - key
                    type: object
                  endpointParams:
                    additionalProperties:
                      type: string
                    description: Parameters to append to the token URL
                    type: object
                  scopes:
                    description: OAuth2 scopes used for the token request
                    items:
                      type: string
                    type: array
                  tokenUrl:
                    description: The URL to fetch the token from
                    minLength: 1
                    type: string
                required:
                - clientId
                - clientSecret
                - tokenUrl
                type: object
              prober:
                description: Specification for the prober to use for probing targets. The prober.URL parameter is required. Targets cannot be probed if left empty.
                properties:
                  path:
                    description: Path to collect metrics from. Defaults to `/probe`.
                    type: string
                  proxyUrl:
                    description: Optional ProxyURL.
                    type: string
                  scheme:
                    description: HTTP scheme to use for scraping. Defaults to `http`.
                    type: string
                  url:
                    description: Mandatory URL of the prober.
                    type: string
                required:
                - url
                type: object
              sampleLimit:
                description: SampleLimit defines per-scrape limit on number of scraped samples that will be accepted.
                format: int64
                type: integer
              scrapeTimeout:
                description: Timeout for scraping metrics from the Prometheus exporter.
                type: string
              targetLimit:
                description: TargetLimit defines a limit on the number of scraped targets that will be accepted.
                format: int64
                type: integer
              targets:
                description: Targets defines a set of static and/or dynamically discovered targets to be probed using the prober.
                properties:
                  ingress:
                    description: Ingress defines the set of dynamically discovered ingress objects which hosts are considered for probing.
                    properties:
                      namespaceSelector:
                        description: Select Ingress objects by namespace.
                        properties:
                          any:
                            description: Boolean describing whether all namespaces are selected in contrast to a list restricting them.
                            type: boolean
                          matchNames:
                            description: List of namespace names.
                            items:
                              type: string
                            type: array
                        type: object
                      relabelingConfigs:
                        description: 'RelabelConfigs to apply to samples before ingestion. More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#relabel_config'
                        items:
                          description: 'RelabelConfig allows dynamic rewriting of the label set, being applied to samples before ingestion. It defines `<metric_relabel_configs>`-section of Prometheus configuration. More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#metric_relabel_configs'
                          properties:
                            action:
                              description: Action to perform based on regex matching. Default is 'replace'
                              type: string
                            modulus:
                              description: Modulus to take of the hash of the source label values.
                              format: int64
                              type: integer
                            regex:
                              description: Regular expression against which the extracted value is matched. Default is '(.*)'
                              type: string
                            replacement:
                              description: Replacement value against which a regex replace is performed if the regular expression matches. Regex capture groups are available. Default is '$1'
                              type: string
                            separator:
                              description: Separator placed between concatenated source label values. default is ';'.
                              type: string
                            sourceLabels:
                              description: The source labels select values from existing labels. Their content is concatenated using the configured separator and matched against the configured regular expression for the replace, keep, and drop actions.
                              items:
                                type: string
                              type: array
                            targetLabel:
                              description: Label to which the resulting value is written in a replace action. It is mandatory for replace actions. Regex capture groups are available.
                              type: string
                          type: object
                        type: array
                      selector:
                        description: Select Ingress objects by labels.
                        properties:
                          matchExpressions:
                            description: matchExpressions is a list of label selector requirements. The requirements are ANDed.
                            items:
                              description: A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.
                              properties:
                                key:
                                  description: key is the label key that the selector applies to.
                                  type: string
                                operator:
                                  description: operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.
                                  type: string
                                values:
                                  description: values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.
                                  items:
                                    type: string
                                  type: array
                              required:
                              - key
                              - operator
                              type: object
                            type: array
                          matchLabels:
                            additionalProperties:
                              type: string
                            description: matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is "key", the operator is "In", and the values array contains only "value". The requirements are ANDed.
                            type: object
                        type: object
                    type: object
                  staticConfig:
                    description: 'StaticConfig defines static targets which are considers for probing. More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#static_config.'
                    properties:
                      labels:
                        additionalProperties:
                          type: string
                        description: Labels assigned to all metrics scraped from the targets.
                        type: object
                      relabelingConfigs:
                        description: 'RelabelConfigs to apply to samples before ingestion. More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#relabel_config'
                        items:
                          description: 'RelabelConfig allows dynamic rewriting of the label set, being applied to samples before ingestion. It defines `<metric_relabel_configs>`-section of Prometheus configuration. More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#metric_relabel_configs'
                          properties:
                            action:
                              description: Action to perform based on regex matching. Default is 'replace'
                              type: string
                            modulus:
                              description: Modulus to take of the hash of the source label values.
                              format: int64
                              type: integer
                            regex:
                              description: Regular expression against which the extracted value is matched. Default is '(.*)'
                              type: string
                            replacement:
                              description: Replacement value against which a regex replace is performed if the regular expression matches. Regex capture groups are available. Default is '$1'
                              type: string
                            separator:
                              description: Separator placed between concatenated source label values. default is ';'.
                              type: string
                            sourceLabels:
                              description: The source labels select values from existing labels. Their content is concatenated using the configured separator and matched against the configured regular expression for the replace, keep, and drop actions.
                              items:
                                type: string
                              type: array
                            targetLabel:
                              description: Label to which the resulting value is written in a replace action. It is mandatory for replace actions. Regex capture groups are available.
                              type: string
                          type: object
                        type: array
                      static:
                        description: Targets is a list of URLs to probe using the configured prober.
                        items:
                          type: string
                        type: array
                    type: object
                type: object
              tlsConfig:
                description: TLS configuration to use when scraping the endpoint.
                properties:
                  ca:
                    description: Struct containing the CA cert to use for the targets.
                    properties:
                      configMap:
                        description: ConfigMap containing data to use for the targets.
                        properties:
                          key:
                            description: The key to select.
                            type: string
                          name:
                            description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                            type: string
                          optional:
                            description: Specify whether the ConfigMap or its key must be defined
                            type: boolean
                        required:
                        - key
                        type: object
                      secret:
                        description: Secret containing data to use for the targets.
                        properties:
                          key:
                            description: The key of the secret to select from.  Must be a valid secret key.
                            type: string
                          name:
                            description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                            type: string
                          optional:
                            description: Specify whether the Secret or its key must be defined
                            type: boolean
                        required:
                        - key
                        type: object
                    type: object
                  cert:
                    description: Struct containing the client cert file for the targets.
                    properties:
                      configMap:
                        description: ConfigMap containing data to use for the targets.
                        properties:
                          key:
                            description: The key to select.
                            type: string
                          name:
                            description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                            type: string
                          optional:
                            description: Specify whether the ConfigMap or its key must be defined
                            type: boolean
                        required:
                        - key
                        type: object
                      secret:
                        description: Secret containing data to use for the targets.
                        properties:
                          key:
                            description: The key of the secret to select from.  Must be a valid secret key.
                            type: string
                          name:
                            description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                            type: string
                          optional:
                            description: Specify whether the Secret or its key must be defined
                            type: boolean
                        required:
                        - key
                        type: object
                    type: object
                  insecureSkipVerify:
                    description: Disable target certificate validation.
                    type: boolean
                  keySecret:
                    description: Secret containing the client key file for the targets.
                    properties:
                      key:
                        description: The key of the secret to select from.  Must be a valid secret key.
                        type: string
                      name:
                        description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?'
                        type: string
                      optional:
                        description: Specify whether the Secret or its key must be defined
                        type: boolean
                    required:
                    - key
                    type: object
                  serverName:
                    description: Used to verify the hostname for the targets.
                    type: string
                type: object
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
