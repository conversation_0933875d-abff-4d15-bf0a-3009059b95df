<?php
/**
 * Campaigns
 */

Route::prefix('/campaigns')->name('campaigns.')->middleware(['auth', 'verified', 'checkMember', 'verifySubscription'])->group(function () use ($pluralTeamString) {

    // Get the campaigns of a specific team (as json). Looks like we dont need this
    //Route::get('/'.$pluralTeamString .'/{team}', 'CampaignController@OfTeam')->name('of-team');

    // Main Campaign routes
    Route::get('/', 'CampaignController@index')->name('index');
    Route::get('/list', 'CampaignController@list')->name('list');
    Route::get('/create', 'CampaignController@create')->name('create')->middleware(['can:create,App\Campaign']);
    Route::post('/', 'CampaignController@store')->name('store')->middleware(['can:create,App\Campaign']);
    Route::get('/{campaign}', 'CampaignController@show')->name('show')->middleware(['can:read,campaign']);
    Route::get('/{campaign}/update', 'CampaignController@edit')->name('edit')->middleware(['can:update,campaign']);
    Route::put('/{campaign}', 'CampaignController@update')->name('update')->middleware(['can:update,campaign']);
    Route::get('/{campaign}/delete', 'CampaignController@delete')->name('delete')->middleware(['can:delete,campaign']);
    Route::delete('/{campaign}', 'CampaignController@destroy')->name('destroy')->middleware(['can:delete,campaign']);
    Route::put('/{campaign}/status', 'CampaignController@updateStatus')
        ->name('update-status')
        ->middleware(['can:update,campaign']);
    Route::post('/{campaign}/duplicate', 'CampaignController@duplicate')
        ->name('duplicate')
        ->middleware(['can:create,campaign']);
    Route::post('/{campaign}/daily-emails', 'CampaignController@setDailyEmails')
        ->name('update-daily-emails')
        ->middleware(['can:update,campaign']);

    // Campaign Email Templates
    Route::get('/{campaign}/email-templates', 'EmailTemplateController@ofCampaign')
        ->name('email-templates.index')
        ->middleware(['can:read,campaign']);
    //Route::get('/{campaign}/email-templates/create', 'EmailTemplateController@create')
    //    ->name('email-templates.create')
    //    ->middleware(['can:update,campaign']);
    // Not used.
    //Route::get('/{campaign}/email-templates/{emailTemplate}/edit', 'EmailTemplateController@edit')
    //    ->name('email-templates.edit')
    //    ->middleware('can:read,campaign');
    Route::post('/{campaign}/campaign-stages/{campaignStage}/email-templates', 'EmailTemplateController@store')
        ->name('email-templates.store')
        ->middleware(['can:update,campaign']);
    Route::put('/{campaign}/email-templates/{emailTemplate}', 'EmailTemplateController@update')
        ->name('email-templates.update')
        ->middleware(['can:update,campaign', 'can:update,emailTemplate']);;
    Route::put('/{campaign}/email-templates/{emailTemplate}/enable', 'EmailTemplateController@enable')
        ->name('email-templates.enable')
        ->middleware(['can:update,campaign', 'can:update,emailTemplate']);
    Route::delete('/{campaign}/email-templates/{emailTemplate}', 'EmailTemplateController@destroy')
        ->name('email-templates.destroy')
        ->middleware(['can:update,campaign', 'can:delete,emailTemplate']);
    Route::get('/{campaign}/email-templates/{emailTemplate}/preview/{prospect}', 'EmailTemplateController@preview')
        ->name('email-templates.preview')
        ->middleware(['can:read,campaign']);

    // Campaign Linkedin Message Templates
    Route::post('/{campaign}/campaign-stages/{campaignStage}/linkedin-message-templates', 'LinkedinMessageTemplateController@store')
        ->name('linkedin-message-templates.store')
        ->middleware(['can:update,campaign']);
    Route::put('/{campaign}/linkedin-message-templates/{linkedinMessageTemplate}', 'LinkedinMessageTemplateController@update')
        ->name('linkedin-message-templates.update')
        ->middleware(['can:update,campaign', 'can:update,linkedinMessageTemplate']);;
    Route::put('/{campaign}/linkedin-message-templates/{linkedinMessageTemplate}/enable', 'LinkedinMessageTemplateController@enable')
        ->name('linkedin-message-templates.enable')
        ->middleware(['can:update,campaign', 'can:update,linkedinMessageTemplate']);
    Route::delete('/{campaign}/linkedin-message-templates/{linkedinMessageTemplate}', 'LinkedinMessageTemplateController@destroy')
        ->name('linkedin-message-templates.destroy')
        ->middleware(['can:update,campaign', 'can:delete,linkedinMessageTemplate']);
    Route::get('/{campaign}/linkedin-message-templates/{linkedinMessageTemplate}/preview/{prospect}', 'LinkedinMessageTemplateController@preview')
        ->name('linkedin-message-templates.preview')
        ->middleware(['can:read,campaign', 'can:delete,linkedinMessageTemplate']);

    // Campaign Stages
    Route::get('/{campaign}/campaign-stages', 'CampaignStageController@ofCampaign')
        ->name('campaign-stages.index')
        ->middleware(['can:read,campaign']);
    Route::post('/{campaign}/campaign-stages', 'CampaignStageController@store')
        ->name('campaign-stages.store')
        ->middleware(['can:update,campaign']);
    Route::put('/{campaign}/campaign-stages/{campaignStage}', 'CampaignStageController@update')
        ->name('campaign-stages.update')
        ->middleware(['can:update,campaign', 'can:update,campaignStage']);
    Route::delete('/{campaign}/campaign-stages/{campaignStage}', 'CampaignStageController@destroy')
        ->name('campaign-stages.destroy')
        ->middleware(['can:update,campaign', 'can:delete,campaignStage']);

    // Snippets
    Route::get('/{campaign}/snippets', 'SnippetController@ofCampaign')
        ->name('snippets.index')
        ->middleware(['can:read,campaign']);
    Route::post('/{campaign}/snippets', 'SnippetController@store')
        ->name('snippets.store')
        ->middleware(['can:update,campaign']);
    Route::delete('/{campaign}/snippets/{snippet}', 'SnippetController@destroy')
        ->name('snippets.destroy')
        ->middleware(['can:update,campaign', 'can:delete,snippet']);
    Route::delete('/{campaign}/snippet-name/{snippetName}', 'SnippetController@destroyByName')
        ->name('snippet-name.destroy')
        ->middleware(['can:update,campaign']);

    // Send Test Emails
    Route::post('/{campaign}/manual-send', 'EmailTemplateController@manualSend')
        ->name('manual-send')
        ->middleware('can:read,campaign');

    // Set campaign email accounts.
    Route::get('/{campaign}/email-accounts', 'CampaignEmailAccountController@index')
        ->name('email-accounts.index')
        ->middleware('can:update,campaign');
    Route::post('/{campaign}/email-accounts', 'CampaignEmailAccountController@store')
        ->name('email-accounts.store')
        ->middleware('can:update,campaign');

    // Set campaign linkedin acconts.
    Route::get('/{campaign}/linkedin-accounts', 'CampaignLinkedinAccountController@index')
        ->name('linkedin-accounts.index')
        ->middleware('can:update,campaign');
    Route::post('/{campaign}/linkedin-accounts', 'CampaignLinkedinAccountController@store')
        ->name('linkedin-accounts.store')
        ->middleware('can:update,campaign');


    // Get the inbox data of a campaign
    Route::get('/{campaign}/inbox', 'EmailThreadController@ofCampaign')
        ->name('inbox')
        ->middleware('can:read,campaign');
    // Get the stats data of a campaign as json
    Route::get('/{campaign}/stats', 'CampaignController@stats')
        ->name('stats')
        ->middleware('can:read,campaign');

    // Daily stats tracking
    Route::get('/{campaign}/daily-stats', 'CampaignDailyStatsController@show')
        ->name('daily-stats.show')
        ->middleware('can:read,campaign');
    Route::get('/{campaign}/daily-stats/data', 'CampaignDailyStatsController@data')
        ->name('daily-stats.data')
        ->middleware('can:read,campaign');

    // chatgpt prompts
    Route::get('/{campaign}/chatgpt-prompts/list', 'ChatgptPromptController@list')
        ->name('chatgpt-prompts.list')
        ->middleware('can:read,campaign');
    Route::post('/{campaign}/chatgpt-prompts', 'ChatgptPromptController@store')
        ->name('chatgpt-prompts.add')
        ->middleware('can:read,campaign');
    Route::put('/{campaign}/chatgpt-prompts/{chatgptPrompt}', 'ChatgptPromptController@update')
        ->name('chatgpt-prompts.update')
        ->middleware('can:read,campaign');
    Route::delete('/{campaign}/chatgpt-prompts/{chatgptPrompt}', 'ChatgptPromptController@destroy')
        ->name('chatgpt-prompts.destroy')
        ->middleware('can:read,campaign');
    Route::post('/{campaign}/chatgpt-prompts/{chatgptPrompt}/run', 'ChatgptPromptController@run')
        ->name('chatgpt-prompts.run')
        ->middleware('can:read,campaign');
});
