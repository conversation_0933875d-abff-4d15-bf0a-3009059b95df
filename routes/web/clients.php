<?php
/*
 * Clients
 */

/*
 * Wavo3 suppression-list home
 */
Route::get('/suppression-list', 'EmailBlockController@show')->middleware(['auth', 'verified', 'checkMember', 'verifySubscription'])
    ->name('suppression-list.show');

// suppression page for user-dashboard
Route::prefix('/team')->name('teamsuppressions.')->middleware(['auth', 'verified', 'checkMember', 'verifySubscription'])->group(function () {
    Route::get('/{team}/suppressions', 'EmailBlockController@index')->name('index');
});

Route::prefix('/clients')->name('clients.')->middleware(['auth', 'verified', 'checkMember', 'verifySubscription'])->group(function () {

    // Manage team (client)
    Route::get('/', 'TeamController@index')->name('index');
    Route::post('/', 'TeamController@store')->name('store')->middleware(['can:create,App\Team']);
    Route::post('/webhook-test', 'TeamController@webhookTest')->name('webhook-test')->middleware(['can:create,App\Team']);
    Route::get('/create', 'TeamController@create')->name('create')->middleware(['can:create,App\Team']);
    Route::get('/list', 'TeamController@list')->name('list');
    Route::get('/{team}', 'TeamController@show')->name('show');
    Route::put('/{team}', 'TeamController@update')->name('update')->middleware(['can:update,team']);
    Route::delete('/{team}', 'TeamController@destroy')->name('destroy')->middleware(['can:delete,team']);
    Route::get('/{team}/edit', 'TeamController@edit')->name('edit')->middleware(['can:update,team']);
    Route::get('/{team}/delete', 'TeamController@delete')->name('delete')->middleware(['can:delete,team']);

    Route::get('/invitations/existinguser', 'Settings\Teams\MailedInvitationController@existinguser')->name('invitations.existinguser');
    Route::post('/invitations/accept-existing/{invitation}', 'Settings\Teams\MailedInvitationController@acceptExisting')->name('invitations.acceptexisting');

    Route::middleware('can:read,team')->group(function () {
        // Suppression list of the client/team
        Route::get('/{team}/suppressions', 'EmailBlockController@index')->name('suppressions.index');
        Route::post('/{team}/suppressions', 'EmailBlockController@store')->name('suppressions.store');
        Route::get('/{team}/suppressions/list', 'EmailBlockController@list')->name('suppressions.list');
        Route::post('/{team}/suppressions/import', 'EmailBlockController@import')->name('suppressions.import');
        Route::post('/{team}/suppressions/export', 'EmailBlockController@export')->name('suppressions.export');
        Route::delete('/{team}/suppressions/blocks', 'EmailBlockController@destroyMany')->name('suppressions.destroy-many');
        Route::delete('/{team}/suppressions/{emailBlock}', 'EmailBlockController@teamDestroy')->name('suppressions.destroy')
            ->middleware(['can:delete,emailBlock']);

        // Unsubscribe domains of the client/team
        Route::get('/{team}/domains/', 'CustomDomainController@index')->name('unsubscribe-domains.index');
        Route::post('/{team}/domains/', 'CustomDomainController@store')->name('unsubscribe-domains.store');
        Route::get('/{team}/domains/list', 'CustomDomainController@list')->name('unsubscribe-domains.list');
        Route::delete('/{team}/domains/{customDomain}', 'CustomDomainController@destroy')->name('unsubscribe-domains.destroy')
            ->middleware(['can:delete,customDomain']);

        // Invitations
        Route::get('/{team}/users/invitations', 'Settings\Teams\MailedInvitationController@index')->name('invitations.index');
        Route::put('/{team}/users/invitations/{invitation}', 'Settings\Teams\MailedInvitationController@update')->name('invitations.update')
            ->middleware(['can:update,invitation']);
        Route::delete('/{team}/users/invitations/{invitation}', 'Settings\Teams\MailedInvitationController@destroy')->name('invitations.destroy')
            ->middleware(['can:delete,invitation']);

        // Team Members
        Route::get('/{team}/users/', 'TeamUserController@index')->name('users.index');
        Route::get('/{team}/users/list', 'TeamUserController@list')->name('users.list');
        Route::put('/{team}/users/{user}', 'TeamUserController@updateRole')->name('users.update-role')
            ->middleware(['can:updateTeamMember,user,team']);
        Route::delete('/{team}/users/{user}', 'TeamUserController@destroy')->name('users.destroy')
            ->middleware(['can:deleteTeamMember,user,team']);
        // Route::post('/{team}/users/', 'TeamController@store');
        // Route::get('/{team}/users/list', 'TeamController@list');
        // Route::delete('/{team}/users/{user}', 'TeamController@destroy');
    });


});

Route::prefix('/clients')->name('clients.')->middleware(['auth', 'verifySubscription'])->group(function () {
    Route::get('/invitations/existinguser', 'Settings\Teams\MailedInvitationController@existinguser')->name('invitations.existinguser');
    Route::post('/invitations/accept-existing/{invitation}', 'Settings\Teams\MailedInvitationController@acceptExisting')->name('invitations.acceptexisting');
});
