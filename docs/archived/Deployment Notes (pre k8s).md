# Deployment Notes (pre k8s)

## Laravel Horizon

Laravel Horizon needs Redis and we also install supervisor which will take care of restarting Horizon in case it shuts down.


### Installing Redis

https://www.digitalocean.com/community/tutorials/how-to-install-and-configure-redis-on-ubuntu-16-04


### Installing Supervisor

apt-get install supervisor
service supervisor restart


### Configure Horizon

#### Create a horizon configuration file for supervisor
sudo vi /etc/supervisor/conf.d/horizon.conf

    [program:horizon]
    process_name=%(program_name)s
    command=php /var/www/laravel/artisan horizon
    autostart=true
    autorestart=true
    user=www-data
    redirect_stderr=true
    stdout_logfile=/var/www/laravel/storage/logs/horizon.log


#### Reload supervisor configuration:

supervisorctl reread
supervisorctl update


### Using Horizon

In case we have updated our codebase, to have Horizon update to the changes, we need to run from the app directory:

php artisan horizon:terminate

Supervisor will then restart Horizon automatically.

To manually update data from Woodpecker api, we can run:

php artisan apiudate:all


## Subscription Setup and Campaign Initialization

Once a campaign is created in the Huron Platform, we need to follow these steps to initialize.

1. In case of a new subscription we first need to create the corresponding subscription in Woodpecker.
2. We update the subscription api key in `/campaign-admin` with the one in Woodpecker.
3. We click Initialize Campaign (once campaign setup is complete) in `/campaign-admin`.

*Woodpecker only allows campaign initialization if the email address used (in 3rd step of campaign setup),
is the same as the one of the Woodpecker subscription.*

For future campaigns of the same subscription, we only need to do the last part (initialize campaign).