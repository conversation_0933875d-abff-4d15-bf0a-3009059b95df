# Office 365 OAuth Setup


## MS Azure Setup

Register a new Azure App based on the instructions of <PERSON><PERSON><PERSON> (https://docs.nylas.com/docs/o365-oauth-setup)


### Main points:

    1 Register a new App in Azure Active Directory, supporting accounts in any organizational directory (without outlook.com, xbox etc)
    2 Set API permssions either manually or by editing the Manifest
    3 Set the Authentication Redirect URI "https://app.wavo.co/oauth/office365/callback"
    4 Create a Client Secret for <PERSON><PERSON><PERSON>. Note down the secret and use it in Nylas dashboard along with the Azure App ID


### Manifest JSON:

```json
"requiredResourceAccess": [
    {
        "resourceAppId": "********-0000-0ff1-ce00-************",
        "resourceAccess": [
            {
                "id": "3b5f3d61-589b-4a3c-a359-5dd4b5ee5bd5",
                "type": "Scope"
            },
            {
                "id": "********-c7a8-481e-a6b4-19458e0b30a5",
                "type": "Scope"
            },
            {
                "id": "5eb43c10-865a-4259-960a-83946678f8dd",
                "type": "Scope"
            },
            {
                "id": "6223a6d3-53ef-4f8f-982a-895b39483c61",
                "type": "Scope"
            },
            {
                "id": "266d2589-20b5-4f91-9a03-89247d1be8da",
                "type": "Scope"
            }
        ]
    },
    {
        "resourceAppId": "********-0000-0000-c000-************",
        "resourceAccess": [
            {
                "id": "311a71cc-e848-46a1-bdf8-97ff7156d8e6",
                "type": "Scope"
            }
        ]
    },
    {
        "resourceAppId": "00000003-0000-0000-c000-************",
        "resourceAccess": [
            {
                "id": "14dad69e-099b-42c9-810b-d002981feec1",
                "type": "Scope"
            },
            {
                "id": "37f7f235-527c-4136-accd-4a02d197296e",
                "type": "Scope"
            },
            {
                "id": "e1fe6dd8-ba31-4d61-89e7-88639da4683d",
                "type": "Scope"
            },
            {
                "id": "7427e0e9-2fba-42fe-b0c0-848c9e6a8182",
                "type": "Scope"
            }
        ]
    }
],
```


## Wavo App Setup

Set env variables for the following:

    AZURE_CLIENT_ID
    AZURE_CLIENT_SECRET
    AZURE_REDIRECT_URI


