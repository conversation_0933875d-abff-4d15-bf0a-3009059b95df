# User Types

There are currently the following user types based on subscription/agency type:


## Users subscribed on the user-dashboard plan

These have no subscription on registration.

If they add any email accounts they are subscribed to the user-inboxes-monthly plan.

They are team owners but cannot create more teams.

Their roles:

- agency-admin

Their agency has the attributes

- is_billed: 1
- creates_campaigns: 1
- bills_customers: 0
- **current_billing_plan: user-dashboard**


## Users invited to the team of agency admins of user-dashboard plan

They have no subscription plan.

They are team members and the amount of email accounts they can create is limited by the agency-admin (settings->team).

They have no roles.

Their agency has the attributes of user-dashboard agency (listed previously).


## Users subscribed on the agency-dashboard plan

These have no subscription on registration.

If they add any email accounts they are subscribed to the user-inboxes-monthly plan.

They are team owners but cannot create more teams.

Their roles:

- agency-admin

Their agency has the attributes

- is_billed: 1
- creates_campaigns: 1
- bills_customers: 0
- **current_billing_plan: agency-dashboard**


## Users invited to a team of agency admins of agency-dashboard plan

They have no subscription plan.

They are team members and the amount of email accounts they can create is limited by the agency-admin (clint settings).

They have no roles.

Their agency has the attributes of agency-dashboard agency (listed previously).


# Roles


## Roles with their permissions

- admin (achilles & anton)
    - admin
- agency-admin
    - agency-admin
    - customer.edit
    - customer.invite
    - customer.view
    - campaign.admin
    - campaign.create *Add to prod*
    - campaign.delete
- campaign-admin (used by agency clients etc)
    - campaign.admin
    - campaign.create *Add to prod*
- campaign-read (used by agency clients etc)
    - campaign.read
- prospect-export (used by agency clients etc)
    - prospect.export
- copywriting (none)
    - campaign.admin
    // - campaign.create *Add to prod*
    - campaign.initialize *Remove from prod*
    - campaign.delete
    - emailTemplate.edit
- sales (none)
    - agency.view
    - customer.edit
    - customer.invite
    - customer.view
    - demo
    - campaign.demo
    - impersonate
- staff
    - staff
    - research
    - research.staff
- support
    - impersonate
    - support *Add to prod and staging*
    
## Unused Permissions

- horizon 
