<?php

namespace App\Services;

use App\Campaign;
use <PERSON>raRest<PERSON><PERSON>\JiraException;
use <PERSON>raRestApi\Issue\IssueField;
use Illuminate\Support\Facades\Log;
use <PERSON>raRestApi\Issue\IssueService;
use <PERSON>raRest<PERSON>pi\IssueLink\IssueLink;
use <PERSON>raRest<PERSON><PERSON>\IssueLink\IssueLinkService;

class CampaignJiraService
{
    /**
     * The Jira Issue Service.
     *
     * @var IssueService
     */
    protected $issueService;

    /**
     * The Jira Issue Link Service.
     *
     * @var IssueLinkService
     */
    protected $issueLinkService;

    /**
     * The campaign that the issues belong to.
     *
     * @var Campaign
     */
    protected $campaign;

    /**
     * CampaignJiraService constructor.
     *
     * @param Campaign $campaign
     * @throws JiraException
     */
    public function __construct(Campaign $campaign)
    {
        $this->campaign = $campaign;
        $this->issueService = new IssueService();
        $this->issueLinkService = new IssueLinkService();
    }

    /**
     * Create a new CM issue.
     *
     * @throws JiraException
     * @throws \JsonMapper_Exception
     * @return \JiraRestApi\Issue\Issue|object
     */
    public function createCampaignIssue()
    {
        $issueField = $this->createIssueField('CM', $this->campaign->name, 'Campaign');

        // Research Criteria is uploaded on campaign creation
        $issueField->addLabel('criteria_uploaded');

        // Set amount of prospects needed.
        $issueField->addCustomField('customfield_10025', $this->campaign->monthly_prospects);

        return $this->issueService->create($issueField);
    }

    /**
     * Create a new RES Issue.
     *
     * @param $researchFolderUrl
     * @throws JiraException
     * @throws \JsonMapper_Exception
     * @return \JiraRestApi\Issue\Issue|object
     */
    public function createResearchIssue($researchFolderUrl)
    {
        $issueField = $this->createIssueField('RES', $this->campaign->name, 'Research Management');

        // Get google folder agency/user/campaign.name/leads-data
        $issueField->addCustomField('customfield_10026', $researchFolderUrl);
        $issueField->addCustomField('customfield_10025', $this->campaign->monthly_prospects);

        return $this->issueService->create($issueField);
    }

    /**
     * Create a new COP Issue.
     *
     * @param $collateralFolderUrl
     * @throws JiraException
     * @throws \JsonMapper_Exception
     * @return \JiraRestApi\Issue\Issue|object
     */
    public function createCopywritingIssue($collateralFolderUrl)
    {
        $issueField = $this->createIssueField('COP', $this->campaign->name, 'Cadence Creation');

        // Get google folder agency/user/campaign.name/collateral
        $issueField->addCustomField('customfield_10026', $collateralFolderUrl);

        return $this->issueService->create($issueField);
    }

    /**
     * Link 2 Jira Issues.
     *
     * @param $inwardIssueId
     * @param $outwardIssueId
     * @param $linkType
     * @throws JiraException
     */
    public function linkIssues($inwardIssueId, $outwardIssueId, $linkType)
    {
        $link = new IssueLink();
        Log::info("Jira API link issues: $inwardIssueId $linkType $outwardIssueId");

        $link->setInwardIssue($inwardIssueId)
            ->setOutwardIssue($outwardIssueId)
            ->setLinkTypeName($linkType)
            ->setComment('Issues linked by Huron Platform.');

        $this->issueLinkService->addIssueLink($link);
    }

    /**
     * Add a label to an Issue.
     *
     * @param $label
     * @param $issueKey
     * @throws JiraException
     * @throws \JsonMapper_Exception
     */
    public function addLabel($label, $issueKey)
    {
        $issueField = new IssueField(true);

        $issueField->labels = $this->issueService->get($issueKey)->fields->labels;

        $issueField->addLabel($label);

        // You can set the $paramArray param to disable notifications in example
        $this->issueService->update($issueKey, $issueField);
    }

    /**
     * Create a new issueField object.
     *
     * @param $projectKey
     * @param $issueName
     * @param $issueType
     * @return IssueField
     */
    protected function createIssueField($projectKey, $issueName, $issueType)
    {
        $issueField = new IssueField();

        return $issueField->setProjectKey($projectKey)
            ->setSummary($issueName)
            ->setIssueType($issueType);
    }
}
