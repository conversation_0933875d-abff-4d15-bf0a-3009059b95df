<?php

namespace App\Services\EmailIntegrationServices;

use App\EmailAccount;
use App\EmailMessageTest;
use App\PendingEmailMessage;
use App\Prospect;
use Carbon\Carbon;
use App\EmailThread;
use App\EmailMessage;
use App\EmailTemplate;
use App\IncomingEmailMessage;
use GuzzleHttp\TransferStats;
use Hashids;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client as HttpClient;
use GuzzleHttp\Psr7\Response as HttpResponse;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ConnectException;
use Illuminate\Support\Str;
use ZBateson\MailMimeParser\Header\HeaderConsts;

class EmailEngineApiService implements EmailIntegration
{
    protected $http;
    protected $emailAccount;

    protected const DB_READ_CONNECTION = 'mysql::read';
    protected const PROVIDERS = [
        'gmail',
        'office365',
        'yahoo',
        'icloud',
        'hotmail',
        'aol',
        'exchange',
        'imap',
    ];

    public function __construct(EmailAccount $emailAccount)
    {
        $this->http = new HttpClient(['base_uri' => config('app.emailEngine.url')]);
        $this->emailAccount = $emailAccount;
    }

    public function getNylasAccount()
    {
        return 'not nylas!';
    }

    /**
     * Verify email authorization on EmailEngine.
     *
     * @return \Illuminate\Contracts\Routing\ResponseFactory|mixed|\Symfony\Component\HttpFoundation\Response
     */
    public function authorize()
    {
        // only "imap" can be verified
        if ($this->emailAccount->email_engine_provider != 'imap') {
            return true;
        }

        $response = $this->http->post(
            'verifyAccount',
            [
                \GuzzleHttp\RequestOptions::JSON => $this->getAuthPayload(),
                'query' => [
                    'access_token' => config('app.emailEngine.token')
                ]
            ]
        );

        return collect(json_decode($response->getBody()->getContents(), true));
    }

    /**
     * Build the payload needed for validating account credentials through EmailEngine API.
     *
     * @return array
     */
    protected function getAuthPayload()
    {
        $payload = ['mailboxes' => false];

        // Build settings based on provider
        switch ($this->emailAccount->email_engine_provider) {
            case 'gmail':
                break;
            case 'office365':
                break;
            case 'outlook':
                break;
            case 'yahoo':
            case 'icloud':
            case 'hotmail':
            case 'aol':
                break;
            case 'exchange':
                break;
            case 'imap':
                $payload['imap'] = [
                    'auth' => [
                        'user' => $this->emailAccount->email_server_username,
                        'pass' => $this->emailAccount->email_server_password,
                    ],
                    'host'     => $this->emailAccount->email_server_imap_address,
                    'port'     => (int) $this->emailAccount->email_server_imap_port,
                    'secure'   => $this->emailAccount->email_server_ssl
                ];

                $payload['smtp'] = [
                    'auth' => [
                        'user' => $this->emailAccount->email_server_username,
                        'pass' => $this->emailAccount->email_server_password,
                    ],
                    'host'     => $this->emailAccount->email_server_smtp_address,
                    'port'     => (int) $this->emailAccount->email_server_smtp_port,
                    'secure'   => false
                ];

                break;
        }

        return $payload;
    }

    /**
     * Re-activate a cancelled a EmailEngine Account.
     *
     * @return mixed
     */
    public function reactivateAccount()
    {
        Log::info('Reactivating account...'.$this->emailAccount->email_address);

        try {
            $this->connect();
        } catch (Exception $e) {
            Log::error('Cannot connect '.$this->emailAccount->email_address.' to EmailEngine');
            Log::error($e->getMessage());

            $this->emailAccount->update(['error' => 'account.invalid']);
            throw new Exception($e->getMessage());
        }

        $emailEngineAccountData = [
            'email_address'     => $this->emailAccount->email_address,
            'name'              => $this->emailAccount->name,
            'organization_unit' => '',
            'provider'          => '',
            'sync_state'        => 'running',
            'linked_at'         => now()->timestamp,
            'billing_state'     => 'paid',
        ];

        if ($this->accountCreated()) {
            Log::info('Updating EmailEngine Account...'.$this->emailAccount->email_address);
            $emailEngineAccount = $this->emailAccount->emailEngineAccount->update($emailEngineAccountData);
        } else {
            Log::info('Creating EmailEngine Account...'.$this->emailAccount->email_address);

            $emailEngineAccountData['account_id'] = $this->emailAccount->hashid;
            // $emailEngineAccountData['access_token'] = $this->emailAccount->hashid;
            $emailEngineAccount = $this->emailAccount->emailEngineAccount()->create($emailEngineAccountData);
            $this->emailAccount->load('emailEngineAccount');
        }

        // TODO: No billing in EmailEngine so it's always true
        $this->emailAccount->emailEngineAccount->update(['billing_state' => 'paid']);

        return true;
    }

    /**
     * Update or create an account on EmailEngine API based on response
     * that was generated after verifying email credentials.
     *
     * @return mixed
     */
    public function updateOrCreateAccount()
    {
        Log::info('UpdateOrCreate account...'.$this->emailAccount->email_address);

        try {
            $this->connect();
        } catch (Exception $e) {
            Log::error('Cannot connect '.$this->emailAccount->email_address.' to EmailEngine');
            Log::error($e->getMessage());

            $this->emailAccount->update(['error' => 'account.invalid']);
            throw new Exception($e->getMessage());
        }

        $emailEngineAccountData = [
            'account_id'        => $this->emailAccount->hashid,
            'email_address'     => $this->emailAccount->email_address,
            'name'              => $this->emailAccount->name,
            'organization_unit' => '',
            'provider'          => '',
            'sync_state'        => 'running',
            'linked_at'         => now()->timestamp,
            'billing_state'     => 'paid'
        ];

        if ($this->accountCreated()) {
            Log::info('Updating EmailEngine Account...'.$this->emailAccount->email_address);

            $emailEngineAccount = $this->emailAccount->emailEngineAccount->update($emailEngineAccountData);
        } else {
            Log::info('Creating EmailEngine Account...' . $this->emailAccount->email_address);

            $emailEngineAccountData['account_id'] = $this->emailAccount->hashid;
            // $emailEngineAccountData['access_token'] = $this->emailAccount->hashid;

            $emailEngineAccount = $this->emailAccount->emailEngineAccount()->create($emailEngineAccountData);
        }

        $this->emailAccount->load('emailEngineAccount');

        // TODO: No billing state in email engine so it's always true

        $this->emailAccount->emailEngineAccount->update([
            'billing_state' => 'paid'
        ]);

//        Log::info($emailEngineAccountData);

        return $emailEngineAccount;
    }

    /**
     * Connect an email to a EmailEngine account.
     * use the 'verifyAccount' for imap to validate credentials
     *
     * @param $code
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Support\Collection|\Symfony\Component\HttpFoundation\Response
     */
    public function connect()
    {
        $payload = $this->getConnectPayload();

        $response = $this->http->post(
            'account',
            [
                \GuzzleHttp\RequestOptions::JSON => $payload,
                'query' => [
                    'access_token' => config('app.emailEngine.token')
                ]
            ]
        );

        $content = collect(json_decode($response->getBody()->getContents(), true));
        return $content;
    }

    /**
     * Build the payload needed for linking an account in EmailEngine.
     *
     * @param $code
     * @return array
     */
    protected function getConnectPayload()
    {
        $this->emailAccount->load(['emailEngineAccount']);
        $smtpUsername = $this->emailAccount->email_server_smtp_username ? $this->emailAccount->email_server_smtp_username : $this->emailAccount->email_server_username;
        $smtpPassword = $this->emailAccount->email_server_smtp_password ? $this->emailAccount->email_server_smtp_password : $this->emailAccount->email_server_password;

        // Find notifyFrom time.
        $reAuth = ! is_null($this->emailAccount->authenticated_at) && is_null($this->emailAccount->cancelled_at);
        $latestMessage = $this->emailAccount->emailMessages()->orderBy('id', 'desc')->first();
        // If reAuth, then find the last message and sync from that time + 5 hrs.
        // If the last message is older than one month, then sync from 1 month ago.
        // If not reAuth, or no message exists on DB then sync from 1 day ago.
        if ($reAuth && $latestMessage) {
            $notifyFrom = $latestMessage->created_at->subHours(5);
            // Don't go over 1 month back. If the account was left unauthenticated for more than 1 month.
            if ($notifyFrom->lt(now()->subMonth())) {
                $notifyFrom = now()->subMonth();
            }
        } else {
            // On new accounts or re-activations, sync back to 1 day ago.
            $notifyFrom = now()->subDay();
        }

        $payload = [
            'account' => $this->emailAccount->hashid,
            'name' => $this->emailAccount->name,
            "email" => $this->emailAccount->email_address,
            "logs" => true,
            'notifyFrom' => $notifyFrom->toIso8601String(),
            'syncFrom' => $notifyFrom->toIso8601String(),
        ];

        switch ($this->emailAccount->email_engine_provider) {
            case 'gmail':
                $payload['oauth2'] = [
                    'provider'     => 'gmail',
                    'auth'         => [
                        'user'     => $this->emailAccount->email_address
                    ],
                    'accessToken'  => $this->emailAccount->emailEngineAccount->access_token,
                    'refreshToken' => $this->emailAccount->oauth_refresh_token,
                    'expires'      => now()->addHour()->toIso8601String()
                ];
                $payload['imap'] = false;
                $payload['smtp'] = false;

                break;
            case 'office365':
                $payload['oauth2'] = [
                    'provider'     => 'outlook',
                    'auth'         => [
                        'user'     => $this->emailAccount->email_address
                    ],
                    'accessToken'  => $this->emailAccount->emailEngineAccount->access_token,
                    'refreshToken' => $this->emailAccount->oauth_refresh_token,
                    'expires'      => now()->addHour()->toIso8601String()
                ];
                $payload['imap'] = false;
                $payload['smtp'] = false;

                break;
            /*
            case 'office365':
                $accessToken = base64_encode("user=" . $this->emailAccount->email_address . "^Aauth=Bearer " . $this->emailAccount->emailEngineAccount->access_token . "^A^A");

                $payload['imap'] = [
                    'auth' => [
                        'user' => $this->emailAccount->email_address,
                        'accessToken' => $this->emailAccount->emailEngineAccount->access_token,
                        // 'accessToken' => $accessToken,
                    ],
                    'host'     => 'outlook.office365.com',
                    'port'     => 993,
                    'secure'   => true
                ];
                $payload['smtp'] = [
                    'auth' => [
                        'user' => $this->emailAccount->email_address,
                        'accessToken' => $this->emailAccount->emailEngineAccount->access_token,
                        // 'accessToken' => $accessToken,
                    ],
                    'host'     => 'smtp.office365.com',
                    'port'     => 587,
                    'secure'   => false
                ];

                break;
            */
            case 'outlook':
            case 'imap':
                $payload['imap'] = [
                    'auth' => [
                        'user' => $this->emailAccount->email_server_username,
                        'pass' => $this->emailAccount->email_server_password,
                    ],
                    'host'     => $this->emailAccount->email_server_imap_address,
                    'port'     => (int) $this->emailAccount->email_server_imap_port,
                    'secure'   => $this->emailAccount->email_server_ssl,
                    'resyncDelay' => 300
                ];
                $payload['smtp'] = [
                    'auth' => [
                        'user' => $smtpUsername,
                        'pass' => $smtpPassword,
                    ],
                    'host'     => $this->emailAccount->email_server_smtp_address,
                    'port'     => (int) $this->emailAccount->email_server_smtp_port,
                    'secure'   => $this->emailAccount->email_server_ssl_smtp
                ];
                $payload['oauth2'] = false;
                break;
        }

        return $payload;
    }

    /**
     * Determine whether a EmailEnginne Account is already saved in the db.
     *
     * @return bool
     */
    public function accountCreated()
    {
        return $this->emailAccount->emailEngineAccount()->exists();
    }

    /**
     * Get the details of a EmailEngine account.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getEmailEngineAccount()
    {
        Log::info('Getting EmailEngine Account data...'.$this->emailAccount->email_address);

        $response = $this->http->get(
            'account/'.$this->emailAccount->hashid,
            [
                'headers' => [
                    'accept' => 'application/json'
                ],
                'query' => [
                    'access_token' => config('app.emailEngine.token')
                ]
            ]
        );

        $responseData = collect(json_decode($response->getBody()->getContents(), true));

        return $responseData;
    }

    /**
     * Get all folders of an email account.
     *
     * @return \Illuminate\Support\Collection | bool
     * @throws RequestException
     */
    public function getFolders()
    {
        if(!$this->emailAccount->isRunning()) {
            return false;
        }

        try {
            $response = $this->http->get('account/'.$this->emailAccount->hashid.'/mailboxes',
                [
                    'headers' => [
                        'accept' => 'application/json'
                    ],
                    'query' => [
                        'access_token' => config('app.emailEngine.token')
                    ]
                ]);
        } catch (RequestException $e) {
            Log::error('EmailAccount-'.$this->emailAccount->id.' Error getting folders of email account: '.$this->emailAccount->email);
            Log::error($e->getMessage());
            // throw $e;

            return false;
        }

        // transform mailboxes into format that "GetEmailFolders" understand (same as nylas)
        $mailboxes = collect(json_decode($response->getBody()->getContents(), true))->get('mailboxes');
        $folders = collect($mailboxes)->map(function($folder) {
            return [
                'id' => $folder['path'],
                'name' => strtolower($folder['name']),
                'display_name' => $folder['path'],
                'object' => 'folder'
            ];
        });

        return $folders;
    }

    /**
     * Send a test email message.
     *
     * @param EmailTemplate $emailTemplate
     * @param Prospect $prospect
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function sendTestEmailMessage(EmailTemplate $emailTemplate, Prospect $prospect)
    {
        return $this->sendEmailMessage($emailTemplate, $prospect, null, true);
    }

    /**
     * Search a folder for a messages based on search criteria
     *
     * Documentation: https://api.emailengine.app/#operation/postV1AccountAccountSearch
     *  Example Response:
     * [
     * "total" => 2,
     * "page" => 0,
     * "pages" => 1,
     * "messages" => [
     * [
     * "id" => "AAAAAQAABdM",
     * "uid" => 1491,
     * "emailId" => "1811362637082646155",
     * "threadId" => "1811013734403565888",
     * "date" => "2024-09-27T15:13:35.000Z",
     * "flags" => [
     * "\Seen",
     * ],
     * "labels" => [
     * "\Sent",
     * ],
     * "size" => 8143,
     * "subject" => "Re: Complimentary pass to Digital Marketing World Forum - Sep 26-27 - Marriott Marquis, Times Square, NY",
     * "from" => [
     * "name" => "Andra Burlacu",
     * "address" => "<EMAIL>",
     * ],
     * "replyTo" => [
     * [
     * "name" => "Andra Burlacu",
     * "address" => "<EMAIL>",
     * ],
     * ],
     * "to" => [
     * [
     * "name" => "Sabrina Morris",
     * "address" => "<EMAIL>",
     * ],
     * ],
     * "messageId" => "<<EMAIL>>",
     * "inReplyTo" => "<<EMAIL>>",
     * "text" => [
     * "id" => "AAAAAQAABdOTkaExkaEykA",
     * "encodedSize" => [
     * "plain" => 1127,
     * "html" => 5620,
     * ],
     * ],
     * ],
     * [
     * "id" => "AAAAAQAABL8",
     * "uid" => 1215,
     * "emailId" => "1811013734403565888",
     * "threadId" => "1811013734403565888",
     * "date" => "2024-09-23T18:47:56.000Z",
     * "flags" => [
     * "\Seen",
     * ],
     * "labels" => [
     * "\Sent",
     * ],
     * "size" => 6759,
     * "subject" => "Complimentary pass to Digital Marketing World Forum - Sep 26-27 - Marriott Marquis, Times Square, NY",
     * "from" => [
     * "name" => "Andra Burlacu",
     * "address" => "<EMAIL>",
     * ],
     * "replyTo" => [
     * [
     * "name" => "Andra Burlacu",
     * "address" => "<EMAIL>",
     * ],
     * ],
     * "to" => [
     * [
     * "name" => "Sabrina Morris",
     * "address" => "<EMAIL>",
     * ],
     * ],
     * "messageId" => "<<EMAIL>>",
     * "text" => [
     * "id" => "AAAAAQAABL-TkaExkaEykA",
     * "encodedSize" => [
     * "plain" => 2090,
     * "html" => 3455,
     * ],
     * ],
     * ],
     * ],
     * ]
     *
     * @param array $search
     * @param string $folder
     * @return false|\Psr\Http\Message\ResponseInterface
     */
    public function findEmailMessages(Array $search, string $folder = '[Gmail]/All Mail')
    {
        try {
            $response = $this->http->post(
                'account/'.$this->emailAccount->hashid.'/search',
                [
                    \GuzzleHttp\RequestOptions::JSON => ['search' => $search],
                    'query' => ['access_token' => config('app.emailEngine.token'),'path' => $folder],
                    'timeout' => 240,
                ]
            );
        } catch (RequestException $e) {
            Log::error('EmailAccount-' . $this->emailAccount->id . ' Error finding email messages: ' . $e->getMessage());
            return false;
        } catch (\Throwable $e) {
            Log::error('EmailAccount-' . $this->emailAccount->id . ' Unexpected error finding email messages: ' . $e->getMessage());
            return false;
        }

        return $response;
    }

    /**
     * Send an email message based on email template and prospect.
     *
     * @param EmailTemplate $emailTemplate
     * @param Prospect $prospect
     * @param bool $testing
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function sendEmailMessage(EmailTemplate $emailTemplate, Prospect $prospect, $trackingHash = null, $testing = false)
    {
        Log::info('EmailAccount-'.$this->emailAccount->id.'. sendEmailMessage by emailEngine');
        $payload = $this->getEmailMessagePayload($emailTemplate, $prospect, $trackingHash, $testing);
        Log::info('EmailAccount-'.$this->emailAccount->id.'. payload ready, doing post request');

        try {
            $response = $this->http->post(
                'account/'.$this->emailAccount->hashid.'/submit',
                [
                    \GuzzleHttp\RequestOptions::JSON => $payload,
                    'query' => [
                        'access_token' => config('app.emailEngine.token')
                    ],
//                    'connect_timeout' => 30,
                    'timeout' => 240,
                ]
            );

        } catch (RequestException $e) {
            Log::error('EmailAccount-'.$this->emailAccount->id.'. sendEmailMessage by emailEngine requestException');
            Log::error('EmailAccount-'.$this->emailAccount->id.'. '.$e->getMessage());
            if ($e->hasResponse()) {
                $response = $e->getResponse();
            } else {
                $response = new HttpResponse(503);
            }
        } catch (\Throwable $e) {
            Log::error('EmailAccount-'.$this->emailAccount->id.'. sendEmailMessage by emailEngine generic exception');
            Log::error('EmailAccount-'.$this->emailAccount->id.'. '.$e->getMessage());
            $response = new HttpResponse(503);
        }

        return array(
            'response'  => $response,
            'payload'   => $payload
        );
    }

    /**
     * Create the payload needed for sending a new email message.
     *
     * @param  EmailTemplate  $emailTemplate
     * @param  Prospect  $prospect
     * @param  null  $trackingHash
     * @param  bool  $testing
     * @return array
     * @throws Exception
     */
    protected function getEmailMessagePayload(EmailTemplate $emailTemplate, Prospect $prospect, $trackingHash = null, $testing = false)
    {
        if($trackingHash && $emailTemplate->track_click && !$testing) {
            $message = $emailTemplate->parseMessageContent($prospect, $this->emailAccount, 'body', $trackingHash);
        } else {
            $message = $emailTemplate->parseMessageContent($prospect, $this->emailAccount, 'body');
        }

        // manually create the plaintext version
        $textMsg = preg_replace( "/\n\s+/", "\n", rtrim(html_entity_decode(strip_tags($message))) );

        // append open tracker
        if($trackingHash && $emailTemplate->track_open && !$testing) {
            $openUrl = url('/api/t/'.$trackingHash.'/o');

            if($prospect->unsub_domain) {
                $subName = 'http://'.substr(md5(microtime()),rand(0,26),10);
                $unsubDomain = $subName . str_replace('*', '', $prospect->unsub_domain);
                $openUrl = $unsubDomain . '/api/t/'.$trackingHash.'/o';
            }

            $openImg = "<div class='wv_ee_tr_op_wrap'><img class='wv_ee_tr_op' src='".$openUrl."' style='visibility:hidden;width:1px;height:1px;'/></div>";
            $message = $message.$openImg;
        }

        $payload = [
            'subject' => $emailTemplate->parseMessageContent($prospect, $this->emailAccount, 'subject'),
            'from'    => [
                'name'  => $this->emailAccount->name,
                'address' => $this->emailAccount->email_address,
            ],
            'to'      => [
                [
                    'name' => $prospect->full_name,
                    'address'=> $prospect->email,
                ],
            ],
            'html'     => $message,
            'text'     => $textMsg
        ];

        // If it's a followup and needs to attach previous message, try to do so.
        if ($emailTemplate->attach_follow && $emailTemplate->campaign_stage_number > 1) {
            // If this is a test email, build quoted messages through the app, otherwise search for previous message in the db.
            if ($testing) {
                $lastStageWithoutAttach = $emailTemplate->campaign->campaignStages()
                    ->where('number', '<', $emailTemplate->campaign_stage_number)
                    ->orderBy('number', 'desc')
                    ->where('attach_follow', false)
                    ->whereHas('emailTemplates', function(Builder $query) {
                        $query->enabled();
                    })->first();
                $subjectTemplate = $lastStageWithoutAttach->emailTemplates()->enabled()->where('attach_follow', false)
                    ->inRandomOrder()->limit(1)->get()->first();
                $payload['subject'] = 'Re: '.$subjectTemplate->parseMessageContent($prospect, $this->emailAccount, 'subject');

                $prevTemplate = $emailTemplate->campaign->campaignStages()
                    ->where('number', $emailTemplate->campaignStage->number -1)
                    ->first()
                    ->emailtemplates()
                    ->enabled()
                    ->inRandomOrder()
                    ->first();
                $prevMessageData = $prevTemplate->getQuotedMessageData($prospect, $this->emailAccount, $emailTemplate->campaignStage->delay);
                $payload['html'] = '<div>'.$message.'</div><br>'.$prevMessageData->get('text');
            } else {
                $previousMessages = EmailMessage::on(self::DB_READ_CONNECTION)
                    ->where('campaign_id', $emailTemplate->campaign_id)
                    ->where('prospect_id', $prospect->id)
                    ->whereNotNull('email_template_id')
                    ->orderBy('submitted_at', 'desc')
                    ->get();
                $lastMessage = $previousMessages->first();

                // From the previous messages sent, make sure we find a valid subject (not placeholder)
                $subjectMessage = $previousMessages->filter(function($emailMessage) {
                    return ! Str::contains($emailMessage->subject, 'Email subject goes here...');
                })->first();

                if (is_null($subjectMessage)) {
                    // If we can't find a proper subject in the prospect's previous messages,
                    // then get the subject of the first available from the currently enabled templates.
                    Log::warning('Could not find subject in previous messages for email template:'.$emailTemplate->id);
                    $lastStageWithoutAttach = $emailTemplate->campaign->campaignStages()
                        ->where('number', '<', $emailTemplate->campaign_stage_number)
                        ->orderBy('number', 'desc')
                        ->where('attach_follow', false)
                        ->whereHas('emailTemplates', function(Builder $query) {
                            $query->enabled();
                        })->first();
                    $subjectTemplate = $lastStageWithoutAttach->emailTemplates()->enabled()->where('attach_follow', false)
                        ->inRandomOrder()->limit(1)->get()->first();
                    $subject = $subjectTemplate->parseMessageContent($prospect, $this->emailAccount, 'subject');
                } else {
                    $subject = $subjectMessage->getQuotedMessageData()->get('subject');
                }

                if (isset($subject)  && ! empty($subject)) {
                    $payload['subject'] = 'Re: '.$subject; // It will not be added as a reply if different subject
                } else {
                    // Don't send a message without a valid subject if this is a follow-up.
                    throw new Exception("Could not find a valid subject for follow-up message");
                }

                // TODO: Get message from EmailEngine for "reference"
                if (! is_null($lastMessage)) {
                    // We have found previous messages, so we will use those to build the message subject and body.
                    // Check that old message exists on EmailEngine before linking.
                    $encodedThreadId = $lastMessage->email_thread_id ? Hashids::connection(EmailThread::class)->encode($lastMessage->email_thread_id) : '';

                    /*
                    if ($lastMessage->email_account_id == $this->emailAccount->id && $lastMessage->ee_id) {

                        // TODO EE: verify that dont need to fetch emailmessage from emailengine
                        // EE doesn't have endpoint to search email message by message_id (only valid base64 string ID assigned by EE)

                        // $lastMessageResponse = $this->getEmailMessage($lastMessage->nylas_message_id);
                        // if ($lastMessageResponse != false && $lastMessageResponse->getStatusCode() == 200) {
                        //     $payload['reply_to_message_id'] = $lastMessage->nylas_message_id;
                        // }

                        $payload['reference'] = [
                            'message'  => $lastMessage->ee_id,
                            'action' => 'reply',
                        ];
                        $payload['headers'] = [
                            'X-wv-ee-thid' => $encodedThreadId,
                        ];
                    } else {
                        // "headers": {
                        //     "references": [
                        //        "<CAAJ8=jj4zZDSMktb_wkF_6yjka5jm=<EMAIL>>"
                        //      ],
                        //      "in-reply-to": [
                        //        "<CAAJ8=jj4zZDSMktb_wkF_6yjka5jm=<EMAIL>>"
                        //      ]
                        //  }
                        $payload['headers'] = [
                            'references' => "<{$lastMessage->message_id}>",
                            'in-reply-to' => "<{$lastMessage->message_id}>",
                            'X-wv-ee-thid' => $encodedThreadId,
                        ];
                    }
                    */

                    $payload['headers'] = [
                        'references' => "<{$lastMessage->message_id}>",
                        'in-reply-to' => "<{$lastMessage->message_id}>",
                        'X-wv-ee-thid' => $encodedThreadId,
                    ];

                    $payload['html'] = '<div>'.$message.'</div><br>'.$lastMessage->getQuotedMessageData()->get('text');
                }
            }
        }

        if ($this->emailAccount->alias_enabled) {
            $payload['headers']['reply-to'] = [
                $this->emailAccount->alias_email_address
            ];
        }

        return $payload;
    }

    /**
     * Get an email message from EmailEngine.
     *
     * @param $messageId
     * @param bool $raw
     * @return \Psr\Http\Message\ResponseInterface | bool
     */
    public function getEmailMessage($messageId, $job = null)
    {
        try {
            $response = $this->http->get(
                'account/'.$this->emailAccount->hashid.'/message/'.$messageId,
                [
                    'headers' => [
                        'accept' => 'application/json'
                    ],
                    'query' => [
                        'access_token' => config('app.emailEngine.token'),
                        'textType' => '*'
                    ],
                    'connect_timeout' => 10.00,
                    'timeout' => 70.00,
                    'on_stats' => function (TransferStats $stats) {
                        Log::channel('emailengine')->info('Fetch message time:'.$stats->getTransferTime());
                    }
                ]
            );
        } catch (RequestException $e) {
            if (is_null($job)) {
                Log::error("Error getting message from EmailEngine with Message Id: $messageId");
                Log::error('RequestException: '.$e->getMessage());
            } else {
                Log::error("$job-$messageId Error getting message from EmailEngine");
                Log::error("$job-$messageId RequestException: " .$e->getMessage());
            }

            if ($e->hasResponse()) {
                $response = $e->getResponse();
            } else {
                $response = (new \GuzzleHttp\Psr7\Response)->withStatus($e->getCode());
            }
        } catch (ConnectException $e) {
            // Exception thrown when a connection cannot be established.
            // Note that no response is present for a ConnectException
            // Usually it's a timeout
            if (is_null($job)) {
                Log::error("Error getting message from EmailEngine with Message Id: $messageId");
                Log::error('ConnectException: '.$e->getMessage());
            } else {
                Log::error("$job-$messageId Error getting message from EmailEngine");
                Log::error("$job-$messageId ConnectException: " .$e->getMessage());
            }

            return false;

        } catch (\Throwable $e) {
            if (is_null($job)) {
                Log::error("Error getting message from EmailEngine with Message Id: $messageId");
                Log::error($e->getMessage());
            } else {
                Log::error("$job-$messageId Error getting message from EmailEngine");
                Log::error("$job-$messageId " .$e->getMessage());
            }

            return false;
        }

        return $response;
    }

    /**
     * TODO: email engine does not have message data in response
     * TODO: so either wait it in webhook or dispatch a job to fetch from
     * TODO: "/account/{accountId}/message/{messageId}
     * TODO: initally create thread and message then complete data from job/webhook
     *
     * Create an email message based on EmailEngine reponse data and also update/create mailbox thread.
     *
     * @param  $responseData
     * @param  \ZBateson\MailMimeParser\Message  $rawMessage
     * @param  Prospect  $prospect
     * @param  string  $origin
     * @param  null  $emailTemplateId
     * @param  null  $status
     * @return EmailMessage
     */
    public function saveEmailMessage($responseData, $messageHeaders, Prospect $prospect, $origin = null, $emailTemplateId = null, $status = null)
    {
        DB::connection('mysql::write')->beginTransaction();

        try {
            // check if message exists and attached to thread
            $emailMessage = EmailMessage::where('message_id', $responseData['messageId'])->with('emailThread')->first();

            if ($emailMessage && $emailMessage->emailThread){
                $emailThread = $emailMessage->emailThread;
            }

            // get thread from "threadId" or in the custom header "X-wv-ee-thid"
            if (!empty($this->getHeaderValue('X-wv-ee-thid', $messageHeaders))) {
                $wavoHeaderThreadId = $this->getHeaderValue('X-wv-ee-thid', $messageHeaders);
            } elseif (!empty($this->getHeaderValue('wv-ee-thid', $messageHeaders))) {
                $wavoHeaderThreadId = $this->getHeaderValue('wv-ee-thid', $messageHeaders);
            }
            $eeThreadId = !empty($responseData['threadId']) ? $responseData['threadId'] : null;

            if (!empty($eeThreadId) && empty($emailThread)) {
                $emailThread = EmailThread::where('ee_thread_id', $eeThreadId)
                    ->where('email_account_id', $this->emailAccount->id)
                    ->first();
            } elseif (!empty($wavoHeaderThreadId) && empty($emailThread)) {
                $emailThread = EmailThread::where('id', $wavoHeaderThreadId[0])
                    ->where('email_account_id', $this->emailAccount->id)
                    ->first();
            }

            // if no thread, check "in-reply-to" header if exists in email_messages.message_id
            if (empty($emailThread)) {
                $inReplyTo = $this->getHeaderValue('in-reply-to', $messageHeaders);
                $inReplyTo = !empty($inReplyTo) ? $inReplyTo[0] : '';

                if ($inReplyTo) {
                    $inReplyTo = str_replace(['<', '>'], '', $inReplyTo);
                    $inReplyMessage = EmailMessage::where('message_id', $inReplyTo)->with('emailThread')->first();

                    if ($inReplyMessage && $inReplyMessage->emailThread) {
                        $emailThread = $inReplyMessage->emailThread;
                    }
                }
            }

            // if still no thread generated from header, just create
            if (empty($emailThread)) {
                $emailThread = EmailThread::updateOrCreate([
                    'ee_thread_id'     => $eeThreadId,
                    'email_account_id' => $this->emailAccount->id,
                    'campaign_id'      => $prospect->campaign_id,
                    'prospect_id'      => $prospect->id,
                ]);
            } else {
                $emailThread->update(['ee_thread_id' => $eeThreadId]);
            }

            // If origin null, then set it as self
            if ($origin == null) {
                if (strtolower($responseData['from']['address']) == strtolower($this->emailAccount->email_address) ||
                    ($this->emailAccount->alias_enabled && strtolower($responseData['from']['address']) == strtolower($this->emailAccount->alias_email_address))
                ) {
                    $origin = 'self';
                } else {
                    $origin = 'prospect';
                }
            }

            $recipients = [];

            // save recipients if multiple "To" or has "CC"
            if (@count($responseData['to']) > 1) {
                foreach ($responseData['to'] as $to) {
                    $recipients['to'][] = [
                        'name' => $to['name'],
                        'email' => $to['address']
                    ];
                }
            }

            if (!empty($responseData['cc'])) {
                // follow EmailEngine recipients format: 'address' -> 'email
                foreach ($responseData['cc'] as $cc) {
                    $recipients['cc'][] = [
                        'name' => $cc['name'],
                        'email' => $cc['address']
                    ];
                }
            }

            // otherwise set it to null
            $recipients = !empty($recipients) ? $recipients : null;

            $inReplyTo = $this->getHeaderValue('in-reply-to', $messageHeaders);
            $references = $this->getHeaderReferences($messageHeaders);
            $subject = trim($responseData['subject']);

            // check for EmailEngine EmailId
            $eeEmailId = null;
            $msOrigMsgId = $this->getHeaderValue('x-microsoft-original-message-id', $messageHeaders);

            // for outlook that replaced EE's orig messageId with their our messageId
            if (!empty($msOrigMsgId[0])) {
                $xmsOrigMsgId = str_replace(['<', '>'], '', $msOrigMsgId[0]);
                $eeEmailId = $xmsOrigMsgId;
            }

            // for gmail that supports global emailId internally
            if (!empty($responseData['emailId'])) {
                $eeEmailId = $responseData['emailId'];
            }

            // construct the snippet
            if (!empty($responseData['text']['plain'])) {
                $snippet = $responseData['text']['plain'];
            } elseif (!empty($responseData['text']['html'])) {
                $snippet = preg_replace( "/\n\s+/", "\n", rtrim(html_entity_decode(strip_tags($responseData['text']['html']))) );
            } else {
                $snippet = $responseData['subject'];
            }

            if (strlen($subject) > 255) {
                $subject = mb_strcut($responseData['subject'], 0, 251) . '...';
            }

            $fromName = $responseData['from']['name'];

            // if no "from.name", use prospect's name if same emailaddress as sender
            if($origin == 'prospect' && empty($fromName) &&
                strtolower($responseData['from']['address']) == strtolower($prospect->email)
            ) {
                $fromName = $prospect->full_name;
            }

            $updateFields = [
                'email_account_id' => $this->emailAccount->id,
                'agency_id'        => $this->emailAccount->agency_id,
                'campaign_id'      => $prospect->campaign_id,
                'prospect_id'      => $prospect->id,
                'email_thread_id'  => $emailThread->id,
                'from'             => $responseData['from']['address'],
                'to'               => $responseData['to'][0]['address'],
                'from_name'        => $fromName,
                'to_name'          => $responseData['to'][0]['name'],
                'subject'          => $subject,
                'snippet'          => mb_substr($snippet, 0, 180),
                'origin'           => $origin,
                'submitted_at'     => Carbon::create($responseData['date']),
                'recipients'       => $recipients,
                'in_reply_to'      => !empty($inReplyTo) ? str_replace(['<', '>'], '', $inReplyTo[0]) : null,
                'references'       => $references,
                'ee_thread_id'     => $emailThread->ee_thread_id,
                'ee_email_id'      => $eeEmailId,
                'ee_id'            => $responseData['id']
            ];

            // sometimes EE failed to send messagebody
            if (!empty($responseData['text']['html'])) {
                $messageBody = $responseData['text']['html'];
            } elseif (!empty($responseData['text']['plain'])) {
                $messageBody = $responseData['text']['plain'];
            } else {
                $messageBody = '';
            }

            if (!empty($messageBody)){
                $updateFields['message'] = $this->getCleanMessageBody($messageBody);
            } else {
                $updateFields['message'] = '';
            }

            if ($emailTemplateId) {
                $updateFields['email_template_id'] = $emailTemplateId;
            }

            if ($origin == 'prospect' && $status) {
                $updateFields['status'] = $status;
            }

            $emailMessage = EmailMessage::updateOrCreate(
                [
                    'message_id'  => $responseData['messageId']
                ],
                $updateFields
            );

            if (!empty($emailMessage->ee_id)) {
                PendingEmailMessage::where('email_message_id', $emailMessage->id)->delete();
            }

            DB::connection('mysql::write')->commit();

        } catch (\Throwable $e) {
            DB::connection('mysql::write')->rollback();
            Log::error('Error saving EmailEngine message. ' . $e->getMessage());

            return null;
        }

        return $emailMessage;
    }

    public function getCleanMessageBody($messageBody, $clear = false) {
        if(!empty($messageBody)){
            // convert html/body into div because outer content will be remove
            // in the case of multiple or nested html/body
            $messageBody = preg_replace( '/(<)(html|body)([^>]+?)(\/?\s*>)/', '<div>', $messageBody);
            $messageBody = str_replace('</body>', '</div>', $messageBody);
            $messageBody = str_replace('</html>', '</div>', $messageBody);

            if($clear) {
                return mb_strcut(purifyAndClearMessageBody($messageBody), 0, 64000);
            }

            return mb_strcut(purifyMessageBody($messageBody), 0, 64000);
        }

        return '';
    }

    public function getHeaderValue($headerKey, $messageHeaders) {
        if(!$messageHeaders) {
            return null;
        }

        if($messageHeaders &&
            isset($messageHeaders[$headerKey]) &&
            @count($messageHeaders[$headerKey])
        ) {
            return $messageHeaders[$headerKey];
        }

        return null;
    }

    public function getHeaderReferences($messageHeaders) {
        $headerReferences = $messageHeaders ? $this->getHeaderValue('references', $messageHeaders) : null;
        $headerReferences = !empty($headerReferences) ? $headerReferences[0] : null;
        $references = $headerReferences ? explode(' ', $headerReferences) : null;

        if(!empty($references)) {
            $references = array_map(function($ref){
                return str_replace(['<', '>'], '', $ref);
            }, $references);
        }

        return $references;
    }

    /**
     * Parse raw message to values of references header.
     *
     * @param  \ZBateson\MailMimeParser\Message  $rawMessage
     * @return array|null
     */
    protected function getReferencesFromRawMessage($messageHeaders)
    {
        return [];
    }

    /**
     * Send an email reply to an existing email thread.
     *
     * @param EmailThread $emailThread
     * @param $reply
     * @param array $ccEmailAddresses
     * @param bool $replyAll
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function sendEmailReply(EmailThread $emailThread, $reply, $ccEmailAddresses = [], $replyAll = false, $toEmailAddresses = [])
    {
        $payload = $this->getEmailReplyPayload($emailThread, $reply, $ccEmailAddresses, $replyAll, $toEmailAddresses);

        try {
            $response = $this->http->post(
                'account/'.$this->emailAccount->hashid.'/submit',
                [
                    \GuzzleHttp\RequestOptions::JSON => $payload,
                    'query' => [
                        'access_token' => config('app.emailEngine.token')
                    ]
                ]
            );
        } catch (RequestException $e) {
            Log::error($e->getMessage());
            Log::error($payload);

            if ($e->hasResponse()) {
                $response = $e->getResponse();
            } else {
                $response = new HttpResponse(503);
            }
        }

        return array(
            'response'  => $response,
            'payload'   => $payload
        );
    }

    /**
     * Forward a thread to a given email address.
     *
     * @param EmailThread $emailThread
     * @param $emailAddress
     * @return null|\Psr\Http\Message\ResponseInterface
     */
    public function forwardThread(EmailThread $emailThread, $emailAddress)
    {
        $payload = $this->getEmailForwardPayload($emailThread, $emailAddress);

        try {
            $response = $this->http->post(
                'account/'.$this->emailAccount->hashid.'/submit',
                [
                    \GuzzleHttp\RequestOptions::JSON => $payload,
                    'query' => [
                        'access_token' => config('app.emailEngine.token')
                    ]
                ]
            );
        } catch (RequestException $e) {
            Log::error("ForwardThread API Error account-{$this->emailAccount->hashid}. {$e->getMessage()}");
            Log::error($payload);

            if ($e->hasResponse()) {
                $response = $e->getResponse();
            } else {
                $response = new HttpResponse(503);
            }
        }

        return $response;
    }

    /**
     * Create the payload needed for forwarding a thread.
     *
     * @param EmailThread $emailThread
     * @param $emailAddress
     * @return array
     */
    protected function getEmailForwardPayload(EmailThread $emailThread, $emailAddress)
    {
        $lastMessage = $emailThread->getLatestMessage();
        $lastRecipientCC = '';

        if($lastMessage->recipients && !empty($lastMessage->recipients['cc'])) {
            $lastCC = $lastMessage->recipients['cc'][0];
            $lastRecipientCC = 'Cc: '.$lastCC['name'].' &lt;<a href="mailto:'.$lastCC['email'].'" target="_blank">'.$lastCC['email'].'</a>&gt;<br>';
        }

        $forwardHeader = '---------- Forwarded message ---------<br>'.
'From: '.$lastMessage->from_name.' &lt;<a href="mailto:'.$lastMessage->from.'" target="_blank">'.$lastMessage->from.'</a>&gt;<br>'.
'Date: '.$lastMessage->submitted_at->timezone($lastMessage->campaign->carbon_timezone)->toDayDateTimeString().'<br>'.
'Subject: '.$lastMessage->subject.'<br>'.
'To: '.$lastMessage->to_name.' &lt;<a href="mailto:'.$lastMessage->to.'" target="_blank">'.$lastMessage->to.'</a>&gt;<br>'.
$lastRecipientCC;

        $message = '<br><br><div class="quote"><div dir="ltr">'.$forwardHeader.'</div><br><br>'.$lastMessage->message.'</div>';

        $payload = [
            'subject' => $lastMessage->subject,
            'from'    => [
                'name'      => $this->emailAccount->name,
                'address'   => $this->emailAccount->alias_enabled ? $this->emailAccount->alias_email_address : $this->emailAccount->email_address,
            ],
            'to'      => [
                [
                    'name'      => $emailAddress,
                    'address'   => $emailAddress,
                ],
            ],
            'html'     => $message,
            "replyTo"  => [
                [
                  "address" => $lastMessage->origin == 'prospect' ? $lastMessage->from : $lastMessage->to
                ]
            ],
        ];

        if($lastMessage->origin == 'prospect') {
            $payload['replyTo'][0]['name'] = $lastMessage->from_name ? $lastMessage->from_name : $lastMessage->from;
        } else {
            $payload['replyTo'][0]['name'] = $lastMessage->to_name ? $lastMessage->to_name : '';
        }


        return $payload;
    }

    /**
     * Create the payload needed for sending an email reply.
     *
     * @param EmailThread $emailThread
     * @param string $reply
     * @param array $ccEmailAddresses
     * @param bool $replyAll
     * @return array
     */
    protected function getEmailReplyPayload(EmailThread $emailThread, $reply, $ccEmailAddresses = [], $replyAll = false, $toEmailAddresses = [])
    {
        $lastMessage = $emailThread->getLatestMessage();
        $lastMsgTz = $lastMessage->campaign->carbon_timezone;

        $allReplies = $emailThread->emailMessages()
            ->where('origin', 'prospect')
            ->orderBy('submitted_at', 'desc');

        $toRecipients = array_map(function($toEmail) use($allReplies, $emailThread) {
            if($toEmail == $emailThread->prospect->email) {
                return [
                    'name' => $emailThread->prospect->full_name,
                    'address'=> $emailThread->prospect->email,
                ];
            }

            $fromReplies = $allReplies->where('from', $toEmail)->first();
            $toRecipient = [ 'address'=> $toEmail ];

            if($fromReplies && $fromReplies->from_name) {
                $toRecipient['name'] = $fromReplies->from_name;
            }

            return $toRecipient;
        }, $toEmailAddresses);

        $quotedMessage = '<div class="quote">'.
            '<div dir="ltr">On '.$lastMessage->submitted_at->timezone($lastMsgTz)->toDayDateTimeString().
            ' '.$lastMessage->from_name.' &lt;<a href="mailto:'.$lastMessage->from.'" target="_blank">'.
            $lastMessage->from.'</a>&gt; wrote:<br></div>
            <blockquote class="quote"><div dir="ltr">'.$lastMessage->message.'</div></blockquote></div>';

        $message = '<div>'.$reply.'</div><br>'.$quotedMessage;

        // manually create the plaintext version
        $textMsg = preg_replace( "/\n\s+/", "\n", rtrim(html_entity_decode(strip_tags($message))) );

        $payload = [
            'subject' => $lastMessage->subject,
            'from'    => [
                'name'      => $this->emailAccount->name,
                'address'   => $this->emailAccount->alias_enabled ? $this->emailAccount->alias_email_address : $this->emailAccount->email_address,
            ],
            'to'      => $toRecipients,
            'html'    => $message,
            'text'    => $textMsg
        ];

        if ($this->emailAccount->alias_enabled) {
            $payload['replyTo'] = [
                [
                    'name'      => $this->emailAccount->name,
                    'address'   => $this->emailAccount->alias_email_address
                ]
            ];
        }

        $validCcEmailAddresses = [];

        if (!empty($ccEmailAddresses)) {
            foreach ($ccEmailAddresses as $ccEmailAddress) {
                array_push($validCcEmailAddresses, ['address' => $ccEmailAddress]);
            }
        }

        if(!empty($validCcEmailAddresses)) {
            $payload['cc'] = $validCcEmailAddresses;
        }

        if ($replyAll && !is_null($lastMessage->recipients)) {
            $usedEmails = [
                $this->emailAccount->email_address,
                $emailThread->prospect->email
            ];
            if ($this->emailAccount->alias_enabled) {
                $usedEmails[] = $this->emailAccount->alias_email_address;
            }
            if(!empty($validCcEmailAddresses)) {
                foreach ($validCcEmailAddresses as $validCcEmailAddress) {
                    $usedEmails[] = $validCcEmailAddress;
                }
            }

            if (isset($lastMessage->recipients['to'])) {
                foreach ($lastMessage->recipients['to'] as $recipient) {
                    if (!in_array($recipient['email'], $usedEmails)) {
                        $to = ['address' => $recipient['email']];

                        if(!empty($recipient['name'])) {
                            $to['name'] = $recipient['name'];
                        }

                        $payload['to'][] = $to;
                        $usedEmails[] = $recipient['email'];
                    }
                }
            }

            if (isset($lastMessage->recipients['cc'])) {
                foreach ($lastMessage->recipients['cc'] as $recipient) {
                    if (!in_array($recipient['email'], $usedEmails)) {
                        $cc = ['address' => $recipient['email']];

                        if(!empty($recipient['name'])) {
                            $cc['name'] = $recipient['name'];
                        }

                        $payload['cc'][] = $cc;
                        $usedEmails[] = $recipient['email'];
                    }
                }
            }
        }

        return $payload;
    }

    public function saveQueuedEmailMessage($responseData, $prospect, $emailTemplateId = null, $status = null, $emailThread = null, $payload = array(), $userId = null)
    {
        $createData = [
            'ee_queue_id'      => $responseData['queueId'],
            'nylas_thread_id'  => null,
            'email_account_id' => $this->emailAccount->id,
            'agency_id'        => $this->emailAccount->agency_id,
            'campaign_id'      => $prospect->campaign_id,
            'prospect_id'      => $prospect->id,
            'from'             => $this->emailAccount->email_address,
            'to'               => $prospect->email,
            'from_name'        => $this->emailAccount->name,
            'to_name'          => $prospect->full_name,
            'origin'           => 'self',
            'submitted_at'     => Carbon::create($responseData['sendAt']),
            'recipients'       => null,
            'message_id'       => str_replace(['<', '>'], '', $responseData['messageId']),
            'in_reply_to'      => null,
            'references'       => null,
        ];

        if(!empty($payload['html'])) {
            $createData['message'] = $payload['html'];
        }

        if(!empty($payload['text'])) {
            $createData['snippet'] = mb_substr($payload['text'], 0, 180);
        }

        if(!empty($payload['subject'])) {
            $createData['subject'] = $payload['subject'];
        }

        if($emailTemplateId) {
            $createData['email_template_id'] = $emailTemplateId;
        }

        if($status) {
            $createData['status'] = $status;
        }

        if($emailThread) {
            $createData['email_thread_id'] = $emailThread->id;
        }

        DB::connection('mysql::write')->beginTransaction();

        try {
            $emailMessage = EmailMessage::create($createData);

            PendingEmailMessage::create([
                'prospect_id' => $emailMessage->prospect_id,
                'email_message_id' => $emailMessage->id,
                'user_id' => $userId,
            ]);

            DB::connection('mysql::write')->commit();

        } catch (\Throwable $e) {
            DB::connection('mysql::write')->rollBack();
            Log::error('Error saving EmailEngine message. ' . $e->getMessage());

            return null;
        }

        return $emailMessage;
    }

    /**
     * Create an incoming email message based on EmailEngine reponse, to be manually reviewed.
     *
     * @param  array  $responseData
     * @param  \ZBateson\MailMimeParser\Message  $rawMessage
     * @return IncomingEmailMessage
     */
    public function saveIncomingEmailMessage($responseData, $messageHeaders)
    {
        $inReplyTo = $this->getHeaderValue('in-reply-to', $messageHeaders);
        $references = $this->getHeaderReferences($messageHeaders);
        $eeEmailId = !empty($responseData['emailId']) ? $responseData['emailId'] : null;
        $eeThreadId = !empty($responseData['threadId']) ? $responseData['threadId'] : null;
        $subject = trim($responseData['subject']);

        // sometimes EE failed to send messagebody
        if(!empty($responseData['text']['html'])) {
            $messageBody = $responseData['text']['html'];
        } elseif (!empty($responseData['text']['plain'])) {
            $messageBody = $responseData['text']['plain'];
        } else {
            $messageBody = '';
        }

        // construct the snippet
        if(!empty($responseData['text']['plain'])) {
            $snippet = $responseData['text']['plain'];
        } elseif (!empty($responseData['text']['html'])) {
            $snippet = preg_replace( "/\n\s+/", "\n", rtrim(html_entity_decode(strip_tags($responseData['text']['html']))) );
        } else {
            $snippet = $subject;
        }

        if(strlen($subject) > 255) {
            $subject = mb_strcut($responseData['subject'], 0, 251) . '...';
        }

        try {
            $incomingEmailMessage = IncomingEmailMessage::firstOrCreate(
                [
                    'ee_id'            => $responseData['id'],
                ],
                $createField = [
                    'email_account_id' => $this->emailAccount->id,
                    'agency_id'        => $this->emailAccount->agency_id,
                    'from'             => $responseData['from']['address'],
                    'to'               => $responseData['to'][0]['address'],
                    'from_name'        => $responseData['from']['name'],
                    'to_name'          => $responseData['to'][0]['name'],
                    'message'          => $this->getCleanMessageBody($messageBody),
                    'snippet'          => mb_substr($snippet, 0, 180),
                    'subject'          => $subject,
                    'submitted_at'     => Carbon::create($responseData['date']),
                    'message_id'       => $responseData['messageId'],
                    'in_reply_to'      => !empty($inReplyTo) ? str_replace(['<', '>'], '', $inReplyTo[0]) : null,
                    'references'       => $references,
                    'ee_thread_id'     => $eeThreadId,
                    'ee_email_id'      => $eeEmailId,
                ]
            );
        } catch (Exception $e) {
            $incomingEmailMessage = IncomingEmailMessage::firstOrCreate(
                [
                    'ee_id'            => $responseData['id'],
                ],
                $createField = [
                    'email_account_id' => $this->emailAccount->id,
                    'agency_id'        => $this->emailAccount->agency_id,
                    'from'             => $responseData['from']['address'],
                    'to'               => $responseData['to'][0]['address'],
                    'from_name'        => $responseData['from']['name'],
                    'to_name'          => $responseData['to'][0]['name'],
                    'message'          => $this->getCleanMessageBody($messageBody, true),
                    'snippet'          => mb_substr($snippet, 0, 180),
                    'subject'          => $subject,
                    'submitted_at'     => Carbon::create($responseData['date']),
                    'message_id'       => $responseData['messageId'],
                    'in_reply_to'      => !empty($inReplyTo) ? str_replace(['<', '>'], '', $inReplyTo[0]) : null,
                    'references'       => $references,
                    'ee_thread_id'     => $eeThreadId,
                    'ee_email_id'      => $eeEmailId,
                ]
            );
        }

        return $incomingEmailMessage;
    }

    /**
     * Submit a fresh token to emailengine
     *
     * @return \Illuminate\Contracts\Routing\ResponseFactory|mixed|\Symfony\Component\HttpFoundation\Response
     */
    public function updateOffice365AccessToken() {
        $this->emailAccount->load(['emailEngineAccount']);

        $payload = [
            'imap' => [
                'auth' => [
                    'user' => $this->emailAccount->email_address,
                    'accessToken' => $this->emailAccount->emailEngineAccount->access_token
                ],
                'host'     => 'outlook.office365.com',
                'port'     => 993,
                'secure'   => true
            ],
            'smtp' => [
                'auth' => [
                    'user' => $this->emailAccount->email_address,
                    'accessToken' => $this->emailAccount->emailEngineAccount->access_token
                ],
                'host'     => 'smtp.office365.com',
                'port'     => 587,
                'secure'   => false
            ]
        ];

        try {
            $response = $this->http->put(
                'account/'.$this->emailAccount->hashid,
                [
                    \GuzzleHttp\RequestOptions::JSON => $payload,
                    'query' => [
                        'access_token' => config('app.emailEngine.token')
                    ]
                ]
            );
        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $response = $e->getResponse();
            } else {
                $response = new HttpResponse(503);
            }
        }

        return $response;
    }

    /**
     * Move an email message to a specific folder.
     *
     * @param string $messageId
     * @param string $folderId
     * @return bool|\Psr\Http\Message\ResponseInterface
     * @throws RequestException
     */
    public function moveMessage($messageId, $folderId)
    {
        $payload = [
            'path' => $folderId
        ];

        try {
            $response = $this->http->put(
                'account/'.$this->emailAccount->hashid.'/message/'.$messageId.'/move',
                [
                    \GuzzleHttp\RequestOptions::JSON => $payload,
                    'query' => [
                        'access_token' => config('app.emailEngine.token')
                    ]
                ]
            );
        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $response = $e->getResponse();
            } else {
                $response = new HttpResponse(503);
            }
        }

        return $response;
    }

    /**
     * Cancel a EmailEngine Account.
     *
     * @return mixed
     */
    public function cancelAccount()
    {
        Log::info('Cancelling EmailEngine Account...'.$this->emailAccount->email_address);

        $cancelled = $this->downgradeEmailEngineAccount();

        if ($cancelled) {
            $this->emailAccount->emailEngineAccount->update(['billing_state' => 'cancelled']);
            $this->emailAccount->update([
                'cancelled_at' => Carbon::now(),
                'is_cancelled' => true,
                'sends_warmup_messages' => false,
            ]);
        }

        return $cancelled;
    }

    /**
     * Downgrade billing state on EmailEngine (to cancelled).
     *
     * @return boolean
     */
    public function downgradeEmailEngineAccount()
    {
        Log::info('Downgrading billing of EmailEngine Account...'.$this->emailAccount->email_address);

        try {
            $response = $this->http->delete(
                'account/'.$this->emailAccount->hashid,
                [
                    'query' => [
                        'access_token' => config('app.emailEngine.token')
                    ]
                ]
            );
        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $response = $e->getResponse();
            } else {
                $response = new HttpResponse(503);
            }
        }

        $responseData = collect(json_decode($response->getBody()->getContents(), true));

        if ($response->getStatusCode() == 200) {
            Log::info('Deleted EmailEngine Account...'.$this->emailAccount->email_address);
            return $responseData['deleted'];
        }

        // possible deleted manually from EE dashboard
        if ($response->getStatusCode() == 404) {
            Log::info('404 EmailEngine Account...'.$this->emailAccount->email_address);
            return true;
        }

        return false;
    }

    /**
     * Create a new mailbox folder.
     *
     * @param $name
     * @return bool|\Illuminate\Support\Collection
     * @throws RequestException
     */
    public function createFolder($name)
    {
        $mailboxPayload = ['path' => array($name)];

        try {
            $response = $this->http->post(
                'account/'.$this->emailAccount->hashid.'/mailbox',
                [
                    \GuzzleHttp\RequestOptions::JSON => $mailboxPayload,
                    'query' => [
                        'access_token' => config('app.emailEngine.token')
                    ]
                ]
            );
        } catch (RequestException $e) {
            Log::error('EmailAccount-'.$this->emailAccount->id.' Error creating folder of email account: '.$this->emailAccount->email);
            Log::error($e->getMessage());

            throw $e;
        }

        $data = collect(json_decode($response->getBody()->getContents(), true));

        // Example $data
        // {
        //     "path": "Archive",
        //     "created": true
        // }

        return $this->emailAccount->emailFolders()->create([
            'folder_id' => $data->get('path'),
            'name' => strtolower($data->get('path') ?? $name),
            'display_name' => $data->get('path'),
            'type' => 'folder'
        ]);
    }

    /**
     * Send an email message to agency to test if the newly connected account has no SMTP issue.
     *
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function sendConnectionTestMsg()
    {
        $this->emailAccount->load(['agency']);

        Log::info('sendConnectionTestMsg: '.$this->emailAccount->email_address);
        if ($this->emailAccount->agency->isAgencyDashboard()) {
            $domain = $this->emailAccount->agency->domain;
        } else {
            $domain = config('app.platformUrl');
        }

        $payload = [
            'subject' => 'Account connect test message from '.$this->emailAccount->email_address,
            'from'    => [
                'name'  => $this->emailAccount->name,
                'address' => $this->emailAccount->email_address,
            ],
            'to'      => [
                [
                    'name' => $this->emailAccount->agency->name,
                    'address'=> $this->emailAccount->agency->email,
                ],
            ],
            'html'     => "<p>Test message connecting email account {$this->emailAccount->email_address} on $domain.</p>",
            'text'     => "Test message connecting email account {$this->emailAccount->email_address} on $domain.",
            "deliveryAttempts" => 3
        ];

        try {
            $response = $this->http->post(
                'account/'.$this->emailAccount->hashid.'/submit',
                [
                    \GuzzleHttp\RequestOptions::JSON => $payload,
                    'query' => [
                        'access_token' => config('app.emailEngine.token')
                    ]
                ]
            );

        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $response = $e->getResponse();
            } else {
                $response = new HttpResponse(503);
            }
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
            $response = new HttpResponse(503);
        }

        return $response;
    }

    /**
     * Find the connection test messages we have recently sent through the email account
     *
     * @param EmailMessageTest $testMessage
     * @return false|\Illuminate\Support\Collection
     */
    public function findConnectionTestMessage(EmailMessageTest $testMessage)
    {
        $path = $this->getSentFolderPath();
        $search = [
            'subject' => 'Account connect test message from '.$this->emailAccount->email_address,
            'sentSince' => $testMessage->created_at->subMinute()->toISOString(),
        ];

        try {
            $response = $this->http->post(
                'account/'.$this->emailAccount->hashid.'/search',
                [
                    \GuzzleHttp\RequestOptions::JSON => ['search' => $search],
                    'query' => [
                        'access_token' => config('app.emailEngine.token'),
                        'path' => $path
                    ],
                    'timeout' => 240,
                ]
            );
        } catch (RequestException $e) {
            // 503 if not authenticated
            Log::error('EmailAccount-' . $this->emailAccount->id . ' Error finding email messages: ' . $e->getMessage());
            return false;
        } catch (\Throwable $e) {
            Log::error('EmailAccount-' . $this->emailAccount->id . ' Unexpected error finding email messages: ' . $e->getMessage());
            return false;
        }

        return collect(json_decode($response->getBody()->getContents(), true));
    }

    /**
     * Get the sent folder path based on email provider
     *
     * @return string
     */
    protected function getSentFolderPath(): string
    {
        switch ($this->emailAccount->email_server_type) {
            case 'gmail':
                // Native Gmail OAuth connection
                return '[Gmail]/Sent Mail';

            case 'gmail_imap':
                // Gmail connected through IMAP
                return '[Gmail]/Sent Mail';

            case 'office365':
                // Microsoft 365/Outlook
                return 'Sent Items';

            case 'imap':
                // Generic IMAP - use most common folder name
                return 'Sent';

            default:
                // Fallback to most common folder name
                Log::warning('Unknown email server type: ' . $this->emailAccount->email_server_type . ' for account: ' . $this->emailAccount->id);
                return 'Sent';
        }
    }

    /**
     * Send an email message to agency to test if the newly connected account has no SMTP issue.
     *
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function sendWarmupInboxMsg($recipientAccount, $subject, $message)
    {
        $this->emailAccount->load(['agency']);

        Log::info('sendWarmupInboxMsg from: '.$this->emailAccount->email_address);
        if ($this->emailAccount->agency->isAgencyDashboard()) {
            $domain = $this->emailAccount->agency->domain;
        } else {
            $domain = config('app.platformUrl');
        }

        $payload = [
            'subject' => $subject,
            'from'    => [
                'name'  => $this->emailAccount->name,
                'address' => $this->emailAccount->email_address,
            ],
            'to'      => [
                [
                    'name' => $recipientAccount->name,
                    'address'=> $recipientAccount->email_address,
                ],
            ],
            'html'     => $message,
            'text'     => strip_tags($message),
            "deliveryAttempts" => 3
        ];

        Log::info($payload);

        try {
            $response = $this->http->post(
                'account/'.$this->emailAccount->hashid.'/submit',
                [
                    \GuzzleHttp\RequestOptions::JSON => $payload,
                    'query' => [
                        'access_token' => config('app.emailEngine.token')
                    ]
                ]
            );

        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $response = $e->getResponse();
            } else {
                $response = new HttpResponse(503);
            }
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
            $response = new HttpResponse(503);
        }

        return $response;
    }

    public function moveWarmupInboxMsg($eeId, $newPath)
    {
        $payload = [
            'path' => $newPath
        ];

        try {
            $response = $this->http->put(
                'account/'.$this->emailAccount->hashid.'/message/'.$eeId.'/move',
                [
                    \GuzzleHttp\RequestOptions::JSON => $payload,
                    'query' => [
                        'access_token' => config('app.emailEngine.token')
                    ]
                ]
            );

        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $response = $e->getResponse();
            } else {
                $response = new HttpResponse(503);
            }
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
            $response = new HttpResponse(503);
        }

        return $response;
    }
}
