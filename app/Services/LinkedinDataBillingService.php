<?php

namespace App\Services;

use App\Events\Subscription\SubscriptionCancelled;
use App\Events\Subscription\SubscriptionUpdated;
use App\Events\Subscription\UserSubscribed;
use App\Jobs\ReportCreditUsage;
use App\Jobs\SetSubscriptionBillingThreshold;
use App\Spark;
use App\User;
use Illuminate\Support\Facades\Log;
use Laravel\Cashier\Exceptions\SubscriptionUpdateFailure;

class LinkedinDataBillingService
{
    /**
     * The billable owner of the accounts.
     *
     * @var \App\User
     */
    protected $user;

    /**
     * The billing plan used for subscriptions (monthly fee).
     *
     * @var \Laravel\Spark\Plan
     */
    protected $plan;

    /**
     * The billing plan used for metered usage.
     *
     * @var \Laravel\Spark\Plan
     */
    protected $meteredPlan;

    /**
     * The existing or new subscription object.
     *
     * @var \Laravel\Spark\Subscription
     */
    protected $subscription;

    /**
     * This type of subscription.
     *
     * @var string
     */
    protected static $subscriptionType = 'linkedin-data';

    /**
     * LinkedinAccountBillingService constructor.
     *
     * @param \App\User $user
     */
    public function __construct(User $user)
    {
        $this->user = $user;
        $this->subscription = $this->user->subscription(static::$subscriptionType);
        $this->getPlans();
    }

    public function activate()
    {
        if ($this->subscription && $this->subscription->onGracePeriod()) {
            info('resume subscription from grace period');
            try {
                Log::info("Update data subscription plan. Agency:{$this->user->agency_id}. Was on grace period.");
                $this->subscription->resume();
            } catch (SubscriptionUpdateFailure $exception) {
                Log::error('Update subscription failed: '.$exception->getMessage());

                return false;
            }

            // TODO: What happens with metered usage if cancelled and then re-activate subscription? Verify invoices.

            event(new SubscriptionUpdated($this->user->fresh(), $this->plan));

            return true;
        } elseif (! $this->subscription || $this->subscription->canceled()) {
            info('creating subscription');
            return $this->subscribe();
        } else {
            info('subscription exists');
            // Subscription already active. Do nothing (show some error?).
            return true;
        }
    }

    /**
     * Cancel active subscription.
     *
     * @return bool
     */
    public function cancel()
    {
        if (! $this->subscription || $this->subscription->canceled()) {
            return false;
        }

        try {
            ReportCreditUsage::dispatch($this->user);
            $this->subscription->cancel();

            event(new SubscriptionCancelled($this->user->fresh(), $this->plan));
        } catch (\Throwable $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }

    /**
     * Update usage already reported to Stripe.
     *
     * @param $credits
     * @param $timestamp
     * @return bool
     */
    public function updateUsage($credits, $timestamp)
    {
        $usageRecord = $this->user->subscription(static::$subscriptionType)
            ->reportUsageFor($this->meteredPlan->id, $credits, $timestamp);

        return true;
    }

    /**
     * Get the usage records of the subscription from Stripe.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getUsage()
    {
        return $this->user->subscription(static::$subscriptionType)->usageRecords([], $this->meteredPlan->id);

    }

    /**
     * Create a new 'linkedin-data' subscription.
     * Also create a CreditUsage row for storing the usage of the subscription.
     * Finally dispatch a job to set the subscription's billing threshold.     *
     *
     * @return bool
     */
    protected function subscribe()
    {
        if ($this->user->agency->is_billed) {
            $subscription = $this->user
                ->newSubscription(static::$subscriptionType, [
                    $this->plan->id,
                    $this->meteredPlan->id
                ])
                ->meteredPrice($this->meteredPlan->id);
        } else {
            // Wavo 2.0. No billing for LI data usage, only credits beyond threshold.
            $subscription = $this->user
                ->newSubscription(static::$subscriptionType, $this->meteredPlan->id)
                ->meteredPrice($this->meteredPlan->id);
        }

        try {
            $this->subscription = $subscription->create();
        } catch (\Throwable $exception) {
            Log::error('Subscription creation failed:'.$exception->getMessage());

            // TODO: Could be handled better?
            // https://github.com/laravel/cashier-stripe/blob/master/UPGRADE.md#simplified-payment-exceptions
            // If payment failed, try to close invoice.
            if ($exception instanceof \Laravel\Cashier\Exceptions\IncompletePayment) {
                if ($this->closeInvoice($exception->payment->invoice)) {

                    return false;
                }
            }

            $this->user->forceFill(['failed_charge' => $exception->getMessage()])->save();

            return false;
        }

        $this->user->forceFill(['failed_charge' => null])->save();

        if ($this->user->agency->is_billed) {
            event(new UserSubscribed($this->user->fresh(), $this->plan, false));
        } else {
            event(new UserSubscribed($this->user->fresh(), $this->meteredPlan, false));
        }

        info('create credit usage');
        $stripeSubscription = $this->subscription->asStripeSubscription();
        $this->user->creditUsages()->create([
            'agency_id' => $this->user->agency_id,
            'subscription_id' => $this->subscription->id,
            'period_start' => $stripeSubscription->current_period_start,
            'period_end' => $stripeSubscription->current_period_end
        ]);

        // Set billing threshold when creating new subscription.
        // Use a job with delay as Stripe needs a few seconds after subscription creation,
        // before it can accept requests for setting the billing threshold.
        info('fire job for setting billing threshold');
        SetSubscriptionBillingThreshold::dispatch($this->subscription)->delay(20);

        info('done!');

        return true;
    }

    /**
     * @param \Stripe\Invoice|string $invoice
     * @return bool
     */
    protected function closeInvoice($invoice)
    {
        try {
            if (! is_object($invoice)) {
                \Stripe\Stripe::setApiKey(config('cashier.secret'));
                $invoice = \Stripe\Invoice::retrieve($invoice);
            }
            $invoice->voidInvoice();

            return true;
        } catch (\Throwable $e) {

            return false;
        }
    }

    /**
     * @return \Laravel\Spark\Plan|null
     */
    protected function getPlans()
    {
        if ($this->subscription && $this->subscription->valid()) {
            $this->subscription->items->each(function ($subscriptionItem) {
                $plan = Spark::plans()->where('id', $subscriptionItem->stripe_price)->first();
                if ($plan->metered) {
                    $this->meteredPlan = $plan;
                } else {
                    $this->plan = $plan;
                }
            });
        }

        // No valid subscription found. Get the default plans.
        $this->plan = Spark::plans()->filter(function ($plan) {
            return $plan->plan_type == static::$subscriptionType && ! $plan->metered;
        })->first();

        $this->meteredPlan = Spark::plans()->filter(function ($plan) {
            return $plan->plan_type == static::$subscriptionType && $plan->metered;
        })->first();
    }
}
