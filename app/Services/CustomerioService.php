<?php

namespace App\Services;

use App\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

use GuzzleHttp\Client as HttpClient;
use GuzzleHttp\Exception\RequestException;

class CustomerioService
{
    private $agency;
    private $user;

    // list of allowed events
    private $events = [
        'trial_requested',
        'trial_started',
        'phone_verified',
        'search_run',
        'campaign_created',
        'email_added',
        'gpt_added',
        'campaign_contacts_added',
        'campaign_started',
        'reply_tagged_positive',
        'signup_',
        'switch_plan_',
        'subscription_cancelled',
        'subscription_ended',
    ];

    public function __construct(User $user)
    {
        $this->user = $user;
        $this->agency = $user->agency;
        $this->http = new HttpClient(['base_uri' => "https://track.customer.io/api/v2/"]);
    }

    public function addUser()
    {
        Log::info('CustomerioService Add User: '.$this->user->email);

        $capitalizedName = mb_convert_case($this->user->name, MB_CASE_TITLE, "UTF-8");
        $fullName = explode(' ', $capitalizedName);
        $firstName = trim(array_shift($fullName));
        $lastName = trim(implode(' ', $fullName));

        $data = array(
            "type" => "person",
            "identifiers" => array(
                "email" => $this->user->email
            ),
            "action" => "identify",
            "attributes" => array(
                "first_name" => $firstName ?: "-",
                "last_name" => $lastName ?: "-",
                "wavo_agency" => $this->agency->name,
                "wavo_plan" => $this->agency->current_billing_plan
            )
        );

        return $this->sendApiRequest($data);
    }

    // Handle event
    public function triggerEvent($eventName)
    {
        Log::info('CustomerioService Trigger: '.$eventName .', User: '.$this->user->email);

        if(!Str::startsWith($eventName, $this->events)) {
            return array(
                'status' => 'error',
                'code' => 422,
                'error' => 'Not allowed event: '.$eventName,
                'action' => 'event'
            );
        }

        if($this->agency->wavo_version !== 3) {
            return array(
                'status' => 'error',
                'code' => 422,
                'error' => 'Agency not allowed: '.$this->agency->wavo_version,
                'action' => 'event'
            );
        }

        // If change of billing, then update customer.io with new plan before sending the event
        if (Str::startsWith($eventName, 'signup_') || Str::startsWith($eventName, 'switch_plan_')) {
            $data = array(
                "type" => "person",
                "identifiers" => array(
                    "email" => $this->user->email
                ),
                "action" => "identify",
                "attributes" => array(
                    "wavo_plan" => Str::remove(['switch_plan_', 'signup_'], $eventName),
                )
            );

            $this->sendApiRequest($data);
        }

        if ($eventName == 'subscription_cancelled' || $eventName == 'subscription_ended') {
            $data = array(
                "type" => "person",
                "identifiers" => array(
                    "email" => $this->user->email
                ),
                "action" => "identify",
                "attributes" => array(
                    "wavo_plan" => null,
                )
            );

            $this->sendApiRequest($data);
        }

        // Send the event to customer.io
        $data = array(
            "type" => "person",
            "identifiers" => array(
                "email" => $this->user->email
            ),
            "action" => "event",
            "name" => $eventName
        );

        $response = $this->sendApiRequest($data);

        // If a phone has been verified, then also update customer.io person
        if ($eventName == 'phone_verified') {
            $data = array(
                "type" => "person",
                "identifiers" => array(
                    "email" => $this->user->email
                ),
                "action" => "identify",
                "attributes" => array(
                    "phone" => $this->user->phone_number,
                )
            );

            $this->sendApiRequest($data);
        }

        return $response;
    }

    // Contact Customer.io API
    protected function sendApiRequest($data)
    {
        if (!config("app.customerio.enable")) {
            Log::info('CustomerioService disabled');
            Log::info(collect($data));

            return [
                'status' => 'success',
                'code' => 200,
                'error' => null,
                'action' => $data['action']
            ];
        }

        $uname = config("app.customerio.username");
        $pword = config("app.customerio.password");

        try {
            $response = $this->http->post('entity', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Basic ' . base64_encode($uname . ":" . $pword)
                ],
                'json' => $data
            ]);

            $statusCode = $response->getStatusCode();

            if ($statusCode === 200) {
                return [
                    'status' => 'success',
                    'code' => $statusCode,
                    'error' => null,
                    'action' => $data['action']
                ];
            }

            Log::error("CustomerioService unexpected status code: {$statusCode}");
            return [
                'status' => 'error',
                'code' => $statusCode,
                'error' => 'Unexpected status code',
                'action' => $data['action']
            ];

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 500;
            $errMsg = $e->getMessage();
            $responseError = $e->getResponse() ? json_decode($e->getResponse()->getBody()->getContents(), true) : null;
            $msgError = $responseError['error']['message'] ?? 'Unknown error';

            Log::error("CustomerioService failed with status-{$statusCode}");
            Log::error("CustomerioService failed with agency-{$this->agency->id}");
            Log::error("CustomerioService failed with action-{$data['action']}");
            Log::error("CustomerioService error message: {$errMsg}");

            if ($data['action'] == 'event') {
                Log::error("CustomerioService failed with event-{$data['name']}");
            }

            return [
                'status' => 'error',
                'code' => $statusCode,
                'error' => $msgError,
                'action' => $data['action']
            ];

        } catch (\Throwable $e) {
            Log::error("CustomerioService unexpected error: " . $e->getMessage());

            return [
                'status' => 'error',
                'code' => 500,
                'error' => 'Unexpected error',
                'action' => $data['action']
            ];
        }
    }
}
