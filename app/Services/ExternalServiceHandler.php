<?php

namespace App\Services;

use App\EmailNotification;
use App\Mail\ErrorNotification;
use App\Models\ExternalService;
use App\Role;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ExternalServiceHandler
{
    /**
     * Activate external service.
     *
     * @param string $name
     * @return ExternalService
     */
    public function activateService($name)
    {
        $service = ExternalService::updateOrCreate(
            ['name' => $name],
            [
                'status' => 'active',
                'error' => null,
                'error_type' => null,
                'error_message' => null,
            ]
        );

        // reset error counters
        switch (strtolower($name)) {
            case 'reverse_contact':
                resetRedisCounter('reverse_contact', 'status_401');
                resetRedisCounter('reverse_contact', 'status_402');
                resetRedisCounter('reverse_contact', 'status_403');
                break;

            case 'rapid_api':
                resetRedisCounter('rapid_api', 'status_401');
                resetRedisCounter('rapid_api', 'status_402');
                resetRedisCounter('rapid_api', 'status_403');
                break;

            case 'serper':
                resetRedisCounter('serper', 'status_400');
                resetRedisCounter('serper', 'status_401');
                break;
        }

        return $service->fresh();
    }

    /**
     * Set external service status to error.
     *
     * @param string $serviceName
     * @param int $status
     * @param string $errorType
     * @param string $errorMessage
     */
    public function setServiceError($serviceName, $status, $errorType, $errorMessage)
    {
        try {
            $externalService = ExternalService::where('name', $serviceName)->first();

            if (!empty($externalService)) {
                $externalService->update([
                    'status' => "error",
                    "error" => $status,
                    "error_type" => $errorType,
                    "error_message" => $errorMessage,
                ]);
            }
        } catch (\Throwable $e) {
            Log::error("Unable to set {$serviceName} error.");
        }
    }

    /**
     * Send error notification email to admins
     *
     * @param string $key
     * @param string $source
     * @param int $status
     * @param string $notificationMessage
     */
    public function notifyServiceError($key, $source, $status, $notificationMessage)
    {
        if (config('app.env') != 'production') {
            Log::info('This command is only available in production');

            return;
        }

        // check if we haven't notified admin today
        $date = now()->format('Y_m_d');
        $errorCounterToday = (int) getRedisCounter($key, 'status_'.$status, $date)->first();

        if ($errorCounterToday) {

            return;
        }

        $notificationUsers = Role::where('name', 'admin')->first()->users;
        $userType = 'admins';
        $notificationMessage = "$source $notificationMessage";

        Mail::to($notificationUsers)
            ->send(new ErrorNotification($notificationMessage, "$source API ERROR $status"));
        EmailNotification::create([
            'origin' => "$source ErrorNotification",
            'subject' => "$source API ERROR $status",
            'recipients' => $userType,
        ]);

        incrementRedisCounter($key, 'status_'.$status, $date);
    }
}
