<?php

namespace App;

use App\Traits\HasAgencyTenants;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Used to store the daily stats of messages (sent, replied, bounced etc)
 * Summed up per agency, team, campaign, email account
 */
class DailyStats extends Model
{
    use HasAgencyTenants;

    protected $guarded = ['id'];

    protected $fillable = [
        'agency_id',
        'team_id',
        'campaign_id',
        'email_account_id',
        'day',
        'sent',
        'autoreplied',
        'unsubscribed',
        'replied',
        'bounced',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'day' => 'datetime',
    ];

    /**
     * Filter messages by agency id.
     *
     * @param mixed $query
     * @param int $agencyId
     * @return mixed $query
     */
    public function scopeOfAgency($query, $agencyId)
    {
        if (!is_null($agencyId)) {
            return $query->where('agency_id', $agencyId);
        }

        return $query;
    }

    /**
     * Filter daily stats by campaigns.
     *
     * @param mixed $query
     * @param array/Collection $arrCampaignIds
     * @return mixed $query
     */
    public function scopeOfCampaigns($query, $campaignIds)
    {
        if (!empty($campaignIds)) {
            if (is_array($campaignIds) || ($campaignIds instanceof Collection)) {
                return $query->whereIn('campaign_id', $campaignIds);
            } else {
                return $query->where('campaign_id', $campaignIds);
            }
        }

        return $query;
    }

    /**
     * Filter messages by email_account_id.
     *
     * @param mixed $query
     * @param array/Collection $emailAccountIds
     * @return mixed $query
     */
    public function scopeOfEmailAccounts($query, $emailAccountIds)
    {
        if (!empty($emailAccountIds)) {
            if (is_array($emailAccountIds) || ($emailAccountIds instanceof Collection)) {
                return $query->whereIn('email_account_id', $emailAccountIds);
            } else {
                return $query->where('email_account_id', $emailAccountIds);
            }
        }

        return $query;
    }

    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    public function emailAccount()
    {
        return $this->belongsTo(EmailAccount::class);
    }
}
