<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class NylasAccount extends Model
{
    // Primary key is a string.
    public $incrementing = false;
    protected $keyType = 'string';

    protected $guarded = [];

    /**
     * Get the nylas account access token, encoded for sending through a request to <PERSON><PERSON><PERSON>.
     *
     * @return string
     */
    public function getEncodedAccessTokenAttribute()
    {
        return 'Basic '.base64_encode($this->access_token.':');
    }

    /**
     * Scope query to those Nylas accounts that are paid and running.
     *
     * @param  $query
     * @return mixed
     */
    public function scopeRunning($query)
    {
        return $query->where('sync_state', 'running')->where('billing_state', 'paid');
    }

    /**
     * Scope query to those Nylas accounts that are cancelled.
     *
     * @param $query
     * @return mixed
     */
    public function scopeCancelled($query)
    {
        return $query->where('billing_state', '=', 'cancelled');
    }

    /**
     * Scope query to those Nylas accounts that are shown as paid.
     *
     * @param $query
     * @return mixed
     */
    public function scopePaid($query)
    {
        return $query->where('billing_state', '=', 'paid');
    }

    /**
     * Check if a Nylas account is running (paid and without errors).
     *
     * @return bool
     */
    public function isRunning()
    {
        return ($this->sync_state == 'running') && ($this->billing_state == 'paid');
    }

    /**
     * Each Nylas Account belongs to an email account.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailAccount()
    {
        return $this->belongsTo(EmailAccount::class);
    }

    // Removed. Not a relationship
    //public function campaigns()
    //{
    //    return $this->emailAccount->campaigns();
    //}
}
