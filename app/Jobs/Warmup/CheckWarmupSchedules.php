<?php

namespace App\Jobs\Warmup;

use App\EmailAccount;
use App\Services\WarmupManagerService;
use App\Services\WarmupSenderService;
use App\Traits\LogsMessages;
use App\WarmupMessage;
use App\WarmupSchedule;
use App\WarmupStat;
use App\WarmupDailyStats;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CheckWarmupSchedules implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsMessages;


    public $timeout = 300;
    public $tries = 1;

    protected const DB_READ_CONNECTION = 'mysql::read';

    protected $emailAccount;
    protected $warmupSchedule;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected int $id)
    {
        $this->logChannel = 'emailengine';
        $this->logPrefix = "CheckWarmupSchedules-$this->id";
    }


    public function tags()
    {
        return ['email-api', 'check-warmup-schedules', 'email-account-'.$this->id];
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $currentJobId = $this->job->getJobId();
        $this->emailAccount = EmailAccount::find($this->id);
        $this->emailAccount->load(['warmupSchedules','freezes','emailWarmup']);
        $this->logInfo('Start CheckWarmupSchedules job');

        if ($this->emailAccount->isRunning() && ! $this->emailAccount->isFrozen() && $this->emailAccount->sends_warmup_messages) {
            $emailAccountDelay = $this->emailAccount->getRandomInterval();
            $scheduleWait = $this->checkSchedules($emailAccountDelay);

            if ($scheduleWait !== false && !$this->emailAccount->isCheckingWarmupSchedules($currentJobId)) {
                // Override the email account random interval delay, if a schedule wait time will come first (and we didn't send a message on this run).
                $delay = $scheduleWait != 0 ? $scheduleWait : $emailAccountDelay;
                $this->logInfo('Dispatch CheckWarmupSchedules with delay of '.$delay.' secs');
                self::dispatch($this->id)->onQueue('schedule')->delay(now()->addSeconds($delay));
            }
        }
    }

    public function failed(\Throwable $exception)
    {
        $this->emailAccount = EmailAccount::find($this->id);

        $this->logError('Failed: '.$exception->getMessage());
        if ($this->emailAccount) {
            $this->warmupSchedule = $this->emailAccount->warmupSchedules()->where('queued', true)->first();
            if (!empty($this->warmupSchedule)) {
                $this->releaseLocks();
            }
        }
    }

    protected function checkSchedules($delay)
    {
        // Each email account has 2 active warmup schedules per day, one for new threads and one for replies.
        // We prioritize replies, as long as there is a message to reply to.
        if (WarmupMessage::ofRecipientAccount($this->emailAccount->id)->where('created_at', '>=', now()->subDays(10))->where('created_at', '<', now()->subMinutes(5))->exists()) {
            $this->logInfo('find schedule, prioritize replies');
            $warmupSchedules = WarmupSchedule::on(self::DB_READ_CONNECTION)
                ->ofEmailAccount($this->emailAccount->id)
                ->canSend()
                ->orderByDesc('is_reply') // Priority to replies
                ->get()
                ->keyBy('id');
        } else {
            $this->logInfo('find schedule, no replies');
            $warmupSchedules = WarmupSchedule::on(self::DB_READ_CONNECTION)
                ->ofEmailAccount($this->emailAccount->id)
                ->canSend()
                ->isNotReply()
                ->get()
                ->keyBy('id');
        }

        if ($warmupSchedules->count() == 0) {
            $this->logInfo('No running warmup schedules found. Stop checking for schedules.');

            return false;
        }

        foreach ($warmupSchedules as $warmupSchedule) {
            if ($warmupSchedule->isReadyToSend()) {
                $this->logInfo('WarmupSchedule-'.$warmupSchedule->id.' is ready to send. Find next recipient...');
                $this->warmupSchedule = $warmupSchedule;

                try {
                    $warmupManager = new WarmupManagerService();
                    $warmupSender = new WarmupSenderService();
                    $recipient = $warmupManager->selectRecipient($this->emailAccount, $warmupSchedule->is_reply);
                    if (empty($recipient)) {
                        $this->logInfo('No recipient found');

                        continue;
                    }
                    $this->logInfo("Send from emailAccount-{$this->emailAccount->id} to emailAccount-$recipient->id ({$this->emailAccount->hashid}->{$recipient->hashid})");
                    $this->setLocks();
                    $messageSent = $warmupSender->sendMessage($this->emailAccount, $recipient, $warmupSchedule->is_reply);
                    $this->releaseLocks($messageSent['success']);
                    if ($messageSent['message']) {
                        $warmupManager->incrementInteractionCount($this->emailAccount, $recipient);
                        $this->incrementSentStats($warmupSchedule->is_reply, $messageSent['message']);
                        $this->logInfo('Success');
                        return 0;
                    } else {
                        // account is not ready dispatch with 60sec delay
                        $this->logInfo('Failed. Account Syncing. Retry in 5 minutes.');

                        return 300;
                    }

                } catch (\Throwable $e) {
                    $this->logError($e->getMessage());
                    $this->releaseLocks();
                    throw new \Exception($e->getMessage());
                }
            }
        }

        // If we reach this place, then we found no recipients.
        $this->logInfo('No recipients found. Retry in 5 minutes.');

        return 300;
    }

    protected function setLocks()
    {
        if (! is_null($this->warmupSchedule)) {
            $this->warmupSchedule->update(['queued' => true]);
        }
    }

    protected function releaseLocks($successful = false)
    {
        $this->logInfo('Release locks');

        if (! is_null($this->warmupSchedule)) {
            if ($successful) {
                $this->warmupSchedule->last_sent_at = now();
                $this->warmupSchedule->amount_sent++;
                $this->warmupSchedule->queued = false;
                $this->warmupSchedule->save();
            } else {
                $this->warmupSchedule->update(['queued' => false]);
            }
        }
    }

    protected function incrementSentStats($isReply = false, $message = null)
    {
        WarmupStat::where('email_account_id', $this->emailAccount->id)->increment('sent');

        // increment daily stats
        $dayStr = now()->format('Y-m-d');
        $accountWarmupDailyStats = $this->emailAccount->warmupDailyStats()->where('day', $dayStr)->first();

        if (!empty($accountWarmupDailyStats)) {
            if ($isReply) {
                $accountWarmupDailyStats->sent++;
                $accountWarmupDailyStats->replied++;

                // if only 2 messages in the thread then conversation has started by replyer
                $threadMsgs = WarmupMessage::where('warmup_thread_id', $message->warmup_thread_id)->count();
                if($threadMsgs == 2) {
                    $accountWarmupDailyStats->conversation_started++;
                    WarmupStat::where('email_account_id', $this->emailAccount->id)->increment('conversation_started');
                }

                $accountWarmupDailyStats->save();
            } else {
                $accountWarmupDailyStats->sent++;
                $accountWarmupDailyStats->threads_started++;
                $accountWarmupDailyStats->save();
            }
        } else {
            WarmupDailyStats::create([
                'agency_id' => $this->emailAccount->agency_id,
                'email_account_id' => $this->emailAccount->id,
                'day' => $dayStr,
                'sent' => 1,
                'threads_started' => 1,
            ]);
        }
    }
}
