<?php

namespace App\Jobs\Linkedin;

use App\Exceptions\LinkedinPuppeteerBrowser\HttpException;
use App\Exceptions\LinkedinPuppeteerBrowser\ProxyException;
use App\Jobs\EmailFinder;
use App\LinkedinProfile;
use App\LinkedinSearchStats;
use App\Services\LinkedinPuppeteerBrowserService;
use App\Traits\LogsMessages;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Nesk\Rialto\Data\JsFunction;
use Throwable;

/**
 * Scrapes company data for a Linkedin profile
 *
 * Currently, used for on-demand profile scrape action
 * Updates profile company data
 *
 * Dispatches EmailFinder job
 */
class ScrapeLinkedinProfile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsMessages;

    public $tries = 3;

    private $linkedinProfile;
    private $linkedinSearch;
    private $linkedinAccount;
    private $company;
    private $page;
    private $browserService;
    private $proxy = 'unblocker';
    private $testMode = false;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(LinkedinProfile $profile)
    {
        $this->linkedinProfile = $profile;
        $this->linkedinSearch = $this->linkedinProfile->linkedinSearch;
        $this->linkedinAccount = $this->linkedinSearch->linkedinAccount;
        $this->logChannel = 'linkedin';
        $this->logPrefix = "LinkedinSearch-{$this->linkedinSearch->id}. ";
    }

    public function tags()
    {
        return [
            'linkedin',
            'linkedin-scrape-profile',
            'linkedin-scrape-profile-'.$this->linkedinProfile->id,
            'linkedin-account-'.$this->linkedinAccount->id,
            'linkedin-search-'.$this->linkedinSearch->id,
        ];
    }

    public function handle()
    {
        $this->logInfo('Starting ScrapeLinkedinProfile. LinkedinProfile-'.$this->linkedinProfile->id);
        $this->linkedinProfile->update([
            'status' => 'PROCESSING',
        ]);
        $this->company = $this->linkedinProfile->contact->company;

        // check emoji name
        $this->checkNameForEmoji();

        if ($this->isCompanyScrapeNeeded()) {
            $this->logInfo('Scraping company for LinkedinProfile-'.$this->linkedinProfile->id);

            try {
                // We need to launch a new browser.
                $this->logInfo('Launching browser...');
                $this->browserService = new LinkedinPuppeteerBrowserService($this->linkedinAccount, $this->proxy, $this->testMode);
                $requestInterceptor = JsFunction::createWithParameters(['request'])
                    ->body("
                    if(request.resourceType() == 'font' || request.resourceType() == 'stylesheet'){
                        request.abort();
                    }
                    else {
                        request.continue();
                    }
                ");

                $this->page = $this->browserService->launchBrowser()
                    ->intercept('request', $requestInterceptor)
                    ->getPage();

                $this->company->update($this->browserService->scrapeCompanyData($this->company));
                $this->company->refresh();
                LinkedinSearchStats::incrementScrapeCompanyPage($this->linkedinSearch);
                if ($this->linkedinProfile->contact->company_name != $this->company->name) {
                    $this->linkedinProfile->contact->update([
                        'company_name' => $this->company->name
                    ]);
                    $this->logInfo('Updated contact company name for LinkedinProfile-'.$this->linkedinProfile->id);
                }

            } catch (ProxyException $exception) {
                $this->logError('LinkedinProfile-'.$this->linkedinProfile->id.'. '.$exception->getMessage());
                $this->linkedinProfile->update([
                    'status' => 'PROXY_ERROR',
                    'job_id' => null,
                ]);

            } catch (HttpException $exception) {
                $this->logError('LinkedinProfile-'.$this->linkedinProfile->id. '. '.$exception->getMessage());
                $this->linkedinProfile->update([
                    'status' => 'ERROR',
                    'job_id' => null,
                ]);
                LinkedinSearchStats::incrementScrapeCompanyPageError($this->linkedinSearch);

            } catch (\Throwable $exception) {
                // Propagate unhandled error to retry job.
                $this->logError('LinkedinProfile-'.$this->linkedinProfile->id.'. Unhandled exception. '.$exception->getMessage());
                LinkedinSearchStats::incrementScrapeCompanyPageError($this->linkedinSearch);
                throw($exception);
            }
        }

        // $this->page->waitForTimeout(1000);
        if(!empty($this->browserService)) {
            $this->browserService->closeBrowser();
        }

        $this->linkedinProfile->refresh();
        if ($this->linkedinSearch->agency->wavo_version == 3) {
            $emailApiCount = $this->linkedinSearch->agency->emailSearchIntegrations()->orderBy('order')->count();
        } else {
            $emailApiCount = 1;
        }

        if ($this->linkedinProfile->status == 'PROCESSING' && $emailApiCount) {
            // Update status to EMAIL_SEARCH and dispatch EmailFinder.
            $this->linkedinProfile->update([
                'status' => 'EMAIL_SEARCH',
                'job_id' => null,
            ]);
            $this->logInfo('Dispatch email finder for LinkedinProfile-'.$this->linkedinProfile->id);
            EmailFinder::dispatch($this->linkedinProfile)->onQueue('emailfinder')->delay(now()->addSeconds(5));
        } else {
            $this->linkedinProfile->update([ 'status' => 'DONE_NO_EMAILSEARCH' ]);
            $this->logInfo('No email finder for liprofile-'.$this->linkedinProfile->id);
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(Throwable $exception)
    {
        if ($this->linkedinProfile) {
            $this->linkedinProfile->update([ 'status' => 'ERROR' ]);
            $this->logError('Profile Error - LinkedinProfile-:'.$this->linkedinProfile->id. '. '.$exception->getMessage());
        }
    }

    /**
     * check malformed name format
     * ex. emoji
     *
     * @return void
     */
    protected function checkNameForEmoji()
    {
        $nameHasEmoji = preg_match(config('constants.emoji_regexp'), $this->linkedinProfile->fullName);

        if($nameHasEmoji) {
            $errors = [
                'name_warning' => 'Name has emoji',
            ];

            $this->linkedinProfile->update([
                'errors' => $errors
            ]);
        }
    }

    /**
     * We may not need to scrape the company if:
     * We have no company OR
     * the company has no valid linkedin slug
     *
     * @return bool
     */
    protected function isCompanyScrapeNeeded() {
        if (! $this->company || ! $this->company->linkedin_slug) {
            $this->logInfo('Profile skip, no company - LinkedinProfile-'.$this->linkedinProfile->id);

            return false;
        }

        if ($this->company->hasRecentScrapedData()) {
            LinkedinSearchStats::incrementCachedCompanyPage($this->linkedinSearch);
            $this->logInfo('Profile skip, company has fresh data - LinkedinProfile-:'.$this->linkedinProfile->id);

            return false;
        }

        return true;
    }
}
