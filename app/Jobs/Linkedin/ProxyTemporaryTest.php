<?php

namespace App\Jobs\Linkedin;

use App\LinkedinAccount;
use App\LinkedinAccountType;
use Facebook\WebDriver\Chrome\ChromeOptions;
use Facebook\WebDriver\Remote\DesiredCapabilities;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Exception\WebDriverException;
use Facebook\WebDriver\WebDriverKeys;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Nesk\Puphpeteer\Puppeteer;
use Nesk\Rialto\Data\JsFunction;
use Nesk\Rialto\Exceptions\Node;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use App\Proxy;

class ProxyTemporaryTest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $proxyHash;
    private $proxy = null;
    public $tries = 1;

    private $page;
    private $browserService;
    private $logger = false;

    /**
     * Create a new job instance.
     *
     * @param \App\LinkedinAccount $linkedinAccount
     */
    public function __construct($proxyHash = null, $logger = false)
    {
        $this->proxyHash = $proxyHash;
        $this->logger = $logger;
    }

    /**
     * Execute the job thru puphpeteer.
     *
     * @return void
     */
    public function handle()
    {
        // look for proxy
        if ($this->proxyHash) {
            $proxyId = decodeModelHashid(Proxy::class, $this->proxyHash);
            $this->proxy = Proxy::find($proxyId);
        }

        $options = [
            'read_timeout' => 180,
            'idle_timeout' => 180,
        ];

        if ($this->logger) {
            $options['logger'] = Log::channel('puppeteer')->getLogger();
            // $options['log_node_console'] = true; // Enabling breaks li search as logger crashes (array to string conversion when saving)
            $options['log_browser_console'] = true;
        }

        $browserArgs = [
            // '--proxy-server=**************:8080', // WAN DNS1
            // '--proxy-server=************:8080', // WAN IP
            // '--proxy-server=**************:8080', // internal IP
            // '--proxy-server=*************:8080', // public IP
            
            // '--proxy-server=**************:9090', // proxyman toyo
            // '--proxy-server=127.0.0.1:9090', // proxyman local toyo
            // '--proxy-server=**************:9090', // proxyman dash

            // '--proxy-server=**************:56643', // PIA proxy IP+PORT
            // '--proxy-server=*************:56643', // PIA proxy IP+PORT
            // '--proxy-server=************:40711', // PIA proxy IP+PORT

            // '--proxy-server=zproxy.lum-superproxy.io:22225', // brightdata
            // '--proxy-server='.config('app.luminati.proxy_url'),

            '--no-sandbox',
            '--disable-setuid-sandbox',
            // '--disable-extensions',
            // '--no-first-run',
            // '--single-process',
            // '--no-zygote',
            // '--disable-gpu'
        ];

        if($this->proxy) {
            $browserArgs[] = '--proxy-server='.$this->proxy->url;
            Log::channel('linkedin')->info("ProxyTemporaryTest proxy url: {$this->proxy->url}");
        } else {
            Log::channel('linkedin')->info("ProxyTemporaryTest no proxy url");
        }

        $puppeteer = new Puppeteer($options);
        $browser = $puppeteer->launch([
            'headless' => false,
            'ignoreHTTPSErrors' => true,
            'args' => $browserArgs,
            "devtools" => true
        ]);

        $this->page = $browser->newPage();

        $this->page->setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36');

        if($this->proxy && $this->proxy->username) {
            $this->page->authenticate([
                'username' => $this->proxy->username,
                'password' => $this->proxy->password
            ]);

            Log::channel('linkedin')->info("ProxyTemporaryTest liaccount proxy authenticate-: {$this->proxy->username}/{$this->proxy->password}");
        } else {
            Log::channel('linkedin')->info("ProxyTemporaryTest liaccount proxy no username/password");
        }

        // $this->testBrightDataIps();
        // $this->testFacebookTagline();
        if($this->logger) {
            $this->testLogger();
        }

        $browser->close();
    }

    public function testBrightDataIps()
    {
        for ($i=0; $i < 10; $i++) {
            $this->page->goto('http://lumtest.com/myip.json');
            $this->page->waitForTimeout(2000);
        }
    }

    // test puppeteer's basic functionalities like navigation and querying elements
    public function testFacebookTagline()
    {
        $startTime = now();

        $this->page->goto('https://facebook.com', ['timeout'=>90000]);
        $this->page->waitForTimeout(3000);
        $fbTagline = '';

        $this->page->evaluate(JsFunction::createWithBody("
            window.scrollTo({top: document.body.scrollHeight, behavior: 'smooth'})
        "));

        // count tags
        try {
            $fbH2Tags = 'h2';
            $this->page->tryCatch->waitForSelector($fbH2Tags);
            $listH2Tags = $this->page->querySelectorAll($fbH2Tags);

            $resultH2Count = @count($listH2Tags) ? @count($listH2Tags) : 0;
            Log::channel('linkedin')->info("ProxyTemporaryTest resultH2Count: ".$resultH2Count);
        } catch (Node\Exception $exception) {
            Log::channel('linkedin')->error("ProxyTemporaryTest Facebook test err: {$exception->getMessage()}");
        }

        // testing empty rooms
        try {
            $noResults = $this->page->querySelectorAll('.empty-room');

            if(!@count($noResults)) {
                Log::channel('linkedin')->info("ProxyTemporaryTest no empty room");
            }
        } catch (Node\Exception $exception) {
            Log::channel('linkedin')->error("ProxyTemporaryTest Facebook test err: {$exception->getMessage()}");
        }

        try {
            $fbTagline = $this->page->tryCatch->querySelectorEval('h2',
                JsFunction::createWithParameters(['node'])->body('return node.textContent.trim();')
            );

            Log::channel('linkedin')->info("ProxyTemporaryTest Facebook tagline: {$fbTagline}");
        } catch (Node\Exception $exception) {
            Log::channel('linkedin')->error("ProxyTemporaryTest Facebook test err: {$exception->getMessage()}");
        }

        $endTime = now();
        
        Log::channel('linkedin')->info("ProxyTemporaryTest done: ".$endTime->diffInSeconds($startTime));
    }

    public function useLogger()
    {
        Log::channel('linkedin')->info("ProxyTemporaryTest setting up logger");

        $requestInterceptor = JsFunction::createWithParameters(['request'])
            ->body("request.continue();");

        $logUserInfo = JsFunction::createWithParameters(['response'])
            ->body('if (response.url() && response.url().includes("member/userInfo")) {
                        try {
                            let str = await response.text();
                            let j = JSON.parse(str);
                            console.log(JSON.stringify({
                                loginId: j.loginId ? j.loginId : null,
                                agree: j.agree ? j.agree : null,
                                signup: j.signup ? j.signup : null,
                                nickname: j.nickname ? j.nickname : null,
                                challengeAuthor: j.challengeAuthor ? j.challengeAuthor : null,
                                canAccessAdMenu: j.canAccessAdMenu ? j.canAccessAdMenu : null,
                                source: "userInfo"
                            }));
                        } catch (error) {}
                    }')
            ->async(true);

        $logMockedCompanyTest = JsFunction::createWithParameters(['response'])
            ->body('if (response.url() && response.url().includes("en/notice")) {
                        try {
                            console.log(JSON.stringify({
                                "entityUrn": "urn:li:fs_salesCompany:10967667",
                                "industry": "Wellness and Fitness Services",
                                "website": "http://www.none.com",
                                "flagshipCompanyUrl": "https://www.linkedin.com/company/4v4n7714/",
                                "companyPictureDisplayImage": {
                                    "artifacts": [
                                      {
                                        "width": 200,
                                        "fileIdentifyingUrlPathSegment": "200_200/0/1631368152331/4v4n7714_logo?e=1709769600&v=beta&t=9SaQuwsOYF9vCPMuPNrtJgXPAlJEWxWihumYlXwpOww",
                                        "height": 200
                                      },
                                      {
                                        "width": 100,
                                        "fileIdentifyingUrlPathSegment": "100_100/0/1631368152332/4v4n7714_logo?e=1709769600&v=beta&t=1REgqRK2KKDiK0I5d-asb3aJi2V0BxSTRJ093kF6jiA",
                                        "height": 100
                                      },
                                      {
                                        "width": 400,
                                        "fileIdentifyingUrlPathSegment": "400_400/0/1631368152332/4v4n7714_logo?e=1709769600&v=beta&t=R9fNNOTPKnA1mvAhXvGqzjtYKdRhuijZsPQbvjYxGtk",
                                        "height": 400
                                      }
                                    ],
                                    "rootUrl": "https://media.licdn.com/dms/image/C4E0BAQFX7yGUd5aNYQ/company-logo_"
                                },
                                source: "salesApiCompanies"
                            }));
                        } catch (error) {}
                    }')
            ->async(true);

        $logMockedProfileTest = JsFunction::createWithParameters(['response'])
            ->body('if (response.url() && response.url().includes("en/getFavoriteGenreTitleList")) {
                        try {
                            for(i=0; i<5; i++) {
                                console.log(JSON.stringify({
                                    entityUrn: "li:fs_salesProfile:("+Math.random()+"_ACwAAAAo3PMBlSsmYs5YyfV4JvcGEr6FIhQ1OXI,NAME_SEARCH,1fu3)",
                                    summary: "Website and Portfolio: http://www.romandesign.ca\\n\\nWhat makes me unique? An unusually broad spectrum of skills\\n",
                                    tenureDescription: "logger_test_tenuredesc",
                                    tenureCompany: null,
                                    tenurePosition: null,
                                    source: "salesApiLeadSearch"
                                }));
                            }
                        } catch (error) {}
                    }')
            ->async(true);

        $logMockedProfileTest2 = JsFunction::createWithParameters(['response'])
            ->body('if (response.url() && response.url().includes("lead")) {
                    try {
                        let str = await response.text();
                        let j = JSON.parse(str);

                        if(j.elements && j.elements?.length) {
                            j.elements?.forEach((k)=>{
                                let position = k?.currentPositions?.[0] ? k?.currentPositions?.[0] : null

                                console.log(JSON.stringify({
                                    entityUrn:k.entityUrn,
                                    summary:k.summary ? k.summary : null,
                                    tenureDescription: position?.description ? position?.description : null,
                                    tenureCompany: position?.tenureAtCompany ? position?.tenureAtCompany : null,
                                    tenurePosition: position?.tenureAtPosition ? position?.tenureAtPosition : null,
                                    source: "salesApiLeadSearch"
                                }));
                            })
                        }
                    } catch (error) {}
                }')
            ->async(true);

        $this->page->setRequestInterception(true);
        $this->page->on('request', $requestInterceptor);
        // $this->page->on('response', $logUserInfo);
        $this->page->on('response', $logMockedCompanyTest);
        $this->page->on('response', $logMockedProfileTest2);

        Log::channel('linkedin')->info("ProxyTemporaryTest logger added");
    }

    public function testLogger()
    {
        $this->useLogger();

        Log::channel('linkedin')->info("ProxyTemporaryTest visiting webtoons.com");

        // $this->page->goto('https://www.webtoons.com/en', ['timeout'=>90000]);
        $this->page->goto('http://leadjson.test', ['timeout'=>90000]);
        $this->page->waitForTimeout(5000);

        Log::channel('linkedin')->info("ProxyTemporaryTest visited webtoons.com");
    }


}
