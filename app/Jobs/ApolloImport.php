<?php

namespace App\Jobs;

use App\Models\Apollo\Person;
use App\Models\StoreLeads\ImportBatch;
use App\Traits\LogsMessages;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ApolloImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsMessages;

    public $timeout = 300;
    public $backoff = 30;
    public $tries = 5;


    public $filename;
    public $start;
    public $batchId;

    protected const JOB_BATCH_SIZE = 20000;

    public function __construct($filename, $start = 0, $batchId = 0)
    {
        $this->filename = $filename;
        $this->batchId = $batchId;
        $this->start = $start;

        $this->logChannel = config('logging.default');
        $this->logPrefix = "AP-Import-$batchId-$start-";
    }

    public function tags()
    {
        return ['apollo', 'ap-import-file-'.$this->filename, 'ap-import-batch-'.$this->batchId];
    }

    public function middleware()
    {
        return [
            (new WithoutOverlapping("ap-import-batch-{$this->batchId}"))
                ->dontRelease()
                ->expireAfter(330),
        ];
    }

    public function handle()
    {
        $this->logInfo("Starting Apollo import for $this->filename, batch $this->batchId, starting from row $this->start");

        if (empty($this->filename)) {
            $this->fail('No filename given');
        }

        // Insert or update ImportBatch with batch_id = $this->batchId
        $importBatch = ImportBatch::on('mysql::write')->updateOrCreate(
            ['batch_id' => $this->batchId],
            [
                'start_row' => $this->start,
                'next_start_row' => null,
                'is_complete' => false,
                'job_started_at' => now(),
                'job_ended_at' => null
            ]
        );

        $filePath = storage_path("/app/sl-export/{$this->filename}");
        $dataFile = new \SplFileObject($filePath, 'r');

        if (!$dataFile) {
            $this->fail('Failed to open the file');
        }

        try {
            $dataFile->seek($this->start);
        } catch (\Throwable $e) {
            $this->logError('Failed to seek to the start position');
            throw $e;
        }
        $this->logInfo("Skipped to line: $this->start");

        if ($dataFile->eof()) {
            $this->logInfo('End of file reached. Import Complete!'.' batch-id: '.$this->batchId. ', file: '.$this->filename);
            $dataFile = null;
            $importBatch->update(['is_complete' => true, 'job_ended_at' => now()]);

            return;
        }

        $i=0;

        while (!$dataFile->eof() && $i <= self::JOB_BATCH_SIZE) {
            // Every 1000 records, log the progress
            if ($i % 1000 == 0 && $i > 0) {
                $this->logInfo("Processed $i records");
            }

            $line = $dataFile->fgets();
            if ($line === false) {
                break;
            }

            if (empty($line)) {
                $i++;
                $dataFile->next();
                continue;
            }

//            $this->logInfo("Processing line: $i");

            // Process the line here
            // get fields from tab delimited line
            $line = trim($line); // Remove any whitespace/newlines at start/end
            if (substr($line, 0, 1) === '"' && substr($line, -1) === '"') {
                $line = substr($line, 1, -1); // Remove surrounding quotes
            }
            $fields = explode("\t", rtrim($line, "\n"));


            try {
                $email = $fields[9];
                if (empty($email)) {
                    $i++;
                    $dataFile->next();
                    continue;
                }

                $fullName = $fields[0];
                $firstName = ucfirst($fields[1]);
                $lastName = ucfirst($fields[2]);
                $title = $fields[4];
                $seniority = $fields[6];
                $emailStatus = $fields[7]; // 'Extrapolated', 'Unavailable', 'Verified'
                $emailConfidence = $fields[8]; // if extrapolated percentage (max=1), else empty
                $phone = $fields[10];
                $phoneSanitized = $fields[11];
                if (!empty($phoneSanitized)) {
                    $phone = $phoneSanitized;
                }
//                $emailAnalyzed = $fields[12];

                // extract domain from email
                $domain = explode('@', $email)[1];
                $linkedin = $fields[13];
                $organizationName = $fields[17];

                // fields 18-23 are location fields
                //    "person_location_city",
                //    "person_location_city_with_state_or_country",
                //    "person_location_state",
                //    "person_location_state_with_country",
                //    "person_location_country",
                //    "person_location_postal_code",
                $city = $fields[18];
                $state = $fields[20];
                $country = $fields[22];
                $postalCode = $fields[23];

                $organizationIds = $fields[25]; // "['54a1b1497468695c827d6908'"," '55e8364ef3e5bb4a32000281'"," '54a1a0097468695860f8e502']"
                // get first organization id from array of ids
                // Remove the array syntax and split
                $str = str_replace('","', ',', $organizationIds); // replace quoted comma with comma
                $str = trim($str, "[]"); // Remove brackets
                $str = str_replace("'", "", $str); // Remove quotes
                $ids = explode(",", $str); // Split by comma
                // Get first ID and trim whitespace
                $organizationId = trim($ids[0]);

                // Insert record
                Person::firstOrCreate(
                    [
                        'email' => $email
                    ],
                    [
                        'domain' => $domain,
                        'email_status' => $emailStatus ?? null,
                        'email_confidence' => !empty($emailConfidence) ? $emailConfidence*100 : null,
                        'full_name' => $fullName ?: null,
                        'first_name' => $firstName ?: null,
                        'last_name' => $lastName ?: null,
                        'title' => $title ?: null,
                        'seniority' => $seniority,
                        'phone' => $phone ?: null,
                        'linkedin' => $linkedin ?: null,
                        'company' => $organizationName ?: null,
                        'city' => $city ?: null,
                        'state' => $state ?: null,
                        'country' => $country ?: null,
                        'organization_id' => $organizationId,
                    ]);
            } catch (\Throwable $e) {
                $lineNumber = $this->start + $i;
                $this->logError("Failed to process batch {$importBatch->batch_id} line $lineNumber: $line");
                $this->logError($e->getMessage());
            }

            $i++;
            $dataFile->next();
        }

        $this->logInfo("Processed $i records");

        $nextRow = $this->start + $i - 1;
        $this->logInfo('Dispatch self to start at line: ' . $nextRow . ', batch-id: ' . $this->batchId . ', file: ' . $this->filename);
        $importBatch->update([
            'next_start_row' => $nextRow,
            'job_ended_at' => now()
        ]);

        if ($dataFile->eof()) {
            $this->logInfo('End of file reached. Import Complete!'.' batch-id: '.$this->batchId. ', file: '.$this->filename);
            $dataFile = null;
            $importBatch->update(['is_complete' => true, 'job_ended_at' => now()]);

            $nextBatch = ImportBatch::on('mysql::write')
                ->where('is_complete', false)
                ->where('batch_id', '>', $this->batchId)
                ->first();
            // if current filename was 'ap_per_data-0.tsv' and next batch is not null, dispatch next batch for file 'ap_per_data-1.tsv'
            if ($nextBatch) {
                $nextFile = str_replace("-$this->batchId.tsv", "-$nextBatch->batch_id.tsv", $this->filename);
                self::dispatch($nextFile, 0, $nextBatch->batch_id)
                    ->onQueue('browser')
                    ->delay(now()->addSeconds(15));
            }

            return;
        }

        $dataFile = null;
        gc_collect_cycles();

        self::dispatch($this->filename, $nextRow, $this->batchId)
            ->onQueue('browser')
            ->delay(now()->addSeconds(5));
    }
}
