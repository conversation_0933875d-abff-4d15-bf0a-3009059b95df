<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\LogsMessages;

use GuzzleHttp\Client as HttpClient;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ConnectException;
use Exception;

use App\ProspectActivity;
use App\Prospect;
use App\Team;
use App\Campaign;
use Hashids;
use Illuminate\Support\Str;

class SendWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsMessages;

    public $tries = 6;
    public $timeout = 300;
    public $backoff = 45;

    protected $prospect;
    public $team;
    protected $trigger;
    protected $hooks;
    protected $errMsg;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Prospect $prospect, $trigger = null)
    {
        $this->prospect = $prospect;
        $this->trigger = strtolower($trigger);

        $this->logPrefix = "SendWebhook {$this->prospect->team_id}: ";
    }

    public function tags()
    {
        return [
            'webhook', 'webhook-'.$this->trigger,
            'webhook-prospect-'.$this->prospect->id,
            'webhook-team-'.$this->prospect->team_id
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->logInfo(" start: {$this->trigger} - {$this->prospect->email} ({$this->prospect->full_name})");
        $this->prospect->load(['campaign:id,name', 'team:id,name,webhooks,webhook_errors']);
        $this->team = $this->prospect->team;
        $this->hooks = $this->prospect->team->webhooks;
        $hookErrors = $this->team->webhook_errors;
        $positiveErr = $hookErrors && isset($hookErrors['positive']) ? $hookErrors['positive'] : null;
        $repliedErr = $hookErrors && isset($hookErrors['replied']) ? $hookErrors['replied'] : null;

        if(empty($this->hooks) || !isset($this->hooks[$this->trigger])) {
            $this->logInfo("{$this->trigger} webhook not configured");
            return;
        }

        if($this->trigger == 'replied' && isset($this->hooks['replied'])) {
            if($repliedErr) {
                $this->logInfo("{$this->team->id}: {$this->trigger} webhook has error - {$repliedErr}");
            } else {
                $webhookData = $this->getTriggerDataFromActivity('ProspectReplied', 'contact_replied');
                if (!empty($webhookData)) {
                    $this->sendWebhook($this->hooks['replied'], $webhookData);
                }
            }
        }

        if($this->trigger == 'positive' && isset($this->hooks['positive'])) {
            if($positiveErr) {
                $this->logInfo("{$this->team->id}: {$this->trigger} webhook has error - {$positiveErr}");
            } else {
                $webhookData = $this->getTriggerInterestFromActivity('POSITIVE', 'contact_positive');
                if (!empty($webhookData)) {
                    $this->sendWebhook($this->hooks['positive'], $webhookData);
                }
            }
        }

        if($this->errMsg) {
            throw new Exception($this->errMsg);
        }
    }

    public function sendWebhook($hookUrl, $webhookData)
    {
        $this->logInfo("sending {$this->trigger} webhook to {$this->prospect->email}");

        try {
            $http = new HttpClient();
            $response = $http->post($hookUrl, [
                'headers' => [],
                'json' => $webhookData
            ]);

            $status = $response->getStatusCode();
        } catch (ConnectException $e) {
            $this->errMsg = "Could not resolve host: {$hookUrl}";
            $this->logError(json_encode($e->getMessage(), JSON_INVALID_UTF8_IGNORE));
        } catch (RequestException $e) {
            $status = $e->getResponse()->getStatusCode();

            $this->errMsg = "failed with status {$status}";
            $this->logError(json_encode($e->getMessage(), JSON_INVALID_UTF8_IGNORE));
        }
    }

    public function getTriggerInterestFromActivity($trigger, $event)
    {
        $activity = ProspectActivity::where('prospect_id', $this->prospect->id)
            ->where('activity', 'updated prospect interest to '.$trigger)
            // ->with(['emailMessage:id,email_account_id,campaign_id,prospect_id'])
            ->orderBy('created_at', 'desc')
            ->first();

        if(empty($activity)) {
            $this->errMsg = "No activity for trigger $trigger";
            $this->logError("SendWebhook Could not find activity:$trigger for prospect-{$this->prospect->id} {$this->prospect->email}");
            return [];
        }

        return [
            'webhook_event' => $event,
            'id' => $activity->id,
            'date' => $activity->activity_date,
            'contact_firstname' => $this->prospect->first_name,
            'contact_lastname' => $this->prospect->last_name,
            'contact_name' => $this->prospect->full_name,
            'contact_email' => $this->prospect->email,
            'contact_id' => $this->prospect->hashid,
            'campaign_name' => $this->prospect->campaign->name,
            'campaign_id' => Hashids::connection(Campaign::class)->encode($this->prospect->campaign_id),
            'team_id' => Hashids::connection(Team::class)->encode($this->prospect->team_id),
            'interest' => $this->prospect->interested,
            'company' => $this->prospect->company,
            'industry' => $this->prospect->industry,
            'website' => $this->prospect->website,
            'title' => $this->prospect->title,
            'phone' => $this->prospect->phone,
            'address' => $this->prospect->address,
            'city' => $this->prospect->city,
            'state' => $this->prospect->state,
            'country' => $this->prospect->country,
        ];
    }

    public function getTriggerDataFromActivity($trigger, $event)
    {
        $activity = ProspectActivity::where('prospect_id', $this->prospect->id)
            ->where('activity', 'like', $trigger)
            ->with(['emailMessage'=>function($q){
                $q->select(
                    'id', 'email_account_id', 'campaign_id', 'prospect_id',
                    'subject', 'message', 'submitted_at', 'snippet'
                );
            }])
            ->orderBy('id', 'desc')
            ->first();

        if(empty($activity)) {
            // $this->errMsg = "No activity for trigger $trigger";
            $this->logError("SendWebhook Could not find activity:$trigger for prospect-{$this->prospect->id}  {$this->prospect->email}");
            return [];
        }

        $trimmed_msg = preg_replace('!\s+!', ' ',
            str_replace("\n", ' ', strip_tags(data_get($activity->emailMessage, 'message', '')))
        );

        $data = [
            'webhook_event' => $event,
            'id' => $activity->id,
            'date' => $activity->activity_date,
            'contact_firstname' => $this->prospect->first_name,
            'contact_lastname' => $this->prospect->last_name,
            'contact_name' => $this->prospect->full_name,
            'contact_email' => $this->prospect->email,
            'contact_id' => $this->prospect->hashid,
            'campaign_name' => $this->prospect->campaign->name,
            'campaign_id' => Hashids::connection(Campaign::class)->encode($this->prospect->campaign_id),
            'team_id' => Hashids::connection(Team::class)->encode($this->prospect->team_id),
            'subject' => data_get($activity->emailMessage, 'subject'),
            'snippet' => data_get($activity->emailMessage, 'snippet'),
            'message_raw' => data_get($activity->emailMessage, 'message'),
            'message_body' => $trimmed_msg,
            'company' => $this->prospect->company,
            'industry' => $this->prospect->industry,
            'website' => $this->prospect->website,
            'title' => $this->prospect->title,
            'phone' => $this->prospect->phone,
            'address' => $this->prospect->address,
            'city' => $this->prospect->city,
            'state' => $this->prospect->state,
            'country' => $this->prospect->country,
        ];

        return $data;
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        $this->logError("Couldn't send webhook:".$this->trigger." for prospect:".$this->prospect->email);
        $this->logError($exception->getMessage());
        $error = $this->errMsg ?? $exception->getMessage();

        if (!Str::startsWith(strtolower($error), 'no activity')) {
            $this->logError("Dispatching SendWebhookErrorNotification for team:{$this->prospect->team_id}");
            SendWebhookErrorNotification::dispatch(
                $error,
                $this->trigger,
                $this->prospect->team_id
            );
        }
    }
}
