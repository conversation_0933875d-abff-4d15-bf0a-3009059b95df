<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/*
 * Blocked prospect email or domain
 */
class EmailSearchIntegrationLog extends Model
{
    protected $guarded = ['id'];

    protected $casts = [
        'query' => 'array',
        'response' => 'array',
    ];

    public function emailSearchIntegration()
    {
        return $this->belongsTo(EmailSearchIntegration::class);
    }

    public function linkedinProfile()
    {
        return $this->belongsTo(LinkedinProfile::class);
    }
}
