<?php

namespace App\Events\Subscription;

class SubscriptionCancelled
{
    /**
     * The user instance.
     *
     * @var \Illuminate\Contracts\Auth\Authenticatable|\Laravel\Cashier\Billable
     */
    public $user;

    /**
     * The subscription plan.
     *
     * @var \Laravel\Spark\Plan
     */
    public $plan;

    /**
     * Create a new event instance.
     *
     * @param \Illuminate\Contracts\Auth\Authenticatable|\Laravel\Cashier\Billable $user
     * @param \Laravel\Spark\Plan $plan
     * @return void
     */
    public function __construct($user, $plan)
    {
        $this->user = $user;
        $this->plan = $plan;
    }
}
