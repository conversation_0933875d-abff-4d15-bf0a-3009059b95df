<?php

namespace App;

use <PERSON><PERSON>\Cashier\Subscription as CashierSubscription;

class AgencySubscription extends CashierSubscription
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'agency_subscriptions';

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['provider_plan'];

    /**
     * Get the agency that owns the subscription.
     */
    public function agency()
    {
        return $this->user();
    }

    /**
     * Get the agency that owns the subscription.
     */
    public function user()
    {
        return $this->owner();
    }

    public function owner()
    {
        return $this->belongsTo(Agency::class, 'agency_id');
    }

    /**
     * Get the subscription items related to the subscription.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function items()
    {
        return $this->hasMany(AgencySubscriptionItem::class);
    }

    /**
     * Get the "provider_plan" attribute from the model.
     *
     * @return string
     */
    public function getProviderPlanAttribute()
    {
        if ($this->items()->count() == 0) {
            return $this->stripe_price;
        } elseif ($this->items()->count() == 1) {
            return $this->items()->first()->stripe_price;
        } else {
            return $this->items->first(function($val,$key) {
                return Spark::agencyPlans()->where('id', $val->stripe_price)->isNotEmpty();
            })->stripe_price;
        }
    }

    public function scopeHasAgencyDashboard($query)
    {
        return $query->where('stripe_price', 'agency-dashboard');
    }

    public function scopeHasUserDashboard($query)
    {
        return $query->where('stripe_price', '!=', 'agency-dashboard');
    }
}
