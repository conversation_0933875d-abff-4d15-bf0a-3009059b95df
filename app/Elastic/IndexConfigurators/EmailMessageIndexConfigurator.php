<?php

namespace App\Elastic\IndexConfigurators;

use ScoutElastic\Migratable;
use ScoutElastic\IndexConfigurator;

class EmailMessageIndexConfigurator extends IndexConfigurator
{
    use Migratable;

    protected $name = 'email_messages';

    /**
     * @var array
     */
    protected $settings = [
        'analysis' => [
            'analyzer' => [
                // Search for email urls in text (doesn't allow splitting of emails in words).
                'email_url_analyzer' => [
                    'type'      => 'custom',
                    'tokenizer' => 'uax_url_email',
                    'filter'    => ['lowercase', 'stop']
                ],
                // Ngram analyzer that first breaks text to words and then lowercases before creating ngram tokens.
                'ngram_analyzer' => [
                    'type'      => 'custom',
                    'tokenizer' => 'standard',
                    'filter'    => ['lowercase', 'ngram_filter']
                ],
                // Ngram analyzer that doesn't break text, but lowercases before creating ngram tokens.
                'ngram_keyword_analyzer' => [
                    'type'      => 'custom',
                    'tokenizer' => 'keyword',
                    'filter'    => ['lowercase', 'ngram_filter', 'trim']
                ],
                // Search in fields analyzed with ngram_keyword_analyzer (doesn't apply ngram to search string).
                'ngram_keyword_search_analyzer' => [
                    'type'      => 'custom',
                    'tokenizer' => 'keyword',
                    'filter'    => ['lowercase', 'trim']
                ],
                // Standard analyzer with lowercase and asciifolding.
                'text_ci_analyzer' => [
                    'type'      => 'custom',
                    'tokenizer' => 'standard',
                    'filter'    => ['lowercase', 'asciifolding']
                ]
            ],
            'normalizer' => [
                'keyword_ci_normalizer' => [
                    'type'        => 'custom',
                    'char_filter' => [],
                    'filter'      => ['lowercase', 'asciifolding']
                ]
            ],
            'filter' => [
                'ngram_filter' => [
                    'type'     => 'ngram',
                    'min_gram' => 3,
                    'max_gram' => 15
                ]
            ]
        ],
        "max_ngram_diff" => "20"
    ];

    public const MAPPING = [
        'properties' => [
            'from_name' => [
                'type' => 'text',
                'analyzer' => 'text_ci_analyzer',
            ],
            'to_name' => [
                'type' => 'text',
                'analyzer' => 'text_ci_analyzer',
            ],
            'from' => [
                'type' => 'keyword',
                'normalizer' => 'keyword_ci_normalizer',
                'fields' => [
                    'ngram' => [
                        'type' => 'text',
                        'analyzer' => 'ngram_keyword_analyzer',
                        'search_analyzer' => 'ngram_keyword_search_analyzer', // use ngram to mimic wildcard search
                    ]
                ]
            ],
            'to' => [
                'type' => 'keyword',
                'normalizer' => 'keyword_ci_normalizer',
                'fields' => [
                    'ngram' => [
                        'type' => 'text',
                        'analyzer' => 'ngram_keyword_analyzer',
                        'search_analyzer' => 'ngram_keyword_search_analyzer', // use ngram to mimic wildcard search
                    ]
                ]
            ],
            'subject' => [
                'type' => 'text',
                'analyzer' => 'text_ci_analyzer',
            ],
            'message' => [
                'type' => 'text',
                'analyzer' => 'text_ci_analyzer',
            ],
            'snippet' => [
                'type' => 'text',
                'analyzer' => 'text_ci_analyzer',
            ],
            'origin' => [
                'type' => 'keyword',
                'normalizer' => 'keyword_ci_normalizer'
            ],
            'agency_id' => [
                'type' => 'long'
            ],
            'campaign_id' => [
                'type' => 'long'
            ],
            'email_account_id' => [
                'type' => 'long'
            ],
            'email_template_id' => [
                'type' => 'long'
            ],
            'nylas_message_id' => [
                'type' => 'keyword'
            ],
            'nylas_thread_id' => [
                'type' => 'keyword'
            ]
        ]
    ];
}