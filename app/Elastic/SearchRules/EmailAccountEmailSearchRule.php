<?php

namespace App\Elastic\SearchRules;

use ScoutElastic\SearchRule;

class EmailAccountEmailSearchRule extends SearchRule
{
    /**
     * @inheritdoc
     */
    public function buildHighlightPayload()
    {
        //
    }

    /**
     * Search in all indexed fields for query word(s).
     * Returns results if any field has a match (should query).
     * If given more than one word, then all words must be present in the a field to be considered a hit (AND operator).
     *
     * @inheritdoc
     */
    public function buildQueryPayload()
    {
        return [
            'must' => [
                [
                    'query_string' => [
                        'fields' => ['email_address'],
                        'query' => "*{$this->builder->query}*",
                    ]
                ]
            ]
        ];
    }
}