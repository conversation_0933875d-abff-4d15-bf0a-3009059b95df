<?php

namespace App;

use App\Traits\Hashidable;
use App\Traits\HasTeamTenants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use ScoutElastic\Searchable;
use Illuminate\Database\Eloquent\Model;
use App\Elastic\SearchRules\EmailBlockSearchRule;
use App\Elastic\IndexConfigurators\EmailBlockIndexConfigurator;

/*
 * Blocked prospect email or domain
 */
class EmailBlock extends Model
{
    use Searchable, Hashidable, HasTeamTenants, HasFactory;

    protected $guarded = ['id'];

    protected $appends = ['hashid'];

    // Used to create the index in Elasticsearch.
    protected $indexConfigurator = EmailBlockIndexConfigurator::class;

    // Default elastic scout search rule.
    protected $searchRules = [EmailBlockSearchRule::class];

    // Field mapping for Elasticsearch.
    protected $mapping = EmailBlockIndexConfigurator::MAPPING;

    /**
     * Get the indexable data array for the model to be used by <PERSON><PERSON> Scout.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'address' => $this->address,
            'team_id' => $this->team_id,
            'type' => $this->type,
        ];
    }
    /**
     * The team that has this blocked correspondence.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Filter blocks by team id.
     *
     * @param mixed $query
     * @param mixed $intTeamId
     * @return mixed $query
     */
    public function scopeOfTeam($query, $intTeamId)
    {
        if($intTeamId)
        {
            return $query->where('team_id', $intTeamId);
        }
        else
        {
            return $query;
        }
    }

    /**
     * Filter blocks by array of team ids.
     *
     * @param mixed $query
     * @param mixed $intTeamId
     * @return mixed $query
     */
    public function scopeOfTeams($query, $arrTeamIds)
    {
        if(@count($arrTeamIds))
        {
            return $query->whereIn('team_id', $arrTeamIds);
        }
        else
        {
            return $query;
        }
    }

    /**
     * Filter blocks by type.
     *
     * @param mixed $query
     * @param mixed $intTeamId
     * @return mixed $query
     */
    public function scopeOfType($query, $strType)
    {
        if($strType)
        {
            return $query->where('type', $strType);
        }
        else
        {
            return $query;
        }
    }

    /**
     * Filter blocks by search keywords.
     *
     * @param  mixed  $query
     * @param  mixed  $strKeywords
     * @param  array  $filters
     * @return mixed $query
     */
    public function scopeOfKeywords($query, $strKeywords, $filters = [])
    {
        if ($strKeywords) {

            if (config('scout.search')) {

                $scoutQuery = $this->search($strKeywords);

                foreach ($filters as $key => $value) {
                    if (!empty($value)) {
                        $scoutQuery = $scoutQuery->where($key, $value);
                    }
                }

                $size = $scoutQuery->count();

                return $query->whereIn('id', $scoutQuery->take($size)->keys());
            }

            return $query->where('address', 'like', '%'.$strKeywords.'%');
        }

        return $query;
    }
}
