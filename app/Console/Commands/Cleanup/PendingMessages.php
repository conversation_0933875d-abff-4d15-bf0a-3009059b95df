<?php

namespace App\Console\Commands\Cleanup;

use App\Jobs\Email\FixPendingMessages;
use Illuminate\Console\Command;

class PendingMessages extends Command
{
    protected $signature = 'cleanup:pending-messages';

    protected $description = 'Dispatch job to check and cleanup pending messages from EmailEngine';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->info('Fixing pending messages');
        FixPendingMessages::dispatch()->onQueue('api');
    }
}
