<?php

namespace App\Console\Commands\Cleanup;

use App\WarmupMessage;
use App\WarmupThread;
use Illuminate\Console\Command;
use App\Console\Concerns\LogsExecution;

class WarmupMessagesCommand extends Command
{
    use LogsExecution;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:warmup-messages';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old warmup messages after 60 days.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        WarmupMessage::where('created_at', '<', now()->subDays(60))->delete(); // Mass Delete
        WarmupThread::where('created_at', '<', now()->subDays(60))->whereDoesntHave('warmupMessages')->delete();
    }
}
