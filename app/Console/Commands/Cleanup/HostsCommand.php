<?php

namespace App\Console\Commands\Cleanup;

use App\Agency;
use App\Services\DomainValidatorService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class HostsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:hosts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove Unused Agency Domains';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Find agencies that are on trial or without active billing and use a custom domain (not wildcard).
        $agencies = Agency::where('is_billed', 1)->where('has_ingress_service', true)->where(function ($query) {
            $query->where('trial_ends_at', '>', now())
                ->orWhereNull('current_billing_plan');
        })->where('domain', 'not like', '%'.config('app.domain'))->get();

        $agencies->each(function ($agency) {
            $isCnameValid = true;
            try {
                $isCnameValid = app(DomainValidatorService::class, ['domain' => $agency->domain])->validate();
            } catch (\Exception $e) {
                Log::error('Error validating domain: '.$agency->domain.' - '.$e->getMessage());
                if ($e->getMessage() == 'Failed while getting DNS record') {
                    $isCnameValid = false;
                }
            }
            if (! $isCnameValid) {
                $this->info('Remove domain from ingress service: ' . $agency->domain);
                Log::info('Remove domain from ingress service: '. $agency->domain);
                $this->call('k8s:remove-host', ['host' => $agency->domain]);
                $agency->has_ingress_service = false;
                $agency->save();
            }
        });

        return 0;
    }
}
