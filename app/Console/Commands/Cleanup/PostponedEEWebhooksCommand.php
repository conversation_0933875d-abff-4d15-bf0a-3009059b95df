<?php

namespace App\Console\Commands\Cleanup;

use App\PostponedEEWebhook;
use Illuminate\Console\Command;

class PostponedEEWebhooksCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:postponed-ee-webhooks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup old webhook data that were saved for when an account reconnects';

    /**
     * Grace period for account to reconnect before we delete the webhook data.
     *
     * @var int
     */
    protected $webhookGracePeriodDays = 10;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        PostponedEEWebhook::where('created_at', '<', now()->subDays($this->webhookGracePeriodDays))->delete();
    }
}
