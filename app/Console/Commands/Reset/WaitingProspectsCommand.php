<?php

namespace App\Console\Commands\Reset;

use App\Jobs\Linkedin\RemoveLinkedinInvites;
use App\Jobs\Linkedin\UpdateLinkedinConnections;
use App\LinkedinAccount;
use App\Prospect;
use Illuminate\Console\Command;
use App\Console\Concerns\LogsExecution;
use Illuminate\Support\Facades\Bus;

class WaitingProspectsCommand extends Command
{
    use LogsExecution;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reset:waiting-prospects';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Prospects that have passed the wait_until of connect requests should be made available for other types of outreach.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $linkedinAccountIds = Prospect::ofLinkedInConnectStatus('PENDING')
            ->distinct('linkedin_account_id')
            ->where('wait_until', '<', now())
            ->pluck('linkedin_account_id');

        LinkedinAccount::whereIn('id', $linkedinAccountIds)->each(function ($linkedinAccount) {
            Bus::chain([
                new UpdateLinkedinConnections($linkedinAccount),
                new RemoveLinkedinInvites($linkedinAccount),
            ])->onQueue('browser')->dispatch();
        });

        $this->logExecution($this->signature);
    }
}
