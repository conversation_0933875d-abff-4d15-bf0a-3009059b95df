<?php

namespace App\Console\Commands\Reset;

use App\Schedule;
use Illuminate\Console\Command;

class DormantSchedulesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reset:dormant-schedules';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Wake up dormant schedules if passed their wake-up time.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $scheduleIds = Schedule::where('is_dormant', true)
            ->where('dormant_until', '<', now())
            ->pluck('id');

        Schedule::whereIn('id', $scheduleIds)
            ->update([
                'is_dormant' => false,
                'dormant_until' => null,
            ]);
    }
}
