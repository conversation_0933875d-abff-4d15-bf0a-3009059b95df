<?php

namespace App\Console\Commands;

use App\EmailAccount;
use App\Jobs\Email\EmailEngineMigrate as EmailEngineMigrateJob;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class EmailEngineMigrate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'emailengine:migrate {limit=10} {--dry-run} {--ignorePending} {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate email accounts from nylas to EmailEngine';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // get all active emailaccounts
        $emailAccounts = EmailAccount::active()
            ->ofNylas()
            ->when(!$this->option('force'), function(Builder $query) {
                return $query->whereHas('nylasAccount', function(Builder $q) {
                    $q->running();
                });
            })
            ->orderByDesc('authenticated_at')
            ->limit($this->argument('limit'))
            ->get();

        foreach ($emailAccounts as $emailAccount) {
            if (!$this->option('dry-run')) {
                EmailEngineMigrateJob::dispatch(
                    $emailAccount,
                    $this->option('ignorePending'),
                    $this->option('force')
                );
                $this->info('Migrating: ' . $emailAccount->email_address);
            } else {
                $this->info('Dry run: ' . $emailAccount->email_address);
            }
        }

        return 0;
    }
}
