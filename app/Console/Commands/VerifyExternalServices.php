<?php

namespace App\Console\Commands;

use App\EmailNotification;
use App\Mail\ErrorNotification;
use App\Models\ExternalService;
use App\Role;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Console\Concerns\LogsExecution;

class VerifyExternalServices extends Command
{
    use LogsExecution;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'verify:external-services';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify that critical APIs are online.';

    /**
     * The apis that we should verify as working.
     *
     * @var array
     */
    protected static $services = [
        'anymail_finder',
        'tomba',
        'serper',
        'reverse_contact',
//        'rapid_api',
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if (config('app.env') != 'production') {
            $this->info('This command is only available in production');

            return;
        }

        $inactiveServices = ExternalService::whereIn('name', self::$services)
            ->where('status', '!=', 'active')
            ->get();

        if ($inactiveServices->count() > 0) {
            $this->info('The following External Services have errors:');
            foreach ($inactiveServices as $service) {
                $this->info("$service->name: $service->error ($service->error_type) - $service->error_message");
            }
            $errorMessage = "The following External Services have errors:<br>";
            foreach ($inactiveServices as $service) {
                $errorMessage .= "$service->name: $service->error ($service->error_type) - $service->error_message<br>";
            }
            Mail::to(Role::where('name', 'admin')->first()->users)
                ->queue(new ErrorNotification($errorMessage, 'WAVO external Services have errors'));
            EmailNotification::create([
                'origin' => 'ErrorNotification',
                'subject' => 'WAVO external Services have errors',
                'recipients' => 'admins',
            ]);
        }

        $this->logExecution($this->signature);
    }
}
