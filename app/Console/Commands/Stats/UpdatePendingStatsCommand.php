<?php

namespace App\Console\Commands\Stats;

use App\Campaign;
use App\Jobs\UpdateCampaignStats;
use App\PendingStatsUpdate;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdatePendingStatsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stats:update-pending';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fire jobs to update pending campaign stats. Should be run every 5-10 minutes.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $campaignIds = DB::transaction(function () {

            $ids = PendingStatsUpdate::pluck('campaign_id');
            DB::delete('delete from pending_stats_updates');

            return $ids;

        }, 3);

        foreach ($campaignIds as $id) {
            UpdateCampaignStats::dispatch(Campaign::find($id))->onQueue('default');
        }
    }
}
