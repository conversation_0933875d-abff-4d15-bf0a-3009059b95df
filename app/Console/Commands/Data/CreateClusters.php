<?php

namespace App\Console\Commands\Data;

use App\Models\StoreLeads\Cluster;
use App\Models\StoreLeads\Domain;
use App\Models\StoreLeads\DomainCluster;
use Illuminate\Console\Command;

class CreateClusters extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:sl-create-clusters
        {--start-id=0}
        {--chunk-size=1000}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process domain clusters from domain data';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $startId = $this->option('start-id');
        $chunkSize = $this->option('chunk-size');
        $lastProcessedId = null;
        $totalProcessed = 0;

        $query = DomainCluster::where('is_best_ranked',1)->select('domain_id')->distinct()->orderBy('domain_id');

        if ($startId) {
            $query->where('domain_id', '>=', $startId);
        }

        $query->chunk($chunkSize, function ($domainClusters) use (&$lastProcessedId, &$totalProcessed) {
            foreach ($domainClusters as $domainCluster) {
                if (Cluster::where('domain_id', $domainCluster->domain_id)->doesntExist()) {
                    Cluster::create([
                        'domain_id' => $domainCluster->domain_id,
                        'domain_name' => Domain::find($domainCluster->domain_id)->name,
                        'total_domains' => DomainCluster::where('domain_id', $domainCluster->domain_id)->count() + 1,
                    ]);
                }

                $lastProcessedId = $domainCluster->domain_id;
                $totalProcessed++;
            }

            $this->info("Chunk complete - Last processed ID: {$lastProcessedId}, Total processed: {$totalProcessed}");
        });

        $this->info("Complete! Last ID: {$lastProcessedId}, Total: {$totalProcessed}");
    }
}
