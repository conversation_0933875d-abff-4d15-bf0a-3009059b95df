<?php

namespace App;

use App\Traits\Hashidable;
use Illuminate\Database\Eloquent\Model;

class EmailFreeze extends Model
{
    use Hashidable;

	protected $casts = [
        'start_at' => 'datetime',
        'end_at' => 'datetime',
    ];

	protected $guarded = ['id'];

	protected $appends = ['tz_start', 'tz_end', 'hashid'];

	/**
     * start_at was formatted into server's timezone before saving
     * so it's easier for the server to query freeze schedule,
     * now let's format it back to timezone that the user set when saving
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function getTzStartAttribute()
    {
        return $this->start_at->timezone($this->timezone)->format('M d, Y');
    }

    /**
     * end_at was formatted into server's timezone before saving
     * so it's easier for the server to query freeze schedule,
     * now let's format it back to timezone that the user set when saving
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function getTzEndAttribute()
    {
        return $this->end_at->timezone($this->timezone)->format('M d, Y');
    }

    /**
     * Email of this freeze schedule.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailAccount()
    {
        return $this->belongsTo(EmailAccount::class);
    }
}
