<?php
/**
 * Created by PhpStorm.
 * User: apanagiotou
 * Date: 22/1/2019
 * Time: 4:31 μμ
 */

namespace App;

use <PERSON><PERSON>\Cashier\Invoice as CashierInvoice;
use Symfony\Component\HttpFoundation\Response;

class Invoice extends CashierInvoice
{
    /**
     * Create an invoice download response.
     *
     * @param  array  $data
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function download(array $data = [])
    {
        $filename = $data['product'].'_'.$this->date()->month.'_'.$this->date()->year.'_'.$this->number.'.pdf';

        return new Response($this->pdf($data), 200, [
            'Content-Description' => 'File Transfer',
            'Content-Disposition' => 'attachment; filename="'.$filename.'"',
            'Content-Transfer-Encoding' => 'binary',
            'Content-Type' => 'application/pdf',
        ]);
    }
}
