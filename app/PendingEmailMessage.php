<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PendingEmailMessage extends Model
{
    protected $guarded = ['id'];

    public function prospect() {
        return $this->belongsTo(Prospect::class);
    }

    public function emailMessage() {
        return $this->belongsTo(EmailMessage::class);
    }

    public function user() {
        return $this->belongsTo(User::class);
    }
}
