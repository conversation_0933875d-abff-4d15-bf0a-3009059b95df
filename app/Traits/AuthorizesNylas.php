<?php

namespace App\Traits;

use App\Exceptions\NylasAuthorizationException;

trait AuthorizesNylas
{
    /**
     * Authorize a nylas webhook request.
     *
     * @param $request
     * @return bool
     * @throws NylasAuthorizationException
     */
    public function authorizeWebhook($request)
    {
        $signature = $request->header('X-Nylas-Signature');
        $validator = hash_hmac('sha256', $request->getContent(), config('app.nylas.appSecret'));

        if ($signature === $validator) {
            return true;
        } else {
            throw new NylasAuthorizationException('Webhook authorization failed.');
        }
    }
}