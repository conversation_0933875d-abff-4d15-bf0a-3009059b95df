<?php

namespace App\Repositories;

use App\EmailAccount;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class CacheableEmailAccount
{
    protected $cache;
    protected $lifetime;

    public function __construct()
    {
        $this->lifetime = config('cache.model.lifetime');
        $this->cache = Cache::store(config('cache.model.driver'))->tags('email_account');
    }

    //public function all()
    //{
    //    return $this->cache->remember("email_account:all", $this->lifetime, function () {
    //        return EmailAccount::all();
    //    });
    //}

    public function find($id)
    {
        return $this->cache->remember("email_account:$id", $this->lifetime, function () use ($id) {
            return EmailAccount::find($id);
        });
    }

    public function findOrFail($id)
    {
        $result = $this->find($id);

        if (! is_null($result)) {
            return $result;
        }

        throw (new ModelNotFoundException)->setModel(
            get_class(new EmailAccount), $id
        );
    }

    public function getScheduleVisibilty($id)
    {
        $result = $this->cache->tags(['email_account','email_engine_account','email_freeze','campaign','email_warmup'])
            ->remember("email_account:$id:schedule", $this->lifetime, function () use ($id) {
                return EmailAccount::with('emailEngineAccount', 'freezes', 'campaigns', 'emailWarmup')->where('id', $id)->first();
            });

        if (! is_null($result)) {
            return $result;
        }

        throw (new ModelNotFoundException)->setModel(
            get_class(new EmailAccount), $id
        );
    }

    public function getNylasAccountVisibility($id)
    {
        $result = $this->cache->tags(['email_account','nylas_account'])
            ->remember("email_account:$id:nylas_account", $this->lifetime, function () use ($id) {
                return EmailAccount::with('nylasAccount')->where('id', $id)->first();
            });

        if (! is_null($result)) {
            return $result;
        }

        throw (new ModelNotFoundException)->setModel(
            get_class(new EmailAccount), $id
        );
    }
}