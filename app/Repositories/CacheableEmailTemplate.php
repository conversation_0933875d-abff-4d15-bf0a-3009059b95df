<?php

namespace App\Repositories;

use App\EmailTemplate;
use Illuminate\Support\Facades\Cache;

class CacheableEmailTemplate
{
    protected $cache;
    protected $lifetime;

    public function __construct()
    {
        $this->lifetime = config('cache.model.lifetime');
        $this->cache = Cache::store(config('cache.model.driver'))->tags('email_template', 'campaign_stage');
    }

    public function find($id)
    {
        return $this->cache->remember("email_template:$id", $this->lifetime, function () use ($id) {
            return EmailTemplate::with('campaignStage')->where('id', $id)->first();
        });
    }
}
