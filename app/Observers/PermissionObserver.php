<?php

namespace App\Observers;

use App\Permission;

class PermissionObserver
{
    public function saved(Permission $permission)
    {
        flushModelCache($permission);
    }

    /**
     * Handle the permission "deleted" event.
     *
     * @param  \App\Permission  $permission
     * @return void
     */
    public function deleted(Permission $permission)
    {
        flushModelCache($permission);
    }

    /**
     * Handle the permission "restored" event.
     *
     * @param  \App\Permission  $permission
     * @return void
     */
    public function restored(Permission $permission)
    {
        flushModelCache($permission);
    }

    /**
     * Handle the permission "force deleted" event.
     *
     * @param  \App\Permission  $permission
     * @return void
     */
    public function forceDeleted(Permission $permission)
    {
        flushModelCache($permission);
    }
}
