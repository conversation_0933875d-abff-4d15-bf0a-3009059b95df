<?php

namespace App\Exports;

use App\Prospect;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;

class CsvErrorsExport implements FromCollection, WithHeadings
{
	use Exportable;

	public function __construct($colErrors, $arrHeaders)
    {
        $this->errors = $colErrors;
        $this->headers = $arrHeaders; 
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return $this->errors;
    }

    public function headings(): array
    {   
        return $this->headers;
    }
}
