<?php

namespace App\Exports;

use App\Prospect;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ProspectsExport implements FromQuery, WithHeadings, WithMapping
{
	use Exportable;

    public function __construct($type, $prospect_ids, $filters, $campaign, $snippets)
    {
        $this->campaign = $campaign;
        $this->filters = $filters;
        $this->prospect_ids = $prospect_ids;
        $this->type = $type;
        $this->snippets = $snippets;

        $this->defaultfields = [
            'email', 'first_name', 'last_name', 'emails_sent', 'completed_steps', 'last_contacted', 'company', 'industry', 'tags', 'website',
            'title', 'phone', 'address', 'city', 'state', 'country', 'status', 'interested', 'last_replied'
        ];
    }

    public function query()
    {
    	if($this->type == 'all') {
    		return Prospect::ofKeywords(data_get($this->filters, 'keywords', ''), ['campaign_id' => $this->campaign->id])
                ->ofStatus(data_get($this->filters, 'status', 'all'))
                ->ofInterest(data_get($this->filters, 'interested', 'all'))
                ->ofCampaign($this->campaign->id)
                ->ofImport(data_get($this->filters, 'import_file', 'all'))
                ->ofEmailsSent(data_get($this->filters, 'emails_sent', null))
                ->ofCompletedSteps(data_get($this->filters, 'completed_steps', null))
                ->select(Arr::prepend($this->defaultfields, 'merge_fields'))
                ->orderBy('email');
    	} else {
    		return Prospect::query()
    			->select(Arr::prepend($this->defaultfields, 'merge_fields'))
                ->whereIn('id', $this->prospect_ids)
                ->orderBy('email');
    	}

    }

    public function map($prospect): array
    {
        $last_contacted = $prospect->last_contacted ?
                            $prospect->last_contacted->tz($this->campaign->carbon_timezone)->format('M jS Y, g:iA') : '';

        $last_replied = $prospect->last_replied ?
                            $prospect->last_replied->tz($this->campaign->carbon_timezone)->format('M jS Y, g:iA') : '';

    	$arrFormattedData = [
            $prospect->email,
			$prospect->first_name,
			$prospect->last_name,
			$prospect->company,
			$prospect->industry,
			$prospect->tags,
			$prospect->website,
			$prospect->title,
			$prospect->phone,
			$prospect->address,
			$prospect->city,
			$prospect->state,
			$prospect->country,
        ];

        foreach ($this->snippets as $snippet) {
            $fieldValue = data_get($prospect->merge_fields, $snippet['name'], '');
            array_push($arrFormattedData, $fieldValue);

        	// array_push($arrFormattedData, $prospect->merge_fields[$snippet['name']]);
        }

        array_push($arrFormattedData, $prospect->interested);
        array_push($arrFormattedData, $prospect->status);
        array_push($arrFormattedData, (string)$prospect->emails_sent); // 0 is empty in xls so cast to string
        array_push($arrFormattedData, (string)$prospect->completed_steps); // 0 is empty in xls so cast to string
        array_push($arrFormattedData, $last_contacted);
        array_push($arrFormattedData, $last_replied);

        return $arrFormattedData;
    }

    public function headings(): array
    {
    	$headers = [];
        $fields = array_diff($this->defaultfields, ['last_contacted', 'emails_sent', 'completed_steps', 'status', 'interested', 'last_replied']);

        foreach ($fields as $field) {
            array_push($headers, ucwords(str_replace('_', ' ', $field)));
        }

    	foreach ($this->snippets as $snippet) {
        	array_push($headers, ucwords(str_replace('_', ' ', $snippet['name'])));
        }

        array_push($headers, 'Interested');
        array_push($headers, 'Status');
        array_push($headers, 'Emails Sent');
        array_push($headers, 'Steps Completed');
        array_push($headers, 'Last Contacted');
        array_push($headers, 'Last Replied');

        return $headers;
    }
}
