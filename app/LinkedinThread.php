<?php

namespace App;

use App\Traits\Hashidable;
use App\Traits\HasTags;
use Illuminate\Database\Eloquent\Model;

class LinkedinThread extends Model
{
    use Hashidable, HasTags;

    protected $guarded = ['id'];

    protected $appends = ['snippet', 'snippet_date', 'snippet_timestamp', 'hashid'];

    protected $with = ['linkedinMessages', 'campaign'];

    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    public function linkedinAccount()
    {
        return $this->belongsTo(LinkedinAccount::class);
    }

    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    public function prospect()
    {
        return $this->belongsTo(Prospect::class);
    }

    public function linkedinMessages()
    {
        return $this->hasMany(LinkedinMessage::class);
    }

    public function tags()
    {
        return $this->belongsToMany(Tag::class);
    }

    public function scopeOfProspect($query, $prospectId)
    {
        if ($prospectId) {
            return $query->where('prospect_id', $prospectId);
        }

        return $query;
    }

    public function scopeOfCampaign($query, $campaignId)
    {
        if ($campaignId) {
            return $query->where('campaign_id', $campaignId);
        }

        return $query;
    }

    /**
     * Get the latest message of a thread.
     *
     * @return \App\LinkedinMessage
     */
    public function getLatestMessage()
    {
        return $this->linkedinMessages->sortByDesc('submitted_at')->first();
    }

    /**
     * Get the message of latest email of a thread to display on thread list.
     *
     * @return string|null
     */
    public function getSnippetAttribute()
    {
        $linkedinMessage = $this->getLatestMessage();
        return $linkedinMessage ? substr($linkedinMessage->message, 0, 250) : null;
    }

    /**
     * Get the Formatted date of latest email of a thread to display on thread list vue UI.
     *
     * @return string|null
     */
    public function getSnippetDateAttribute()
    {
        $linkedinMessage = $this->getLatestMessage();
        return $linkedinMessage ? $linkedinMessage->submitted_at->timezone($this->campaign->carbon_timezone)->format('d M, h:i A') : null;
    }

    /**
     * Get the timestamp date of latest email of a thread to display on thread list vue UI.
     *
     * @return string|null
     */
    public function getSnippetTimestampAttribute()
    {
        $msg = $this->getLatestMessage();
        return $msg ? $msg->submitted_at->timezone($this->campaign->carbon_timezone)->timestamp : null;
    }
}
