<?php

namespace App\Http\Requests\Settings\Agency\Subscription;

use App\Spark;
use Illuminate\Support\Facades\Validator;
use Lara<PERSON>\Spark\Contracts\Http\Requests\Settings\Subscription\CreateSubscriptionRequest as Contract;

class CreateBraintreeSubscriptionRequest extends CreateSubscriptionRequest implements Contract
{
    /**
     * Get the validator for the request.
     *
     * @return \Illuminate\Validation\Validator
     */
    public function validator()
    {
        $validator = Validator::make($this->all(), [
            'braintree_type' => 'required',
            'braintree_token' => 'required',
            'plan' => 'required|in:'.Spark::activeAgencyPlanIdList()
        ]);

        return $validator->after(function ($validator) {
            $this->validatePlanEligibility($validator);

            if ($this->coupon) {
                $this->validateCoupon($validator);
            }
        });
    }
}
