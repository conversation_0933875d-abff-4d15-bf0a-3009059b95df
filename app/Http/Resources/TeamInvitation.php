<?php

namespace App\Http\Resources;

use Illuminate\Support\Str;
use Illuminate\Http\Resources\Json\JsonResource;

class TeamInvitation extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $roles = [];
        if (Str::contains($this->roles, 'View')) {
            $roles[] = 'view_campaigns';
        }
        if (Str::contains($this->roles, 'Edit')) {
            $roles[] = 'create_campaigns';
        }
        if (Str::contains($this->roles, 'Export')) {
            $roles[] = 'export_campaigns';
        }

        return [
            'email'      => $this->email,
            'created_at' => $this->created_at->timestamp,
            'roles'      => collect($roles)
        ];
    }
}
