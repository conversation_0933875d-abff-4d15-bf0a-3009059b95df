<?php

namespace App\Http\Controllers;

use App\Campaign;
use App\Jobs\Linkedin\ScrapeLinkedinProfile;
use App\Jobs\PdlEnrichment;
use App\Jobs\UpdateDuplicateProspects;
use App\LinkedinProfile;
use App\LinkedinSearch;
use App\Prospect;
use App\Snippet;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\StreamedResponse;

class LinkedinProfileController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param \App\LinkedinProfile $linkedinProfile
     * @return \Illuminate\Http\Response
     */
    public function show(LinkedinProfile $linkedinProfile)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\LinkedinProfile $linkedinProfile
     * @return \Illuminate\Http\Response
     */
    public function edit(LinkedinProfile $linkedinProfile)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\LinkedinProfile $linkedinProfile
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, LinkedinProfile $linkedinProfile)
    {
        $this->validate($request, [
            'first_name' => 'required|max:255|string',
            'last_name' => 'required|max:255|string'
        ]);

        // check name format
        $errors = $linkedinProfile->errors ? $linkedinProfile->errors : [];
        $fullName = $request->first_name . ' ' . $request->last_name;

        if(preg_match(config('constants.emoji_regexp'), $fullName)) {
            $errors['name_warning'] = 'Name has emoji';
        } else {
            unset($errors['name_warning']);
        }

        $linkedinProfile->update([
            'errors' => $errors,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
        ]);

        return response()->json([
            'status' => 'success',
            'linkedin_profile' => $linkedinProfile->only(['first_name', 'last_name', 'hashid', 'name_format_warning']),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\LinkedinProfile $linkedinProfile
     * @return \Illuminate\Http\Response
     */
    public function destroy(LinkedinProfile $linkedinProfile)
    {
        //
    }

    /**
     * Return a list of profiles for the AJAX, paginated and filtered if there are filter data.
     *
     * @param Request $request
     * @param \App\LinkedinSearch $linkedinSearch
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request, LinkedinSearch $linkedinSearch)
    {
        $limit = $request->input('per_page', 20);
        $status = $request->input('status', '');
        $withEmail = $request->input('email', '');
        $emailStatus = $request->input('email_status', '');
        $strKeywords = $request->input('keywords', null);
        $strSortBy = $request->input('sortby', 'email');
        $strSortOrder = $request->input('order', 'asc');
        $freeOpen = $request->input('profile_free_open', '');

        $profiles = LinkedinProfile::where('linkedin_search_id', $linkedinSearch->id)
            ->ofStatus($status)
            ->ofFreeOpen($freeOpen)
            ->ofEmailStatus($emailStatus)
            ->ofEmailFilter($withEmail)
            ->ofKeywords($strKeywords, ['linkedin_search_id' => $linkedinSearch->id])
            ->join('contacts', 'linkedin_profiles.contact_id', '=', 'contacts.id')
            ->leftJoin('companies', 'contacts.company_id', '=', 'companies.id')
            ->select('linkedin_profiles.*', 'companies.name as company_name', 'companies.website as company_website')
            ->with(['contact' => function($c) {
                $c->select('id', 'company_name', 'company_id', 'linkedin_hash', 'linkedin_avatar_url', 'email')
                    ->with(['company' => function($q){
                        $q->select('id', 'name', 'linkedin_id', 'linkedin_logo_url', 'website', 'linkedin_public_url');
                    }]);
            }])
            ->orderBy($strSortBy, $strSortOrder)
            ->paginate($limit);

        return response()->json([
            'status' => 'success',
            'linkedin_profiles' => $profiles,
        ]);
    }

    /**
     * Scrape a given linkedin profile
     *
     * @param Request $request
     * @param \App\LinkedinSearch $linkedinSearch
     * @return \Illuminate\Http\JsonResponse
     */
    public function scrape(LinkedinProfile $linkedinProfile)
    {
        if (!Auth::user()->can('support')) {
            return response()->json([
                'message'   => 'Error'
            ], 401);
        }

        $linkedinSearch = LinkedinSearch::find($linkedinProfile->linkedin_search_id);

        if($linkedinSearch->type == 'sales_nav') {
            $linkedinProfile->update([ 'status' => 'PROCESSING' ]);
            ScrapeLinkedinProfile::dispatch($linkedinProfile)->onQueue('browser');
        } else {
            $linkedinProfile->update([ 'status' => 'EMAIL_SEARCH' ]);
            PdlEnrichment::dispatch($linkedinProfile, $linkedinSearch->pdl_api_key)
                ->onQueue('emailfinder')->delay(now()->addSeconds(5));
        }

        return response()->json(['status' => 'success', 'msg' => 'Scraping linkedin profile']);
    }

    /**
     * Import the selected profiles to a campaign
     *
     * @param Request $request
     * @param \App\LinkedinSearch $linkedinSearch
     * @return \Illuminate\Http\JsonResponse
     */
    public function import(Request $request)
    {
        $isSelectAll = $request->input('select_all', false);
        $filters = json_decode($request->input('filters', '{}'));
        $liSearchId = decodeModelHashid(LinkedinSearch::class, $request->search_id, '');

        // prepare the profile query
        if ($request->select_all) {
            $withEmail = isset($filters->email) ? $filters->email : '';
            $emailStatus = isset($filters->email_status) ? $filters->email_status : '';
            $status = isset($filters->status) ? $filters->status : '';
            $freeOpen = isset($filters->profile_free_open) ? $filters->profile_free_open : '';

            $profiles = LinkedinProfile::ofEmailFilter($withEmail)
                ->ofFreeOpen($freeOpen)
                ->ofEmailStatus($emailStatus)
                ->ofStatus($status)
                ->with(['contact' => function($c) {
                    $c->select('id', 'company_id', 'linkedin_avatar_url')
                        ->with(['company' => function ($cp) {
                            $cp->select( 'id', 'industry', 'website', 'name');
                        }]);
                }])
                ->ofSearch($liSearchId)
                ->orderBy('created_at', 'desc');
        } else {
            $profileIds = collect(json_decode($request->input('profile_ids', '[]')))->map(function($profile) {
                return decodeModelHashid(LinkedinProfile::class, $profile);
            })->toArray();

            $profiles = LinkedinProfile::query()
                ->whereIn('id', $profileIds)
                ->with(['contact'=>function($c){
                    $c->select('id', 'company_id', 'linkedin_avatar_url')
                        ->with(['company'=>function($cp){
                            $cp->select( 'id', 'industry', 'website', 'name');
                        }]);
                }])
                ->ofSearch($liSearchId)
                ->orderBy('created_at', 'desc');
        }

        // import heree
        $results = $this->importToCampaign($profiles, $request);

        return [
            'status' => 'success',
            'results' => $results
        ];
    }

    /**
     * Helper function to import profiles to a campaign
     */
    protected function importToCampaign($profiles, $request)
    {
        $campaignId = decodeModelHashid(Campaign::class, $request->campaign_id);

        $total = [
            'imported' => 0,
            'importedNoEmails' => 0,
            'duplicates' => 0,
            'updatedDuplicates' => 0,
            'importedDuplicates' => 0,
            // 'batchDuplicates' => 0,
            'noEmails' => 0,
        ];

        $currentCampaign = Campaign::where('id', $campaignId)->first();
        $otherCampaigns = Campaign::where('team_id', $currentCampaign->team_id)
            ->where('id', '!=', $campaignId)
            ->get();
        $agency = $currentCampaign->agency;

        $profiles->chunk(10, function ($profiles) use ( $currentCampaign, $otherCampaigns, $agency, $request, &$total ) {
            $currentCampaignDuplicates = [];
            $completedCampaignDuplicates = [];
            $profilesToAdd = [];
            $createLiSnippets = false;
            $linkedinMergeFields = ['about', 'tenure_description', 'tenure_company', 'tenure_position'];

            // get possible duplicates from current campaign's prospects
            $currentDuplicates = Prospect::where('campaign_id', $currentCampaign->id)
                ->where(function ($query) use($profiles) {
                    return $query->whereIn('email', $profiles->pluck('email')->filter()->toArray())
                        ->orWhereIn('linkedin_hash', $profiles->pluck('linkedin_hash')->filter()->toArray());
                })
                ->select('id', 'email', 'first_name', 'last_name', 'linkedin_hash', 'campaign_id')
                ->get();

            // get possible duplicates from completed and archived campaigns' prospects
            $otherCampaignIds = $otherCampaigns->pluck('id')->toArray();
            $otherDuplicates = Prospect::whereIn('campaign_id', $otherCampaignIds)
                ->where(function ($query) use($profiles) {
                    return $query->whereIn('email', $profiles->pluck('email')->filter()->toArray())
                        ->orWhereIn('linkedin_hash', $profiles->pluck('linkedin_hash')->filter()->toArray());
                })
                ->select('id', 'email', 'first_name', 'last_name', 'linkedin_hash', 'campaign_id')
                ->get();

            // loop $profiles and assemble $profileToAdd for insertMany
            foreach ($profiles as $profile) {
                if (!$profile->email && !(config('app.linkedinAllowNoEmailImport') && $agency->has_linkedin_outreach && $request->import_noemails)) {
                    $total['noEmails'] = $total['noEmails'] + 1;
                    continue;
                }

                if(!$profile->email && config('app.linkedinAllowNoEmailImport')) {
                    // if no email, check "linkedin_hash"
                    $existingProfileToAdd = Arr::first($profilesToAdd, function ($profilesToAdd, $key) use($profile) {
                        return $profilesToAdd['linkedin_hash'] == $profile->linkedin_hash;
                    });
                } else {
                    // check if email is already in $profilesToAdd
                    // $profileCheckToAdd = $profilesToAdd->where('email', $profile->email)->first();
                    $existingProfileToAdd = Arr::first($profilesToAdd, function ($profilesToAdd, $key) use($profile) {
                        return $profilesToAdd['email'] == $profile->email;
                    });
                }

                if(!empty($existingProfileToAdd)) {
                    // $total['batchDuplicates'] += 1;
                    $total['duplicates'] += 1;
                    continue;
                }

                $company = $profile->contact->company;

                $objProfile = array(
                    'first_name' => $profile->first_name,
                    'last_name' => $profile->last_name,
                    'company' => $company && $company->name ? $company->name : null,
                    'industry' => $company && $company->industry ? $company->industry : null,
                    'website' => $company && $company->website ? $company->website : null,
                    'title' => $profile->title,
                    'address' => $profile->address,
                    'email' => $profile->email,
                    'campaign_id' => $currentCampaign->id,
                    'team_id' => $currentCampaign->team_id,
                    'agency_id' => $currentCampaign->agency_id,
                    'timezone' => $currentCampaign->timezone,
                    'status' => 'OK',
                    'contact_id' => $profile->contact_id,
                    'import_file'  => time().'__LinkedIn Search: '.$request->search_name,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                    'domain' => Str::after($profile->email, '@'),
                    'linkedin_hash' => $profile->linkedin_hash,
                    'linkedin_avatar_url' => $profile->contact->linkedin_avatar_url,
                    'merge_fields' => null
                );

                // add linkedin about as merge field
                $mergeFields = array();
                foreach ($linkedinMergeFields as $field) {
                    if(!empty($profile[$field])) {
                        $liData = $profile[$field];

                        // convert json to string
                        if(in_array($field, array('tenure_position', 'tenure_company'))) {
                            $tenureYears = !empty($profile[$field]["numYears"]) ? $profile[$field]["numYears"] . " years" : "";
                            $tenureMnths = !empty($profile[$field]["numMonths"]) ? $profile[$field]["numMonths"] . " months" : "";
                            $tenureConj = $tenureYears && $tenureMnths ? " and " : " ";

                            $liData = "{$tenureYears}{$tenureConj}{$tenureMnths}";
                        }
                        $mergeFields["linkedin_profile_{$field}"] = $liData;
                    }
                }

                if(!empty($mergeFields)) {
                    $createLiSnippets = true;
                    $objProfile['merge_fields'] = json_encode($mergeFields);
                }

                // check duplicates
                $duplicate = $currentDuplicates->filter(function ($item) use ($profile) {
                    return $item->email == $profile->email || $item->linkedin_hash == $profile->linkedin_hash;
                })->first();

                if ($duplicate) {
                    $total['duplicates'] = $total['duplicates'] + 1;
                    $objProfile['prospect_id'] = $duplicate->id;

                    $newData = Arr::except($objProfile, [
                        'email', 'interested', 'status', 'contact_id', 'team_id', 'agency_id',
                        'created_at', 'updated_at', 'import_file', 'timezone'
                    ]);

                    array_push($currentCampaignDuplicates, $newData);

                    continue;
                }

                // check duplicates from other campaign
                $otherDup = $otherDuplicates->filter(function ($item) use ($profile) {
                    return $item->email == $profile->email || $item->linkedin_hash == $profile->linkedin_hash;
                })->first();

                if ($otherDup) {
                    if (in_array($otherDup->campaign->status, ['COMPLETED', 'ARCHIVED'])) {
                        array_push($completedCampaignDuplicates, $objProfile);
                    }
                    $total['duplicates'] = $total['duplicates'] + 1;

                    continue;
                }

                array_push($profilesToAdd, $objProfile);
                $total['imported'] = $total['imported'] + 1;
                if (!$profile->email) {
                    // If we reached this point, it means we allow importing no email profiles.
                    // Count the amount so that we know how many were imported.
                    $total['importedNoEmails'] = $total['importedNoEmails'] + 1;
                }
            }

            // bulk insert $profileToAdd
            Prospect::insert($profilesToAdd);

            // create li about snippet for this campaign
            if($createLiSnippets) {
                foreach ($linkedinMergeFields as $field) {
                    $snippet = Snippet::firstOrCreate([
                        'name' => "linkedin_profile_{$field}",
                        'campaign_id' => $currentCampaign->id
                    ], [
                        'type' => 'hidden'
                    ]);
                }
            }

            // import duplicates from other campaign
            if ($request->import_duplicates && count($completedCampaignDuplicates)) {
                Prospect::insert($completedCampaignDuplicates);
                $total['imported'] = $total['imported'] + count($completedCampaignDuplicates);
                $total['importedDuplicates'] = $total['importedDuplicates'] + count($completedCampaignDuplicates);
            }

            // run job to update duplicates in this current campaign
            if ($request->update_duplicates && count($currentCampaignDuplicates)) {
                $total['imported'] = $total['imported'] + count($currentCampaignDuplicates);
                $total['updatedDuplicates'] = $total['updatedDuplicates'] + count($currentCampaignDuplicates);

                DB::table('prospects_temp')->insert($currentCampaignDuplicates);
                UpdateDuplicateProspects::dispatch($currentCampaign)->onQueue('default');
            }
        });

        return $total;
    }

    /**
     * Export Selected profiles
     *
     * @param  \Illuminate\Http\Request $request
     * @param  LinkedinSearch $linkedinSearch
     * @return \Illuminate\Http\Response|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(LinkedinSearch $linkedinSearch, Request $request) {
        $exportType = $request->input('export_type');
        $filters = json_decode($request->input('profile_filters', '{}'));
        $orderRaw = $linkedinSearch->status == 'DONE' ? '-email DESC' : 'created_at DESC';

        // prepare the profile query
        if($request->export_type == 'all') {
            $withEmail = isset($filters->email) ? $filters->email : '';
            $emailStatus = isset($filters->email_status) ? $filters->email_status : '';
            $status = isset($filters->status) ? $filters->status : '';
            $freeOpen = isset($filters->profile_free_open) ? $filters->profile_free_open : '';

            $profiles = LinkedinProfile::ofEmailFilter($withEmail)
                ->ofFreeOpen($freeOpen)
                ->ofEmailStatus($emailStatus)
                ->ofStatus($status)
                ->with(['contact'=>function($c){
                    $c->select('id', 'company_id')
                        ->with(['company'=>function($cp){
                            $cp->select( 'id', 'industry', 'website', 'name', 'linkedin_id', 'linkedin_public_url');
                        }]);
                }])
                ->ofSearch($linkedinSearch->id)
                ->orderByRaw($orderRaw);
        } else {
            $profileIds = collect(json_decode($request->input('profile_ids', '[]')))->map(function($profile) {
                return decodeModelHashid(LinkedinProfile::class, $profile);
            })->toArray();

            $profiles = LinkedinProfile::query()
                ->whereIn('id', $profileIds)
                ->with(['contact'=>function($c){
                    $c->select('id', 'company_id')
                        ->with(['company'=>function($cp){
                            $cp->select( 'id', 'industry', 'website', 'name', 'linkedin_id', 'linkedin_public_url');
                        }]);
                }])
                ->ofSearch($linkedinSearch->id)
                ->orderByRaw($orderRaw);
        }

        return $this->exportStream($profiles, $linkedinSearch);
    }

    /**
     * Stream the export of profiles
     * @param $profiles
     * @param $linkedinSearch
     * @return StreamedResponse
     */
    protected function exportStream($profiles, $linkedinSearch)
    {
        $filename = $linkedinSearch->hashid.' - '.$linkedinSearch->name.' - '.Carbon::now()->format('Y.m.d'). '.csv';
        $headers = ['Email', 'First Name', 'Last Name', 'Address', 'Title', 'Company', 'Website',
            'Industry', 'LinkedIn Slug', 'Linkedin Profile Open', 'LinkedIn Company Url', 'Status', 'Email Status'
        ];

        $response = new StreamedResponse(function() use($profiles, $headers) {
            $streamHandle = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($streamHandle, $headers);

            // Get all profiles in chunks
            $profiles->chunk(1000, function ($profiles) use($streamHandle) {
                foreach ($profiles as $profile) {
                    $linknedinCompanyUrl = '';
                    if($profile->contact->company) {
                        $linknedinCompanyUrl = $profile->contact->company->linkedin_public_url 
                            ? $profile->contact->company->linkedin_public_url : $profile->contact->company->linkedin_slug;
                    }

                    $companyName = $profile->contact->company ? $profile->contact->company->name : '';
                    $companyWebsite = $profile->contact->company ? $profile->contact->company->website : '';
                    $companyIndustry = $profile->contact->company ? $profile->contact->company->industry : '';
                    $emailStatus = ucwords(str_replace('_', ' ', $profile->email_status));
                    $freeToOpen = $profile->profile_free_open ? "yes" : "no";

                    $formattedProfile = [
                        $profile->email,
                        $profile->first_name,
                        $profile->last_name,
                        $profile->address,
                        $profile->title,
                        $companyName,
                        $companyWebsite,
                        $companyIndustry,
                        $profile->linkedin_slug,
                        $freeToOpen,
                        $linknedinCompanyUrl,
                        $profile->status,
                        $emailStatus
                    ];

                    fputcsv($streamHandle, $formattedProfile);
                }
            });

            // Close the output stream
            fclose($streamHandle);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="'.$filename.'"',
        ]);

        // download thhe csv file
        return $response;
    }
}
