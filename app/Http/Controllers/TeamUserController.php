<?php

namespace App\Http\Controllers;

use App\Models\Journey\Journey;
use App\Role;
use App\Services\JourneyService;
use App\Team;
use App\User;
use App\Agency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Spark\Team as SparkTeam;
use Lara<PERSON>\Spark\Events\Teams\TeamMemberRemoved;
use Illuminate\Foundation\Auth\EmailVerificationRequest;

class TeamUserController extends Controller
{
    /**
     * Display the page of client/team members/users list.
     *
     * @param \App\Team $team
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index(Team $team, Request $request)
    {
        // check client access
        if (Auth::user()->cannot('agency-admin') && Auth::user()->teams()->first()->id != $team->id) {
            return redirect('/clients/'.Auth::user()->teams()->first()->id);
        }

        return view('users.index', compact(['team']));
    }

    /**
     * Return a list of client/team members/users for the AJAX.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Team $team, Request $request)
    {
        $sparkTeam = SparkTeam::where('id', $team->id)->first();
        $agency = Agency::where('id', $sparkTeam->agency_id)->with('owner')->first();

        if ($agency->isAgencyDashboard()) {
            $agencyAdminIds = Role::where('name', 'agency-admin')->first()->users()
                ->where('agency_id', $agency->id)
                ->pluck('id');
        } else {
            $agencyAdminIds = collect($agency->owner->id);
        }
        if ($agency->wavo_version != 3) {
            $sparkTeam->load(['users' => function ($q) use ($agencyAdminIds) {
                $q->whereNotIn('id', $agencyAdminIds);
            }]);
        } else {
            $sparkTeam->load('users');
        }

        $myAccess = [
            'can_edit'=>true,
            'can_export'=>true,
            'can_read'=>true,
            'can_read_lisearch'=>true,
            'can_edit_lisearch'=>true,
        ];

        $members = $sparkTeam->users->map(function($user, $key) use(&$myAccess) {
            // do not return other info
            $member = $user->only(['id', 'email', 'name']);
            $member['can_edit'] = $user->hasRole('campaign-admin') ? true : false;
            $member['can_export'] = $user->hasRole('prospect-export') ? true : false;
            $member['can_read'] = $user->hasRole('campaign-read') ? true : false;
            $member['can_read_lisearch'] = $user->hasRole('linkedinsearch-read') ? true : false;
            $member['can_edit_lisearch'] = $user->hasRole('linkedinsearch-admin') ? true : false;

            // get the user's access so we can check what permissions
            // he can allow when inviting another team user
            // agency owners have all permissions enabled
            if($user->id == Auth::user()->id && !Auth::user()->isAgencyOwner()){
                $myAccess = [
                    'can_edit'=>$member['can_edit'],
                    'can_export'=>$member['can_export'],
                    'can_read'=>$member['can_read'],
                    'can_read_lisearch'=>$member['can_read_lisearch'],
                    'can_edit_lisearch'=>$member['can_edit_lisearch'],
                ];
            }

            return $member;
        });

        return response()->json([
            'status'=>'success',
            'members' => $members,
            'myAccess' => $myAccess
        ], 200);
    }

    /**
     * Update a client's contact information.
     *
     * @param  Request $request
     * @throws \Illuminate\Auth\Access\AuthorizationException
     * @return mixed
     */
    public function updateRole(Request $request, Team $team, User $user)
    {
        // check if user can view campaign
        if ($request->can_read) {
            if (!$user->hasRole('campaign-read')) {
                $user->assignRole('campaign-read');
            }
        } else {
            $user->detachRole('campaign-read');
        }

        // check if user can create/edit campaign
        if ($request->can_edit) {
            if (!$user->hasRole('campaign-admin')) {
                $user->assignRole('campaign-admin');
            }
        } else {
            $user->detachRole('campaign-admin');
        }

        // check if user can export contacts/prospects csv
        if ($request->can_export) {
            if (!$user->hasRole('prospect-export')) {
                $user->assignRole('prospect-export');
            }
        } else {
            $user->detachRole('prospect-export');
        }

        // check if user can read linkedin search
        if ($request->can_read_lisearch) {
            if (!$user->hasRole('linkedinsearch-read')) {
                $user->assignRole('linkedinsearch-read');
            }
        } else {
            $user->detachRole('linkedinsearch-read');
        }

        // check if user can export contacts/prospects csv
        if ($request->can_edit_lisearch) {
            if (!$user->hasRole('linkedinsearch-admin')) {
                $user->assignRole('linkedinsearch-admin');
            }
        } else {
            $user->detachRole('linkedinsearch-admin');
        }

        return $user;
    }

    /**
     * Remove the given team member from the team.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Laravel\Spark\Team $team
     * @param mixed $user
     * @return void
     */
    public function destroy(Request $request, $team, User $user)
    {
        $user->roles()->detach();
        $team->users()->detach($user);
        event(new TeamMemberRemoved($team, $user));
    }

    /**
     * Display the page of client/team members/users list.
     *
     * @param \App\Team $team
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function userTeamDetails(Team $team, Request $request)
    {
        // check client access
        if (Auth::user()->cannot('agency-admin') && Auth::user()->teams()->first()->id != $team->id) {
            return redirect('/clients/'.Auth::user()->teams()->first()->id);
        }

        $sparkTeam = SparkTeam::where('id', $team->id)->first();
        $agency = Agency::where('id', $sparkTeam->agency_id)->with('owner')->first();

        $teamAdmin = $agency->owner;
        $sparkTeam->load(['users'=> function ($q) use ($teamAdmin) {
            $q->where('id', '!=', $teamAdmin->id);
        }]);

        $myAccess = [ 'can_edit'=>true, 'can_export'=>true, 'can_read'=>true ];

        $members = $sparkTeam->users->map(function($user, $key) use(&$myAccess) {
            $user['can_edit'] = $user->hasRole('campaign-admin') ? true : false;
            $user['can_export'] = $user->hasRole('prospect-export') ? true : false;
            $user['can_read'] = $user->hasRole('campaign-read') ? true : false;

            // get the user's access so we can check what permissions
            // he can allow when inviting another team user
            if($user->id == Auth::user()->id){
                $myAccess = $user->only(['can_edit', 'can_read', 'can_export']);
            }

            return $user;
        });

        return response()->json([
            'status'=>'success',
            'team' => $team,
            'members' => $members,
            'myAccess' => $myAccess
        ], 200);
    }

    public function userVerify(EmailVerificationRequest $request, $id, $hash)
    {
        $user = Auth::user();
        $prevVerification = $user->email_verified_at ?? null;

        $request->fulfill();

        $agency = Auth::user()->agency;
        if ($agency->chatGptPromptTemplates()->count() === 0 && $agency->has_chat_gpt) {
            $agency->addGlobalPrompts();
        }
        if ($agency->wavo_version == 3 && $agency->current_billing_plan == 'free-ec' && $user->journeys()->doesntExist() && $user->isAgencyOwner()) {
            $journeyService = new JourneyService();
            $journeyService->createJourney(Auth::user(), Journey::where('slug','wavo3-trial-onboarding')->first());
            $journeyService->completeStep(Auth::user(), 'trial_started');
        }

        return redirect('/search?verified=1');
    }
}
