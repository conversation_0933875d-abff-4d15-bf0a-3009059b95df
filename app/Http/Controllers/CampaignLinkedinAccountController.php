<?php

namespace App\Http\Controllers;

use App\LinkedinAccount;
use Spark;
use App\Team;
use App\Campaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class CampaignLinkedinAccountController extends Controller
{
    /**
     * Return available emails by campaign's team
     *
     * @param Campaign $campaign
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Campaign $campaign)
    {
        $campaignLinkedinAccountIds = $campaign->linkedinAccounts->pluck('hashid')->toArray();

        // only allow emails from the campaign's team
        $teams = Team::where('id', $campaign->team_id)
            ->with(['linkedinAccounts'=> function ($q) {
                $q->orderBy('status')
                    ->withCount(['campaigns' => function($l) {
                        $l->where('status','RUNNING')->where('is_ready', true);
                    }]);
                }])
            ->get();

        // format data before returning
        $teams->transform(function ($team) use ($campaignLinkedinAccountIds) {
            $team->linkedinAccounts->transform(function ($account) use ($campaignLinkedinAccountIds) {
                $isSelected = in_array($account->hashid, $campaignLinkedinAccountIds);

                return array(
                    'name' => $account->name,
                    'username' => $account->username,
                    'status' => $account->status,
                    'hashid' => $account->hashid,
                    //'account_type' => $account->linkedinAccountType->name,
                    'messages_limit' => $account->messages_limit,
                    'id' => $account->id,
                    'active_campaigns' => $account->campaigns_count,
                    'is_selected' => $isSelected,
                    'linkedin_account_type' => $account->linkedinAccountType
                );
            });

            return array(
                "name" => $team->name,
                "slug" => $team->slug,
                "id" => $team->id,
                "linkedin_accounts" => $team->linkedinAccounts
            );
        });

        return response()->json([
            'status' => 'success',
            'teams' => $teams,
            'is_currently_sending' => $campaign->schedules()->active()->exists(),
            'selected_accounts' => $campaignLinkedinAccountIds
        ]);
    }

    /**
     * Add and Remove emailAccounts to campaign.
     *
     * @param Campaign $campaign
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Campaign $campaign, Request $request)
    {
        $campaignLinkedinAccountIds = $campaign->linkedinAccounts()->pluck('id')->toArray();
        $hasErrAdding = false;
        $hasErrRemoving = false;
        $newLinkedinAccounts = null;

        $linkedinAccountIds = [];
        foreach($request->linkedin_accounts as $linkedinAccount) {
            array_push($linkedinAccountIds, decodeModelHashid(LinkedinAccount::class, $linkedinAccount));
        }

        // ADD: if item in request_ids is not found in curr_ids
        $toAdd = array_diff($linkedinAccountIds, $campaignLinkedinAccountIds);

        // We might need to update the campaign schedules.
        //$campaignScheduler = new CampaignSchedulerService($campaign);

        // check if toAdd has id to add
        if(@count($toAdd))
        {
            $linkedinAccounts = LinkedinAccount::whereIn('id', $toAdd)->with(['team'])->get();
            foreach ($linkedinAccounts as $key => $linkedinAccount) {
                if (!$linkedinAccount->team()->exists()) {
                    return response()->json([
                        'status' => 'error',
                        'msg'=>'Unable to add to Campaign - No '.Spark::teamString().' available for linkedin account.',
                    ]);
                }
            }

            try {
                DB::transaction(function() use ($campaign, $toAdd) {
                    $campaign->addAccounts($toAdd, 'linkedin');
                    //$campaignScheduler->reschedule();
                });
            } catch (\Exception $e) {
                Log::error($e->getMessage());
                $hasErrAdding = true;
            }
        }

        // REMOVE: if item in curr_ids is not found in request_ids
        $toRemove = array_diff($campaignLinkedinAccountIds, $linkedinAccountIds);

        if(@count($toRemove))
        {
            try {
                DB::transaction(function() use ($campaign, $toRemove) {
                    $campaign->removeAccounts($toRemove, 'linkedin');
                    //foreach ($toRemove as $linkedinAccountId) {
                    //    $campaignScheduler->removeLinkedinAccount(LinkedinAccount::find($linkedinAccountId));
                    //}
                });
            } catch (\Exception $e) {
                Log::error($e->getMessage());
                $hasErrRemoving = true;
            }
        }

        // get updated email accounts to display
        if (!$hasErrAdding && !$hasErrRemoving) {
            $newLinkedinAccounts = $campaign->linkedinAccounts()->with('linkedinAccountType')->get();
        }

        return response()->json([
            'status' => $hasErrAdding || $hasErrRemoving ? 'error' : 'success',
            'err_adding' => $hasErrAdding,
            'err_removing' => $hasErrRemoving,
            'new_linkedin_accounts' => $newLinkedinAccounts,
        ]);
    }
}
