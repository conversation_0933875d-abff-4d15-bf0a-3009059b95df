<?php

namespace App\Http\Controllers;

use App\Team;
use App\Agency;
use App\Campaign;
use Carbon\Carbon;
use App\DailyStats;
use App\EmailMessage;
use App\EmailAccount;
use App\EmailTemplate;
use App\ProspectActivity;
use App\ScheduledProspect;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ActivityController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth']);
    }

    /**
     * return data for email activity in a format required by chartistjs
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function campaign(Request $request)
    {
        $campaignId = $request->input('campaign', 0); 
        $strActivity = $request->input('activity', null);

        $campaign = Campaign::where('id', $campaignId)
            ->with(['campaignActivities'=>function($q){
                $q->orderBy('id', 'desc');
            }, 'campaignActivities.user'=>function($q){
                $q->select('id', 'name', 'email');
            }])
            ->first();

        $colCampaignActivities = $campaign->campaignActivities->transform(function ($activity) use ($campaign) {
            $date = $activity->created_at->timezone($campaign->carbon_timezone)->format('M jS Y, g:iA');
            $activity['activity_date_tz'] = $date;
            return $activity;
        }); 

        return response()->json([
            'status'         => 'success', 
            'campaign_activities' => $colCampaignActivities
        ]);
    }

    /**
     * return data for prospect activities in a format required by chartistjs
     * NOT USED
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    //public function prospects(Request $request)
    //{
    //    $campaignId = $request->input('campaign', 0);
    //    $strKeywords = $request->input('keywords', null);
    //    $strActivity = $request->input('activity', null);
    //    $allActivities = ['ProspectReplied', 'ProspectAutoReplied', 'EmailBounced', 'updated prospect status to UNSUBSCRIBED'];
    //    $arrActivities = $allActivities;
    //
    //    if($strActivity){
    //        $strActivity = $strActivity != 'Unsubscribed' ? $strActivity : 'updated prospect status to UNSUBSCRIBED';
    //        $arrActivities = [$strActivity];
    //    }
    //
    //    // select prospect_id, min(id) as last_id from prospect_activities group by prospect_id ORDER BY last_id DESC
    //    $colProspectActivities = ProspectActivity::select('prospect_id', DB::raw('max(id) AS id'))
    //        ->whereHas('prospect', function($query) use($campaignId, $strKeywords){
    //            $query->where('campaign_id', $campaignId)
    //                ->ofKeywords($strKeywords);
    //        })
    //        ->groupBy('prospect_id')
    //        ->whereIn('activity', $arrActivities)
    //        ->with(['prospect.prospectActivities'=>function($query) use($allActivities) {
    //            array_push($allActivities, 'CampaignEmailSent');
    //
    //            $query->orderBy('id', 'asc')
    //                ->whereIn('activity', $allActivities);
    //        }])
    //        ->orderBy('id', 'desc')
    //        ->paginate(20);
    //
    //    // transform data and use the campaign's timezone to match mailbox time
    //    $colProspectActivities->getCollection()->transform(function ($activity) {
    //        // there are prospect without timezone so use the app's timezone
    //        $last_activity = $activity->prospect->prospectActivities->sortByDesc('id')->first();
    //        $prospectTz = data_get($activity->prospect, 'timezone', config('app.timezone'));
    //        $tz = str_replace(' ', '_', $prospectTz);
    //        $activity['activity_date_tz'] = $last_activity->created_at->timezone($tz)->format('M jS Y, g:iA');
    //        $activity['activity'] = $last_activity->activity;
    //
    //
    //
    //
    //        $mappedActivities = $activity->prospect->prospectActivities->map(function($objAct, $key) use($tz){
    //            $objAct['activity_date_tz'] = $objAct->created_at->timezone($tz)->format('M jS Y, g:iA');
    //            return $objAct;
    //        });
    //
    //        return $activity;
    //    });
    //
    //    return response()->json([
    //        'status'         => 'success',
    //        'prospects_thread' => $colProspectActivities
    //    ]);
    //}

    /**
     * New email stats calculation for home dashboard chart.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function emailStats(Request $request)
    {
        $agencyId = decodeModelHashid(Agency::class, $request->input('agency', null));
        $campaignId = decodeModelHashid(Campaign::class, $request->input('campaign', null));
        $clientId = decodeModelHashid(Team::class, $request->input('client', null));
        $emailId = decodeModelHashid(EmailAccount::class, $request->input('email_account', null));
        $campaignIds = null;
        $emailsToSend = 0;
        $emailsSent = 0;
        $campaign = null;
        $campaigns = null;

        if (! (Auth::user()->isAdmin() || Auth::user()->isSupport())) {
            $agencyId = Auth::user()->agency_id;
        }

        if ($campaignId) {
            // if campaign filter is available,
            $campaignIds = [$campaignId];
            $campaign = Campaign::find($campaignId);
        } elseif ($clientId && !$campaignId) {
            // if filter by client/team only, get team's campaign ids
            $campaigns = Campaign::where('team_id', $clientId)->get();
            // check if there's only 1 campaign, this will be used in calculating queued message today
            if ($campaigns && $campaigns->count() == 1) {
                $campaign = $campaigns->first();
            }
            $campaignIds = $campaigns->pluck('id')->toArray();
        }

        // If campaign is available, use campaign's timezone. Otherwise get timezone from first campaign of client/agency.
        if ($campaign) {
            $campaignTimezone = $campaign->carbon_timezone;
        } elseif ($campaignIds) {
            $timezone = DB::table('campaigns')
                ->selectRaw("timezone, count(*) as tz_total")
                ->whereIn('id', $campaignIds)
                ->groupBy('timezone')
                ->orderBy('tz_total', 'desc')
                ->first()
                ->timezone;
            $campaignTimezone = str_replace(' ', '_', $timezone);
        } elseif ($agencyId) {
            $timezone = DB::table('campaigns')
                ->selectRaw("timezone, count(*) as tz_total")
                ->where('agency_id', $agencyId)
                ->groupBy('timezone')
                ->orderBy('tz_total', 'desc')
                ->first()
                ->timezone;
            $campaignTimezone = str_replace(' ', '_', $timezone);
        } else {
            $campaignTimezone = 'US/Eastern'; //config('app.timezone');
        }

        // count emails new
        $start = now($campaignTimezone)->endOfDay()->subWeek()->toDateString();
        $messageStats = DailyStats::selectRaw('day, sum(sent) as sent, sum(autoreplied) as autoreplied, sum(unsubscribed) as unsubscribed, sum(replied) as replied, sum(bounced) as bounced')
            ->where('day', '>=', $start)
            ->ofCampaigns($campaignIds)
            ->ofEmailAccounts($emailId)
            ->ofAgency($agencyId)
            ->groupBy('day')
            ->get();

        $dayLabels = [];
        $day = Carbon::parse($start);

        while ($day <= now($campaignTimezone)->startOfDay()) {
            if ($messageStats->where('day', $day)->count() == 0) {
                $messageStats->push(new DailyStats([
                    'day' => $day,
                    'sent' => '0',
                    'autoreplied' => '0',
                    'unsubscribed' => '0',
                    'replied' => '0',
                    'bounced' => '0',
                ]));
            }

            array_push($dayLabels, $day->format('D / Md'));
            $day->addDay();
        }

        $messageStats = $messageStats->sortBy('day')->values();

        $sentData = $messageStats->map(function($data) {
            return [
                'meta' => 'Sent:',
                'value' => $data->sent,
            ];
        })->all();
        $unsubscribedData = $messageStats->map(function($data) {
            return [
                'meta' => 'Unsubscribed:',
                'value' => $data->unsubscribed,
            ];
        })->all();
        $autorepliedData = $messageStats->map(function($data) {
            return [
                'meta' => 'Autoreplied:',
                'value' => $data->autoreplied,
            ];
        })->all();
        $repliedData = $messageStats->map(function($data) {
            return [
                'meta' => 'Replied:',
                'value' => $data->replied,
            ];
        })->all();

        $emailsSent = $messageStats->last()->sent;
        $emailsToSend = $this->getQueuedToday($campaign, 'RUNNING', $campaignIds, $emailId, $agencyId);

        return response()->json([
            'status' => 'success',
            'labels' => $dayLabels,
            'series' => [
                $sentData,
                $unsubscribedData,
                $autorepliedData,
                $repliedData
            ],
            'emails_to_send' => $emailsToSend + $emailsSent,
            'emails_sent'    => $emailsSent
        ]);
    }

    /**
     * return data for email activity in a format required by chartistjs
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function emails(Request $request)
    {
        $agencyId = decodeModelHashid(Agency::class, $request->input('agency', null));
        $campaignId = decodeModelHashid(Campaign::class, $request->input('campaign', null));
        $clientId = decodeModelHashid(Team::class, $request->input('client', null));
        $emailId = decodeModelHashid(EmailAccount::class, $request->input('email_account', null));
        $campStatus = data_get($request, 'status', null);
        $createDate = Carbon::today()->endOfDay()->subWeek();
        $campIds = null;
        $emailsToSend = 0;
        $emailsSent = 0;
        $campaign = null;
        $campaigns = null;

        if (!Auth::user()->isAdmin()) {
            $agencyId = Auth::user()->agency_id;
        }

        if ($campaignId) {
            // if campaign filter is available, 
            $campIds = [$campaignId];
            $campaign = Campaign::where('id', $campaignId)
                ->ofStatus($campStatus)
                ->with(['campaignStages', 'emailAccounts'])
                ->first();
        } elseif ($clientId && !$campaignId) {
            // if filter by client/team only, get team's campaign ids
            $campaigns = Campaign::where('team_id', $clientId)
                ->ofStatus($campStatus)
                ->with(['campaignStages', 'emailAccounts'])
                ->get();

            // check if there's only 1 campaign, this will be used in calculating queued message today
            if ($campaigns && $campaigns->count() == 1) {
                $campaign = $campaigns->first();
            }

            $campIds = $campaigns->pluck('id')->toArray();
        }

        // If campaign is available, use campaign's timezone. Otherwise get timezone from first campaign of client/agency.
        if ($campaign) {
            $campTZ = $campaign->carbon_timezone;
        } elseif ($campIds) {
            $timezone = DB::table('campaigns')
                ->selectRaw("timezone, count(*) as tz_total")
                ->whereIn('id', $campIds)
                ->groupBy('timezone')
                ->orderBy('tz_total', 'desc')
                ->first()
                ->timezone;
            $campTZ = str_replace(' ', '_', $timezone);
        } elseif ($agencyId) {
            $timezone = DB::table('campaigns')
                ->selectRaw("timezone, count(*) as tz_total")
                ->where('agency_id', $agencyId)
                ->groupBy('timezone')
                ->orderBy('tz_total', 'desc')
                ->first()
                ->timezone;
            $campTZ = str_replace(' ', '_', $timezone);
        } else {
            $campTZ = 'US/Eastern'; //config('app.timezone');
        }

        $createDate = Carbon::now()->timezone($campTZ)->endOfDay()->subWeek()->timezone(config('app.timezone'));

        $serverBase = Carbon::now()->timezone(config('app.timezone'))->format('P');
        $campaignBase = Carbon::now()->timezone($campTZ)->format('P');

        // count emails
        $messages = EmailMessage::selectRaw("DATE_FORMAT(CONVERT_TZ(submitted_at, '$serverBase', '$campaignBase'), '%Y %m %e') date, origin, status, COUNT(*) emails")
            ->where('submitted_at', '>=', $createDate)
            ->ofCampaigns($campIds)
            ->ofEmailAccount($emailId)
            ->whereHas('campaign', function ($query) use ($campStatus) {
                if ($campStatus) {
                    $query->where('status', $campStatus);
                }
            })
            ->ofAgency($agencyId)
            ->groupBy('date', 'origin', 'status')
            ->get();

        $msgSent = $messages->where('origin','self')->groupBy('date')
            ->map(function ($item) {
                return $item->sum('emails');
            });
        $msgAutoreplied = $messages->where('origin', 'prospect')->where('status', 'AUTOREPLIED')->groupBy('date')
            ->map(function ($item) {
                return $item->sum('emails');
            });
        $msgUnsubscribed = $messages->where('origin', 'prospect')->where('status', 'UNSUBSCRIBED')->groupBy('date')
            ->map(function ($item) {
                return $item->sum('emails');
            });
        $msgReplied = $messages->where('origin', 'prospect')->where('status', 'REPLIED')->groupBy('date')
            ->map(function ($item) {
                return $item->sum('emails');
            });

        // transform the data into chartisjs required format
        $dayLabels          = [];
        $sentData           = [];
        $unsubscribedData   = [];
        $autorepliedData    = [];
        $repliedData        = [];

        for ($i = 6; $i > -1; $i--) {
            $day = Carbon::now()->timezone($campTZ)->endOfDay()->subDays($i);

            // get data of this current day in loop
            // assemble the chartisjs series data - "meta" is for the tooltip label
            array_push($sentData, array(
                'meta'=>'Sent:', 'value' => $msgSent->get($day->format('Y m j'), 0)
            ));
            array_push($unsubscribedData, array(
                'meta'=>'Unsubscribed:', 'value' => $msgUnsubscribed->get($day->format('Y m j'), 0)
            ));
            array_push($autorepliedData, array(
                'meta'=>'Autoreplied:', 'value' => $msgAutoreplied->get($day->format('Y m j'), 0)
            ));
            array_push($repliedData, array(
                'meta'=>'Replied:', 'value' => $msgReplied->get($day->format('Y m j'), 0)
            ));

            // this is the chart labels
            array_push($dayLabels, $day->format('D / Md'));
        }

        $emailsSent = data_get(Arr::last($sentData), 'value', 0);
        $emailsToSend = $this->getQueuedToday($campaign, $campStatus, $campIds, $emailId, $agencyId);
 
        return response()->json([
            'status' => 'success', 
            'labels' => $dayLabels,
            'series' => [
                $sentData,
                $unsubscribedData,
                $autorepliedData,
                $repliedData
            ],
            'emails_to_send' => $emailsToSend + $emailsSent,
            'emails_sent'    => $emailsSent
        ]);
    }

    /**
     * NOT USED ?
     *
     * @param $campStatus
     * @param $campIds
     * @param $emailId
     * @param $strMsgStatus
     * @param $agencyId
     * @param $origin
     * @param $createDate
     * @param $timezone
     * @return mixed
     */
    protected function getMessages($campStatus, $campIds, $emailId, $strMsgStatus, $agencyId, $origin, $createDate, $timezone)
    {
        $serverBase = Carbon::now()->timezone(config('app.timezone'))->format('P');
        $campaignBase = Carbon::now()->timezone($timezone)->format('P');

        return EmailMessage::selectRaw("COUNT(*) emails, DATE_FORMAT(CONVERT_TZ(submitted_at, '$serverBase', '$campaignBase'), '%Y %m %e') date")
            ->where('origin', $origin)->where('submitted_at', '>=', $createDate)
            ->where('status', $strMsgStatus)
            ->ofCampaigns($campIds)
            ->ofEmailAccount($emailId)
            ->whereHas('campaign', function ($query) use ($campStatus) {
                if($campStatus){ 
                    $query->where('status', $campStatus);
                }
            })
            ->ofAgency($agencyId)
            ->groupBy('date')
            ->get();
    }

    /**
     * Get calculation of emails queued for today.
     *
     * @param $campaign
     * @param $campStatus
     * @param $campIds
     * @param $emailId
     * @param $agencyId
     * @return int
     */
    protected function getQueuedToday($campaign, $campStatus, $campIds, $emailId, $agencyId)
    {
        if ($campaign) {
            return $campaign->getPendingProspects();
        } elseif (empty($campIds) && is_null($agencyId)) {
            return ScheduledProspect::count();
        } else {
            $queuedCampaigns = Campaign::ofAgency($agencyId)
                ->ofStatus($campStatus)
                ->when($campIds, function ($query, $campIds) {
                    return $query->whereIn('id', $campIds);
                })
                ->get();

            return $queuedCampaigns->sum(function ($campaign) {
                return $campaign->getPendingProspects();
            });
        }
    }

    /**
     * Return list for email template CLICK activity for an email template of a campaign.
     *
     * @param  \App\EmailTemplate  $emailTemplate
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function emailClicks(EmailTemplate $emailTemplate)
    {
        $emailTemplate->load(['campaign']);

        $clicked = ProspectActivity::where('activity', 'ProspectClickedLink')
                        ->whereHas('prospect', function($q) use($emailTemplate) {
                            $q->where('campaign_id', $emailTemplate->campaign_id);
                        })
                        ->whereHas('emailMessage', function($q) use($emailTemplate) {
                            $q->where('email_template_id', $emailTemplate->id);
                        })
                        ->selectRaw("prospect_id, created_at, COUNT(*) as clicks")
                        ->orderBy('created_at', 'desc')
                        ->groupBy('prospect_id', 'created_at')
                        ->with(['prospect'=>function($q){
                            $q->select('id','email', 'first_name', 'last_name');
                        }])
                        ->paginate(20);

        // convert activity date to campaign timezone
        $clicked->getCollection()->transform(function ($objClick) use($emailTemplate) {
            $dateActivity = $objClick->created_at
                                ->timezone($emailTemplate->campaign->carbon_timezone)
                                ->format('M jS Y, g:iA');

            $objClick['date'] = $dateActivity;
            return $objClick;
        });

        return response()->json([
            'status'=>'success',
            'clicks'=>$clicked
        ]);
    }

    /**
     * Return list for email template OPEN activity for an email template of a campaign.
     *
     * @param  \App\EmailTemplate  $emailTemplate
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function emailOpens(EmailTemplate $emailTemplate)
    {
        $emailTemplate->load(['campaign']);

        $opened = ProspectActivity::where('activity', 'ProspectOpenedEmail')
                    ->whereHas('prospect', function($q) use($emailTemplate) {
                        $q->where('campaign_id', $emailTemplate->campaign_id);
                    })
                    ->whereHas('emailMessage', function($q) use($emailTemplate) {
                        $q->where('email_template_id', $emailTemplate->id);
                    })
                    ->selectRaw("prospect_id, created_at, COUNT(*) as open")
                    ->orderBy('created_at', 'desc')
                    ->groupBy('prospect_id', 'created_at')
                    ->with(['prospect'=>function($q){
                        $q->select('id','email', 'first_name', 'last_name');
                    }])
                    ->paginate(20); 

        // convert activity date to campaign timezone
        $opened->getCollection()->transform(function ($objOpen) use($emailTemplate) {
            $dateActivity = $objOpen->created_at
                                ->timezone($emailTemplate->campaign->carbon_timezone)
                                ->format('M jS Y, g:iA');

            $objOpen['date'] = $dateActivity;
            return $objOpen;
        });

        return response()->json([
            'status'=>'success',
            'opens'=>$opened
        ]);
    }
}
