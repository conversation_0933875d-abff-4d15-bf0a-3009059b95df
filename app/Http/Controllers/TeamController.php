<?php

namespace App\Http\Controllers;

use App\Team;
use App\Agency;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\ClientFormRequest;
use <PERSON><PERSON>\Spark\Events\Teams\TeamDeleted;
use <PERSON><PERSON>\Spark\Events\Teams\DeletingTeam;
use Lara<PERSON>\Spark\Contracts\Interactions\Settings\Teams\CreateTeam;
use Lara<PERSON>\Spark\Http\Controllers\Settings\Teams\TeamController as ClientGroupController;

use GuzzleHttp\Client as HttpClient;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ConnectException;
use Illuminate\Support\Facades\Log;

// extend spark's TeamsController to override 'store' method
class TeamController extends ClientGroupController
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Delete the given team.
     *
     * @param  Request  $request
     * @param  \App\Team  $team
     * @return Response
     * @throws \Exception
     */
    public function destroy(Request $request, $team)
    {
        event(new DeletingTeam($team));

        if ($team->detachUsersAndDestroy()) {
            event(new TeamDeleted($team));

            return redirect('/clients/')->with(['status'=>'success', 'msg'=>'Client was successfully deleted.']);
        }

        return redirect('/clients/')->with(['status'=>'error', 'msg'=>'There was an error when deleting the client.']);
    }

    /**
     * Display the page of client/team list.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // check if user is deleted client
        Auth::user()->load(['roles', 'teams']);
        if(!Auth::user()->roles->count() && !Auth::user()->teams->count()) {
            return redirect('/settings');
        }

        // Authorization
        if (Auth::user()->cannot('support')) {
            if (Auth::user()->agency->isUserDashboard()) {
                return redirect(route('home'));
            } elseif (Auth::user()->cannot('agency-admin')) {
                // agency dashboard subscription but not an agency admin
                return redirect(route('clients.show', Auth::user()->teams()->first()));
            }
        }

        $agencies = Auth::user()->getAccessibleAgencies();

        return view('clients.client-list', compact('agencies'));
    }

    /**
     * Display the create form of client/team.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // check client access
        if (Auth::user()->cannot('agency-admin') || Auth::user()->agency->isUserDashboard()) {
            return redirect('home');
        }

        return view('clients.client-create');
    }

    /**
     * Create a new client/team.
     *
     * @param Request $request
     * @throws \Illuminate\Validation\ValidationException
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'max_emails' => 'required|digits_between:0,3',
            'webhook_replied'   => 'nullable|url',
            'webhook_positive'   => 'nullable|url',
            'name'       => 'required',
        ]);

        // append the agencyID to team slug to make it unique per agency
        // spark team slug uniqueness is global
        $request['slug'] = Str::slug($request->name.Auth::user()->agency_id, '-');
        $webhookUrls = [];
        $webhookErrors = [];

        if(!empty($request->webhook_replied)) {
            $repliedHookStatus = $this->testWebhookUrl($request->webhook_replied, 'contact_replied');
            if($repliedHookStatus != "ok") {
                $webhookErrors['webhook_replied'] = "{$repliedHookStatus}";
            }

            $webhookUrls['replied'] = $request->webhook_replied;
        }

        if(!empty($request->webhook_positive)) {
            $positiveHookStatus = $this->testWebhookUrl($request->webhook_positive, 'contact_positive');
            if($positiveHookStatus != "ok") {
                $webhookErrors['webhook_positive'] = "{$positiveHookStatus}";
            }

            $webhookUrls['positive'] = $request->webhook_positive;
        }

        if(@count($webhookErrors)) {
            return back()->withInput()->withErrors($webhookErrors);
        }

        $request->merge(['webhooks'=>$webhookUrls]);

        // catch the newly created team to know where to redirect
        $team = $this->interaction($request, CreateTeam::class, [
            Auth::user()->agency->owner, $request->all(),
        ]);

        if ($team) {
            return redirect(route('clients.show', $team))
                ->with([
                    'status'        => 'success',
                    'msg'           => 'Client added successfully.',
                    'intercom_event'=> 'client_created',
                    'intercom_data' => $team->toJson(),
                ]);
        }

        return redirect('/clients/')->with(['status'=>'danger', 'msg'=>'An error occured while adding new client']);
    }

    /**
     * Display the client/team details.
     *
     * @param \App\Team $team
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\View\View
     */
    public function show(Team $team, Request $request)
    {
        // check client access
        if (Auth::user()->cannot('agency-admin') && Auth::user()->teams()->first()->id != $team->id) {
            return redirect(route('clients.show', Auth::user()->teams()->first()));
        }

        $team->load(['notArchivedCampaigns', 'emailAccounts', 'positiveProspects']);

        return view('clients.client-show', compact('team'));
    }

    /**
     * Display the edit form of client/team.
     *
     * @param \App\Team $team
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\View\View
     */
    public function edit(Team $team, Request $request)
    {
        return view('clients.client-edit', compact('team'));
    }

    /**
     * Update a client/team.
     *
     * @param Team $team
     * @param \App\Http\Requests\ClientFormRequest $request
     * @throws \Illuminate\Validation\ValidationException
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Team $team, ClientFormRequest $request)
    {
        $this->validate($request, [
            'max_emails' => 'required|digits_between:0,3',
            'name'       => 'required',
            'webhook_replied'   => 'nullable|url',
            'webhook_positive'   => 'nullable|url',
        ]);

        $objMsg = ['status'=>'danger', 'msg'=>"An error occured while updating client's details"];
        $intStatus = 422;

        $webhookUrls = [];
        $webhookErrors = [];
        $webhookAjaxErrors = [];
        if(!empty($request->webhook_replied)) {
            $repliedHookStatus = $this->testWebhookUrl($request->webhook_replied, 'contact_replied');
            if($repliedHookStatus != "ok") {
                $webhookErrors['webhook_replied'] = "{$repliedHookStatus}";
                $webhookAjaxErrors['webhook_replied'] = ["{$repliedHookStatus}"];
            }

            $webhookUrls['replied'] = $request->webhook_replied;
        }
        if(!empty($request->webhook_positive)) {
            $positiveHookStatus = $this->testWebhookUrl($request->webhook_positive, 'contact_positive');
            if($positiveHookStatus != "ok") {
                $webhookErrors['webhook_positive'] = "{$positiveHookStatus}";
                $webhookAjaxErrors['webhook_positive'] = ["{$positiveHookStatus}"];
            }

            $webhookUrls['positive'] = $request->webhook_positive;
        }

        if(@count($webhookErrors) || @count($webhookAjaxErrors)) {
            if($request->ajax()){
                return response()->json(["errors"=>$webhookAjaxErrors], 422);
            } else {
                return back()->withInput()
                    ->withErrors($webhookErrors);
            }
        }

        $updateTeam = $team->update([
            'name'          =>$request->name,
            'max_emails'    =>$request->max_emails,
            'webhooks'      => @count($webhookUrls) ? $webhookUrls : null,
            'webhook_errors'=> null
        ]);

        if ($updateTeam) {
            $objMsg = ['status'=>'success', 'msg'=>'Client updated successfully.'];
            $intStatus = 200;
        }

        if($request->ajax()){
            return response()->json($objMsg, $intStatus);
        }

        return redirect(route('clients.show', $team))->with($objMsg);
    }

    /**
     * Display the delete confirmation of client/team.
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function delete(Team $team, Request $request)
    {
        if(Auth::user()->agency->isUserDashboard()){
            return redirect('home');
        }

        $emailAccounts = $team->emailAccounts()->active()->get();
        return view('clients.client-delete', compact(['team', 'emailAccounts']));
    }

    /**
     * Return a list of clients/teams for the AJAX, paginated and filtered if there are filter data.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request)
    {
        $intLimit = $request->input('per_page', 10);
        $agencyId = decodeModelHashid(Agency::class, $request->input('agency_id', null));
        $strKeywords = $request->input('keywords', null);

        // check if user has access to the requested agency
        if (Auth::user()->can('support') || (Auth::user()->can('agency-admin') && Auth::user()->agency_id == $agencyId)) {
            $teams = Team::ofAgency($agencyId)
                // ->where('agency_id', $agencyId)
                ->ofKeywords($strKeywords, ['agency_id' => $agencyId])
                ->withCount(['notArchivedCampaigns', 'emailAccounts', 'positiveProspects','users'])
                ->with(['agency'=>function($q){
                    $q->select('name', 'id');
                }])
                ->orderBy('name', 'asc')
                ->paginate($intLimit);

            return response()->json([
                'status'    => 'success',
                'teams'     => $teams,
            ]);
        } else {
            return response()->json([
                'status'    => 'error',
                'msg'     => 'Unauthorized',
            ], 403);
        }
    }

    public function webhookTest(Request $request)
    {
        $this->validate($request, [
            'webhook_replied'   => 'nullable|url',
            'webhook_positive'   => 'nullable|url',
        ]);

        $testedWebhooks = [];
        $webhookAjaxErrors = [];
        if(!empty($request->webhook_replied)) {
            $repliedHookStatus = $this->testWebhookUrl($request->webhook_replied, 'contact_replied');
            if($repliedHookStatus != "ok") {
                $webhookAjaxErrors['webhook_replied'] = ["{$repliedHookStatus}"];
            } else {
                $testedWebhooks['webhook_replied'] = 'success';
            }
        }
        if(!empty($request->webhook_positive)) {
            $positiveHookStatus = $this->testWebhookUrl($request->webhook_positive, 'contact_positive');
            if($positiveHookStatus != "ok") {
                $webhookAjaxErrors['webhook_positive'] = ["{$positiveHookStatus}"];
            } else {
                $testedWebhooks['webhook_positive'] = 'success';
            }
        }

        return response()->json([
            'status' => 'success',
            'msg' => 'Webhooks tested successfully.',
            'tested_webhooks' => $testedWebhooks,
            "errors" => $webhookAjaxErrors
        ], 200);
    }

    // $event: contact_replied, contact_positive
    public function testWebhookUrl($url, $event)
    {
        $statusMsg = "Failed to reach webhook url";
        $testData = [
            'webhook_event' => $event,
            'id' => 1,
            'date' => now()->format('M jS Y, g:iA'),
            'contact_firstname' => 'John',
            'contact_lastname' => 'Smith',
            'contact_name' => 'John Smith',
            'contact_email' => '<EMAIL>',
            'contact_id' => '1',
            'campaign_name' => 'Webhook Connection Test',
            'campaign_id' => 'abc123',
            'team_id' => 'def456',
            'company' => 'Example',
            'industry' => 'Technology',
            'website' => 'example.com',
            'title' => 'CEO',
            'phone' => '09xx-xxx-xxxx',
            'address' => '',
            'city' => 'Los Angeles',
            'state' => 'California',
            'country' => 'USA',
        ];

        if($event == 'contact_replied') {
            $testData['subject'] = 'Testing webhook connection';
            $testData['snippet'] = 'Testing webhook connection';
            $testData['message_raw'] = 'Testing webhook connection';
            $testData['message_body'] = 'Testing webhook connection';
        }

        if($event == 'contact_positive') {
            $testData['interest'] = 'POSITIVE';
        }

        try {
            $http = new HttpClient();
            $response = $http->post($url, [
                'headers' => [
                    'Accept' => 'application/json',
                ],
                'json' => $testData
            ]);

            $status = $response->getStatusCode();
            $statusMsg = "ok";
        } catch (ConnectException $e) {
            $statusMsg = "Could not resolve host: {$url}";
            Log::error(json_encode($e->getMessage(), JSON_INVALID_UTF8_IGNORE));
        } catch (RequestException $e) {
            $status = $e->getResponse()->getStatusCode();
            $statusMsg = "Webhook url failed with status {$status}";
            Log::error(json_encode($e->getMessage(), JSON_INVALID_UTF8_IGNORE));
        } catch (\Throwable $e) {
            $statusMsg = "Webhook test failed with error: {$e->getMessage()}";
            Log::error("Webhook test failed: {$e->getMessage()}");
        }

        return $statusMsg;
    }
}
