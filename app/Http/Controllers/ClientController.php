<?php

namespace App\Http\Controllers;

use App\User;
use App\Spark;
use <PERSON><PERSON>\Spark\Invitation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\Spark\Events\Profile\ContactInformationUpdated;

class ClientController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth']);
    }

    /**
     * Show all clients of an Agency admin.
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        $clients = Spark::user()->where('id', '!=', Auth::user()->id);

        if (Auth::user()->isAdmin() || Auth::user()->isSupport()) {
            $clients = $clients->whereDoesntHave('roles', function ($query) {
                $query->where('name', '=', 'admin')
                    ->orWhere('name', '=', 'staff');
            })->get();
        } else {
            $clients = $clients->whereAgencyId(Auth::user()->agency_id)->whereDoesntHave('roles')->get();
        }

        $clients->load(['subscriptions', 'roles', 'agency', 'teams.subscriptions']);

        return view('clients.index', compact('clients'));
    }

    /**
     * Update a client's contact information.
     *
     * @param Request $request
     * @throws \Illuminate\Auth\Access\AuthorizationException
     * @return mixed
     */
    public function update(Request $request)
    {
        $user = User::findOrFail($request['id']);

        $this->authorize('customer.edit');
        $this->authorize('update', $user);

        $this->validate($request, [
            'name'  => 'required|max:255',
            'email' => 'required|email|unique:users,email,'.$user->id,
        ]);

        $user->forceFill([
            'company' => $request['company'],
            'name'    => $request['name'],
            'email'   => $request['email'],
        ])->save();

        event(new ContactInformationUpdated($user));

        $user->load(['subscriptions', 'roles', 'agency', 'teams.subscriptions']);

        return $user;
    }

    /**
     * Cancel / delete the given invitation.
     *
     * @param  Request  $request
     * @param  \Laravel\Spark\Invitation  $invitation
     * @return Response
     */
    public function destroy_invite(Request $request, Invitation $invitation)
    {
        $invitation->delete();
    }
}
