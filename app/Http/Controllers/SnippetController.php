<?php

namespace App\Http\Controllers;

use App\Snippet;
use App\Campaign;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SnippetController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Campaign  $campaign
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Campaign $campaign)
    { 
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255'
        ]);

        // check if validation failed
        if ($validator->fails()) { 
            return response()->json([
                        'errors'=>$validator->errors()->all()
                    ], 422);
        }

        $snippetName = Str::slug(strtolower($request->name), '_');

        // check if snippet exists in default fields
        $defaultFields = [
            'first_name', 'last_name', 'email', 'company', 'industry', 'website',
            'title', 'phone', 'address', 'city', 'state', 'country', 'tags'
        ];

        if(in_array($snippetName, $defaultFields)){
            return response()->json([
                'status' => 'failed', 
                'message' => $request->name . ' ('.$snippetName.') field already exists for this campaign.'
            ], 422);
        }

        // if everything is validated, create the snippet object
        $snippet = Snippet::firstOrNew([
            'name' => $snippetName, 
            'campaign_id' => $campaign->id
        ]);

        // if this snippet exists in database, return a duplicate error
        if($snippet->exists)
        {
            return response()->json([
                'status' => 'failed', 
                'message' => $request->name . ' ('.$snippetName.') field already exists for this campaign.'
            ], 422);
        }
        else
        {
            // if not, save this snippet object to database
            $snippet->save();
        }
 
        return response()->json([
                'status' => 'success', 
                'snippet' => $snippet
            ]);
    }

    /**
     * Delete the snippet from database.
     *
     * @param  \App\Campaign  $campaign
     * @param  \App\Snippet  $snippet
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\Response
     */
    public function destroy(Campaign $campaign, Snippet $snippet)
    {
        // do not delete prompt's snippets
        if($snippet->type == 'prompt') {
            return response()->json([
                'status'=> 'failed',
                'snippet'=> null
            ], 422);
        }

        try {
            $snippet->delete();
        } catch (\Exception $e) {

            return response()->json([
                'status'=> 'failed',
                'snippet'=> null
            ], 422);
        }

        return response()->json([
                'status' => 'success',
                'snippet' => $snippet->id
            ]);
    }

    /**
     * Delete the snippet from database, by its name.
     *
     * @param  \App\Campaign  $campaign
     * @param  String  $snippetName
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\Response
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function destroyByName(Campaign $campaign, string $snippetName)
    {
        $snippet = $campaign->snippets()->where('name', $snippetName)->first();

        $this->authorize('delete', $snippet);

        // do not delete prompt's snippets
        if($snippet->type == 'prompt') {
            return response()->json([
                'status'=> 'failed',
                'snippet'=> null
            ], 422);
        }

        try {
            $snippet->delete();
        } catch (\Exception $e) {

            return response()->json([
                'status'=> 'failed',
                'snippet'=> null
            ], 422);
        }

        return response()->json([
            'status' => 'success',
            'snippet' => $snippet->id
        ]);
    }

    /**
     * Get all the snippet(custom fields) of a campaign.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function ofCampaign(Campaign $campaign)
    {
        // fetch the snippets (custom fields)
        $snippets = $campaign->snippets()->select('name', 'id')->get();
        //$snippets = Snippet::where('campaign_id',$campaign->id)
        //                ->select('name', 'id')
        //                ->get();

        return response()->json([
                'status'=>'success',
                'snippets'=>$snippets
            ]);
    }
}
