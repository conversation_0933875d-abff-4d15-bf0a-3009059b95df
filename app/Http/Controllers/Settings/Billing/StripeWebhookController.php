<?php

namespace App\Http\Controllers\Settings\Billing;

use App\Agency;
use App\Events\AgencySubscription\AgencySubscribed;
use App\Events\AgencySubscription\AgencySubscriptionCancelled;
use App\Events\AgencySubscription\AgencySubscriptionRenewed;
use App\Events\AgencySubscription\AgencySubscriptionUpdated;
use App\Events\Subscription\SubscriptionCancelled;
use App\Spark;
use App\Traits\SendsInvoiceNotifications;
use App\User;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Laravel\Cashier\Http\Controllers\WebhookController;
use Lara<PERSON>\Cashier\Subscription;
use Laravel\Spark\Contracts\Repositories\LocalInvoiceRepository;
use Lara<PERSON>\Spark\Events\Teams\Subscription\SubscriptionCancelled as TeamSubscriptionCancelled;
use Stripe\Subscription as StripeSubscription;

class StripeWebhookController extends WebhookController
{
    use SendsInvoiceNotifications;

    /**
     * Handle a successful invoice payment from a Stripe subscription.
     *
     * By default, this e-mails a copy of the invoice to the customer.
     *
     * @param  array  $payload
     * @return Response
     */
    protected function handleInvoicePaymentSucceeded(array $payload)
    {
        $user = $this->getUserByStripeId(
            $payload['data']['object']['customer']
        );

        if (is_null($user)) {
            return $this->teamInvoicePaymentSucceeded($payload);
        }

        $invoice = $user->findInvoice($payload['data']['object']['id']);

        if (($invoice->rawTotal()/100) > 0) {
            app(LocalInvoiceRepository::class)->createForUser($user, $invoice);

            $this->sendInvoiceNotification(
                $user, $invoice
            );
        }

        // Find if it is an invoice for agency subscription (monthly invoice and renew limits)
        $subscriptionId = $payload['data']['object']['subscription'];
        $agency = $user->agency;
        $agencySubscription = $agency->subscriptions()->where('stripe_id', $subscriptionId)->first();
        if ($agencySubscription) {
            $plan = Spark::agencyPlans()->where('id', $agency->current_billing_plan)->first();
            if ($plan->hasContactsLimit()) {
                foreach ($payload['data']['object']['lines']['data'] as $invoiceItem) {
                    if ($invoiceItem['plan']['id'] == $plan->id) {
                        $agency->update([
                            'es_contacts_limit' => $plan->attributes['es_contacts_limit']
                        ]);
                    }
                }
            }
            if ($agency->has_ecommerce && $agency->wavo_version == 3 && $payload['data']['object']['status'] === 'paid' && $payload['data']['object']['billing_reason'] === 'subscription_cycle') {
                foreach ($payload['data']['object']['lines']['data'] as $invoiceItem) {
                    if ($invoiceItem['plan']['id'] == $plan->id) {
                        event(new AgencySubscriptionRenewed($agency, $plan->id));
                    }
                }
            }
        }

        return new Response('Webhook Handled', 200);
    }

    /**
     * Handle a successful invoice payment from a Stripe subscription.
     *
     * @param  array  $payload
     * @return Response
     */
    protected function teamInvoicePaymentSucceeded(array $payload)
    {
        $team = Spark::team()->where(
            'stripe_id', $payload['data']['object']['customer']
        )->first();

        if (is_null($team)) {
            return;
        }

        $invoice = $team->findInvoice($payload['data']['object']['id']);

        if (($invoice->rawTotal()/100) > 0) {
            app(LocalInvoiceRepository::class)->createForTeam($team, $invoice);

            $this->sendInvoiceNotification(
                $team, $invoice
            );
        }

        return new Response('Webhook Handled', 200);
    }

    protected function handleCustomerUpdated(array $payload)
    {
        if ($user = $this->getUserByStripeId($payload['data']['object']['id'])) {
            info('Received stripe event customerUpdated for user:'.$user->id);
            $user->updateDefaultPaymentMethodFromStripe();
            if (!empty($user->agency->stripe_id))
            $user->agency->updateDefaultPaymentMethodFromStripe();
        } else {
            // Use data to update existing user with unused stripe id (on free trial)
            $user = User::where('email', $payload['data']['object']['email'])->first();
            if ($user) {
                $agency = $user->agency;
                if ($user && empty($user->pm_last_four) && (empty($agency->current_billing_plan) || $agency->current_billing_plan === 'free')) {
                    $user->forceFill([
                        'stripe_id' => $payload['data']['object']['id']
                    ])->save();
                    $agency->forceFill([
                        'stripe_id' => $payload['data']['object']['id']
                    ]);
                    $user->updateDefaultPaymentMethodFromStripe();
                    $user->agency->updateDefaultPaymentMethodFromStripe();
                }
            }
        }

        return $this->successMethod();
    }

    /**
     * Handle a cancelled customer from a Stripe subscription.
     *
     * @param  array  $payload
     * @return Response|\Symfony\Component\HttpFoundation\Response
     */
    protected function handleCustomerSubscriptionDeleted(array $payload)
    {
        if (isset($payload['data']['object']['plan'])) {
            if (Spark::agencyPlans()->contains('id', $payload['data']['object']['plan']['id'])) {
                 $this->agencySubscriptionDeleted($payload);

                 return $this->successMethod();
            }

            parent::handleCustomerSubscriptionDeleted($payload);

            $user = $this->getUserByStripeId($payload['data']['object']['customer']);
            $plan = Spark::plans()->where('id', $payload['data']['object']['plan']['id'])->first();

            if (! $user) {
                return $this->teamSubscriptionDeleted($payload);
            }

            event(new SubscriptionCancelled($user, $plan));

            return $this->successMethod();
        } else {
            // multi-plan subscriptions, like wavo3 agency subscriptions
            parent::handleCustomerSubscriptionDeleted($payload);

            // Multi-plan subscription
            $user = $this->getUserByStripeId($payload['data']['object']['customer']);

            foreach ($payload['data']['object']['items']['data'] as $subscriptionItem) {
                if (Spark::agencyPlans()->contains('id', $subscriptionItem['plan']['id'])) {
                    $this->agencySubscriptionItemDeleted($subscriptionItem, $user);
                } else {
                    $plan = Spark::plans()->where('id', $subscriptionItem['plan']['id'])->first();

                    if ($plan) {
                        event(new SubscriptionCancelled($user, $plan));
                    }
                }
            }

            return $this->successMethod();
        }
    }

    protected function handleCustomerSubscriptionUpdated(array $payload)
    {
        $planId = $this->getPlanId($payload);
        if ($planId && Spark::agencyPlans()->contains('id', $planId)) {
            return $this->agencySubscriptionUpdated($payload);
        }

        return parent::handleCustomerSubscriptionUpdated($payload);
    }

    /**
     * Handle a cancelled customer from a Stripe subscription.
     *
     * @param  array  $payload
     * @return Response
     */
    protected function teamSubscriptionDeleted(array $payload)
    {
        $team = Spark::team()->where(
            'stripe_id', $payload['data']['object']['customer']
        )->first();

        if ($team) {
            $team->subscriptions->filter(function ($subscription) use ($payload) {
                return $subscription->stripe_id === $payload['data']['object']['id'];
            })->each(function ($subscription) {
                $subscription->markAsCanceled();
            });
        } else {
            return new Response('Webhook Handled', 200);
        }

        event(new TeamSubscriptionCancelled($team));

        return new Response('Webhook Handled', 200);
    }

    /**
     * Handle a cancelled customer from a Stripe subscription.
     *
     * @param  array  $payload
     * @return void
     */
    protected function agencySubscriptionDeleted(array $payload)
    {
        // check if the deleted subscription is 'user-dashboard'
        // no need to delete 'user-dashboard' subscription as it was replaced with 'agency-dashboard'
        // email subscriptions are swapped with 'agency-inboxes-monthly' during the plan upgrade
        if ($payload['data']['object']['plan']['id'] == 'user-dashboard') {

            return;
        }

        $agency = $this->getAgencyByStripeId($payload['data']['object']['customer']);

        if ($agency) {
            $agency->subscriptions->filter(function ($subscription) use ($payload) {
                return $subscription->stripe_id === $payload['data']['object']['id'];
            })->each(function ($subscription) {
                $subscription->markAsCanceled();
            });
        }

        event(new AgencySubscriptionCancelled($agency));
    }

    protected function agencySubscriptionItemDeleted(array $payload, $user)
    {
        // check if the deleted subscription is 'user-dashboard'
        // no need to delete 'user-dashboard' subscription as it was replaced with 'agency-dashboard'
        // email subscriptions are swapped with 'agency-inboxes-monthly' during the plan upgrade
        if ($payload['plan'] && $payload['plan']['id'] == 'user-dashboard') {

            return;
        }

        $agency = $user->agency;

        if ($agency) {
            $agency->subscriptions->filter(function ($subscription) use ($payload) {
                return $subscription->stripe_id === $payload['subscription'];
            })->each(function ($subscription) {
                $subscription->markAsCanceled();
            });
        }

        event(new AgencySubscriptionCancelled($agency));
    }

    /**
     * Handle agency subscription updated.
     *
     * @param array $payload
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \Exception
     */
    protected function agencySubscriptionUpdated(array $payload)
    {
        if ($agency = $this->getAgencyByStripeId($payload['data']['object']['customer'])) {
            $data = $payload['data']['object'];

            $subscription = $agency->subscriptions()->where('stripe_id', $data['id'])->first();

            if (!$subscription) {
                return $this->successMethod();
            }

            if (
                isset($data['status']) &&
                $data['status'] === StripeSubscription::STATUS_INCOMPLETE_EXPIRED
            ) {
                $subscription->items()->delete();
                $subscription->delete();

                event(new AgencySubscriptionCancelled($agency));

                return $this->successMethod();
            }

            $firstItem = $data['items']['data'][0];
            $isSinglePlan = count($data['items']['data']) === 1;

            // Plan...
            $subscription->stripe_price = $isSinglePlan ? $firstItem['price']['id'] : null;

            // Trial ending date...
            if (isset($data['trial_end'])) {
                $trialEnd = Carbon::createFromTimestamp($data['trial_end']);

                if (! $subscription->trial_ends_at || $subscription->trial_ends_at->ne($trialEnd)) {
                    $subscription->trial_ends_at = $trialEnd;
                }
            }

            // Cancellation date...
            if (isset($data['cancel_at_period_end'])) {
                if ($data['cancel_at_period_end']) {
                    $subscription->ends_at = $subscription->onTrial()
                        ? $subscription->trial_ends_at
                        : Carbon::createFromTimestamp($data['current_period_end']);
                } elseif (isset($data['cancel_at'])) {
                    $subscription->ends_at = Carbon::createFromTimestamp($data['cancel_at']);
                } else {
                    $subscription->ends_at = null;
                }
            }

            // Status...
            if (isset($data['status'])) {
                $subscription->stripe_status = $data['status'];
            }

            $subscription->save();

            // Update subscription items...
            if (isset($data['items'])) {
                $plans = [];

                foreach ($data['items']['data'] as $item) {
                    $plans[] = $item['price']['id'];

                    $subscription->items()->updateOrCreate([
                        'stripe_id' => $item['id'],
                    ], [
                        'stripe_price' => $item['price']['id'],
                        'quantity' => $item['quantity'] ?? null,
                    ]);
                }

                // Delete items that aren't attached to the subscription anymore...
                $subscription->items()->whereNotIn('stripe_price', $plans)->delete();
            }

            event(new AgencySubscriptionUpdated($agency, $agency->current_billing_plan, $subscription->fresh()->provider_plan));
        }

        return $this->successMethod();
    }

    /**
     * Get the customer instance by Stripe ID.
     *
     * @param  string  $stripeId
     * @return \App\Agency|null
     */
    public function getAgencyByStripeId($stripeId)
    {
        if ($stripeId === null) {
            return;
        }

        return Agency::where('stripe_id', $stripeId)->first();
    }

    /**
     * Handle customer subscription created. Should only be called for subscriptions created from the stripe dashboard.
     * Override cashier's method to add handling for agency subscriptions.
     *
     * @param  array $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleCustomerSubscriptionCreated(array $payload)
    {
        $user = $this->getUserByStripeId($payload['data']['object']['customer']);
        info('Received stripe event CustomerSubscriptionCreated for user:'.$user->id);

        if ($user) {
            $data = $payload['data']['object'];

            if (! $user->subscriptions->contains('stripe_id', $data['id']) &&
                ! $user->agency->subscriptions->contains('stripe_id', $data['id'])
            ) {
                if (!empty($data['trial_end'])) {
                    $trialEndsAt = Carbon::createFromTimestamp($data['trial_end']);
                } else {
                    $trialEndsAt = null;
                }

                $firstItem = $data['items']['data'][0];
                $isSinglePlan = count($data['items']['data']) === 1;
                $stripePlan = $firstItem['price']['id'];

                if (Spark::agencyPlans()->contains('id', $stripePlan)) {
                    $agency = $user->agency;
                    $subscription = $agency->subscriptions()->create([
                        'name' => $data['metadata']['name'] ?? $this->newSubscriptionName($payload),
                        'stripe_id' => $data['id'],
                        'stripe_status' => $data['status'],
                        'stripe_price' => $isSinglePlan ? $firstItem['price']['id'] : null,
                        'quantity' => $isSinglePlan && isset($firstItem['quantity']) ? $firstItem['quantity'] : null,
                        'trial_ends_at' => $trialEndsAt,
                        'ends_at' => null,
                    ]);
                    foreach ($data['items']['data'] as $item) {
                        $subscription->items()->create([
                            'stripe_id' => $item['id'],
                            'stripe_price' => $item['price']['id'],
                            'quantity' => $item['quantity'] ?? null,
                        ]);
                    }

                    if ($agency->wavo_version === 3) {
                        $newPlan = Spark::agencyPlans()->where('id', $stripePlan)->first();
                        info('Subscribe agency:'.$agency->id.' to plan:'. $newPlan->name);
                        $oldPlan = Spark::agencyPlans()->where('id', $agency->current_billing_plan)->first();
                        if (!empty($oldPlan)) {
                            event(new AgencySubscriptionUpdated($agency->fresh(), $oldPlan->id, $newPlan->id));
                        } else {
                            event(new AgencySubscribed($agency->fresh(), $newPlan, false));
                        }
                    }

                } else {
                    $subscription = $user->subscriptions()->create([
                        'name' => $data['metadata']['name'] ?? $this->newSubscriptionName($payload),
                        'stripe_id' => $data['id'],
                        'stripe_status' => $data['status'],
                        'stripe_price' => $isSinglePlan ? $firstItem['price']['id'] : null,
                        'quantity' => $isSinglePlan && isset($firstItem['quantity']) ? $firstItem['quantity'] : null,
                        'trial_ends_at' => $trialEndsAt,
                        'ends_at' => null,
                    ]);

                    foreach ($data['items']['data'] as $item) {
                        $subscription->items()->create([
                            'stripe_id' => $item['id'],
                            'stripe_price' => $item['price']['id'],
                            'quantity' => $item['quantity'] ?? null,
                        ]);
                    }
                }
            }
        }

        return $this->successMethod();
    }

    protected function getPlanId($payload)
    {
        if (isset($payload['data']['object']['plan'])) {
            return $payload['data']['object']['plan']['id'];
        } else {
            foreach ($payload['data']['object']['items']['data'] as $subscriptionItem) {
                $agencyPlan = Spark::agencyPlans()->where('id', $subscriptionItem['plan']['id'])->first();
                if ($agencyPlan) {
                    return $agencyPlan;
                }

                $plan = Spark::plans()->where('id', $subscriptionItem['plan']['id'])->first();
                if ($plan) {
                    return $plan;
                }
            }
        }

        return null;
    }
}
