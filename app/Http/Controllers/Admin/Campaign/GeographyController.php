<?php

namespace App\Http\Controllers\Admin\Campaign;

use App\Geography;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Achillesp\CrudForms\CrudForms;
use App\Http\Controllers\Controller;

class GeographyController extends Controller
{
    use CrudForms;

    /**
     * GeographyController constructor.
     *
     * @param Geography $geography
     * @param Request $request
     */
    public function __construct(Geography $geography, Request $request)
    {
        $this->middleware(['auth', 'can:admin']);

        $this->model = $geography;

        $this->formFields = [
            ['name' => 'name', 'label' => 'Geography Name', 'type' => 'text'],
            ['name' => 'abbreviation', 'label' => 'Abbreviation', 'type' => 'text'],
            ['name' => 'type', 'label' => 'Geography Type', 'type' => 'text'],
            ['name' => 'country_id', 'label' => 'Country', 'type' => 'select', 'relationship' => 'country'],
        ];

        $this->indexFields = ['name', 'type'];

        $this->validationRules = [
            'name' => 'required|max:255|string',
            'type' => [
                'required',
                Rule::in(['country', 'state', 'city']),
            ],
        ];

        $this->validationAttributes = [
            'name' => 'Geography Name',
        ];
    }
}
