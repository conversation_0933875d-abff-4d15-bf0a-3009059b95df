<?php

namespace App\Http\Controllers\Spiffy\Webhooks;

use App\Http\Controllers\Controller;
use App\Jobs\SpiffyCustomerSync;
use Illuminate\Support\Facades\Log;

class CheckoutWebhookController extends Controller
{
    /**
     * Get the current user's agency with subscription data.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle()
    {
        $email = request()->get('email');
        $stripeId = request()->get('stripe_customer_id');

        if ($email == '<EMAIL>') {
            // Webhook test email

            return;
        }

        Log::info('Got webhook request from <PERSON>pi<PERSON>. Email:'.$email.' StripeId:'.$stripeId);

        SpiffyCustomerSync::dispatch($email, $stripeId, request()->get('name_first'), request()->get('name_last'));

        return response()->json([
            'status' => 'success'
        ], 200);
    }
}