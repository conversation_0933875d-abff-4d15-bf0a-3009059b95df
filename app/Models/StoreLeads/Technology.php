<?php

namespace App\Models\StoreLeads;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Technology extends Model
{
    public $timestamps = false;

    protected $table = 'sl_technologies';

    protected $guarded = [];

    public function domains()
    {
        return $this->belongsToMany(Domain::class, 'sl_domain_technology', 'technology_id', 'domain_id');
    }
}
