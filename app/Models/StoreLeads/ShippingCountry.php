<?php

namespace App\Models\StoreLeads;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingCountry extends Model
{
    public $timestamps = false;

    protected $table = 'sl_shipping_countries';

    protected $guarded = [];

    public function domains()
    {
        return $this->belongsToMany(Domain::class, 'sl_domain_shipping_country', 'shipping_country_id', 'domain_id');
    }
}
