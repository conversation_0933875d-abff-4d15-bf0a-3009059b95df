<?php

namespace App;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class LinkedinSearchStats extends Model
{
    public $timestamps = false;

    protected $guarded = ['id'];

    public const EVENT_TYPES = [
        1 => 'Scrape search page',
        2 => 'Scrape company page',
        3 => 'Fetch cached company',
        4 => 'Find email address',
        5 => 'Fetch cached email address',
        6 => 'Fetch cached email address of same agency',
        7 => 'Scrape search page error',
        8 => 'Scrape company page error',
    ];

    public static function incrementSearchEvent(LinkedinSearch $linkedinSearch, $eventTypeId, $amount = 1, $time = null)
    {
        if (is_null($time)) {
            $time = now();
        }

        $hourlyStat = $linkedinSearch->linkedinSearchStats()->ofTime($time)->where('event_type_id', $eventTypeId)->first();

        if (is_null($hourlyStat)) {
            $hourlyStat = $linkedinSearch->linkedinSearchStats()->create([
                'agency_id' => $linkedinSearch->agency_id,
                'team_id' => $linkedinSearch->team_id,
                'event_type_id' => $eventTypeId,
                'amount' => intval($amount),
                'created_at' => $time->startOfHour(),
            ]);
        } else {
            $hourlyStat->increment('amount', $amount);
        }

        return $hourlyStat;
    }

    public static function incrementScrapeSearchPage(LinkedinSearch $linkedinSearch, $amount = 1, $time = null)
    {
        return static::incrementSearchEvent($linkedinSearch, 1, $amount, $time);
    }

    public static function incrementScrapeCompanyPage(LinkedinSearch $linkedinSearch, $amount = 1, $time = null)
    {
        return static::incrementSearchEvent($linkedinSearch, 2, $amount, $time);
    }

    public static function incrementCachedCompanyPage(LinkedinSearch $linkedinSearch, $amount = 1, $time = null)
    {
        return static::incrementSearchEvent($linkedinSearch, 3, $amount, $time);
    }

    public static function incrementEmailFind(LinkedinSearch $linkedinSearch, $amount = 1, $time = null)
    {
        return static::incrementSearchEvent($linkedinSearch, 4, $amount, $time);
    }

    public static function incrementCachedEmail(LinkedinSearch $linkedinSearch, $amount = 1, $time = null)
    {
        return static::incrementSearchEvent($linkedinSearch, 5, $amount, $time);
    }

    public static function incrementCachedAgencyEmail(LinkedinSearch $linkedinSearch, $amount = 1, $time = null)
    {
        return static::incrementSearchEvent($linkedinSearch, 6, $amount, $time);
    }

    public static function incrementScrapeSearchPageError(LinkedinSearch $linkedinSearch, $amount = 1, $time = null)
    {
        return static::incrementSearchEvent($linkedinSearch, 7, $amount, $time);
    }

    public static function incrementScrapeCompanyPageError(LinkedinSearch $linkedinSearch, $amount = 1, $time = null)
    {
        return static::incrementSearchEvent($linkedinSearch, 8, $amount, $time);
    }

    public function getEventTypeAttribute()
    {
        return static::EVENT_TYPES[$this->event_type_id];
    }

    public function scopeOfTime($query, $time = null)
    {
        if (is_null($time)) {
            $time = now();
        }

        return $query->where('created_at', $time->startOfHour());
    }

    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function linkedinSearch()
    {
        return $this->belongsTo(LinkedinSearch::class);
    }

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        /*
         * Add a 'user' global scope to restrict search access for simple users.
         * Agency admins can access all searches of their agency.
         * Admins & Staff skip global scope and can access all searches.
         */
        static::addGlobalScope('user', function (Builder $builder) {
            if (! app()->runningInConsole() && auth()->user()) {
                if (!auth()->user()->isAdmin() && !auth()->user()->isStaff() && !auth()->user()->isSupport()) {
                    if (auth()->user()->can('agency-admin')) {
                        $builder->where('agency_id', auth()->user()->agency_id);
                    }
                }
            }
        });
    }
}
