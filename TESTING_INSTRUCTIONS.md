# Campaign Daily Stats Testing Instructions

## Prerequisites

1. **User with Support Permission**: Ensure you have a user account with the `support` permission/role
2. **Database**: Make sure your database is set up and migrations are run
3. **Assets**: Compile the JavaScript assets with the new Vue component

## Step 1: Compile Assets

Run the following command to compile the JavaScript assets including the new Vue component:

```bash
npm run dev
# or for production
npm run production
```

## Step 2: Run the Test Data Seeder

Execute the seeder to create test data:

```bash
php artisan db:seed --class=CampaignDailyStatsTestSeeder
```

This will create:
- **4 Email Accounts** (test1@example.<NAME_EMAIL>)
- **1 Campaign** ("Daily Stats Test Campaign") 
- **3 Campaign Stages** with different follow-up delays
- **12 Email Templates** (4 per stage) with realistic subjects
- **50+ Prospects** distributed across email accounts
- **Daily Schedules** for today with varying sent amounts
- **90 Email Messages** sent today (distributed throughout business hours)
- **Scheduled Prospects** for remaining capacity

## Step 3: Access the Daily Stats View

After running the seeder, you'll see output like:
```
Campaign ID: 123
Campaign Hash: abc123def
Test URL: /campaigns/abc123def/daily-stats
```

### Option A: Via Campaign Stats Page
1. Go to the campaign stats page: `/stats/campaigns/{campaign_hash}`
2. Look for the "View Daily Tracking" button (top-right, only visible to support users)
3. Click the button to access daily stats

### Option B: Direct URL
1. Use the direct URL: `/campaigns/{campaign_hash}/daily-stats`

## Step 4: Test the Features

### 1. **New Contacts Today**
- Should show ~54 new contacts (60% of messages are stage 1)
- Progress percentage vs daily limit (100)

### 2. **Emails Sent Per Account**
- <EMAIL>: ~15 emails
- <EMAIL>: ~20 emails  
- <EMAIL>: ~25 emails
- <EMAIL>: ~30 emails
- Progress bars showing relative distribution

### 3. **Emails Per Stage & Template**
- **Stage 1**: ~54 emails across 4 templates
- **Stage 2**: ~27 emails across 4 templates  
- **Stage 3**: ~9 emails across 4 templates
- Each template shows subject and count

### 4. **Delivery Distribution Chart**
- Timeline chart showing message distribution (9 AM - 5 PM)
- Different colored lines for each email account
- 15-minute intervals with varying message counts

### 5. **Schedule Completion**
- Each account shows: sent/scheduled (completion %)
- Progress bars (green/yellow/red based on completion)
- Details: Sent, Remaining, Max possible

### 6. **Manual Refresh**
- Click "Refresh Data" button to reload stats
- Loading spinner should appear
- Data should update without page reload

## Step 5: Permission Testing

### Test Support Permission
1. **With Support User**: Should see button and access view
2. **Without Support User**: 
   - Button should be hidden on stats page
   - Direct URL should return 403 Forbidden

### Test Campaign Access
1. **Campaign Owner**: Should work if user also has support permission
2. **Different Team**: Should return 403 Forbidden

## Expected Data Distribution

The seeder creates realistic test data:

| Metric | Expected Value |
|--------|----------------|
| Total Emails Today | 90 messages |
| New Contacts | ~54 (stage 1 messages) |
| Account Distribution | 15, 20, 25, 30 per account |
| Time Distribution | 9 AM - 5 PM business hours |
| Stage Distribution | 60% stage 1, 30% stage 2, 10% stage 3 |
| Schedule Completion | 30-80% (random sent amounts) |

## Troubleshooting

### Vue Component Not Loading
- Check browser console for JavaScript errors
- Ensure `npm run dev` completed successfully
- Verify component is included in app.js

### Permission Denied
- Check user has `support` permission: `User::find($id)->can('support')`
- Verify user can read the campaign: `User::find($id)->can('read', $campaign)`

### No Data Showing
- Verify seeder ran successfully
- Check campaign timezone matches your expectations
- Ensure EmailMessages have `origin = 'self'` and `email_template_id != null`

### Chart Not Rendering
- Check Chartist.js is loaded
- Verify chart container element exists
- Check browser console for chart-related errors

## Cleanup

To remove test data:
```sql
-- Replace {campaign_id} with the actual campaign ID from seeder output
DELETE FROM email_messages WHERE campaign_id = {campaign_id};
DELETE FROM scheduled_prospects WHERE schedule_id IN (SELECT id FROM schedules WHERE campaign_id = {campaign_id});
DELETE FROM schedules WHERE campaign_id = {campaign_id};
DELETE FROM prospects WHERE campaign_id = {campaign_id};
DELETE FROM email_templates WHERE campaign_id = {campaign_id};
DELETE FROM campaign_stages WHERE campaign_id = {campaign_id};
DELETE FROM campaign_email_account WHERE campaign_id = {campaign_id};
DELETE FROM campaigns WHERE id = {campaign_id};
DELETE FROM email_accounts WHERE email_address LIKE '<EMAIL>';
```
