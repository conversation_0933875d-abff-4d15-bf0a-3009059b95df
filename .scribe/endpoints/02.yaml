name: Contacts
description: |-

  APIs for managing contacts.
endpoints:
  -
    httpMethods:
      - POST
    uri: api/v1/contacts
    metadata:
      groupName: Contacts
      groupDescription: |-

        APIs for managing contacts.
      subgroup: ''
      subgroupDescription: ''
      title: 'Create a new contact'
      description: 'A new contact can be attached to an existing campaign. As such the campaign_id is required'
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      campaign_id:
        name: campaign_id
        description: 'The id of the campaign.'
        required: true
        example: fkjtf4390kfgu8903nsk
        type: string
        custom: []
      email:
        name: email
        description: 'The email address of the contact.'
        required: true
        example: <EMAIL>
        type: string
        custom: []
      custom_merge_fields:
        name: custom_merge_fields
        description: ''
        required: false
        example: null
        type: object
        custom: []
      first_name:
        name: first_name
        description: 'The first name of the contact.'
        required: false
        example: aut
        type: string
        custom: []
      last_name:
        name: last_name
        description: 'The last name of contact.'
        required: false
        example: aut
        type: string
        custom: []
      company:
        name: company
        description: 'The company of the contact.'
        required: false
        example: aut
        type: string
        custom: []
      industry:
        name: industry
        description: 'The industry of the contact.'
        required: false
        example: aut
        type: string
        custom: []
      website:
        name: website
        description: 'The website of the contact.'
        required: false
        example: aut
        type: string
        custom: []
      title:
        name: title
        description: 'The job title of the contact.'
        required: false
        example: aut
        type: string
        custom: []
      phone:
        name: phone
        description: 'The phone of the contact.'
        required: false
        example: aut
        type: string
        custom: []
      address:
        name: address
        description: 'The address of the contact.'
        required: false
        example: aut
        type: string
        custom: []
      city:
        name: city
        description: 'The city of the contact.'
        required: false
        example: aut
        type: string
        custom: []
      state:
        name: state
        description: 'The state of the contact.'
        required: false
        example: aut
        type: string
        custom: []
      country:
        name: country
        description: 'The country of the contact.'
        required: false
        example: aut
        type: string
        custom: []
      'custom_merge_fields[]':
        name: 'custom_merge_fields[]'
        description: 'An array of merge data to be used in email messages.'
        required: false
        example:
          - aut
        type: 'string[]'
        custom: []
    cleanBodyParameters:
      campaign_id: fkjtf4390kfgu8903nsk
      email: <EMAIL>
      first_name: aut
      last_name: aut
      company: aut
      industry: aut
      website: aut
      title: aut
      phone: aut
      address: aut
      city: aut
      state: aut
      country: aut
      'custom_merge_fields[]':
        - aut
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
              "data": [
                  {
                      "id": "we52o4rv130mz1qxyzk8pmljd",
                      "email": "<EMAIL>",
                      "first_name": "Aditya",
                      "last_name": "Kohler",
                      "company": "Gerlach, Ziemann and Reilly",
                      "industry": "Nonprofit Organization Management",
                      "website": "kunde.com",
                      "title": "Ms.",
                      "phone": "************",
                      "address": "41164 Osvaldo Row",
                      "city": "Baileyborough",
                      "state": "New Hampshire",
                      "country": "US",
                      "custom_merge_fields": {
                          "fav color": "purple",
                          "sport": "running",
                          "os": "Windows NT 5.0"
                      },
                      "timezone": "US\/Mountain",
                      "status": "OK",
                      "interest": null,
                      "emails_sent": 0,
                      "completed_steps": 0,
                      "is_missing_data": false,
                      "is_suppressed": false,
                      "created_at": 1579888600,
                      "campaign": {
                          "id": "fkjtf4390kfgu8903nsk",
                          "name": "Grady-Runolfsson 16",
                          "timezone": "US\/Mountain",
                          "status": "DRAFT",
                          "created_at": 1579888600,
                          "client": {
                              "id": "a83445df46as5432mga",
                              "name": "Grady-Runolfsson",
                              "created_at": 1579888600
                          }
                      }
                  }
          	]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer token'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/contacts/{contact_id}'
    metadata:
      groupName: Contacts
      groupDescription: |-

        APIs for managing contacts.
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a contact'
      description: 'Find a contact by id and display its details.'
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      contact_id:
        name: contact_id
        description: 'The id of the contact.'
        required: true
        example: we52o4rv130mz1qxyzk8pmljd
        type: string
        custom: []
    cleanUrlParameters:
      contact_id: we52o4rv130mz1qxyzk8pmljd
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
              "data": {
                      "id": "we52o4rv130mz1qxyzk8pmljd",
                      "email": "<EMAIL>",
                      "first_name": "Aditya",
                      "last_name": "Kohler",
                      "company": "Gerlach, Ziemann and Reilly",
                      "industry": "Nonprofit Organization Management",
                      "website": "kunde.com",
                      "title": "Ms.",
                      "phone": "************",
                      "address": "41164 Osvaldo Row",
                      "city": "Baileyborough",
                      "state": "New Hampshire",
                      "country": "US",
                      "custom_merge_fields": {
                          "fav color": "purple",
                          "sport": "running",
                          "os": "Windows NT 5.0"
                      },
                      "timezone": "US\/Mountain",
                      "status": "OK",
                      "interest": null,
                      "emails_sent": 0,
                      "completed_steps": 0,
                      "is_missing_data": false,
                      "is_suppressed": false,
                      "created_at": 1579888600,
                      "campaign": {
                          "id": "fkjtf4390kfgu8903nsk",
                          "name": "Grady-Runolfsson 16",
                          "timezone": "US\/Mountain",
                          "status": "DRAFT",
                          "created_at": 1579888600,
                          "client": {
                              "id": "a83445df46as5432mga",
                              "name": "Grady-Runolfsson",
                              "created_at": 1579888600
                          }
                      }
               }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer token'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/contact-interest
    metadata:
      groupName: Contacts
      groupDescription: |-

        APIs for managing contact interest.
      subgroup: ''
      subgroupDescription: ''
      title: 'Update contact interest'
      description: "Set a contact's interest to a value of POSITIVE, NEUTRAL, or NEGATIVE."
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      contact_id:
        name: contact_id
        description: 'The contact ID.'
        required: true
        example: fkjtf4390kfgu8903nsk
        type: string
        custom: []
      interest:
        name: interest
        description: 'The interest to set.'
        required: true
        example: POSITIVE
        type: string
        custom: []
    cleanBodyParameters:
      contact_id: fkjtf4390kfgu8903nsk
      interest: POSITIVE
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
          "message": "Interest updated."
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer token'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: api/v1/contact-interest
    metadata:
      groupName: Contacts
      groupDescription: |-

        APIs for managing contact interest.
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove contact interest'
      description: "Set a contact's interest to UNMARKED."
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      contact_id:
        name: contact_id
        description: 'The contact ID.'
        required: true
        example: fkjtf4390kfgu8903nsk
        type: string
        custom: []
    cleanBodyParameters:
      contact_id: fkjtf4390kfgu8903nsk
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
          "message": "Interest UNMARKED."
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer token'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/campaigns/{campaign_id}/contacts'
    metadata:
      groupName: Contacts
      groupDescription: |-

        APIs for managing contacts.
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of contacts'
      description: 'Return all contacts of a campaign.'
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      campaign_id:
        name: campaign_id
        description: 'The id of a campaign.'
        required: true
        example: fkjtf4390kfgu8903nsk
        type: string
        custom: []
    cleanUrlParameters:
      campaign_id: fkjtf4390kfgu8903nsk
    queryParameters:
      status:
        name: status
        description: 'Get contacts of a specific status. Can be one of: ok, replied, unsubscribed, bounced, autoreplied, stopped.'
        required: false
        example: replied
        type: string
        custom: []
      is_missing_data:
        name: is_missing_data
        description: 'Get contacts with missing data.'
        required: false
        example: '0'
        type: string
        custom: []
      is_suppressed:
        name: is_suppressed
        description: 'Get contacts that are suppressed.'
        required: false
        example: '1'
        type: string
        custom: []
      interest:
        name: interest
        description: 'Get contacts that have a specific interest level. Can be one of: unmarked, positive, neutral, negative.'
        required: false
        example: positive
        type: string
        custom: []
      emails_sent:
        name: emails_sent
        description: 'Get contacts that have been contacted a specific number of times.'
        required: false
        example: 3
        type: integer
        custom: []
      limit:
        name: limit
        description: 'The number of objects to return. Defaults to 100. Maximum 500.'
        required: false
        example: 2
        type: integer
        custom: []
      offset:
        name: offset
        description: 'The zero-based offset for the default object sorting.'
        required: false
        example: '0'
        type: string
        custom: []
    cleanQueryParameters:
      status: replied
      is_missing_data: '0'
      is_suppressed: '1'
      interest: positive
      emails_sent: 3
      limit: 2
      offset: '0'
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
              "data": [
                  {
                      "id": "j5ropgvndm6l1v0z7lwe9481y",
                      "email": "<EMAIL>",
                      "first_name": "Vincent",
                      "last_name": "Kemmer",
                      "company": "Hartmann, Lockman and Thompson",
                      "industry": "Luxury Goods & Jewelry",
                      "website": "mosciski.com",
                      "title": "Mr.",
                      "phone": "**************",
                      "address": "6166 Jakubowski Cliff Suite 605",
                      "city": "Port Reidville",
                      "state": "North Carolina",
                      "country": "US",
                      "custom_merge_fields": {
                          "fav color": "teal",
                          "sport": "tennis",
                          "os": "Windows NT 6.2"
                      },
                      "timezone": "US\/Mountain",
                      "status": "OK",
                      "interest": null,
                      "emails_sent": 1,
                      "completed_steps": 1,
                      "is_missing_data": 1,
                      "is_suppressed": 0,
                      "created_at": 1579888424
                  },
                  {
                      "id": "ny4j2vd8k56248q1wzgeo9r3p",
                      "email": "<EMAIL>",
                      "first_name": "Jacquelyn",
                      "last_name": "Okuneva",
                      "company": "Armstrong, Ferry and Nolan",
                      "industry": "Design",
                      "website": "sanford.com",
                      "title": "Prof.",
                      "phone": "(************* x05539",
                      "address": "1971 Colten Ways",
                      "city": "Batzland",
                      "state": "Kansas",
                      "country": "US",
                      "custom_merge_fields": {
                          "fav color": "silver",
                          "sport": "tennis",
                          "os": "Windows 95"
                      },
                      "timezone": "US\/Mountain",
                      "status": "REPLIED",
                      "interest": "POSITIVE",
                      "emails_sent": 2,
                      "completed_steps": 2,
                      "is_missing_data": 1,
                      "is_suppressed": 0,
                      "created_at": 1579888424
                  }
              ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer token'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/contacts
    metadata:
      groupName: Contacts
      groupDescription: |-

        APIs for managing contacts.
      subgroup: ''
      subgroupDescription: ''
      title: 'Search for contacts'
      description: 'Find contacts of a specific email address.'
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      email:
        name: email
        description: 'The email address we are searching for.'
        required: true
        example: <EMAIL>
        type: string
        custom: []
      client_id:
        name: client_id
        description: 'The id of a client.'
        required: false
        example: a83445df46as5432mga
        type: string
        custom: []
      campaign_id:
        name: campaign_id
        description: 'The id of a campaign.'
        required: false
        example: fkjtf4390kfgu8903nsk
        type: string
        custom: []
    cleanQueryParameters:
      email: <EMAIL>
      client_id: a83445df46as5432mga
      campaign_id: fkjtf4390kfgu8903nsk
    bodyParameters:
      email:
        name: email
        description: 'Must be a valid email address. Must not be greater than 255 characters.'
        required: true
        example: <EMAIL>
        type: string
        custom: []
    cleanBodyParameters:
      email: <EMAIL>
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
              "data": [
                  {
                      "id": "we52o4rv130mz1qxyzk8pmljd",
                      "email": "<EMAIL>",
                      "first_name": "Aditya",
                      "last_name": "Kohler",
                      "company": "Gerlach, Ziemann and Reilly",
                      "industry": "Nonprofit Organization Management",
                      "website": "kunde.com",
                      "title": "Ms.",
                      "phone": "************",
                      "address": "41164 Osvaldo Row",
                      "city": "Baileyborough",
                      "state": "New Hampshire",
                      "country": "US",
                      "custom_merge_fields": {
                          "fav color": "purple",
                          "sport": "running",
                          "os": "Windows NT 5.0"
                      },
                      "timezone": "US\/Mountain",
                      "status": "OK",
                      "interest": null,
                      "emails_sent": 0,
                      "completed_steps": 0,
                      "is_missing_data": 0,
                      "is_suppressed": 0,
                      "created_at": 1579888600,
                      "campaign": {
                          "id": "fkjtf4390kfgu8903nsk",
                          "name": "Grady-Runolfsson 16",
                          "timezone": "US\/Mountain",
                          "status": "DRAFT",
                          "created_at": 1579888600,
                          "client": {
                              "id": "a83445df46as5432mga",
                              "name": "Grady-Runolfsson",
                              "created_at": 1579888600
                          }
                      }
                  }
          	]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer token'
    controller: null
    method: null
    route: null
    custom: []
