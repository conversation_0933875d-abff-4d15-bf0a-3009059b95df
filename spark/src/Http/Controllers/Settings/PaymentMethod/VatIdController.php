<?php

namespace Lara<PERSON>\Spark\Http\Controllers\Settings\PaymentMethod;

use <PERSON><PERSON>\Spark\Spark;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Spark\Http\Controllers\Controller;
use <PERSON><PERSON>\Spark\Contracts\Repositories\UserRepository;

class VatIdController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Update the VAT ID for the user.
     *
     * @param  Request  $request
     * @return Response
     */
    public function update(Request $request)
    {
        $this->validate($request, [
            'vat_id' => 'max:50|vat_id',
        ]);

        Spark::call(UserRepository::class.'@updateVatId', [
            $request->user(), $request->vat_id
        ]);
    }
}
