<?php

namespace <PERSON><PERSON>\Spark\Http\Controllers\Settings\Teams;

use <PERSON><PERSON>\Spark\Spark;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Spark\Invitation;
use <PERSON><PERSON>\Spark\Http\Controllers\Controller;
use <PERSON><PERSON>\Spark\Contracts\Repositories\TeamRepository;
use <PERSON><PERSON>\Spark\Http\Requests\Settings\Teams\CreateInvitationRequest;
use <PERSON><PERSON>\Spark\Contracts\Interactions\Settings\Teams\SendInvitation;

class MailedInvitationController extends Controller
{
    /**
     * The team repository implementation.
     *
     * @var \Laravel\Spark\Contracts\Repositories\TeamRepository
     */
    protected $teams;

    /**
     * Create a new controller instance.
     *
     * @param  TeamRepository  $teams
     * @return void
     */
    public function __construct(TeamRepository $teams)
    {
        $this->teams = $teams;

        $this->middleware('auth');
    }

    /**
     * Get all of the mailed invitations for the given team.
     *
     * @param  Request  $request
     * @param  \Laravel\Spark\Team  $team
     * @return Response
     */
    public function all(Request $request, $team)
    {
        abort_unless($request->user()->onTeam($team), 404);

        return $team->invitations;
    }

    /**
     * Create a new invitation.
     *
     * @param  CreateInvitationRequest  $request
     * @param  \Laravel\Spark\Team  $team
     * @return Response
     */
    public function store(CreateInvitationRequest $request, $team)
    {
        Spark::interact(SendInvitation::class, [$team, $request->email]);
    }

    /**
     * Cancel / delete the given invitation.
     *
     * @param  Request  $request
     * @param  \Laravel\Spark\Invitation  $invitation
     * @return Response
     */
    public function destroy(Request $request, Invitation $invitation)
    {
        abort_unless($request->user()->ownsTeam($invitation->team), 404);

        $invitation->delete();
    }
}
