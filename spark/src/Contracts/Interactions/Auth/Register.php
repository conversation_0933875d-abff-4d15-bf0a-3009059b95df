<?php

namespace Lara<PERSON>\Spark\Contracts\Interactions\Auth;

use <PERSON><PERSON>\Spark\Contracts\Http\Requests\Auth\RegisterRequest;

interface Register
{
    /**
     * Register a new user with the application.
     *
     * @param  \Laravel\Spark\Http\Requests\Auth\RegisterRequest  $request
     * @return \Illuminate\Contracts\Auth\Authenticatable
     */
    public function handle(RegisterRequest $request);
}
