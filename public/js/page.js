(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('/Site', ['exports', 'jquery', 'Base', 'Menubar', 'Sidebar', 'PageAside'], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports, require('jquery'), require('Base'), require('Menubar'), require('Sidebar'), require('PageAside'));
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports, global.jQuery, global.Base, global.SectionMenubar, global.SectionSidebar, global.SectionPageAside);
    global.Site = mod.exports;
  }
})(this, function (exports, _jquery, _Base2, _Menubar, _Sidebar, _PageAside) {
  'use strict';

  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.getInstance = exports.run = exports.Site = undefined;

  var _jquery2 = babelHelpers.interopRequireDefault(_jquery);

  var _Base3 = babelHelpers.interopRequireDefault(_Base2);

  var _Menubar2 = babelHelpers.interopRequireDefault(_Menubar);

  var _Sidebar2 = babelHelpers.interopRequireDefault(_Sidebar);

  var _PageAside2 = babelHelpers.interopRequireDefault(_PageAside);

  var DOC = document;
  var $DOC = (0, _jquery2.default)(document);
  var $BODY = (0, _jquery2.default)('body');

  var Site = function (_Base) {
    babelHelpers.inherits(Site, _Base);

    function Site() {
      babelHelpers.classCallCheck(this, Site);
      return babelHelpers.possibleConstructorReturn(this, (Site.__proto__ || Object.getPrototypeOf(Site)).apply(this, arguments));
    }

    babelHelpers.createClass(Site, [{
      key: 'initialize',
      value: function initialize() {
        this.startLoading();
        this.initializePluginAPIs();
        this.initializePlugins();

        this.initComponents();
      }
    }, {
      key: 'process',
      value: function process() {
        this.polyfillIEWidth();
        this.initBootstrap();

        this.setupMenubar();
        this.setupFullScreen();
        this.setupMegaNavbar();
        this.setupTour();
        this.setupNavbarCollpase();
        // Dropdown menu setup
        // ===================
        this.$el.on('click', '.dropdown-menu-media', function (e) {
          e.stopPropagation();
        });
      }
    }, {
      key: '_getDefaultMeunbarType',
      value: function _getDefaultMeunbarType() {
        return 'hide';
      }
    }, {
      key: 'menubarType',
      value: function menubarType(type) {
        (0, _jquery2.default)('[data-toggle="menubar"]').each(function () {
          var $this = (0, _jquery2.default)(this);
          var $hamburger = (0, _jquery2.default)(this).find('.hamburger');

          if ($hamburger.length > 0) {
            $hamburger.toggleClass('hided', !(type === 'open'));
          } else {
            $this.toggleClass('hided', !(type === 'open'));
          }
        });
      }
    }, {
      key: 'initComponents',
      value: function initComponents() {
        this.menubar = new _Menubar2.default({
          $el: (0, _jquery2.default)('.site-menubar')
        });

        this.sidebar = new _Sidebar2.default();
        var $aside = (0, _jquery2.default)('.page-aside');
        if ($aside.length > 0) {
          this.aside = new _PageAside2.default({
            $el: $aside
          });

          this.aside.run();
        }

        this.menubar.run();
        this.sidebar.run();
      }
    }, {
      key: 'getCurrentBreakpoint',
      value: function getCurrentBreakpoint() {
        var bp = Breakpoints.current();
        return bp ? bp.name : 'lg';
      }
    }, {
      key: 'initBootstrap',
      value: function initBootstrap() {
        // Tooltip setup
        // =============
        $DOC.tooltip({
          selector: '[data-tooltip=true]',
          container: 'body'
        });

        (0, _jquery2.default)('[data-toggle="tooltip"]').tooltip();
        (0, _jquery2.default)('[data-toggle="popover"]').popover();
      }
    }, {
      key: 'polyfillIEWidth',
      value: function polyfillIEWidth() {
        if (navigator.userAgent.match(/IEMobile\/10\.0/)) {
          var msViewportStyle = DOC.createElement('style');
          msViewportStyle.appendChild(DOC.createTextNode('@-ms-viewport{width:auto!important}'));
          DOC.querySelector('head').appendChild(msViewportStyle);
        }
      }
    }, {
      key: 'setupFullScreen',
      value: function setupFullScreen() {
        if (typeof screenfull !== 'undefined') {
          $DOC.on('click', '[data-toggle="fullscreen"]', function () {
            if (screenfull.enabled) {
              screenfull.toggle();
            }

            return false;
          });

          if (screenfull.enabled) {
            DOC.addEventListener(screenfull.raw.fullscreenchange, function () {
              (0, _jquery2.default)('[data-toggle="fullscreen"]').toggleClass('active', screenfull.isFullscreen);
            });
          }
        }
      }
    }, {
      key: 'setupMegaNavbar',
      value: function setupMegaNavbar() {
        $DOC.on('click', '.navbar-mega .dropdown-menu', function (e) {
          e.stopPropagation();
        }).on('show.bs.dropdown', function (e) {
          var $target = (0, _jquery2.default)(e.target);
          var $trigger = e.relatedTarget ? (0, _jquery2.default)(e.relatedTarget) : $target.children('[data-toggle="dropdown"]');
          var animation = $trigger.data('animation');

          if (animation) {
            var $menu = $target.children('.dropdown-menu');
            $menu.addClass('animation-' + animation).one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function () {
              $menu.removeClass('animation-' + animation);
            });
          }
        }).on('shown.bs.dropdown', function (e) {
          var $menu = (0, _jquery2.default)(e.target).find('.dropdown-menu-media > .list-group');

          if ($menu.length > 0) {
            var api = $menu.data('asScrollable');
            if (api) {
              api.update();
            } else {
              $menu.asScrollable({
                namespace: 'scrollable',
                contentSelector: '> [data-role=\'content\']',
                containerSelector: '> [data-role=\'container\']'
              });
            }
          }
        });
      }
    }, {
      key: 'setupMenubar',
      value: function setupMenubar() {
        var _this2 = this;

        (0, _jquery2.default)(document).on('click', '[data-toggle="menubar"]', function () {
          var type = _this2.menubar.type;

          switch (type) {
            case 'open':
              type = 'hide';
              break;
            case 'hide':
              type = 'open';
              break;
            // no default
          }

          _this2.menubar.change(type);
          _this2.menubarType(type);

          return false;
        });

        (0, _jquery2.default)(document).on('collapsed.site.menu expanded.site.menu', function () {
          var type = _this2.menubar.type;

          if (type === 'open' && _this2.menubar && _this2.menubar.scrollable) {
            _this2.menubar.scrollable.update();
          }
        });

        Breakpoints.on('change', function () {
          _this2.menubar.type = _this2._getDefaultMeunbarType();
          _this2.menubar.change(_this2.menubar.type);
        });
      }
    }, {
      key: 'setupNavbarCollpase',
      value: function setupNavbarCollpase() {
        (0, _jquery2.default)(document).on('click', "[data-target='#site-navbar-collapse']", function (e) {
          var $trigger = (0, _jquery2.default)(this);
          var isClose = $trigger.hasClass('collapsed');
          $BODY.addClass("site-navbar-collapsing");
          $BODY.toggleClass("site-navbar-collapse-show", !isClose);
          setTimeout(function () {
            $BODY.removeClass("site-navbar-collapsing");
          }, 350);
        });
      }
    }, {
      key: 'startLoading',
      value: function startLoading() {
        if (typeof _jquery2.default.fn.animsition === 'undefined') {
          return false;
        }

        /*
        // let loadingType = 'default';
        $BODY.animsition({
          inClass: 'fade-in',
          inDuration: 800,
          loading: true,
          loadingClass: 'loader-overlay',
          loadingParentElement: 'html',
          loadingInner: '\n      <div class="loader-content">\n        <div class="loader-index">\n          <div></div>\n          <div></div>\n          <div></div>\n          <div></div>\n          <div></div>\n          <div></div>\n        </div>\n      </div>',
          onLoadEvent: true
        });
        */
      }
    }, {
      key: 'setupTour',
      value: function setupTour(flag) {
        if (typeof this.tour === 'undefined') {
          if (typeof introJs === 'undefined') {
            return;
          }
          var overflow = (0, _jquery2.default)('body').css('overflow'),
              self = this,
              tourOptions = Config.get('tour');

          this.tour = introJs();

          this.tour.onbeforechange(function () {
            (0, _jquery2.default)('body').css('overflow', 'hidden');
          });

          this.tour.oncomplete(function () {
            (0, _jquery2.default)('body').css('overflow', overflow);
          });

          this.tour.onexit(function () {
            (0, _jquery2.default)('body').css('overflow', overflow);
          });

          this.tour.setOptions(tourOptions);
          (0, _jquery2.default)('.site-tour-trigger').on('click', function () {
            self.tour.start();
          });
        }
        // if (window.localStorage && window.localStorage.getItem('startTour') && (flag !== true)) {
        //   return;
        // } else {
        //   this.tour.start();
        //   window.localStorage.setItem('startTour', true);
        // }
      }
    }]);
    return Site;
  }(_Base3.default);

  var instance = null;

  function getInstance() {
    if (!instance) {
      instance = new Site();
    }
    return instance;
  }

  function run() {
    var site = getInstance();
    site.run();
  }

  exports.Site = Site;
  exports.run = run;
  exports.getInstance = getInstance;
  exports.default = Site;
});
(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('/Plugin/asscrollable', ['exports', 'Plugin'], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports, require('Plugin'));
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports, global.Plugin);
    global.PluginAsscrollable = mod.exports;
  }
})(this, function (exports, _Plugin2) {
  'use strict';

  Object.defineProperty(exports, "__esModule", {
    value: true
  });

  var _Plugin3 = babelHelpers.interopRequireDefault(_Plugin2);

  var NAME = 'scrollable';

  var Scrollable = function (_Plugin) {
    babelHelpers.inherits(Scrollable, _Plugin);

    function Scrollable() {
      babelHelpers.classCallCheck(this, Scrollable);
      return babelHelpers.possibleConstructorReturn(this, (Scrollable.__proto__ || Object.getPrototypeOf(Scrollable)).apply(this, arguments));
    }

    babelHelpers.createClass(Scrollable, [{
      key: 'getName',
      value: function getName() {
        return NAME;
      }
    }, {
      key: 'render',
      value: function render() {
        var $el = this.$el;

        $el.asScrollable(this.options);
      }
    }], [{
      key: 'getDefaults',
      value: function getDefaults() {
        return {
          namespace: 'scrollable',
          contentSelector: "> [data-role='content']",
          containerSelector: "> [data-role='container']"
        };
      }
    }]);
    return Scrollable;
  }(_Plugin3.default);

  _Plugin3.default.register(NAME, Scrollable);

  exports.default = Scrollable;
});
(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('/Plugin/slidepanel', ['exports', 'jquery', 'Plugin'], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports, require('jquery'), require('Plugin'));
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports, global.jQuery, global.Plugin);
    global.PluginSlidepanel = mod.exports;
  }
})(this, function (exports, _jquery, _Plugin2) {
  'use strict';

  Object.defineProperty(exports, "__esModule", {
    value: true
  });

  var _jquery2 = babelHelpers.interopRequireDefault(_jquery);

  var _Plugin3 = babelHelpers.interopRequireDefault(_Plugin2);

  var NAME = 'slidePanel';

  var SlidePanel = function (_Plugin) {
    babelHelpers.inherits(SlidePanel, _Plugin);

    function SlidePanel() {
      babelHelpers.classCallCheck(this, SlidePanel);
      return babelHelpers.possibleConstructorReturn(this, (SlidePanel.__proto__ || Object.getPrototypeOf(SlidePanel)).apply(this, arguments));
    }

    babelHelpers.createClass(SlidePanel, [{
      key: 'getName',
      value: function getName() {
        return NAME;
      }
    }, {
      key: 'render',
      value: function render() {
        if (typeof _jquery2.default.slidePanel === 'undefined') {
          return;
        }
        if (!this.options.url) {
          this.options.url = this.$el.attr('href');
          this.options.url = this.options.url && this.options.url.replace(/.*(?=#[^\s]*$)/, '');
        }

        this.$el.data('slidePanelWrapAPI', this);
      }
    }, {
      key: 'show',
      value: function show() {
        var options = this.options;

        _jquery2.default.slidePanel.show({
          url: options.url
        }, options);
      }
    }], [{
      key: 'getDefaults',
      value: function getDefaults() {
        return {
          closeSelector: '.slidePanel-close',
          mouseDragHandler: '.slidePanel-handler',
          loading: {
            template: function template(options) {
              return '<div class="' + options.classes.loading + '">\n                    <div class="loader loader-default"></div>\n                  </div>';
            },
            showCallback: function showCallback(options) {
              this.$el.addClass(options.classes.loading + '-show');
            },
            hideCallback: function hideCallback(options) {
              this.$el.removeClass(options.classes.loading + '-show');
            }
          }
        };
      }
    }, {
      key: 'api',
      value: function api() {
        return 'click|show';
      }
    }]);
    return SlidePanel;
  }(_Plugin3.default);

  _Plugin3.default.register(NAME, SlidePanel);

  exports.default = SlidePanel;
});
(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('/Plugin/matchheight', ['exports', 'jquery', 'Plugin'], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports, require('jquery'), require('Plugin'));
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports, global.jQuery, global.Plugin);
    global.PluginMatchheight = mod.exports;
  }
})(this, function (exports, _jquery, _Plugin2) {
  'use strict';

  Object.defineProperty(exports, "__esModule", {
    value: true
  });

  var _jquery2 = babelHelpers.interopRequireDefault(_jquery);

  var _Plugin3 = babelHelpers.interopRequireDefault(_Plugin2);

  var NAME = 'matchHeight';

  var MatchHeight = function (_Plugin) {
    babelHelpers.inherits(MatchHeight, _Plugin);

    function MatchHeight() {
      babelHelpers.classCallCheck(this, MatchHeight);
      return babelHelpers.possibleConstructorReturn(this, (MatchHeight.__proto__ || Object.getPrototypeOf(MatchHeight)).apply(this, arguments));
    }

    babelHelpers.createClass(MatchHeight, [{
      key: 'getName',
      value: function getName() {
        return NAME;
      }
    }, {
      key: 'render',
      value: function render() {
        if (typeof _jquery2.default.fn.matchHeight === 'undefined') {
          return;
        }

        var $el = this.$el,
            matchSelector = $el.data('matchSelector');

        if (matchSelector) {
          $el.find(matchSelector).matchHeight(this.options);
        } else {
          $el.children().matchHeight(this.options);
        }
      }
    }], [{
      key: 'getDefaults',
      value: function getDefaults() {
        return {};
      }
    }]);
    return MatchHeight;
  }(_Plugin3.default);

  _Plugin3.default.register(NAME, MatchHeight);

  exports.default = MatchHeight;
});
(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('/Plugin/aspieprogress', ['exports', 'jquery', 'Plugin'], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports, require('jquery'), require('Plugin'));
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports, global.jQuery, global.Plugin);
    global.PluginAspieprogress = mod.exports;
  }
})(this, function (exports, _jquery, _Plugin2) {
  'use strict';

  Object.defineProperty(exports, "__esModule", {
    value: true
  });

  var _jquery2 = babelHelpers.interopRequireDefault(_jquery);

  var _Plugin3 = babelHelpers.interopRequireDefault(_Plugin2);

  var NAME = 'pieProgress';

  var PieProgress = function (_Plugin) {
    babelHelpers.inherits(PieProgress, _Plugin);

    function PieProgress() {
      babelHelpers.classCallCheck(this, PieProgress);
      return babelHelpers.possibleConstructorReturn(this, (PieProgress.__proto__ || Object.getPrototypeOf(PieProgress)).apply(this, arguments));
    }

    babelHelpers.createClass(PieProgress, [{
      key: 'getName',
      value: function getName() {
        return NAME;
      }
    }, {
      key: 'render',
      value: function render() {
        if (!_jquery2.default.fn.asPieProgress) {
          return;
        }

        var $el = this.$el;

        $el.asPieProgress(this.options);
      }
    }], [{
      key: 'getDefaults',
      value: function getDefaults() {
        return {
          namespace: 'pie-progress',
          speed: 30,
          classes: {
            svg: 'pie-progress-svg',
            element: 'pie-progress',
            number: 'pie-progress-number',
            content: 'pie-progress-content'
          }
        };
      }
    }]);
    return PieProgress;
  }(_Plugin3.default);

  _Plugin3.default.register(NAME, PieProgress);

  exports.default = PieProgress;
});
(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('/Plugin/datatables', ['exports', 'jquery', 'Plugin'], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports, require('jquery'), require('Plugin'));
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports, global.jQuery, global.Plugin);
    global.PluginDatatables = mod.exports;
  }
})(this, function (exports, _jquery, _Plugin2) {
  'use strict';

  Object.defineProperty(exports, "__esModule", {
    value: true
  });

  var _jquery2 = babelHelpers.interopRequireDefault(_jquery);

  var _Plugin3 = babelHelpers.interopRequireDefault(_Plugin2);

  var NAME = 'dataTable';

  var DataTable = function (_Plugin) {
    babelHelpers.inherits(DataTable, _Plugin);

    function DataTable() {
      babelHelpers.classCallCheck(this, DataTable);
      return babelHelpers.possibleConstructorReturn(this, (DataTable.__proto__ || Object.getPrototypeOf(DataTable)).apply(this, arguments));
    }

    babelHelpers.createClass(DataTable, [{
      key: 'getName',
      value: function getName() {
        return NAME;
      }
    }, {
      key: 'render',
      value: function render() {
        if (!_jquery2.default.fn.dataTable) {
          return;
        }

        // if ($.fn.dataTable.TableTools) {
        //   // Set the classes that TableTools uses to something suitable for Bootstrap
        //   $.extend(true, $.fn.dataTable.TableTools.classes, {
        //     container: 'DTTT btn-group btn-group float-left',
        //     buttons: {
        //       normal: 'btn btn-outline btn-default',
        //       disabled: 'disabled'
        //     },
        //     print: {
        //       body: 'site-print DTTT_Print'
        //     }
        //   });
        // }

        this.$el.dataTable(this.options);
      }
    }], [{
      key: 'getDefaults',
      value: function getDefaults() {
        return {
          responsive: true,
          language: {
            lengthMenu: '_MENU_',
            searchPlaceholder: 'Search..',
            search: "_INPUT_"
            // ,paginate: {
            //   previous: '<i class="icon wb-chevron-left-mini"></i>',
            //   next: '<i class="icon wb-chevron-right-mini"></i>'
            // }

            // ,
            // classes: {
            //   sFilterInput: "form-control form-control-sm",
            //   sLengthSelect: "form-control form-control-sm"
            // }
          } };
      }
    }]);
    return DataTable;
  }(_Plugin3.default);

  _Plugin3.default.register(NAME, DataTable);

  exports.default = DataTable;
});
(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('/Plugin/bootstrap-datepicker', ['exports', 'Plugin'], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports, require('Plugin'));
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports, global.Plugin);
    global.PluginBootstrapDatepicker = mod.exports;
  }
})(this, function (exports, _Plugin2) {
  'use strict';

  Object.defineProperty(exports, "__esModule", {
    value: true
  });

  var _Plugin3 = babelHelpers.interopRequireDefault(_Plugin2);

  var NAME = 'datepicker';

  var Datepicker = function (_Plugin) {
    babelHelpers.inherits(Datepicker, _Plugin);

    function Datepicker() {
      babelHelpers.classCallCheck(this, Datepicker);
      return babelHelpers.possibleConstructorReturn(this, (Datepicker.__proto__ || Object.getPrototypeOf(Datepicker)).apply(this, arguments));
    }

    babelHelpers.createClass(Datepicker, [{
      key: 'getName',
      value: function getName() {
        return NAME;
      }
    }], [{
      key: 'getDefaults',
      value: function getDefaults() {
        return {
          autoclose: true
        };
      }
    }]);
    return Datepicker;
  }(_Plugin3.default);

  _Plugin3.default.register(NAME, Datepicker);

  exports.default = Datepicker;
});
(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('/Plugin/bootstrap-select', ['exports', 'Plugin'], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports, require('Plugin'));
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports, global.Plugin);
    global.PluginBootstrapSelect = mod.exports;
  }
})(this, function (exports, _Plugin2) {
  'use strict';

  Object.defineProperty(exports, "__esModule", {
    value: true
  });

  var _Plugin3 = babelHelpers.interopRequireDefault(_Plugin2);

  var NAME = 'selectpicker';

  var Selectpicker = function (_Plugin) {
    babelHelpers.inherits(Selectpicker, _Plugin);

    function Selectpicker() {
      babelHelpers.classCallCheck(this, Selectpicker);
      return babelHelpers.possibleConstructorReturn(this, (Selectpicker.__proto__ || Object.getPrototypeOf(Selectpicker)).apply(this, arguments));
    }

    babelHelpers.createClass(Selectpicker, [{
      key: 'getName',
      value: function getName() {
        return NAME;
      }
    }], [{
      key: 'getDefaults',
      value: function getDefaults() {
        return {
          style: 'btn-select',
          iconBase: 'icon',
          tickIcon: 'wb-check'
        };
      }
    }]);
    return Selectpicker;
  }(_Plugin3.default);

  _Plugin3.default.register(NAME, Selectpicker);

  exports.default = Selectpicker;
});
(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('/Plugin/select2', ['exports', 'Plugin'], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports, require('Plugin'));
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports, global.Plugin);
    global.PluginSelect2 = mod.exports;
  }
})(this, function (exports, _Plugin2) {
  'use strict';

  Object.defineProperty(exports, "__esModule", {
    value: true
  });

  var _Plugin3 = babelHelpers.interopRequireDefault(_Plugin2);

  var NAME = 'select2';

  var Select2 = function (_Plugin) {
    babelHelpers.inherits(Select2, _Plugin);

    function Select2() {
      babelHelpers.classCallCheck(this, Select2);
      return babelHelpers.possibleConstructorReturn(this, (Select2.__proto__ || Object.getPrototypeOf(Select2)).apply(this, arguments));
    }

    babelHelpers.createClass(Select2, [{
      key: 'getName',
      value: function getName() {
        return NAME;
      }
    }], [{
      key: 'getDefaults',
      value: function getDefaults() {
        return {
          width: 'style'
        };
      }
    }]);
    return Select2;
  }(_Plugin3.default);

  _Plugin3.default.register(NAME, Select2);

  exports.default = Select2;
});
(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('/Plugin/input-group-file', ['exports', 'jquery', 'Plugin'], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports, require('jquery'), require('Plugin'));
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports, global.jQuery, global.Plugin);
    global.PluginInputGroupFile = mod.exports;
  }
})(this, function (exports, _jquery, _Plugin2) {
  'use strict';

  Object.defineProperty(exports, "__esModule", {
    value: true
  });

  var _jquery2 = babelHelpers.interopRequireDefault(_jquery);

  var _Plugin3 = babelHelpers.interopRequireDefault(_Plugin2);

  var NAME = 'inputGroupFile';

  var InputGroupFile = function (_Plugin) {
    babelHelpers.inherits(InputGroupFile, _Plugin);

    function InputGroupFile() {
      babelHelpers.classCallCheck(this, InputGroupFile);
      return babelHelpers.possibleConstructorReturn(this, (InputGroupFile.__proto__ || Object.getPrototypeOf(InputGroupFile)).apply(this, arguments));
    }

    babelHelpers.createClass(InputGroupFile, [{
      key: 'getName',
      value: function getName() {
        return NAME;
      }
    }, {
      key: 'render',
      value: function render() {
        this.$file = this.$el.find('[type=file]');
        this.$text = this.$el.find('.form-control');
      }
    }, {
      key: 'change',
      value: function change() {
        var value = '';
        _jquery2.default.each(this.$file[0].files, function (i, file) {
          value += file.name + ', ';
        });

        value = value.substring(0, value.length - 2);

        this.$text.val(value);
      }
    }], [{
      key: 'api',
      value: function api() {
        return 'change|change';
      }
    }]);
    return InputGroupFile;
  }(_Plugin3.default);

  _Plugin3.default.register(NAME, InputGroupFile);

  exports.default = InputGroupFile;
});
(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('/Plugin/material', ['exports', 'jquery', 'Plugin'], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports, require('jquery'), require('Plugin'));
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports, global.jQuery, global.Plugin);
    global.PluginMaterial = mod.exports;
  }
})(this, function (exports, _jquery, _Plugin2) {
  'use strict';

  Object.defineProperty(exports, "__esModule", {
    value: true
  });

  var _jquery2 = babelHelpers.interopRequireDefault(_jquery);

  var _Plugin3 = babelHelpers.interopRequireDefault(_Plugin2);

  var NAME = 'formMaterial';

  function isChar(e) {
    if (typeof e.which === 'undefined') {
      return true;
    } else if (typeof e.which === 'number' && e.which > 0) {
      return !e.ctrlKey && !e.metaKey && !e.altKey && e.which !== 8 && e.which !== 9;
    }
    return false;
  }

  var FormMaterial = function (_Plugin) {
    babelHelpers.inherits(FormMaterial, _Plugin);

    function FormMaterial() {
      babelHelpers.classCallCheck(this, FormMaterial);
      return babelHelpers.possibleConstructorReturn(this, (FormMaterial.__proto__ || Object.getPrototypeOf(FormMaterial)).apply(this, arguments));
    }

    babelHelpers.createClass(FormMaterial, [{
      key: 'getName',
      value: function getName() {
        return NAME;
      }
    }, {
      key: 'render',
      value: function render() {
        var $el = this.$el;

        var $control = this.$control = $el.find('.form-control');

        // Add hint label if required
        if ($control.attr('data-hint')) {
          $control.after('<div class=hint>' + $control.attr('data-hint') + '</div>');
        }

        if ($el.hasClass('floating')) {
          // Add floating label if required
          if ($control.hasClass('floating-label')) {
            var placeholder = $control.attr('placeholder');
            $control.attr('placeholder', null).removeClass('floating-label');
            $control.after('<div class=floating-label>' + placeholder + '</div>');
          }

          // Set as empty if is empty
          if ($control.val() === null || $control.val() === 'undefined' || $control.val() === '') {
            $control.addClass('empty');
          }
        }

        // Support for file input
        if ($control.next().is('[type=file]')) {
          $el.addClass('form-material-file');
        }

        this.$file = $el.find('[type=file]');
        this.bind();
        $el.data('formMaterialAPI', this);
      }
    }, {
      key: 'bind',
      value: function bind() {
        var _this2 = this;

        var $el = this.$el;
        var $control = this.$control = $el.find('.form-control');

        $el.on('keydown.site.material paste.site.material', '.form-control', function (e) {
          if (isChar(e)) {
            $control.removeClass('empty');
          }
        }).on('keyup.site.material change.site.material', '.form-control', function () {
          if ($control.val() === '' && typeof $control[0].checkValidity !== 'undefined' && $control[0].checkValidity()) {
            $control.addClass('empty');
          } else {
            $control.removeClass('empty');
          }
        });

        if (this.$file.length > 0) {
          this.$file.on('focus', function () {
            _this2.$el.find('input').addClass('focus');
          }).on('blur', function () {
            _this2.$el.find('input').removeClass('focus');
          }).on('change', function () {
            var $this = (0, _jquery2.default)(this);
            var value = '';

            _jquery2.default.each($this[0].files, function (i, file) {
              value += file.name + ', ';
            });
            value = value.substring(0, value.length - 2);
            if (value) {
              $this.prev().removeClass('empty');
            } else {
              $this.prev().addClass('empty');
            }
            $this.prev().val(value);
          });
        }
      }
    }]);
    return FormMaterial;
  }(_Plugin3.default);

  _Plugin3.default.register(NAME, FormMaterial);

  exports.default = FormMaterial;
});