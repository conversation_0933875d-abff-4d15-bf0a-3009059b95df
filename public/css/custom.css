/*

* Override Template default styles here

*/

html.authpage, html.authpage body {
    height: auto !important;
}

#spark-app {
    /* flex: 1; would be enough but it looks bad in IE */
    /*flex: 1 0 auto;*/
    position: relative;
    height: 100%;
    margin: 0;
    padding-top: 0;
}

body {
    animation-duration: 0ms !important;
}

body.no-menu {
    padding-top: 4.286rem;
}

.spark-screen .agency-logo-preview {
    border-radius: 6px;
    display: inline-block;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    height: 150px;
    vertical-align: middle;
    width: 90%;
}

.spark-screen .profile-photo-preview {
    border-radius: 6px;
    display: inline-block;
    background-position: center;
    background-size: cover;
    height: 150px;
    vertical-align: middle;
    width: 150px;
}

.spark-profile-photo-xl {
    border: 2px solid #d3e0e9;
    border-radius: 50%;
    height: 40px;
    padding: 2px;
    width: 40px;
    height: 125px;
    width: 125px;
}

.spark-screen .team-photo-preview {
    border-radius: 6px;
    display: inline-block;
    background-position: center;
    background-size: cover;
    height: 150px;
    vertical-align: middle;
    width: 150px;
}

.btn-group-block, .btn-group-block .btn {
    display: block;
    width: 100%;
    white-space: normal;
}

.btn-upload {
    overflow: hidden;
    position: relative;
    margin-top: 15px;
    margin-bottom: 0;
    width: 150px;
}

.btn-upload input[type="file"] {
    cursor: pointer;
    margin: 0;
    opacity: 0;
    padding: 0;
    position: absolute;
    right: 0;
    top: 0;
}

input[type="file"] {
    display: block;
}
.spark-team-photo {
    border: 2px solid #d3e0e9;
    border-radius: 50%;
    height: 40px;
    padding: 2px;
    width: 40px;
}
.spark-team-photo-xs {
    border-radius: 50%;
    height: 1.28571429em;
    width: 1.28571429em;
}

.spark-profile-photo {
    border: 2px solid #d3e0e9;
    border-radius: 50%;
    height: 40px;
    padding: 2px;
    width: 40px;
}
/*
.btn-table-align {
    padding-top: 7px;
    padding-bottom: 7px;
}
*/
.fa-btn {
    margin-right: 5px;
}
.scrollable-menu {
    height: auto;
    max-height: 400px;
    overflow-x: hidden;
}

[v-cloak] {
    display: none;
}
/*
.dataTables_wrapper .dataTables_filter {
    float: none;
    margin-top: 10px;
}

.dataTables_filter label {
    color: transparent;
}

.dataTables_filter input {
    background-color: #fff;
    background-image: none;
    border: 1px solid rgba(0, 0, 0, 0.07);
    font-family: Arial, sans-serif;
    -webkit-appearance: none;
    color: #2c2d2f;
    outline: 0;
    height: 35px;
    padding: 9px 12px;
    line-height: normal;
    font-size: 14px;
    font-weight: normal;
    vertical-align: middle;
    min-height: 35px;
    transition: all 0.12s ease;
    box-shadow: none;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    transition: background 0.2s linear 0s;
}

.notification-toggle {
    top: 40px;
    left: auto;
    padding: 0;
    position: absolute;
    right: -20px;
}

.menu-bar {
    border-bottom: 0px solid;
}

.brand .font-heading {
    font-size: 1.5em;
}
*/

/* Apply same styling as in .invalid-feedback to .error */
.error {
    margin-top: .25rem;
    font-size: .875rem;
    color: #ff4c52;
}
/* Apply same styling as in form-control.is-invalid to form-group.has-error */
.form-group.has-error .form-control {
    border-color: #ff4c52;
}

.display-initial { display: initial; }

/* Email Template Views */
/*.modal h4 { color: #eb6709; }*/
.example-well { padding: 15px 0 1px; }
.example-well.personalization .btn { margin-bottom: 10px; }
.example-well .form-control-label { font-weight: 400 !important; }

.time .checkbox-custom { padding-left: 15px; }
.time input,
.time .checkbox-custom input, .time .checkbox-custom select { text-align: center; text-align-last: center; }
.time .form-control {
    width: 38px;
    display: initial;
}
.time .form-control-label {
    font-weight: 300;
    padding: .429rem 0;
}
.time label { padding-right: 10px; }
.time label.schedule { width: 55px; }
.show-email-template-modal .modal-dialog { max-width: 80%; }
.show-email-template {
    overflow: auto;
    height: 250px;
    border: 1px solid #98a3a338;
    background-color: #f3f7f9;
    border-radius: .215rem;
}
.vue-html5-editor.full-screen { z-index: 9999 !important; }

.schedule-checkbox-disable span, .schedule-checkbox-disable input[type='text'],
.schedule-checkbox-disable input[type='number'],
.schedule-checkbox-disable select, .schedule-checkbox-disable button {
    color: grey;
    opacity: 0.3;
    cursor: none;
    pointer-events: none;
}

.schedule-checkbox-disable label {
    color: grey;
    opacity: 0.3;
}

.copy-to-all-fields { background: #fc8f55; }
.copy-to-all-fields i { color: #ffffff; }
.copy-to-all-fields:hover {
    outline: 0;
    opacity: 0.8;
    color: #fff;
}
/* End Email Template Views */

/* Vue Form Wizard style overrides */
/*
.vue-form-wizard .wizard-nav-pills li {
    color: #a3afb7;
    background-color: #f3f7f9;
}

.vue-form-wizard .wizard-nav-pills a {
    flex: 0 1 auto !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    padding: 12px 20px !important;
}

.vue-form-wizard.lg .wizard-icon-circle, .vue-form-wizard.lg .wizard-icon-circle.tab-shape {
    width: 40px !important;
    min-width: 40px !important;
    height: 40px !important;
    font-size: 24px !important;
    background-color: transparent !important;
}
.vue-form-wizard .wizard-icon-circle .wizard-icon-container.tab_shape {
    background-color: transparent !important;
    border-color: transparent !important;
}

.vue-form-wizard li .wizard-icon-circle .wizard-icon {
    width: 40px;
    height: 40px;
    font-size: 24px;
    font-style: normal;
    font-weight: 300;
    line-height: 40px;
    color: #fff;
    text-align: center;
    background-color: #e4eaec;
    border-radius: 50%;
}

.vue-form-wizard li .stepTitle {
    color: #526069;
    font-size: 20px;
    text-align: left;
    margin-left: 10px;
}

.vue-form-wizard .wizard-nav-pills li.active {
    color: #fff;
}

.vue-form-wizard li.active .wizard-icon-circle .wizard-icon {
    background-color: #fff;
}

.vue-form-wizard li.active .stepTitle {
    color: #fff !important;
}
*/
/* End Vue Form Wizard style overrides */

/* Vue Dropzone style overrides */
.vue-dropzone {
    border: 2px dashed #a3afb7 !important;
    border-radius: .215rem !important;
}

.form-group.has-error .vue-dropzone {
    border: 1px solid #ff4c52 !important;
}
/* End Vue Dropzone style overrides */

/* Notification dropdown additions */
.dropdown-menu-media .list-group {
    max-height: 400px;
}
.nav-link .icon.wb-bell, .nav-link .icon.wb-envelope {
    font-size: 18px;
}
.nav-link i.activity-indicator {
    background-color: #ff4c52;
}
.nav-link i.activity-indicator {
    border: 2px solid #fff;
}
.nav-link i.activity-indicator {
    background-color: #ff4c52;
}
.nav-link i.activity-indicator {
    position: absolute;
    right: -5px;
    bottom: -7px;
    width: 10px;
    height: 10px;
    border: 2px solid #fff;
    border-radius: 100%;
}
/* End Notification dropdown additions */

/* Login V3 CSS */
.page-login-v3:before {
    position:fixed;
    top:0;
    left:0;
    content:"";
    width:100%;
    height:100%;
    background-position:center top;
    -webkit-background-size:cover;
    background-size:cover;
    z-index:-1;
    background:#3e8ef7;
    background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzYyYThlYSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMxNTcxYjEiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
    background-image:-webkit-gradient(linear,left top,left bottom,from(#3e8ef7),to(#0b69e3));
    background-image:-webkit-linear-gradient(top,#3e8ef7 0,#0b69e3 100%);
    background-image:-o-linear-gradient(top,#3e8ef7 0,#0b69e3 100%);
    background-image:linear-gradient(to bottom,#3e8ef7 0,#0b69e3 100%);
    background-repeat:repeat-x
}

.page-login-v3 .panel {
    width: 400px;
    margin-bottom: 45px;
    background: #fff;
    border-radius: .286rem;
}
.page-login-v3 .panel .panel-heading {
    padding: 50px 40px 20px;
}

.page-login-v3 .panel .panel-body {
    padding: 0 40px 40px;
}

.page-login-v3 .panel .brand-text {
    margin-top: 8px;
}

.page-login-v3 form {
    margin: 25px 0 30px;
}

.page-login-v3 form a {
    margin-left: 20px;
}

.page-login-v3 form .form-material.floating + .page-login-v3 form .form-material.floating {
    margin-top: 30px;
}

.page-login-v3 form .form-material label {
    font-weight: 300;
    color: #a3afb7;
}

.custom-text {
    display: inline-block;
}

.w-190 {
    width: 190px;
}

.counter-text {
    font-size: 20px;
    line-height: 25px;
    color: #37474f!important;
    margin-bottom: 10px;
}

.v-align-top {
    vertical-align: top;
}
.v-align-middle {
    vertical-align: middle;
}
.v-align-bottom {
    vertical-align: bottom;
}

@media (max-width: 479px) {
    .page-login-v3 .page-content {
        padding: 30px 20px;
    }

    .page-login-v3 .panel {
        width: auto;
        padding: 10px;
    }

    .page-login-v3 .panel .panel-body {
        padding: 35px 25px;
    }
}
/* End Login V3 CSS */

/* Login page CSS */
.page-login:before {
    background-image: url("/images/login.jpg");
}

.page-login form {
    width: 340px;
    margin: 30px 0;
}

.page-login form a {
    margin-left: 20px;
}

.page-login .brand-img {
    max-width: 90%;
}

.page-login .brand {
    max-width: 340px;
}

@media (max-width: 767px) {
    .page-login form {
        width: auto;
    }
}
/* End Login CSS */

/* Register V2 CSS */
.page-register-v2 {
    height:100%
}
.page-register-v2:before {
    background-image:url('/images/login.jpg')
}
.page-register-v2.page-dark.layout-full:after {
    background-color:rgba(38,50,56,.6)
}
.page-register-v2 .page-brand-info {
    margin:200px 100px 0 90px;
    max-width: 800px;
}
.page-register-v2 .page-brand-info .brand-img {
    vertical-align:middle
}
.page-register-v2 .page-brand-info .brand-text {
    display:inline-block;
    vertical-align:middle;
    margin:11px 0 11px 20px
}
.page-register-v2 .page-brand-info p {
    opacity:.6;
    max-width:650px
}
.page-register-v2 .page-register-main {
    position:absolute;
    right:0;
    top:0;
    height:auto;
    min-height:100%;
    padding:120px 50px 100px;
    color:#76838f;
    background:#fff;
    width: 460px;
    max-width: 460px; /* Add this to limit width when adding bootstrap row and col divs. */
}
.page-register-v2 .page-register-main .brand {
    margin-bottom:60px
}
.page-register-v2 .page-register-main .brand-img {
    vertical-align:middle
}
.page-register-v2 .page-register-main .brand-text {
    display:inline-block;
    vertical-align:middle;
    margin:11px 0 11px 20px;
    color:#3e8ef7
}
.page-register-v2 form {
    width: 100%;
    margin:0 0 20px
}
.page-register-v2 form>button {
    margin-top:38px
}
.page-register-v2 footer {
    position:absolute;
    bottom:0;
    left:0;
    right:0;
    margin:50px 60px;
    text-align:center
}
.page-register-v2 .social .icon,.page-register-v2 .social .icon:active,.page-register-v2 .social .icon:hover {
    color:#fff
}
.page-register-v2 td {
    line-height: 1.35;
}

.page-register-v2 .brand {
    margin-bottom: 42px !important;
}
.page-register-v2 .brand-img {
    max-width: 90%;
}

.plan-feature-list {
    color: #76838f;
}

.wv_ee_tr_op_wrap {
    display: none;
    display: none !important;
}
.wv_ee_tr_op {
    display: none !important;
}

@media (min-width:768px) {
    .page-register-v2 .page-content {
        padding-right:500px
    }
}
@media (max-width:767px) {
    .page-register-v2 .page-register-main {
        padding-top:60px
    }
}
@media (min-width:768px) and (max-width:991px) {
    .page-register-v2 .page-register-main {
        padding-top:80px
    }
    .page-register-v2 .page-brand-info {
        margin:160px 0 0 35px
    }
    .page-register-v2 .page-brand-info>p {
        /*opacity:0;*/
        /*color:transparent*/
    }
}
@media (max-width:767px) {
    .page-register-v2 .page-register-main {
        padding-top:60px;
        width:100%;
        max-width: 100%;  /* Reset max width. */
    }
    .page-register-v2 form {
        width:auto
    }
}
@media (max-width:767px) {
    .page-register-v2 .page-brand-info {
        margin:220px 0 0
    }
    .page-register-v2 .page-register-main {
        padding:50px 30px 180px
    }
    .page-register-v2 form {
        width:auto
    }
    .page-register-v2 footer {
        margin:50px 30px
    }
}
/* End Register V2 CSS */

/* Register V1 CSS */
.page-register:before {
    background-image: url("/images/login.jpg");
}

.page-register form {
    width: 340px;
    margin: 22px auto;
}

.page-register .brand-img {
    max-width: 90%;
}

.page-register .brand {
    max-width: 340px;
}

.campaign-admin-lg {
    display: inline-block;
}
.campaign-admin-md {
    display: none;
}



/* campaign panel */
.campaign-panel {
    margin-bottom: 1px;
    position: relative;
}
.campaign-panel:hover {
    color: #000 !important;
}
.campaign-panel .panel-title-meta {
    margin: 0;
    padding: 0px 30px 20px 30px;
}
.campaign-panel .panel-title-meta span {
    display: inline-block;
    padding-right: 10px;
}
.campaign-panel .panel-title {
    padding-bottom: 0px;
}
.campaign-panel .panel-body {
    padding: 20px 30px;
}
.campaign-panel-input {
    display: inline-block;
    width: 200px;
}
.campaign-panel-stats {
    padding: 5px 10px;
    position: relative;
}
.campaign-panel h3 a {
    color: #343a40;
    text-decoration: none !important;
}
.campaign-panel h3 a:hover {
    color: #000;
}
.stats-count {
    /*color: #37474f;*/
    font-size: 16px;
    line-height: 16px;
    margin-bottom: 3px;
    margin-top: 3px;
    display: block;
}
.stats-label {
    font-size: 9px;
    line-height: 10px;
    display: block;
}
.campaign-panel-interest {
    position: relative;
    padding: 10px;
    background-color: #f3f7fa;
    border: 1px solid #eee;
    border-radius: 3px;
    overflow: hidden;
}
.campaign-panel-interest .stats-count {
    font-size: 15px;
    line-height: 15px;
    margin-bottom: 0px;
}
.campaign-panel .panel-body {
    text-align: center;
}
.campaign-panel-dropdown .btn {
    padding-top: 8px;
    padding-bottom: 8px;
}

.campaign-panel-stats-wrap {
    float: left;
}
.counter-number-group {
    font-size: 14px;
}
.counter-label {
    font-size: 10px;
}
.counter-box {
    padding-right: 3px;
    padding-left: 3px;
}
.campaign-panel-btngroup {
    float: right;
}

.loader-wrapper {
    position: relative;
}
.loader-box {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0px;
    left: 0px;
    background: rgba(255,255,255,0.5);
    display: none;
}
.loader-wrapper.loader-wrapper-loading .loader-box {
    display: block;
}

.btn-campaign-status {
    width: 100px;
}
.btn-blue {
    color: #fff;
    background-color: #007bff;
}
.btn-yellow {
    color: #111;
    background-color: #ffc107;
}
.h-70 {
    height: 70px !important;
}

.font15 {
    font-size: 15px;
}
.mt-8 {
    margin-top: 8px !important;
}
.cap {text-transform: capitalize;}

label.black {
    color: #37474f;
}
.font12 {
    font-size: 12px;
    line-height: 12px;
}
.mb1 {
    margin-bottom: 1px;
}
.select2-invalid .select2-selection {
    border-color: #ff4c52;
}

.select2-invalid .bootstrap-select {
    border: 1px solid #ff4c52;
}

.vueeditor-invalid .trumbowyg-box, .vueeditor-invalid .trumbowyg-editorr {
    border: 1px solid #ff4c52 !important;
}

.signature-box {
    position: relative;
}
.btn-unsub {
    position: absolute;
    top: 3px;
    right: 3px;
    z-index: 1010
}
.editor-toggle-box .editor-toggle-btn {
    position: absolute;
    z-index: 1010;
    top: 3px;
    left: 321px;
    font-weight: 600;
    font-size: 14px;
    padding-top: 5px;
}
.editor-toggle-box .editor-toggle-btn i {
    font-weight: bold;
    color: #000;
}
.editor-toggle-box .editor-toggle-back {
    position: absolute;
    z-index: 1010;
    top: 3px;
    right: 3px;
}
.editor-toggle-toolbar {
    height: 37px;
    line-height: 37px;
    padding: 0px 20px;
    margin: 0px;
    border: 1px solid #ddd;
    border-radius: 5px;
}
.editor-toggle-box {
    position: relative;
}
.editor-toggle-box .toolbar>ul {
    padding-right: 80px !important;
}
.editor-toggle-box .editor-toggle-textarea {
    min-height: 170px;
}

.editor-toggle-box.template-editor-toggle-box .editor-toggle-btn {
    left: 425px;
}

@media (max-width: 615px) {
    .editor-toggle-box.template-editor-toggle-box .editor-toggle-btn {
        left: auto;
    }
}


/* unsubscribe pages */
.card-unsub-wrap {
    text-align: center;
}
.card-unsub-box {
    background-color: #007bff !important;
    border-radius: 3px;
    color: #fff;
    margin: 0px auto;
    font-family: 'Arial', san-serif;
    font-weight: normal;
    font-size: 20px;
    display: inline-block;
    text-align: left;
}
.card-unsub-block {
    padding: 20px;
}
.card-unsub-block h2 {
    margin: 0 0 20px;
    color: #fff;
    font-weight: 500;
}
.card-unsub-btn {
    padding: 6px 14px;
    border: #fff 1px solid;
    border-radius: 2px;
    color: #333;
    font-size: 14px;
    cursor: pointer;
}

.card-shadow-1 {
    box-shadow: 0 2px 5px rgba(0, 0, 0, .1);
}
.shadow-md {
    box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
}

.cd, .cursor-default {
    cursor: default !important;
}
.cp, .cursor-pointer {
    cursor: pointer !important;
}

.step-template-btnbox {
    text-align: right;
}

/* prospect table */
.prospecttable-wrap {
    position: relative;
    padding-left: 250px;
    overflow: hidden;
    border-top: 1px solid rgba(0,0,0,0.1);
    border-right: 1px solid rgba(0,0,0,0.1);
}
.prospecttable-left {
    position: absolute;
    left: 0;
    top: 0;
    width: 250px;
    border-right: 1px solid rgba(0,0,0,0.1);
}
.prospecttable-head {
    font-weight: bold;
    cursor: pointer;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    border-left: 1px solid rgba(0,0,0,0.1);
    padding: 5px 10px;
    height: 34px;
    overflow: hidden;
    line-height: 24px;
}
.prospecttable-item {
    border-bottom: 1px solid rgba(0,0,0,0.1);
    border-left: 1px solid rgba(0,0,0,0.1);
    padding: 5px 10px;
    height: 34px;
    overflow: hidden;
}
.prospecttable-right {
    position: relative;
    max-width: 100%;
    overflow-x: scroll;
}
.prospecttable-right-inner {
    min-width: 2000px;
}
.prospecttable-row {

}
.prospecttable-row .prospecttable-head {
    display: block;
    float: left;
    width: 200px;
    text-indent: 10px;
}
.prospecttable-row .prospecttable-item {
    display: block;
    float: left;
    width: 200px;
}
.prospecttable-item select {
    padding-left: 5px;
    padding-right: 0px;
    background-position: 115px 50%;
}
.prospecttable-item-paused {
    width: 165px;
    display: inline-block;
}

.container-flex {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-around;
    align-items: stretch;
align-content: space-around;
}
.container-flex .card-flex {
    flex-grow: 1;
    align-self: auto;
    margin-bottom: 0px;
}
@media (max-width: 991px) {
    .container-flex {
        flex-wrap: wrap;
    }
    .container-flex .card-flex {
        width: 50%;
    }
}

@media (max-width: 776px) {
    .container-flex {
        flex-wrap: wrap;
    }
    .container-flex .card-flex {
        width: 50%;
        margin-bottom: 10px;
    }
}

@media (max-width: 767px) {
    .container-flex {
        display: 100%;
    }
    .container-flex .card-flex {
        width: 100%;
        margin-bottom: 10px;
    }
}

@media (max-width: 776px) {
    .card-group .card {
        margin-bottom: 10px;
    }
}

@media (max-width: 479px) {
    .page-register form {
        width: auto;
    }
}
/* End Register V1 CSS */


/* responsive menu bar */
@media (max-width: 1110px) {
    .site-menubar .site-menu-item a {
        padding: 0 10px;
    }
}
@media (max-width: 980px) {
    .site-menubar .site-menu-item a {
        font-size: 13px;
        padding: 0 15px;
    }

    .campaign-admin-lg {
        display: none !important;
    }
    .campaign-admin-md {
        display: block  !important;
    }
}
@media (max-width: 880px) {
    .site-menubar .site-menu-item a {
        font-size: 13px;
        padding: 0 9px;
    }

    .campaign-admin-lg {
        display: none !important;
    }
    .campaign-admin-md {
        display: block  !important;
    }
}

/* size:; col-sm */
@media (max-width: 767px) {
    .site-menubar .site-menu-item a {
        padding: 0 30px;
    }

    .campaign-admin-lg {
        display: block !important;
    }
    .campaign-admin-md {
        display: none  !important;
    }

    .step-template-btnbox {
        text-align: left;
        padding-top: 10px;
    }
}

@media (max-width: 1340px) {
    .custom-text {
        display: none;
    }
}




/* background colors */
.bg-red-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#FF4C52+0,E62020+100 */
    background: #FF4C52; /* Old browsers */
    background: -moz-linear-gradient(top, #FF4C52 0%, #E62020 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #FF4C52 0%,#E62020 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #FF4C52 0%,#E62020 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#FF4C52', endColorstr='#E62020',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #E62020;
}
.bg-pink-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#F74584+0,E6155E+100 */
    background: #F74584; /* Old browsers */
    background: -moz-linear-gradient(top, #F74584 0%, #E6155E 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #F74584 0%,#E6155E 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #F74584 0%,#E6155E 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#F74584', endColorstr='#E6155E',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #E6155E;
}
.bg-purple-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#9463F7+0,7231F5+100 */
    background: #9463F7; /* Old browsers */
    background: -moz-linear-gradient(top, #9463F7 0%, #7231F5 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #9463F7 0%,#7231F5 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #9463F7 0%,#7231F5 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#9463F7', endColorstr='#7231F5',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #7231F5;
}
.bg-indigo-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#667AFA+0,364FF5+100 */
    background: #667AFA; /* Old browsers */
    background: -moz-linear-gradient(top, #667AFA 0%, #364FF5 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #667AFA 0%,#364FF5 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #667AFA 0%,#364FF5 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#667AFA', endColorstr='#364FF5',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #364FF5;
}
.bg-blue-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#3E8EF7+0,0B69E3+100 */
    background: #3E8EF7; /* Old browsers */
    background: -moz-linear-gradient(top, #3E8EF7 0%, #0B69E3 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #3E8EF7 0%,#0B69E3 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #3E8EF7 0%,#0B69E3 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3E8EF7', endColorstr='#0B69E3',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #0B69E3;
}
.bg-cyan-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#0BB2D4+0,007D96+100 */
    background: #0BB2D4; /* Old browsers */
    background: -moz-linear-gradient(top, #0BB2D4 0%, #007D96 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #0BB2D4 0%,#007D96 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #0BB2D4 0%,#007D96 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0BB2D4', endColorstr='#007D96',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #007D96;
}
.bg-teal-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#17B3A3+0,008577+100 */
    background: #17B3A3; /* Old browsers */
    background: -moz-linear-gradient(top, #17B3A3 0%, #008577 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #17B3A3 0%,#008577 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #17B3A3 0%,#008577 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#17B3A3', endColorstr='#008577',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #008577;
}
.bg-green-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#11C26D+0,008C4D+100 */
    background: #11C26D; /* Old browsers */
    background: -moz-linear-gradient(top, #11C26D 0%, #008C4D 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #11C26D 0%,#008C4D 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #11C26D 0%,#008C4D 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#11C26D', endColorstr='#008C4D',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #008C4D;
}
.bg-light-green-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#6DA611+0,4A7800+100 */
    background: #6DA611; /* Old browsers */
    background: -moz-linear-gradient(top, #6DA611 0%, #4A7800 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #6DA611 0%,#4A7800 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #6DA611 0%,#4A7800 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6DA611', endColorstr='#4A7800',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #4A7800;
}
.bg-yellow-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#FFCD17+0,FAA700+100 */
    background: #FFCD17; /* Old browsers */
    background: -moz-linear-gradient(top, #FFCD17 0%, #FAA700 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #FFCD17 0%,#FAA700 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #FFCD17 0%,#FAA700 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#FFCD17', endColorstr='#FAA700',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #FAA700;
}
.bg-orange-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#EB6709+0,B53F00+100 */
    background: #EB6709; /* Old browsers */
    background: -moz-linear-gradient(top, #EB6709 0%, #B53F00 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #EB6709 0%,#B53F00 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #EB6709 0%,#B53F00 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#EB6709', endColorstr='#B53F00',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #B53F00;
}
.bg-brown-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#997B71+0,6B534C+100 */
    background: #997B71; /* Old browsers */
    background: -moz-linear-gradient(top, #997B71 0%, #6B534C 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #997B71 0%,#6B534C 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #997B71 0%,#6B534C 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#997B71', endColorstr='#6B534C',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #6B534C;
}
.bg-grey-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#757575+0,424242+100 */
    background: #757575; /* Old browsers */
    background: -moz-linear-gradient(top, #757575 0%, #424242 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #757575 0%,#424242 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #757575 0%,#424242 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#757575', endColorstr='#424242',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #424242;
}
.bg-blue-grey-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#526069+0,263238+100 */
    background: #526069; /* Old browsers */
    background: -moz-linear-gradient(top, #526069 0%, #263238 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #526069 0%,#263238 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #526069 0%,#263238 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#526069', endColorstr='#263238',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #263238;
}
.bg-wavo-gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#11C26D+0,008C4D+100 */
    background: #42b883; /* Old browsers */
    background: -moz-linear-gradient(top, #42b883 0%, #008C4D 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top, #42b883 0%,#008C4D 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom, #42b883 0%,#008C4D 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#42b883', endColorstr='#008C4D',GradientType=0 ); /* IE6-9 */
    background-repeat: no-repeat;
    background-color: #008C4D;
}
.bg-black-gradient {
    background: #333333;
    background: -moz-linear-gradient(top, #333333 0%, #000000 100%);
    background: -webkit-linear-gradient(top, #333333 0%,#000000 100%);
    background: linear-gradient(to bottom, #333333 0%,#000000 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#333333', endColorstr='#000000',GradientType=0 );
    background-repeat: no-repeat;
    background-color: #000000;
}

.bg-wavo-600 {
    background: #42b883;
}

/* fix input outline for primary  */
.input-outline-primary.input-outline-red {
    border-color: #FF4C52 !important;
    color: #FF4C52 !important;
}
.input-outline-primary.input-outline-pink {
    border-color: #F74584 !important;
    color: #F74584 !important;
}
.input-outline-primary.input-outline-purple {
    border-color: #9463F7 !important;
    color: #9463F7 !important;
}
.input-outline-primary.input-outline-indigo {
    border-color: #667AFA !important;
    color: #667AFA !important;
}
.input-outline-primary.input-outline-blue {
    border-color: #3E8EF7 !important;
    color: #3E8EF7 !important;
}
.input-outline-primary.input-outline-cyan {
    border-color: #0BB2D4 !important;
    color: #0BB2D4 !important;
}
.input-outline-primary.input-outline-teal {
    border-color: #17B3A3 !important;
    color: #17B3A3 !important;
}
.input-outline-primary.input-outline-green {
    border-color: #11C26D !important;
    color: #11C26D !important;
}
.input-outline-primary.input-outline-light-green {
    border-color: #6DA611 !important;
    color: #6DA611 !important;
}
.input-outline-primary.input-outline-yellow {
    border-color: #FFCD17 !important;
    color: #FFCD17 !important;
}
.input-outline-primary.input-outline-orange {
    border-color: #EB6709 !important;
    color: #EB6709 !important;
}
.input-outline-primary.input-outline-brown {
    border-color: #997B71 !important;
    color: #997B71 !important;
}
.input-outline-primary.input-outline-grey {
    border-color: #757575 !important;
    color: #757575 !important;
}
.input-outline-primary.input-outline-blue-grey {
    border-color: #526069 !important;
    color: #526069 !important;
}
/* update button color conflict of : orange theme - warning/primary, red theme - danger/primary */
.page-theme-orange .btn-warning {
    background-color: #FCB900;
    border-color: #FAA700;
}
.page-theme-red .btn-danger {
    background-color: #FCB900;
    border-color: #FAA700;
}


/* inbox related */
.inbox-modal-interest-label {
    line-height: 22px;
}
.inbox-modal-status-select {
    height: 36px !important;
}
.colleagues-icon-set {
    position: relative;
    margin-right: 7px;
    display: inline-block;
}
.colleagues-icon-set i {
    margin-right: 0px !important;
}
.colleagues-icon-set .subicon {
    position: absolute;
    bottom: 5px;
    right: -5px;
    font-size: 7px;
    font-weight: bold;
    text-shadow: -1px -1px 0px rgb(255 255 255);
}
.form-control.is-warning {
    border-color: #ffc107;
}



.authbox {
    width: 400px;
    background: #fff;
    border-radius: .286rem;
}
.authbox.authbox-lg {
    width: 500px;
}
.div-1 {
    border-bottom: 1px solid rgba(0,0,0,0.1);
}
.ht-auto {
    height: auto !important;
}
.w-70 {
    width: 70px !important;
}
.w-90 {
    width: 90px !important;
}
.w-130 {
    width: 130px !important;
}
.plan-feature-list {
    list-style: disc;
    font-size: 14px;
    color: #676767;
    text-align: left;
}
.list-group-topline {
    /*border-top: 1px solid #e4eaec;*/
}

.email-step-label {
    line-height: 18px;
}
.email-step-helper {

}
.stage-email-box {
    padding-right: 115px;
    position: relative;
}
.stage-email-label {
    position: absolute;
    right: 20px;
    top: 20px;
    width: 75px;
    text-align: center;
}
.radius-3 {
    border-radius: 3px !important;
}
.nav-round.nav-pills .nav-link {
    border-radius: 200px !important;
    padding: 7px 25px !important;
}
.text-decoration-none {
    text-decoration: none !important;
}
.form-icons.form-icons-right .form-control-icon {
    left: auto !important;
    right: 10px !important;
}
.form-icons.form-icons-right .form-control-icon + .form-control {
    padding-left: 14px !important;
    padding-right: 30px !important;
}

/* fix modal and select2 z-index */
.modal-open .select2 {
    z-index: 700 !important;
}
.modal-open .modal-body .select2 {
    z-index: 1701 !important;
}

/*.override sweetalert css to match remark style */
.swal-text,
.swal-footer {
    text-align: center;
}
.swal-footer .swal-button {
    padding: 7px 24px;
    font-weight: 100;
    outline: none;
}
.swal-title {
    font-size: 18px;
}
/* sweetalert custom option button colors */
.page-theme-red .swal-footer .swal-button.swal-button--option {
    background-color: #FF4C52;
}
.page-theme-pink .swal-footer .swal-button.swal-button--option {
    background-color: #F74584;
}
.page-theme-purple .swal-footer .swal-button.swal-button--option {
    background-color: #9463F7;
}
.page-theme-indigo .swal-footer .swal-button.swal-button--option {
    background-color: #667AFA;
}
.page-theme-indigo .swal-footer .swal-button.swal-button--option:hover {
    background-color: #7d8efa;
}
.page-theme-blue .swal-footer .swal-button.swal-button--option {
    background-color: #3E8EF7;
}
.page-theme-cyan .swal-footer .swal-button.swal-button--option {
    background-color: #0BB2D4;
}
.page-theme-teal .swal-footer .swal-button.swal-button--option {
    background-color: #17B3A3;
}
.page-theme-green .swal-footer .swal-button.swal-button--option {
    background-color: #11C26D;
}
.page-theme-light-green .swal-footer .swal-button.swal-button--option {
    background-color: #6DA611;
}
.page-theme-yellow .swal-footer .swal-button.swal-button--option {
    background-color: #FFCD17;
}
.page-theme-orange .swal-footer .swal-button.swal-button--option {
    background-color: #EB6709;
}
.page-theme-brown .swal-footer .swal-button.swal-button--option {
    background-color: #997B71;
}
.page-theme-grey .swal-footer .swal-button.swal-button--option {
    background-color: #757575;
}
.page-theme-blue-grey .swal-footer .swal-button.swal-button--option {
    background-color: #526069;
}

.h-230 {
    height: 230px !important;
}
.h-275 {
    height: 275px !important;
}

.child-modal {
    background-color: rgba(0,0,0,0.4);
}

.campaign-stage-tab-head {
    min-height: 54px;
}

.colleague-btn-wrap {
    text-align: right;
}


/*.responsive visibility */
@media (max-width: 1600px) {
    .hidden-xl {
        display: none;
    }
    .hidden-lg,
    .hidden-md,
    .hidden-sm,
    .hidden-xs {
        display: inline-block;
    }
}
@media (max-width: 1200px) {
    .hidden-lg {
        display: none;
    }
    .hidden-xl,
    .hidden-md,
    .hidden-sm,
    .hidden-xs {
        display: inline-block;
    }
}
@media (max-width: 1070px) {
    .signature-unsublabel {
        display: none;
    }
    .trumbowyg-button-pane {
        padding-right: 100px !important;
    }
}
@media (max-width: 992px) {
    .hidden-md,
    .hidden-md.hidden-lg {
        display: none;
    }
    .hidden-lg,
    .hidden-xl,
    .hidden-sm,
    .hidden-xs {
        display: inline-block;
    }

    .modal-xl {
        max-width: 1200px;
    }

    /*.biling page update payment modal */
    #updatePaymentModal .modal-dialog.modal-lg {
        max-width: 90% !important;
    }

    .colleague-btn-wrap {
        text-align: left;
    }
}
@media (max-width: 768px) {
    .hidden-sm {
        display: none;
    }
    .hidden-lg,
    .hidden-md,
    .hidden-xl,
    .hidden-xs {
        display: inline-block;
    }
}

@media (min-width: 480px) {
    .modal-xl {
        max-width: 1200px;
    }
}
@media (max-width: 480px) {
    .hidden-xs {
        display: none;
    }
    .hidden-lg,
    .hidden-md,
    .hidden-sm,
    .hidden-xl {
        display: inline-block;
    }

    .authbox {
        width: 100%;
    }
}

/* override select2 outline */
.page-theme-red .select2-container--default .select2-dropdown,
.page-theme-red .select2-container--default.select2-container--open .select2-selection {
    border-color: #FF4C52;
}
.page-theme-pink .select2-container--default .select2-dropdown,
.page-theme-pink .select2-container--default.select2-container--open .select2-selection {
    border-color: #F74584;
}
.page-theme-purple .select2-container--default .select2-dropdown,
.page-theme-purple .select2-container--default.select2-container--open .select2-selection {
    border-color: #9463F7;
}
.page-theme-indigo .select2-container--default .select2-dropdown,
.page-theme-indigo .select2-container--default.select2-container--open .select2-selection {
    border-color: #667AFA;
}
.page-theme-blue .select2-container--default .select2-dropdown,
.page-theme-blue .select2-container--default.select2-container--open .select2-selection {
    border-color: #3E8EF7;
}
.page-theme-cyan .select2-container--default .select2-dropdown,
.page-theme-cyan .select2-container--default.select2-container--open .select2-selection {
    border-color: #0BB2D4;
}
.page-theme-teal .select2-container--default .select2-dropdown,
.page-theme-teal .select2-container--default.select2-container--open .select2-selection {
    border-color: #17B3A3;
}
.page-theme-green .select2-container--default .select2-dropdown,
.page-theme-green .select2-container--default.select2-container--open .select2-selection {
    border-color: #11C26D;
}
.page-theme-light-green .select2-container--default .select2-dropdown,
.page-theme-light-.select2-container--default.select2-container--open .select2-selection {
    border-color: #6DA611;
}
.page-theme-yellow .select2-container--default .select2-dropdown,
.page-theme-yellow .select2-container--default.select2-container--open .select2-selection {
    border-color: #FFCD17;
}
.page-theme-orange .select2-container--default .select2-dropdown,
.page-theme-orange .select2-container--default.select2-container--open .select2-selection {
    border-color: #EB6709;
}
.page-theme-brown .select2-container--default .select2-dropdown,
.page-theme-brown .select2-container--default.select2-container--open .select2-selection {
    border-color: #997B71;
}
.page-theme-grey .select2-container--default .select2-dropdown,
.page-theme-grey .select2-container--default.select2-container--open .select2-selection {
    border-color: #757575;
}
.page-theme-blue-grey .select2-container--default .select2-dropdown,
.page-theme-blue-.select2-container--default.select2-container--open .select2-selection {
    border-color: #526069;
}

.img-logo {
    max-height: 80px !important;
}

.bordered-dark.checkbox-custom label::before {
    border-color: #bbb !important;
}



/* sweet alert confirm button */
.swal-button:focus {
  box-shadow: none;
}

.w-auto {
    width: auto !important;
}

.email-status {
    display: none;
}
.email-status.email-saved,
.email-status.email-saving,
.email-status.email-err {
    display: inline-block;
}

.email-status.email-saving .email-saved-text,
.email-status.email-saving .email-err-text,
.email-status.email-saved .email-saving-text,
.email-status.email-saved .email-err-text,
.email-status.email-err .email-saving-text,
.email-status.email-err .email-saved-text {
    display: none;
}
.email-status.email-saving .email-saving-text,
.email-status.email-saved .email-saved-text,
.email-status.email-err .email-err-text {
    display: inline-block;
}

.list-checkbox {
    position: relative;
    padding-left: 33px;
}
.list-checkbox-input {
    position: absolute;
    left: 0px;
}

.datatable-box {
    position: relative;
    overflow-x: hidden;
}

.datatable-select-column {
    position: absolute;
    top: 0px;
    right: 230px;
}




/* ct-email-activity */
.ct-email-activity .ct-series-d .ct-line,
.ct-email-activity .ct-series-d .ct-point,
.ct-email-activity .ct-series-d .ct-bar,
.ct-email-activity .ct-series-d .ct-slice-donut {
    stroke: #05a85c !important; /*green*/
}
.ct-email-activity .ct-series-c .ct-line,
.ct-email-activity .ct-series-c .ct-point,
.ct-email-activity .ct-series-c .ct-bar,
.ct-email-activity .ct-series-c .ct-slice-donut {
    stroke: #fcb900 !important; /*yellow*/
}
.ct-email-activity .ct-series-b .ct-line,
.ct-email-activity .ct-series-b .ct-point,
.ct-email-activity .ct-series-b .ct-bar,
.ct-email-activity .ct-series-b .ct-slice-donut {
    stroke: #f2353c !important; /*red*/
}
.ct-email-activity .ct-series-a .ct-line,
.ct-email-activity .ct-series-a .ct-point,
.ct-email-activity .ct-series-a .ct-bar,
.ct-email-activity .ct-series-a .ct-slice-donut {
    stroke: #76838f !important; /*grey*/
}

/* show each line slightly above each other so it's still visible when they have similar values  */
.ct-email-activity .ct-series-a {
    transform: translate(0px, -3px);
}
.ct-email-activity .ct-series-b {
    transform: translate(0px, -2px);
}
.ct-email-activity .ct-series-c {
    transform: translate(0px, -1px);
}
.ct-email-activity .ct-series-d {
    transform: translate(0px, 0px);
}

/* update arrow color of breadcrumb style activities in contact modal view */
.contact-activity-breadcrumb .breadcrumb-item::before {
    color: #fff !important;
}

a.no-line {
    text-decoration: none !important;
}

.display-more-threads {
    position: absolute;
    left: 14px;
    top: 20px;
}
.older-threads, .display-more-threads-close {
    display: none;
}
.thread-group-opened .display-more-threads-close {
    display: block;
}
.thread-group-opened .display-more-threads-open {
    display: none;
}

.text-dark-gray {
    color: #333 !important;
}

.opacity-1 {
    opacity: 1 !important;
}
.opacity-04 {
    opacity: 0.4 !important;
}

.dataTables_wrapper {
    min-height: 450px;
}
.dataTables_wrapper .dataTables_processing {
    background-color: #838f9a;
    color: white;
}
.trumbowyg-box, .trumbowyg-editor {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}
.signature-editor {
    position: relative;
}
.signature-editor .trumbowyg-box, .signature-editor .trumbowyg-editor{
    min-height: 300px !important;
}
.signature-editor.signature-editor-lg .trumbowyg-box,
.signature-editor.signature-editor-lg .trumbowyg-editor{
    min-height: 250px !important;
}
#replyEditorWrap .trumbowyg-box, #replyEditorWrap .trumbowyg-editor{
    min-height: 150px !important;
}
.bg-grey-150 {
  background-color: #f4f4f4 !important;
}
.incoming-msg-box,
.no-spill {
    overflow: hidden;
}
.incoming-msg-box img,
.no-spill img {
    max-width: 100%;
}
.incoming-related-contact {
    padding: 5px 20px !important;
}
.incoming-related-contact span,
.incoming-related-contact strong {
    display: block;

}
.incoming-related-contact span {

}
#scrollable-dropdown-menu .tt-dropdown-menu {
  max-height: 150px;
  overflow-y: auto;
}
.twitter-typeahead .empty-message {
    padding: 5px 20px !important;
}
.signature-editor .trumbowyg-button-pane {
    padding-right: 130px !important;
}

.breadcrumb-activities li:last-child{
    font-weight: bold;
}
.email-activity-box {
    min-height: 150px;
    position: relative;
}
.loadingEmailActivityChart {
    padding: 50px;
    height: 150px;
    background: rgba(255,255,255,0.5);
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    text-align: center;
}

#warmupBox {
    position: relative;
    margin-top: 10px;
}
#warmupBox .progress {
    z-index: 10;
}
#warmupSteps {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    /*background: rgba(0,0,0,0.3);*/
    height: 10px;
    z-index: 5;
}
.warmstep {
    height: 10px;
    width: 8px;
    position: absolute;
    top: -10px;
    left: 0px;
    margin-left: -4px;
    cursor: pointer;
}
.warmstep i {
    color: #fafafa;
}
.warmstep.is-warmed i {
    color: #155724;
}
.warmstep1 {
    left: 0%;
}
.warmstep2 {
    left: 5%;
}
.warmstep3 {
    left: 11%;
}
.warmstep4 {
    left: 16%;
}
.warmstep5 {
    left: 28%;
}
.warmstep6 {
    left: 52%;
}
.warmstep7 {
    left: 100%;
}

/* vue expand directive */
.expand {
    overflow: hidden;
    -webkit-transition-property: height;
    transition-property: height;
    -webkit-transition-duration: .4s;
    transition-duration: .4s;
    -webkit-transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
.expand[aria-expanded="false"] {
    height: 0 !important;
    -webkit-transition-timing-function: cubic-bezier(0.6, -0.28, 0.735, 0.045);
    transition-timing-function: cubic-bezier(0.6, -0.28, 0.735, 0.045);
}
.expand-preview[aria-expanded="false"] {
    height: 8rem !important;
}
.u-no-transition {
    -webkit-transition-duration: 0s !important;
    transition-duration: 0s !important;
}

/* v-tooltip plugin styling */
.v-tooltip {
    display: block !important;
    z-index: 10000;
}

.v-tooltip .tooltip-inner {
    background: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    /*border-radius: 16px;*/
    padding: 5px 10px 4px;
}

.v-tooltip .tooltip-arrow {
    width: 0;
    height: 0;
    border-style: solid;
    position: absolute;
    margin: 5px;
    border-color: rgba(0, 0, 0, 0.8);
}

.v-tooltip[x-placement^="top"] {
    margin-bottom: 5px;
}

.v-tooltip[x-placement^="top"] .tooltip-arrow {
    border-width: 5px 5px 0 5px;
    border-left-color: transparent !important;
    border-right-color: transparent !important;
    border-bottom-color: transparent !important;
    bottom: -5px;
    left: calc(50% - 5px);
    margin-top: 0;
    margin-bottom: 0;
}

.v-tooltip[x-placement^="bottom"] {
    margin-top: 5px;
}

.v-tooltip[x-placement^="bottom"] .tooltip-arrow {
    border-width: 0 5px 5px 5px;
    border-left-color: transparent !important;
    border-right-color: transparent !important;
    border-top-color: transparent !important;
    top: -5px;
    left: calc(50% - 5px);
    margin-top: 0;
    margin-bottom: 0;
}

.v-tooltip[x-placement^="right"] {
    margin-left: 5px;
}

.v-tooltip[x-placement^="right"] .tooltip-arrow {
    border-width: 5px 5px 5px 0;
    border-left-color: transparent !important;
    border-top-color: transparent !important;
    border-bottom-color: transparent !important;
    left: -5px;
    top: calc(50% - 5px);
    margin-left: 0;
    margin-right: 0;
}

.v-tooltip[x-placement^="left"] {
    margin-right: 5px;
}

.v-tooltip[x-placement^="left"] .tooltip-arrow {
    border-width: 5px 0 5px 5px;
    border-top-color: transparent !important;
    border-right-color: transparent !important;
    border-bottom-color: transparent !important;
    right: -5px;
    top: calc(50% - 5px);
    margin-left: 0;
    margin-right: 0;
}

.v-tooltip[aria-hidden='true'] {
    visibility: hidden;
    opacity: 0;
    transition: opacity .15s, visibility .15s;
}

.v-tooltip[aria-hidden='false'] {
    visibility: visible;
    opacity: 0.9;
    transition: opacity .15s;
}

.progress-bar-yellow {
    background-color: #ffc107;
}
.progress-striped .progress-bar-yellow {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
}

.radio-custom.radio-success label::before {
    border-color: #11c26d;
}
.radio-custom.radio-yellow label::before {
    border-color: #ffc107;
}
.radio-custom.radio-yellow label::after {
    border-color: #fff;
}
.radio-custom.radio-yellow input[type="radio"]:checked + label::before {
    border-color: #ffc107;
}
.radio-custom.radio-danger label::before {
    border-color: #ff4c52;
}
.radio-custom.radio-danger label::after {
    border-color: #fff;
}

/* TinyMCE */
.tox-tinymce-aux {
    z-index: 1800 !important;
}
.tox .tox-toolbar .tox-toolbar__group .tox-tbtn--select:nth-of-type(2) {
    margin-left: 4px;
}
.tox .tox-dialog-wrap {
    align-items: center;
    bottom: 0;
    display: flex;
    justify-content: center;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1801 !important;
}
.tox .tox-dialog-wrap__backdrop {
    background-color: rgba(255,255,255,.75);
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1802 !important;
}
.tox .tox-dialog {
    background-color: #fff;
    border-color: #d7e0e2;
    border-radius: 3px;
    border-style: solid;
    border-width: 1px;
    box-shadow: 0 16px 16px -10px rgba(51,51,51,.15),0 0 40px 1px rgba(51,51,51,.15);
    display: flex;
    flex-direction: column;
    max-height: 100%;
    max-width: 480px;
    overflow: hidden;
    position: relative;
    width: 95vw;
    z-index: 1803 !important;
}
.tox-dialog .tox-dialog__footer .tox-dialog__footer-end button[title="Submit"],
.tox-dialog .tox-dialog__footer .tox-dialog__footer-end button[title="Save"] {
    color: #fff;
    background-color: #3e8ef7;
    border-color: #3e8ef7;
}
.tox-dialog .tox-dialog__footer .tox-dialog__footer-end button[title="Submit"]:hover,
.tox-dialog .tox-dialog__footer .tox-dialog__footer-end button[title="Submit"]:focus,
.tox-dialog .tox-dialog__footer .tox-dialog__footer-end button[title="Save"]:hover,
.tox-dialog .tox-dialog__footer .tox-dialog__footer-end button[title="Save"]:focus {
    background-color: #589ffc;
    border-color: #589ffc;
}

#subject-toolbar .tox .tox-tbtn {
    height: 31px;
}
#subject-toolbar .tox-tinymce-inline {
    display: block !important;
}
#subject-toolbar .tox .tox-toolbar__primary {
    justify-content: center;
}
#template-main-editor .tox .tox-toolbar,
.message-editor .tox .tox-toolbar {
    justify-content: space-between;
}
#signature-editor .tox .tox-toolbar {
    justify-content: space-between;
}
/* TinyMCE Custom Button Skins */
/* Default (blue) */
.message-editor .tox-tbtn.tox-tbtn--select,
#template-main-editor .tox-tbtn.tox-tbtn--select,
#subject-toolbar .tox-tbtn.tox-tbtn--select,
#signature-editor .tox-tbtn.tox-tbtn--select,
#email-invite-editor .tox-tbtn.tox-tbtn--select {
    color: #fff;
    box-shadow: none;
    background-color: #3e8ef7;
    border-color: #3e8ef7;
}
.message-editor .tox-tbtn.tox-tbtn--select:hover,
.message-editor .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar .tox-tbtn.tox-tbtn--select:focus,
#signature-editor .tox-tbtn.tox-tbtn--select:hover,
#signature-editor .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor .tox-tbtn.tox-tbtn--select:focus {
    background-color: #589ffc;
    border-color: #589ffc;
}
/* Brown */
.message-editor.brown .tox-tbtn.tox-tbtn--select,
#template-main-editor.brown .tox-tbtn.tox-tbtn--select,
#subject-toolbar.brown .tox-tbtn.tox-tbtn--select,
#signature-editor.brown .tox-tbtn.tox-tbtn--select,
#email-invite-editor.brown .tox-tbtn.tox-tbtn--select {
    background-color: #997b71;
    border-color: #997b71;
}
.message-editor.brown .tox-tbtn.tox-tbtn--select:hover,
.message-editor.brown .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor.brown .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor.brown .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar.brown .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar.brown .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.brown .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.brown .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor.brown .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor.brown .tox-tbtn.tox-tbtn--select:focus {
    background-color: #ab8c82;
    border-color: #ab8c82;
}
/* Cyan */
.message-editor.cyan .tox-tbtn.tox-tbtn--select,
#template-main-editor.cyan .tox-tbtn.tox-tbtn--select,
#subject-toolbar.cyan .tox-tbtn.tox-tbtn--select,
#signature-editor.cyan .tox-tbtn.tox-tbtn--select,
#email-invite-editor.cyan .tox-tbtn.tox-tbtn--select {
    background-color: #0bb2d4;
    border-color: #0bb2d4;
}
.message-editor.cyan .tox-tbtn.tox-tbtn--select:hover,
.message-editor.cyan .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor.cyan .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor.cyan .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar.cyan .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar.cyan .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.cyan .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.cyan .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor.cyan .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor.cyan .tox-tbtn.tox-tbtn--select:focus {
    background-color: #28c0de;
    border-color: #28c0de;
}
/* Green */
.message-editor.green .tox-tbtn.tox-tbtn--select,
#template-main-editor.green .tox-tbtn.tox-tbtn--select,
#subject-toolbar.green .tox-tbtn.tox-tbtn--select,
#signature-editor.green .tox-tbtn.tox-tbtn--select,
#email-invite-editor.green .tox-tbtn.tox-tbtn--select {
    background-color: #11c26d;
    border-color: #11c26d;
}
.message-editor.green .tox-tbtn.tox-tbtn--select:hover,
.message-editor.green .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor.green .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor.green .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar.green .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar.green .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.green .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.green .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor.green .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor.green .tox-tbtn.tox-tbtn--select:focus {
    background-color: #28d17c;
    border-color: #28d17c;
}
/* Grey */
.message-editor.grey .tox-tbtn.tox-tbtn--select,
#template-main-editor.grey .tox-tbtn.tox-tbtn--select,
#subject-toolbar.grey .tox-tbtn.tox-tbtn--select,
#signature-editor.grey .tox-tbtn.tox-tbtn--select,
#email-invite-editor.grey .tox-tbtn.tox-tbtn--select {
    background-color: #757575;
    border-color: #757575;
}
.message-editor.grey .tox-tbtn.tox-tbtn--select:hover,
.message-editor.grey .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor.grey .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor.grey .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar.grey .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar.grey .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.grey .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.grey .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor.grey .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor.grey .tox-tbtn.tox-tbtn--select:focus {
    background-color: #9e9e9e;
    border-color: #9e9e9e;
}
/* Indigo */
.message-editor.indigo .tox-tbtn.tox-tbtn--select,
#template-main-editor.indigo .tox-tbtn.tox-tbtn--select,
#subject-toolbar.indigo .tox-tbtn.tox-tbtn--select,
#signature-editor.indigo .tox-tbtn.tox-tbtn--select,
#email-invite-editor.indigo .tox-tbtn.tox-tbtn--select {
    background-color: #667afa;
    border-color: #667afa;
}
.message-editor.indigo .tox-tbtn.tox-tbtn--select:hover,
.message-editor.indigo .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor.indigo .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor.indigo .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar.indigo .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar.indigo .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.indigo .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.indigo .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor.indigo .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor.indigo .tox-tbtn.tox-tbtn--select:focus {
    background-color: #7d8efa;
    border-color: #7d8efa;
}
/* Orange */
.message-editor.orange .tox-tbtn.tox-tbtn--select,
#template-main-editor.orange .tox-tbtn.tox-tbtn--select,
#subject-toolbar.orange .tox-tbtn.tox-tbtn--select,
#signature-editor.orange .tox-tbtn.tox-tbtn--select,
#email-invite-editor.orange .tox-tbtn.tox-tbtn--select {
    background-color: #eb6709;
    border-color: #eb6709;
}
.message-editor.orange .tox-tbtn.tox-tbtn--select:hover,
.message-editor.orange .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor.orange .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor.orange .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar.orange .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar.orange .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.orange .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.orange .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor.orange .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor.orange .tox-tbtn.tox-tbtn--select:focus {
    background-color: #f57d1b;
    border-color: #f57d1b;
}
/* Pink */
.message-editor.pink .tox-tbtn.tox-tbtn--select,
#template-main-editor.pink .tox-tbtn.tox-tbtn--select,
#subject-toolbar.pink .tox-tbtn.tox-tbtn--select,
#signature-editor.pink .tox-tbtn.tox-tbtn--select,
#email-invite-editor.pink .tox-tbtn.tox-tbtn--select {
    background-color: #f74584;
    border-color: #f74584;
}
.message-editor.pink .tox-tbtn.tox-tbtn--select:hover,
.message-editor.pink .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor.pink .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor.pink .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar.pink .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar.pink .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.pink .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.pink .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor.pink .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor.pink .tox-tbtn.tox-tbtn--select:focus {
    background-color: #ff5e97;
    border-color: #ff5e97;
}
/* Purple */
.message-editor.purple .tox-tbtn.tox-tbtn--select,
#template-main-editor.purple .tox-tbtn.tox-tbtn--select,
#subject-toolbar.purple .tox-tbtn.tox-tbtn--select,
#signature-editor.purple .tox-tbtn.tox-tbtn--select,
#email-invite-editor.purple .tox-tbtn.tox-tbtn--select {
    background-color: #9463f7;
    border-color: #9463f7;
}
.message-editor.purple .tox-tbtn.tox-tbtn--select:hover,
.message-editor.purple .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor.purple .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor.purple .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar.purple .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar.purple .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.purple .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.purple .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.purple .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.purple .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor.purple .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor.purple .tox-tbtn.tox-tbtn--select:focus {
    background-color: #a57afa;
    border-color: #a57afa;
}
/* Red */
.message-editor.red .tox-tbtn.tox-tbtn--select,
#template-main-editor.red .tox-tbtn.tox-tbtn--select,
#subject-toolbar.red .tox-tbtn.tox-tbtn--select,
#signature-editor.red .tox-tbtn.tox-tbtn--select,
#email-invite-editor.red .tox-tbtn.tox-tbtn--select {
    background-color: #ff4c52;
    border-color: #ff4c52;
}
.message-editor.red .tox-tbtn.tox-tbtn--select:hover,
.message-editor.red .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor.red .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor.red .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar.red .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar.red .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.red .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.red .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor.red .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor.red .tox-tbtn.tox-tbtn--select:focus {
    background-color: #ff666b;
    border-color: #ff666b;
}
/* Teal */
.message-editor.teal .tox-tbtn.tox-tbtn--select,
#template-main-editor.teal .tox-tbtn.tox-tbtn--select,
#subject-toolbar.teal .tox-tbtn.tox-tbtn--select,
#signature-editor.teal .tox-tbtn.tox-tbtn--select,
#email-invite-editor.teal .tox-tbtn.tox-tbtn--select {
    background-color: #17b3a3;
    border-color: #17b3a3;
}
.message-editor.teal .tox-tbtn.tox-tbtn--select:hover,
.message-editor.teal .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor.teal .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor.teal .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar.teal .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar.teal .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.teal .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.teal .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor.teal .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor.teal .tox-tbtn.tox-tbtn--select:focus {
    background-color: #28c7b7;
    border-color: #28c7b7;
}
/* Wavo */
.message-editor.wavo .tox-tbtn.tox-tbtn--select,
#template-main-editor.wavo .tox-tbtn.tox-tbtn--select,
#subject-toolbar.wavo .tox-tbtn.tox-tbtn--select,
#signature-editor.wavo .tox-tbtn.tox-tbtn--select,
#email-invite-editor.wavo .tox-tbtn.tox-tbtn--select {
    background-color: #42b883;
    border-color: #42b883;
}
.message-editor.wavo .tox-tbtn.tox-tbtn--select:hover,
.message-editor.wavo .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor.wavo .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor.wavo .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar.wavo .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar.wavo .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.wavo .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.wavo .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor.wavo .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor.wavo .tox-tbtn.tox-tbtn--select:focus {
    background-color: #3ba575;
    border-color: #3ba575;
}
/* Yellow */
.message-editor.yellow .tox-tbtn.tox-tbtn--select,
#template-main-editor.yellow .tox-tbtn.tox-tbtn--select,
#subject-toolbar.yellow .tox-tbtn.tox-tbtn--select,
#signature-editor.yellow .tox-tbtn.tox-tbtn--select,
#email-invite-editor.yellow .tox-tbtn.tox-tbtn--select {
    background-color: #ffcd17;
    border-color: #ffcd17;
}
.message-editor.yellow .tox-tbtn.tox-tbtn--select:hover,
.message-editor.yellow .tox-tbtn.tox-tbtn--select:focus,
#template-main-editor.yellow .tox-tbtn.tox-tbtn--select:hover,
#template-main-editor.yellow .tox-tbtn.tox-tbtn--select:focus,
#subject-toolbar.yellow .tox-tbtn.tox-tbtn--select:hover,
#subject-toolbar.yellow .tox-tbtn.tox-tbtn--select:focus,
#signature-editor.yellow .tox-tbtn.tox-tbtn--select:hover,
#signature-editor.yellow .tox-tbtn.tox-tbtn--select:focus,
#email-invite-editor.yellow .tox-tbtn.tox-tbtn--select:hover,
#email-invite-editor.yellow .tox-tbtn.tox-tbtn--select:focus {
    background-color: #ffdc2e;
    border-color: #ffdc2e;
}
/* Disabled */
.message-editor .tox-tbtn.tox-tbtn--select.tox-tbtn--disabled,
#template-main-editor .tox-tbtn.tox-tbtn--select.tox-tbtn--disabled,
#subject-toolbar .tox-tbtn.tox-tbtn--select.tox-tbtn--disabled,
#signature-editor .tox-tbtn.tox-tbtn--select.tox-tbtn--disabled,
#email-invite-editor .tox-tbtn.tox-tbtn--select.tox-tbtn--disabled {
    background-color: #ecf0f1 !important;
    border-color: #ecf0f1 !important;
    color: rgba(51, 51, 51, 0.5) !important;
}


.gap-10 {
    gap: 10px;
}
.chatgpt-prompt-box {
    border: 1px solid #eee;
    border-radius: 10px;
    padding: 20px;
    position: relative;
    margin-bottom: 10px;
    background: #fff;
}
.prompt-remove {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 99;
}

.prompt-add {
    position: absolute;
    top: 26px;
    right: 85px;
}

.setting-close {
    position: absolute;
    top: 26px;
    right: 20px;
}

.wavo-tel-input {
    color: #a3afb7;
    background-color: #fff;
    border: 1px solid #e4eaec;
}
.wavo-tel-input-wrapper:focus-within {
    border-color: #42b883 !important;
    box-shadow: none !important;
    outline: 0 !important;
}

/* introjs plugin custom styles */
.introjs-helperLayer {
    background-color: #fff !important;
    border-radius: 5px;
}
.introjs-tooltip {
    min-width: 400px;
    max-width: 840px;
    background: rgba(0, 0, 0, 0.6);
    padding: 10px;
    border-radius: 5px;
    font-size: 1.3rem;
    color: #fff;
    background: rgb(50, 143, 101);
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}
.introjs-tooltip h1, .introjs-tooltip h2, .introjs-tooltip h3, .introjs-tooltip h4 {
    color: #fff;
    margin: 0 0 10px 0;
}
.introjs-tooltip h3 {
    font-size: 1.8rem;
}
.tooltip-img-box {
    width: 800px;
    text-align: center;
    padding-top: 15px;
}
.introjs-tooltip img {
    max-width: 100%;
}
.introjs-tooltip .introjs-tooltipbuttons .introjs-hidden {
    display: none;
}
.introjs-button {
  cursor: pointer;
  border-radius: .215rem;
  width: auto;
}
.introjs-prevbutton, .introjs-nextbutton {
    font-size: 1.2rem;
    padding: 6px 12px;
}
.introjs-skipbutton {
    font-size: 1.6rem;
    border-radius: 200px;
    padding: 0px 25px;
}
.introjs-skipbutton:hover {
  text-decoration: none;
}
.introjs-helperNumberLayer.introjs-helperNumberLayer-corner {
    top: -16px !important;
    left: -16px !important;
}
.tooltip-yt-embeded-video {
    display: flex;
    justify-content: center;
    width: 800px;
}
.tooltip-yt-embeded-video iframe {
    aspect-ratio: 16 / 9;
    width: 100% !important;
}

@media (max-width: 900px) {
    .tooltip-yt-embeded-video,
    .tooltip-img-box {
        width: 600px;
    }
}

@media (max-width: 600px) {
    .tooltip-yt-embeded-video,
    .tooltip-img-box {
        width: 400px;
    }
}

@media (max-width: 400px) {
    .introjs-tooltip {
        min-width: 300px !important;
    }
    .tooltip-yt-embeded-video,
    .tooltip-img-box {
        width: 360px;
    }
}
.search-company-thumbnail {
    width: 22px;
    height: 22px;
    margin-right: 2px;
}
.search-company-thumbnail.search-company-thumbnail-lg {
    width: auto;
    margin-right: 4px;
    display: inline-block;
    margin-top: -9px;
    height: 39px;
    background: #fff;
    border-radius: 3px;
    padding: 1px;
}
.search-company-initial {
    background: #E8E5DE;
    color: #333;
    text-transform: capitalize;
    border-radius: 2px;
    line-height: 23px;
    text-align: center;
    font-size: 11px;
    display: inline-block;
    width: 22px;
    height: 22px;
}
.search-company-initial-lg {
    line-height: 39px;
    text-align: center;
    font-size: 18px;
    display: inline-block;
    border-radius: 3px;
    font-weight: bold;
    width: 39px;
    height: 39px;
    margin-top: -9px;
}

/* flex spacing */
.gap-5 {
    gap: 5px;
}
.gap-10 {
    gap: 10px;
}
.gap-15 {
    gap: 15px;
}
.gap-20 {
    gap: 20px;
}
.text-ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

/* update css of scrollbar */
.scrollbar::-webkit-scrollbar-track
{
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    border-radius: 10px;
    background-color: #F5F5F5;
}
.scrollbar.scrollbar-clear::-webkit-scrollbar-track
{
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.0);
}

.scrollbar::-webkit-scrollbar
{
    width: 4px;
    height: 4px;
    background-color: #F5F5F5;
}
.scrollbar.scrollbar-lg::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
.scrollbar::-webkit-scrollbar-thumb
{
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.2);
    background-color: #c2ccd2;
}