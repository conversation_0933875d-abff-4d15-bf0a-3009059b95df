.scrollable.is-enabled {
  overflow: hidden !important;
}

.is-enabled .scrollable-container {
  box-sizing: content-box !important;
  overflow: hidden !important;
}

.is-enabled .scrollable-container::-webkit-scrollbar {
  width: 0;
  height: 0;
  -webkit-appearance: none;
}

.scrollable-vertical.is-enabled .scrollable-container {
  overflow-y: scroll !important;
}

.scrollable-horizontal.is-enabled .scrollable-container {
  overflow-x: scroll !important;
}

.is-enabled .scrollable-content {
  position: relative !important;
  box-sizing: border-box;
  overflow: visible !important;
}

.is-enabled .scrollable-content::before, .is-enabled .scrollable-content::after {
  display: table;
  content: " ";
}

.is-enabled .scrollable-content::after {
  clear: both;
}

.scrollable-bar {
  position: absolute;
  right: 0;
  bottom: 0;
  box-sizing: border-box;
  overflow: hidden;
  line-height: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border-radius: 2px;
  transition: opacity .5s;
  -webkit-touch-callout: none;
  user-input: disabled;
  user-focus: ignore;
}

.is-disabled .scrollable-bar {
  display: none;
}

.scrollable-bar-hide {
  opacity: 0;
  transition-delay: 400ms;
}

.scrollable-bar.is-hovering {
  background: transparent;
}

.scrollable-bar.is-dragging {
  background: transparent !important;
  opacity: 1;
}

.scrollable-bar.is-disabled {
  display: none;
}

.scrollable-bar-handle {
  position: absolute;
  top: 0;
  left: 0;
  line-height: 0;
  cursor: pointer;
  background: rgba(163, 175, 183, .6);
  border-radius: 2px;
  transition: width, height .5s;
}

.scrollable-bar.is-dragging .scrollable-bar-handle {
  background: rgba(163, 175, 183, .8) !important;
}

.scrollable-bar.is-dragging, .scrollable-bar.is-hovering {
  border-radius: 4px;
}

.scrollable-bar.is-dragging .scrollable-bar-handle, .scrollable-bar.is-hovering .scrollable-bar-handle {
  border-radius: 4px;
}

.scrollable-bar-vertical {
  width: 4px;
  height: 100%;
  height: calc(100% - 8px);
  margin: 4px 2px;
}

.scrollable-bar-vertical.is-dragging, .scrollable-bar-vertical.is-hovering {
  width: 8px;
  margin: 4px 1px;
}

.scrollable-bar-vertical .scrollable-bar-handle {
  width: 100%;
}

.scrollable-bar-horizontal {
  width: 100%;
  width: calc(100% - 8px);
  height: 4px;
  margin: 2px 4px;
}

.scrollable-bar-horizontal.is-dragging, .scrollable-bar-horizontal.is-hovering {
  height: 8px;
  margin: 1px 4px;
}

.scrollable-bar-horizontal .scrollable-bar-handle {
  height: 100%;
}

.scrollable.is-scrolling .scrollable-bar {
  opacity: 1;
  transition: opacity 0;
}

.scrollable.is-hovering .scrollable-bar-handle {
  background: rgba(163, 175, 183, .8);
}

.scrollable.is-dragging {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  user-input: disabled;
  user-focus: ignore;
}

.scrollable-inverse.scrollable .scrollable-bar-handle {
  background: rgba(228, 234, 236, .6);
}

.scrollable-inverse.scrollable.is-dragging .scrollable-bar-handle {
  background: rgba(228, 234, 236, .8) !important;
}

.scrollable-shadow.scrollable.is-dragging .scrollable-bar, .scrollable-shadow.scrollable.is-hovering .scrollable-bar {
  background: rgba(228, 234, 236, .5);
}

.scrollable-inverse.scrollable-shadow.scrollable.is-dragging .scrollable-bar, .scrollable-inverse.scrollable-shadow.scrollable.is-hovering .scrollable-bar {
  background: rgba(228, 234, 236, .4);
}

.slidePanel {
  position: fixed;
  z-index: 1310;
  max-width: 100%;
  max-height: 100%;
  visibility: hidden;
  background: #fff;
  box-shadow: -10px 0 20px 0 rgba(66, 66, 66, .2);
}

.slidePanel-right, .slidePanel-left {
  top: 66.01px;
  bottom: 0;
  width: 700px;
}

@media (max-width: 1199px) {
  .slidePanel-right, .slidePanel-left {
    width: calc(100% - 230px);
  }
}

@media (max-width: 767px) {
  .slidePanel-right, .slidePanel-left {
    width: 100%;
  }
}

.slidePanel-top, .slidePanel-bottom {
  width: 100%;
  height: 500px;
}

@media (max-width: 1199px) {
  .slidePanel-top, .slidePanel-bottom {
    top: 0;
    bottom: 0;
    height: auto;
  }
}

.slidePanel-left {
  left: 0;
}

.slidePanel-right {
  right: 0;
}

.slidePanel-top {
  top: 0;
  left: 0;
}

.slidePanel-bottom {
  bottom: 0;
  left: 0;
}

.slidePanel .scrollable-container {
  height: 100%;
}

.slidePanel-show {
  visibility: visible;
}

.slidePanel-handler {
  position: absolute;
  top: 0;
  left: 0;
  width: 30px;
  height: 100%;
  cursor: e-resize;
  background-color: transparent;
}

.slidePanel-loading {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  font-size: 0;
  text-align: center;
  visibility: hidden;
}

.slidePanel-loading:before {
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  content: "";
}

.slidePanel-loading .loader {
  vertical-align: middle;
}

.slidePanel-loading-show {
  visibility: visible;
}

.slidePanel-scrollable {
  height: 100%;
}

.slidePanel-header {
  position: relative;
  padding: 40px 30px;
  padding-right: 120px;
  color: #fff;
}

.slidePanel-header h1 {
  margin: 0;
  font-size: 22px;
  line-height: 33px;
  color: #fff;
}

.slidePanel-actions {
  float: right;
  min-height: 67px;
  margin: 0;
  margin-left: 15px;
  text-align: right;
  list-style: none;
}

.slidePanel-actions .btn {
  line-height: 33px;
}

.slidePanel-action {
  border: 1px solid transparent;
  border-radius: .215rem;
  transition: border .25s linear;
}

.slidePanel-action:last-child {
  margin-right: 0;
}

.slidePanel-action.dropdown.open {
  border-color: #fff;
}

.slidePanel-action .icon {
  cursor: pointer;
}

.slidePanel-action .icon:hover {
  color: #ecf0f3;
}

.slidePanel-action .dropdown-menu {
  right: 0;
  left: auto;
  margin-top: 10px;
}

.slidePanel-action .dropdown-menu .icon {
  margin-right: 10px;
}

.slidePanel-inner {
  padding: 0 30px;
}

.slidePanel-inner-section {
  padding-top: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid #e4eaec;
}

.slidePanel-comment {
  position: relative;
  margin-top: 50px;
  margin-bottom: 50px;
}

.slidePanel-comment textarea {
  margin-bottom: 5px;
}

.slidePanel-comment .reply {
  position: absolute;
  top: 10px;
  left: 10px;
}

.flag-icon {
  position: relative;
  display: inline-block;
  width: 1.3333333333em;
  line-height: 1em;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: contain;
}

.flag-icon:before {
  content: "\00a0";
}

.dropdown-menu .flag-icon {
  margin-right: 5px;
}

.flag-icon-ad {
  background-image: url(flags/4x3/ad.svg);
}

.flag-icon-ad.flag-icon-squared {
  background-image: url(flags/1x1/ad.svg);
}

.flag-icon-ae {
  background-image: url(flags/4x3/ae.svg);
}

.flag-icon-ae.flag-icon-squared {
  background-image: url(flags/1x1/ae.svg);
}

.flag-icon-af {
  background-image: url(flags/4x3/af.svg);
}

.flag-icon-af.flag-icon-squared {
  background-image: url(flags/1x1/af.svg);
}

.flag-icon-ag {
  background-image: url(flags/4x3/ag.svg);
}

.flag-icon-ag.flag-icon-squared {
  background-image: url(flags/1x1/ag.svg);
}

.flag-icon-ai {
  background-image: url(flags/4x3/ai.svg);
}

.flag-icon-ai.flag-icon-squared {
  background-image: url(flags/1x1/ai.svg);
}

.flag-icon-al {
  background-image: url(flags/4x3/al.svg);
}

.flag-icon-al.flag-icon-squared {
  background-image: url(flags/1x1/al.svg);
}

.flag-icon-am {
  background-image: url(flags/4x3/am.svg);
}

.flag-icon-am.flag-icon-squared {
  background-image: url(flags/1x1/am.svg);
}

.flag-icon-ao {
  background-image: url(flags/4x3/ao.svg);
}

.flag-icon-ao.flag-icon-squared {
  background-image: url(flags/1x1/ao.svg);
}

.flag-icon-aq {
  background-image: url(flags/4x3/aq.svg);
}

.flag-icon-aq.flag-icon-squared {
  background-image: url(flags/1x1/aq.svg);
}

.flag-icon-ar {
  background-image: url(flags/4x3/ar.svg);
}

.flag-icon-ar.flag-icon-squared {
  background-image: url(flags/1x1/ar.svg);
}

.flag-icon-as {
  background-image: url(flags/4x3/as.svg);
}

.flag-icon-as.flag-icon-squared {
  background-image: url(flags/1x1/as.svg);
}

.flag-icon-at {
  background-image: url(flags/4x3/at.svg);
}

.flag-icon-at.flag-icon-squared {
  background-image: url(flags/1x1/at.svg);
}

.flag-icon-au {
  background-image: url(flags/4x3/au.svg);
}

.flag-icon-au.flag-icon-squared {
  background-image: url(flags/1x1/au.svg);
}

.flag-icon-aw {
  background-image: url(flags/4x3/aw.svg);
}

.flag-icon-aw.flag-icon-squared {
  background-image: url(flags/1x1/aw.svg);
}

.flag-icon-ax {
  background-image: url(flags/4x3/ax.svg);
}

.flag-icon-ax.flag-icon-squared {
  background-image: url(flags/1x1/ax.svg);
}

.flag-icon-az {
  background-image: url(flags/4x3/az.svg);
}

.flag-icon-az.flag-icon-squared {
  background-image: url(flags/1x1/az.svg);
}

.flag-icon-ba {
  background-image: url(flags/4x3/ba.svg);
}

.flag-icon-ba.flag-icon-squared {
  background-image: url(flags/1x1/ba.svg);
}

.flag-icon-bb {
  background-image: url(flags/4x3/bb.svg);
}

.flag-icon-bb.flag-icon-squared {
  background-image: url(flags/1x1/bb.svg);
}

.flag-icon-bd {
  background-image: url(flags/4x3/bd.svg);
}

.flag-icon-bd.flag-icon-squared {
  background-image: url(flags/1x1/bd.svg);
}

.flag-icon-be {
  background-image: url(flags/4x3/be.svg);
}

.flag-icon-be.flag-icon-squared {
  background-image: url(flags/1x1/be.svg);
}

.flag-icon-bf {
  background-image: url(flags/4x3/bf.svg);
}

.flag-icon-bf.flag-icon-squared {
  background-image: url(flags/1x1/bf.svg);
}

.flag-icon-bg {
  background-image: url(flags/4x3/bg.svg);
}

.flag-icon-bg.flag-icon-squared {
  background-image: url(flags/1x1/bg.svg);
}

.flag-icon-bh {
  background-image: url(flags/4x3/bh.svg);
}

.flag-icon-bh.flag-icon-squared {
  background-image: url(flags/1x1/bh.svg);
}

.flag-icon-bi {
  background-image: url(flags/4x3/bi.svg);
}

.flag-icon-bi.flag-icon-squared {
  background-image: url(flags/1x1/bi.svg);
}

.flag-icon-bj {
  background-image: url(flags/4x3/bj.svg);
}

.flag-icon-bj.flag-icon-squared {
  background-image: url(flags/1x1/bj.svg);
}

.flag-icon-bl {
  background-image: url(flags/4x3/bl.svg);
}

.flag-icon-bl.flag-icon-squared {
  background-image: url(flags/1x1/bl.svg);
}

.flag-icon-bm {
  background-image: url(flags/4x3/bm.svg);
}

.flag-icon-bm.flag-icon-squared {
  background-image: url(flags/1x1/bm.svg);
}

.flag-icon-bn {
  background-image: url(flags/4x3/bn.svg);
}

.flag-icon-bn.flag-icon-squared {
  background-image: url(flags/1x1/bn.svg);
}

.flag-icon-bo {
  background-image: url(flags/4x3/bo.svg);
}

.flag-icon-bo.flag-icon-squared {
  background-image: url(flags/1x1/bo.svg);
}

.flag-icon-bq {
  background-image: url(flags/4x3/bq.svg);
}

.flag-icon-bq.flag-icon-squared {
  background-image: url(flags/1x1/bq.svg);
}

.flag-icon-br {
  background-image: url(flags/4x3/br.svg);
}

.flag-icon-br.flag-icon-squared {
  background-image: url(flags/1x1/br.svg);
}

.flag-icon-bs {
  background-image: url(flags/4x3/bs.svg);
}

.flag-icon-bs.flag-icon-squared {
  background-image: url(flags/1x1/bs.svg);
}

.flag-icon-bt {
  background-image: url(flags/4x3/bt.svg);
}

.flag-icon-bt.flag-icon-squared {
  background-image: url(flags/1x1/bt.svg);
}

.flag-icon-bv {
  background-image: url(flags/4x3/bv.svg);
}

.flag-icon-bv.flag-icon-squared {
  background-image: url(flags/1x1/bv.svg);
}

.flag-icon-bw {
  background-image: url(flags/4x3/bw.svg);
}

.flag-icon-bw.flag-icon-squared {
  background-image: url(flags/1x1/bw.svg);
}

.flag-icon-by {
  background-image: url(flags/4x3/by.svg);
}

.flag-icon-by.flag-icon-squared {
  background-image: url(flags/1x1/by.svg);
}

.flag-icon-bz {
  background-image: url(flags/4x3/bz.svg);
}

.flag-icon-bz.flag-icon-squared {
  background-image: url(flags/1x1/bz.svg);
}

.flag-icon-ca {
  background-image: url(flags/4x3/ca.svg);
}

.flag-icon-ca.flag-icon-squared {
  background-image: url(flags/1x1/ca.svg);
}

.flag-icon-cc {
  background-image: url(flags/4x3/cc.svg);
}

.flag-icon-cc.flag-icon-squared {
  background-image: url(flags/1x1/cc.svg);
}

.flag-icon-cd {
  background-image: url(flags/4x3/cd.svg);
}

.flag-icon-cd.flag-icon-squared {
  background-image: url(flags/1x1/cd.svg);
}

.flag-icon-cf {
  background-image: url(flags/4x3/cf.svg);
}

.flag-icon-cf.flag-icon-squared {
  background-image: url(flags/1x1/cf.svg);
}

.flag-icon-cg {
  background-image: url(flags/4x3/cg.svg);
}

.flag-icon-cg.flag-icon-squared {
  background-image: url(flags/1x1/cg.svg);
}

.flag-icon-ch {
  background-image: url(flags/4x3/ch.svg);
}

.flag-icon-ch.flag-icon-squared {
  background-image: url(flags/1x1/ch.svg);
}

.flag-icon-ci {
  background-image: url(flags/4x3/ci.svg);
}

.flag-icon-ci.flag-icon-squared {
  background-image: url(flags/1x1/ci.svg);
}

.flag-icon-ck {
  background-image: url(flags/4x3/ck.svg);
}

.flag-icon-ck.flag-icon-squared {
  background-image: url(flags/1x1/ck.svg);
}

.flag-icon-cl {
  background-image: url(flags/4x3/cl.svg);
}

.flag-icon-cl.flag-icon-squared {
  background-image: url(flags/1x1/cl.svg);
}

.flag-icon-cm {
  background-image: url(flags/4x3/cm.svg);
}

.flag-icon-cm.flag-icon-squared {
  background-image: url(flags/1x1/cm.svg);
}

.flag-icon-cn {
  background-image: url(flags/4x3/cn.svg);
}

.flag-icon-cn.flag-icon-squared {
  background-image: url(flags/1x1/cn.svg);
}

.flag-icon-co {
  background-image: url(flags/4x3/co.svg);
}

.flag-icon-co.flag-icon-squared {
  background-image: url(flags/1x1/co.svg);
}

.flag-icon-cr {
  background-image: url(flags/4x3/cr.svg);
}

.flag-icon-cr.flag-icon-squared {
  background-image: url(flags/1x1/cr.svg);
}

.flag-icon-cu {
  background-image: url(flags/4x3/cu.svg);
}

.flag-icon-cu.flag-icon-squared {
  background-image: url(flags/1x1/cu.svg);
}

.flag-icon-cv {
  background-image: url(flags/4x3/cv.svg);
}

.flag-icon-cv.flag-icon-squared {
  background-image: url(flags/1x1/cv.svg);
}

.flag-icon-cw {
  background-image: url(flags/4x3/cw.svg);
}

.flag-icon-cw.flag-icon-squared {
  background-image: url(flags/1x1/cw.svg);
}

.flag-icon-cx {
  background-image: url(flags/4x3/cx.svg);
}

.flag-icon-cx.flag-icon-squared {
  background-image: url(flags/1x1/cx.svg);
}

.flag-icon-cy {
  background-image: url(flags/4x3/cy.svg);
}

.flag-icon-cy.flag-icon-squared {
  background-image: url(flags/1x1/cy.svg);
}

.flag-icon-cz {
  background-image: url(flags/4x3/cz.svg);
}

.flag-icon-cz.flag-icon-squared {
  background-image: url(flags/1x1/cz.svg);
}

.flag-icon-de {
  background-image: url(flags/4x3/de.svg);
}

.flag-icon-de.flag-icon-squared {
  background-image: url(flags/1x1/de.svg);
}

.flag-icon-dj {
  background-image: url(flags/4x3/dj.svg);
}

.flag-icon-dj.flag-icon-squared {
  background-image: url(flags/1x1/dj.svg);
}

.flag-icon-dk {
  background-image: url(flags/4x3/dk.svg);
}

.flag-icon-dk.flag-icon-squared {
  background-image: url(flags/1x1/dk.svg);
}

.flag-icon-dm {
  background-image: url(flags/4x3/dm.svg);
}

.flag-icon-dm.flag-icon-squared {
  background-image: url(flags/1x1/dm.svg);
}

.flag-icon-do {
  background-image: url(flags/4x3/do.svg);
}

.flag-icon-do.flag-icon-squared {
  background-image: url(flags/1x1/do.svg);
}

.flag-icon-dz {
  background-image: url(flags/4x3/dz.svg);
}

.flag-icon-dz.flag-icon-squared {
  background-image: url(flags/1x1/dz.svg);
}

.flag-icon-ec {
  background-image: url(flags/4x3/ec.svg);
}

.flag-icon-ec.flag-icon-squared {
  background-image: url(flags/1x1/ec.svg);
}

.flag-icon-ee {
  background-image: url(flags/4x3/ee.svg);
}

.flag-icon-ee.flag-icon-squared {
  background-image: url(flags/1x1/ee.svg);
}

.flag-icon-eg {
  background-image: url(flags/4x3/eg.svg);
}

.flag-icon-eg.flag-icon-squared {
  background-image: url(flags/1x1/eg.svg);
}

.flag-icon-eh {
  background-image: url(flags/4x3/eh.svg);
}

.flag-icon-eh.flag-icon-squared {
  background-image: url(flags/1x1/eh.svg);
}

.flag-icon-er {
  background-image: url(flags/4x3/er.svg);
}

.flag-icon-er.flag-icon-squared {
  background-image: url(flags/1x1/er.svg);
}

.flag-icon-es {
  background-image: url(flags/4x3/es.svg);
}

.flag-icon-es.flag-icon-squared {
  background-image: url(flags/1x1/es.svg);
}

.flag-icon-et {
  background-image: url(flags/4x3/et.svg);
}

.flag-icon-et.flag-icon-squared {
  background-image: url(flags/1x1/et.svg);
}

.flag-icon-fi {
  background-image: url(flags/4x3/fi.svg);
}

.flag-icon-fi.flag-icon-squared {
  background-image: url(flags/1x1/fi.svg);
}

.flag-icon-fj {
  background-image: url(flags/4x3/fj.svg);
}

.flag-icon-fj.flag-icon-squared {
  background-image: url(flags/1x1/fj.svg);
}

.flag-icon-fk {
  background-image: url(flags/4x3/fk.svg);
}

.flag-icon-fk.flag-icon-squared {
  background-image: url(flags/1x1/fk.svg);
}

.flag-icon-fm {
  background-image: url(flags/4x3/fm.svg);
}

.flag-icon-fm.flag-icon-squared {
  background-image: url(flags/1x1/fm.svg);
}

.flag-icon-fo {
  background-image: url(flags/4x3/fo.svg);
}

.flag-icon-fo.flag-icon-squared {
  background-image: url(flags/1x1/fo.svg);
}

.flag-icon-fr {
  background-image: url(flags/4x3/fr.svg);
}

.flag-icon-fr.flag-icon-squared {
  background-image: url(flags/1x1/fr.svg);
}

.flag-icon-ga {
  background-image: url(flags/4x3/ga.svg);
}

.flag-icon-ga.flag-icon-squared {
  background-image: url(flags/1x1/ga.svg);
}

.flag-icon-gb {
  background-image: url(flags/4x3/gb.svg);
}

.flag-icon-gb.flag-icon-squared {
  background-image: url(flags/1x1/gb.svg);
}

.flag-icon-gd {
  background-image: url(flags/4x3/gd.svg);
}

.flag-icon-gd.flag-icon-squared {
  background-image: url(flags/1x1/gd.svg);
}

.flag-icon-ge {
  background-image: url(flags/4x3/ge.svg);
}

.flag-icon-ge.flag-icon-squared {
  background-image: url(flags/1x1/ge.svg);
}

.flag-icon-gf {
  background-image: url(flags/4x3/gf.svg);
}

.flag-icon-gf.flag-icon-squared {
  background-image: url(flags/1x1/gf.svg);
}

.flag-icon-gg {
  background-image: url(flags/4x3/gg.svg);
}

.flag-icon-gg.flag-icon-squared {
  background-image: url(flags/1x1/gg.svg);
}

.flag-icon-gh {
  background-image: url(flags/4x3/gh.svg);
}

.flag-icon-gh.flag-icon-squared {
  background-image: url(flags/1x1/gh.svg);
}

.flag-icon-gi {
  background-image: url(flags/4x3/gi.svg);
}

.flag-icon-gi.flag-icon-squared {
  background-image: url(flags/1x1/gi.svg);
}

.flag-icon-gl {
  background-image: url(flags/4x3/gl.svg);
}

.flag-icon-gl.flag-icon-squared {
  background-image: url(flags/1x1/gl.svg);
}

.flag-icon-gm {
  background-image: url(flags/4x3/gm.svg);
}

.flag-icon-gm.flag-icon-squared {
  background-image: url(flags/1x1/gm.svg);
}

.flag-icon-gn {
  background-image: url(flags/4x3/gn.svg);
}

.flag-icon-gn.flag-icon-squared {
  background-image: url(flags/1x1/gn.svg);
}

.flag-icon-gp {
  background-image: url(flags/4x3/gp.svg);
}

.flag-icon-gp.flag-icon-squared {
  background-image: url(flags/1x1/gp.svg);
}

.flag-icon-gq {
  background-image: url(flags/4x3/gq.svg);
}

.flag-icon-gq.flag-icon-squared {
  background-image: url(flags/1x1/gq.svg);
}

.flag-icon-gr {
  background-image: url(flags/4x3/gr.svg);
}

.flag-icon-gr.flag-icon-squared {
  background-image: url(flags/1x1/gr.svg);
}

.flag-icon-gs {
  background-image: url(flags/4x3/gs.svg);
}

.flag-icon-gs.flag-icon-squared {
  background-image: url(flags/1x1/gs.svg);
}

.flag-icon-gt {
  background-image: url(flags/4x3/gt.svg);
}

.flag-icon-gt.flag-icon-squared {
  background-image: url(flags/1x1/gt.svg);
}

.flag-icon-gu {
  background-image: url(flags/4x3/gu.svg);
}

.flag-icon-gu.flag-icon-squared {
  background-image: url(flags/1x1/gu.svg);
}

.flag-icon-gw {
  background-image: url(flags/4x3/gw.svg);
}

.flag-icon-gw.flag-icon-squared {
  background-image: url(flags/1x1/gw.svg);
}

.flag-icon-gy {
  background-image: url(flags/4x3/gy.svg);
}

.flag-icon-gy.flag-icon-squared {
  background-image: url(flags/1x1/gy.svg);
}

.flag-icon-hk {
  background-image: url(flags/4x3/hk.svg);
}

.flag-icon-hk.flag-icon-squared {
  background-image: url(flags/1x1/hk.svg);
}

.flag-icon-hm {
  background-image: url(flags/4x3/hm.svg);
}

.flag-icon-hm.flag-icon-squared {
  background-image: url(flags/1x1/hm.svg);
}

.flag-icon-hn {
  background-image: url(flags/4x3/hn.svg);
}

.flag-icon-hn.flag-icon-squared {
  background-image: url(flags/1x1/hn.svg);
}

.flag-icon-hr {
  background-image: url(flags/4x3/hr.svg);
}

.flag-icon-hr.flag-icon-squared {
  background-image: url(flags/1x1/hr.svg);
}

.flag-icon-ht {
  background-image: url(flags/4x3/ht.svg);
}

.flag-icon-ht.flag-icon-squared {
  background-image: url(flags/1x1/ht.svg);
}

.flag-icon-hu {
  background-image: url(flags/4x3/hu.svg);
}

.flag-icon-hu.flag-icon-squared {
  background-image: url(flags/1x1/hu.svg);
}

.flag-icon-id {
  background-image: url(flags/4x3/id.svg);
}

.flag-icon-id.flag-icon-squared {
  background-image: url(flags/1x1/id.svg);
}

.flag-icon-ie {
  background-image: url(flags/4x3/ie.svg);
}

.flag-icon-ie.flag-icon-squared {
  background-image: url(flags/1x1/ie.svg);
}

.flag-icon-il {
  background-image: url(flags/4x3/il.svg);
}

.flag-icon-il.flag-icon-squared {
  background-image: url(flags/1x1/il.svg);
}

.flag-icon-im {
  background-image: url(flags/4x3/im.svg);
}

.flag-icon-im.flag-icon-squared {
  background-image: url(flags/1x1/im.svg);
}

.flag-icon-in {
  background-image: url(flags/4x3/in.svg);
}

.flag-icon-in.flag-icon-squared {
  background-image: url(flags/1x1/in.svg);
}

.flag-icon-io {
  background-image: url(flags/4x3/io.svg);
}

.flag-icon-io.flag-icon-squared {
  background-image: url(flags/1x1/io.svg);
}

.flag-icon-iq {
  background-image: url(flags/4x3/iq.svg);
}

.flag-icon-iq.flag-icon-squared {
  background-image: url(flags/1x1/iq.svg);
}

.flag-icon-ir {
  background-image: url(flags/4x3/ir.svg);
}

.flag-icon-ir.flag-icon-squared {
  background-image: url(flags/1x1/ir.svg);
}

.flag-icon-is {
  background-image: url(flags/4x3/is.svg);
}

.flag-icon-is.flag-icon-squared {
  background-image: url(flags/1x1/is.svg);
}

.flag-icon-it {
  background-image: url(flags/4x3/it.svg);
}

.flag-icon-it.flag-icon-squared {
  background-image: url(flags/1x1/it.svg);
}

.flag-icon-je {
  background-image: url(flags/4x3/je.svg);
}

.flag-icon-je.flag-icon-squared {
  background-image: url(flags/1x1/je.svg);
}

.flag-icon-jm {
  background-image: url(flags/4x3/jm.svg);
}

.flag-icon-jm.flag-icon-squared {
  background-image: url(flags/1x1/jm.svg);
}

.flag-icon-jo {
  background-image: url(flags/4x3/jo.svg);
}

.flag-icon-jo.flag-icon-squared {
  background-image: url(flags/1x1/jo.svg);
}

.flag-icon-jp {
  background-image: url(flags/4x3/jp.svg);
}

.flag-icon-jp.flag-icon-squared {
  background-image: url(flags/1x1/jp.svg);
}

.flag-icon-ke {
  background-image: url(flags/4x3/ke.svg);
}

.flag-icon-ke.flag-icon-squared {
  background-image: url(flags/1x1/ke.svg);
}

.flag-icon-kg {
  background-image: url(flags/4x3/kg.svg);
}

.flag-icon-kg.flag-icon-squared {
  background-image: url(flags/1x1/kg.svg);
}

.flag-icon-kh {
  background-image: url(flags/4x3/kh.svg);
}

.flag-icon-kh.flag-icon-squared {
  background-image: url(flags/1x1/kh.svg);
}

.flag-icon-ki {
  background-image: url(flags/4x3/ki.svg);
}

.flag-icon-ki.flag-icon-squared {
  background-image: url(flags/1x1/ki.svg);
}

.flag-icon-km {
  background-image: url(flags/4x3/km.svg);
}

.flag-icon-km.flag-icon-squared {
  background-image: url(flags/1x1/km.svg);
}

.flag-icon-kn {
  background-image: url(flags/4x3/kn.svg);
}

.flag-icon-kn.flag-icon-squared {
  background-image: url(flags/1x1/kn.svg);
}

.flag-icon-kp {
  background-image: url(flags/4x3/kp.svg);
}

.flag-icon-kp.flag-icon-squared {
  background-image: url(flags/1x1/kp.svg);
}

.flag-icon-kr {
  background-image: url(flags/4x3/kr.svg);
}

.flag-icon-kr.flag-icon-squared {
  background-image: url(flags/1x1/kr.svg);
}

.flag-icon-kw {
  background-image: url(flags/4x3/kw.svg);
}

.flag-icon-kw.flag-icon-squared {
  background-image: url(flags/1x1/kw.svg);
}

.flag-icon-ky {
  background-image: url(flags/4x3/ky.svg);
}

.flag-icon-ky.flag-icon-squared {
  background-image: url(flags/1x1/ky.svg);
}

.flag-icon-kz {
  background-image: url(flags/4x3/kz.svg);
}

.flag-icon-kz.flag-icon-squared {
  background-image: url(flags/1x1/kz.svg);
}

.flag-icon-la {
  background-image: url(flags/4x3/la.svg);
}

.flag-icon-la.flag-icon-squared {
  background-image: url(flags/1x1/la.svg);
}

.flag-icon-lb {
  background-image: url(flags/4x3/lb.svg);
}

.flag-icon-lb.flag-icon-squared {
  background-image: url(flags/1x1/lb.svg);
}

.flag-icon-lc {
  background-image: url(flags/4x3/lc.svg);
}

.flag-icon-lc.flag-icon-squared {
  background-image: url(flags/1x1/lc.svg);
}

.flag-icon-li {
  background-image: url(flags/4x3/li.svg);
}

.flag-icon-li.flag-icon-squared {
  background-image: url(flags/1x1/li.svg);
}

.flag-icon-lk {
  background-image: url(flags/4x3/lk.svg);
}

.flag-icon-lk.flag-icon-squared {
  background-image: url(flags/1x1/lk.svg);
}

.flag-icon-lr {
  background-image: url(flags/4x3/lr.svg);
}

.flag-icon-lr.flag-icon-squared {
  background-image: url(flags/1x1/lr.svg);
}

.flag-icon-ls {
  background-image: url(flags/4x3/ls.svg);
}

.flag-icon-ls.flag-icon-squared {
  background-image: url(flags/1x1/ls.svg);
}

.flag-icon-lt {
  background-image: url(flags/4x3/lt.svg);
}

.flag-icon-lt.flag-icon-squared {
  background-image: url(flags/1x1/lt.svg);
}

.flag-icon-lu {
  background-image: url(flags/4x3/lu.svg);
}

.flag-icon-lu.flag-icon-squared {
  background-image: url(flags/1x1/lu.svg);
}

.flag-icon-lv {
  background-image: url(flags/4x3/lv.svg);
}

.flag-icon-lv.flag-icon-squared {
  background-image: url(flags/1x1/lv.svg);
}

.flag-icon-ly {
  background-image: url(flags/4x3/ly.svg);
}

.flag-icon-ly.flag-icon-squared {
  background-image: url(flags/1x1/ly.svg);
}

.flag-icon-ma {
  background-image: url(flags/4x3/ma.svg);
}

.flag-icon-ma.flag-icon-squared {
  background-image: url(flags/1x1/ma.svg);
}

.flag-icon-mc {
  background-image: url(flags/4x3/mc.svg);
}

.flag-icon-mc.flag-icon-squared {
  background-image: url(flags/1x1/mc.svg);
}

.flag-icon-md {
  background-image: url(flags/4x3/md.svg);
}

.flag-icon-md.flag-icon-squared {
  background-image: url(flags/1x1/md.svg);
}

.flag-icon-me {
  background-image: url(flags/4x3/me.svg);
}

.flag-icon-me.flag-icon-squared {
  background-image: url(flags/1x1/me.svg);
}

.flag-icon-mf {
  background-image: url(flags/4x3/mf.svg);
}

.flag-icon-mf.flag-icon-squared {
  background-image: url(flags/1x1/mf.svg);
}

.flag-icon-mg {
  background-image: url(flags/4x3/mg.svg);
}

.flag-icon-mg.flag-icon-squared {
  background-image: url(flags/1x1/mg.svg);
}

.flag-icon-mh {
  background-image: url(flags/4x3/mh.svg);
}

.flag-icon-mh.flag-icon-squared {
  background-image: url(flags/1x1/mh.svg);
}

.flag-icon-mk {
  background-image: url(flags/4x3/mk.svg);
}

.flag-icon-mk.flag-icon-squared {
  background-image: url(flags/1x1/mk.svg);
}

.flag-icon-ml {
  background-image: url(flags/4x3/ml.svg);
}

.flag-icon-ml.flag-icon-squared {
  background-image: url(flags/1x1/ml.svg);
}

.flag-icon-mm {
  background-image: url(flags/4x3/mm.svg);
}

.flag-icon-mm.flag-icon-squared {
  background-image: url(flags/1x1/mm.svg);
}

.flag-icon-mn {
  background-image: url(flags/4x3/mn.svg);
}

.flag-icon-mn.flag-icon-squared {
  background-image: url(flags/1x1/mn.svg);
}

.flag-icon-mo {
  background-image: url(flags/4x3/mo.svg);
}

.flag-icon-mo.flag-icon-squared {
  background-image: url(flags/1x1/mo.svg);
}

.flag-icon-mp {
  background-image: url(flags/4x3/mp.svg);
}

.flag-icon-mp.flag-icon-squared {
  background-image: url(flags/1x1/mp.svg);
}

.flag-icon-mq {
  background-image: url(flags/4x3/mq.svg);
}

.flag-icon-mq.flag-icon-squared {
  background-image: url(flags/1x1/mq.svg);
}

.flag-icon-mr {
  background-image: url(flags/4x3/mr.svg);
}

.flag-icon-mr.flag-icon-squared {
  background-image: url(flags/1x1/mr.svg);
}

.flag-icon-ms {
  background-image: url(flags/4x3/ms.svg);
}

.flag-icon-ms.flag-icon-squared {
  background-image: url(flags/1x1/ms.svg);
}

.flag-icon-mt {
  background-image: url(flags/4x3/mt.svg);
}

.flag-icon-mt.flag-icon-squared {
  background-image: url(flags/1x1/mt.svg);
}

.flag-icon-mu {
  background-image: url(flags/4x3/mu.svg);
}

.flag-icon-mu.flag-icon-squared {
  background-image: url(flags/1x1/mu.svg);
}

.flag-icon-mv {
  background-image: url(flags/4x3/mv.svg);
}

.flag-icon-mv.flag-icon-squared {
  background-image: url(flags/1x1/mv.svg);
}

.flag-icon-mw {
  background-image: url(flags/4x3/mw.svg);
}

.flag-icon-mw.flag-icon-squared {
  background-image: url(flags/1x1/mw.svg);
}

.flag-icon-mx {
  background-image: url(flags/4x3/mx.svg);
}

.flag-icon-mx.flag-icon-squared {
  background-image: url(flags/1x1/mx.svg);
}

.flag-icon-my {
  background-image: url(flags/4x3/my.svg);
}

.flag-icon-my.flag-icon-squared {
  background-image: url(flags/1x1/my.svg);
}

.flag-icon-mz {
  background-image: url(flags/4x3/mz.svg);
}

.flag-icon-mz.flag-icon-squared {
  background-image: url(flags/1x1/mz.svg);
}

.flag-icon-na {
  background-image: url(flags/4x3/na.svg);
}

.flag-icon-na.flag-icon-squared {
  background-image: url(flags/1x1/na.svg);
}

.flag-icon-nc {
  background-image: url(flags/4x3/nc.svg);
}

.flag-icon-nc.flag-icon-squared {
  background-image: url(flags/1x1/nc.svg);
}

.flag-icon-ne {
  background-image: url(flags/4x3/ne.svg);
}

.flag-icon-ne.flag-icon-squared {
  background-image: url(flags/1x1/ne.svg);
}

.flag-icon-nf {
  background-image: url(flags/4x3/nf.svg);
}

.flag-icon-nf.flag-icon-squared {
  background-image: url(flags/1x1/nf.svg);
}

.flag-icon-ng {
  background-image: url(flags/4x3/ng.svg);
}

.flag-icon-ng.flag-icon-squared {
  background-image: url(flags/1x1/ng.svg);
}

.flag-icon-ni {
  background-image: url(flags/4x3/ni.svg);
}

.flag-icon-ni.flag-icon-squared {
  background-image: url(flags/1x1/ni.svg);
}

.flag-icon-nl {
  background-image: url(flags/4x3/nl.svg);
}

.flag-icon-nl.flag-icon-squared {
  background-image: url(flags/1x1/nl.svg);
}

.flag-icon-no {
  background-image: url(flags/4x3/no.svg);
}

.flag-icon-no.flag-icon-squared {
  background-image: url(flags/1x1/no.svg);
}

.flag-icon-np {
  background-image: url(flags/4x3/np.svg);
}

.flag-icon-np.flag-icon-squared {
  background-image: url(flags/1x1/np.svg);
}

.flag-icon-nr {
  background-image: url(flags/4x3/nr.svg);
}

.flag-icon-nr.flag-icon-squared {
  background-image: url(flags/1x1/nr.svg);
}

.flag-icon-nu {
  background-image: url(flags/4x3/nu.svg);
}

.flag-icon-nu.flag-icon-squared {
  background-image: url(flags/1x1/nu.svg);
}

.flag-icon-nz {
  background-image: url(flags/4x3/nz.svg);
}

.flag-icon-nz.flag-icon-squared {
  background-image: url(flags/1x1/nz.svg);
}

.flag-icon-om {
  background-image: url(flags/4x3/om.svg);
}

.flag-icon-om.flag-icon-squared {
  background-image: url(flags/1x1/om.svg);
}

.flag-icon-pa {
  background-image: url(flags/4x3/pa.svg);
}

.flag-icon-pa.flag-icon-squared {
  background-image: url(flags/1x1/pa.svg);
}

.flag-icon-pe {
  background-image: url(flags/4x3/pe.svg);
}

.flag-icon-pe.flag-icon-squared {
  background-image: url(flags/1x1/pe.svg);
}

.flag-icon-pf {
  background-image: url(flags/4x3/pf.svg);
}

.flag-icon-pf.flag-icon-squared {
  background-image: url(flags/1x1/pf.svg);
}

.flag-icon-pg {
  background-image: url(flags/4x3/pg.svg);
}

.flag-icon-pg.flag-icon-squared {
  background-image: url(flags/1x1/pg.svg);
}

.flag-icon-ph {
  background-image: url(flags/4x3/ph.svg);
}

.flag-icon-ph.flag-icon-squared {
  background-image: url(flags/1x1/ph.svg);
}

.flag-icon-pk {
  background-image: url(flags/4x3/pk.svg);
}

.flag-icon-pk.flag-icon-squared {
  background-image: url(flags/1x1/pk.svg);
}

.flag-icon-pl {
  background-image: url(flags/4x3/pl.svg);
}

.flag-icon-pl.flag-icon-squared {
  background-image: url(flags/1x1/pl.svg);
}

.flag-icon-pm {
  background-image: url(flags/4x3/pm.svg);
}

.flag-icon-pm.flag-icon-squared {
  background-image: url(flags/1x1/pm.svg);
}

.flag-icon-pn {
  background-image: url(flags/4x3/pn.svg);
}

.flag-icon-pn.flag-icon-squared {
  background-image: url(flags/1x1/pn.svg);
}

.flag-icon-pr {
  background-image: url(flags/4x3/pr.svg);
}

.flag-icon-pr.flag-icon-squared {
  background-image: url(flags/1x1/pr.svg);
}

.flag-icon-ps {
  background-image: url(flags/4x3/ps.svg);
}

.flag-icon-ps.flag-icon-squared {
  background-image: url(flags/1x1/ps.svg);
}

.flag-icon-pt {
  background-image: url(flags/4x3/pt.svg);
}

.flag-icon-pt.flag-icon-squared {
  background-image: url(flags/1x1/pt.svg);
}

.flag-icon-pw {
  background-image: url(flags/4x3/pw.svg);
}

.flag-icon-pw.flag-icon-squared {
  background-image: url(flags/1x1/pw.svg);
}

.flag-icon-py {
  background-image: url(flags/4x3/py.svg);
}

.flag-icon-py.flag-icon-squared {
  background-image: url(flags/1x1/py.svg);
}

.flag-icon-qa {
  background-image: url(flags/4x3/qa.svg);
}

.flag-icon-qa.flag-icon-squared {
  background-image: url(flags/1x1/qa.svg);
}

.flag-icon-re {
  background-image: url(flags/4x3/re.svg);
}

.flag-icon-re.flag-icon-squared {
  background-image: url(flags/1x1/re.svg);
}

.flag-icon-ro {
  background-image: url(flags/4x3/ro.svg);
}

.flag-icon-ro.flag-icon-squared {
  background-image: url(flags/1x1/ro.svg);
}

.flag-icon-rs {
  background-image: url(flags/4x3/rs.svg);
}

.flag-icon-rs.flag-icon-squared {
  background-image: url(flags/1x1/rs.svg);
}

.flag-icon-ru {
  background-image: url(flags/4x3/ru.svg);
}

.flag-icon-ru.flag-icon-squared {
  background-image: url(flags/1x1/ru.svg);
}

.flag-icon-rw {
  background-image: url(flags/4x3/rw.svg);
}

.flag-icon-rw.flag-icon-squared {
  background-image: url(flags/1x1/rw.svg);
}

.flag-icon-sa {
  background-image: url(flags/4x3/sa.svg);
}

.flag-icon-sa.flag-icon-squared {
  background-image: url(flags/1x1/sa.svg);
}

.flag-icon-sb {
  background-image: url(flags/4x3/sb.svg);
}

.flag-icon-sb.flag-icon-squared {
  background-image: url(flags/1x1/sb.svg);
}

.flag-icon-sc {
  background-image: url(flags/4x3/sc.svg);
}

.flag-icon-sc.flag-icon-squared {
  background-image: url(flags/1x1/sc.svg);
}

.flag-icon-sd {
  background-image: url(flags/4x3/sd.svg);
}

.flag-icon-sd.flag-icon-squared {
  background-image: url(flags/1x1/sd.svg);
}

.flag-icon-se {
  background-image: url(flags/4x3/se.svg);
}

.flag-icon-se.flag-icon-squared {
  background-image: url(flags/1x1/se.svg);
}

.flag-icon-sg {
  background-image: url(flags/4x3/sg.svg);
}

.flag-icon-sg.flag-icon-squared {
  background-image: url(flags/1x1/sg.svg);
}

.flag-icon-sh {
  background-image: url(flags/4x3/sh.svg);
}

.flag-icon-sh.flag-icon-squared {
  background-image: url(flags/1x1/sh.svg);
}

.flag-icon-si {
  background-image: url(flags/4x3/si.svg);
}

.flag-icon-si.flag-icon-squared {
  background-image: url(flags/1x1/si.svg);
}

.flag-icon-sj {
  background-image: url(flags/4x3/sj.svg);
}

.flag-icon-sj.flag-icon-squared {
  background-image: url(flags/1x1/sj.svg);
}

.flag-icon-sk {
  background-image: url(flags/4x3/sk.svg);
}

.flag-icon-sk.flag-icon-squared {
  background-image: url(flags/1x1/sk.svg);
}

.flag-icon-sl {
  background-image: url(flags/4x3/sl.svg);
}

.flag-icon-sl.flag-icon-squared {
  background-image: url(flags/1x1/sl.svg);
}

.flag-icon-sm {
  background-image: url(flags/4x3/sm.svg);
}

.flag-icon-sm.flag-icon-squared {
  background-image: url(flags/1x1/sm.svg);
}

.flag-icon-sn {
  background-image: url(flags/4x3/sn.svg);
}

.flag-icon-sn.flag-icon-squared {
  background-image: url(flags/1x1/sn.svg);
}

.flag-icon-so {
  background-image: url(flags/4x3/so.svg);
}

.flag-icon-so.flag-icon-squared {
  background-image: url(flags/1x1/so.svg);
}

.flag-icon-sr {
  background-image: url(flags/4x3/sr.svg);
}

.flag-icon-sr.flag-icon-squared {
  background-image: url(flags/1x1/sr.svg);
}

.flag-icon-ss {
  background-image: url(flags/4x3/ss.svg);
}

.flag-icon-ss.flag-icon-squared {
  background-image: url(flags/1x1/ss.svg);
}

.flag-icon-st {
  background-image: url(flags/4x3/st.svg);
}

.flag-icon-st.flag-icon-squared {
  background-image: url(flags/1x1/st.svg);
}

.flag-icon-sv {
  background-image: url(flags/4x3/sv.svg);
}

.flag-icon-sv.flag-icon-squared {
  background-image: url(flags/1x1/sv.svg);
}

.flag-icon-sx {
  background-image: url(flags/4x3/sx.svg);
}

.flag-icon-sx.flag-icon-squared {
  background-image: url(flags/1x1/sx.svg);
}

.flag-icon-sy {
  background-image: url(flags/4x3/sy.svg);
}

.flag-icon-sy.flag-icon-squared {
  background-image: url(flags/1x1/sy.svg);
}

.flag-icon-sz {
  background-image: url(flags/4x3/sz.svg);
}

.flag-icon-sz.flag-icon-squared {
  background-image: url(flags/1x1/sz.svg);
}

.flag-icon-tc {
  background-image: url(flags/4x3/tc.svg);
}

.flag-icon-tc.flag-icon-squared {
  background-image: url(flags/1x1/tc.svg);
}

.flag-icon-td {
  background-image: url(flags/4x3/td.svg);
}

.flag-icon-td.flag-icon-squared {
  background-image: url(flags/1x1/td.svg);
}

.flag-icon-tf {
  background-image: url(flags/4x3/tf.svg);
}

.flag-icon-tf.flag-icon-squared {
  background-image: url(flags/1x1/tf.svg);
}

.flag-icon-tg {
  background-image: url(flags/4x3/tg.svg);
}

.flag-icon-tg.flag-icon-squared {
  background-image: url(flags/1x1/tg.svg);
}

.flag-icon-th {
  background-image: url(flags/4x3/th.svg);
}

.flag-icon-th.flag-icon-squared {
  background-image: url(flags/1x1/th.svg);
}

.flag-icon-tj {
  background-image: url(flags/4x3/tj.svg);
}

.flag-icon-tj.flag-icon-squared {
  background-image: url(flags/1x1/tj.svg);
}

.flag-icon-tk {
  background-image: url(flags/4x3/tk.svg);
}

.flag-icon-tk.flag-icon-squared {
  background-image: url(flags/1x1/tk.svg);
}

.flag-icon-tl {
  background-image: url(flags/4x3/tl.svg);
}

.flag-icon-tl.flag-icon-squared {
  background-image: url(flags/1x1/tl.svg);
}

.flag-icon-tm {
  background-image: url(flags/4x3/tm.svg);
}

.flag-icon-tm.flag-icon-squared {
  background-image: url(flags/1x1/tm.svg);
}

.flag-icon-tn {
  background-image: url(flags/4x3/tn.svg);
}

.flag-icon-tn.flag-icon-squared {
  background-image: url(flags/1x1/tn.svg);
}

.flag-icon-to {
  background-image: url(flags/4x3/to.svg);
}

.flag-icon-to.flag-icon-squared {
  background-image: url(flags/1x1/to.svg);
}

.flag-icon-tr {
  background-image: url(flags/4x3/tr.svg);
}

.flag-icon-tr.flag-icon-squared {
  background-image: url(flags/1x1/tr.svg);
}

.flag-icon-tt {
  background-image: url(flags/4x3/tt.svg);
}

.flag-icon-tt.flag-icon-squared {
  background-image: url(flags/1x1/tt.svg);
}

.flag-icon-tv {
  background-image: url(flags/4x3/tv.svg);
}

.flag-icon-tv.flag-icon-squared {
  background-image: url(flags/1x1/tv.svg);
}

.flag-icon-tw {
  background-image: url(flags/4x3/tw.svg);
}

.flag-icon-tw.flag-icon-squared {
  background-image: url(flags/1x1/tw.svg);
}

.flag-icon-tz {
  background-image: url(flags/4x3/tz.svg);
}

.flag-icon-tz.flag-icon-squared {
  background-image: url(flags/1x1/tz.svg);
}

.flag-icon-ua {
  background-image: url(flags/4x3/ua.svg);
}

.flag-icon-ua.flag-icon-squared {
  background-image: url(flags/1x1/ua.svg);
}

.flag-icon-ug {
  background-image: url(flags/4x3/ug.svg);
}

.flag-icon-ug.flag-icon-squared {
  background-image: url(flags/1x1/ug.svg);
}

.flag-icon-um {
  background-image: url(flags/4x3/um.svg);
}

.flag-icon-um.flag-icon-squared {
  background-image: url(flags/1x1/um.svg);
}

.flag-icon-us {
  background-image: url(flags/4x3/us.svg);
}

.flag-icon-us.flag-icon-squared {
  background-image: url(flags/1x1/us.svg);
}

.flag-icon-uy {
  background-image: url(flags/4x3/uy.svg);
}

.flag-icon-uy.flag-icon-squared {
  background-image: url(flags/1x1/uy.svg);
}

.flag-icon-uz {
  background-image: url(flags/4x3/uz.svg);
}

.flag-icon-uz.flag-icon-squared {
  background-image: url(flags/1x1/uz.svg);
}

.flag-icon-va {
  background-image: url(flags/4x3/va.svg);
}

.flag-icon-va.flag-icon-squared {
  background-image: url(flags/1x1/va.svg);
}

.flag-icon-vc {
  background-image: url(flags/4x3/vc.svg);
}

.flag-icon-vc.flag-icon-squared {
  background-image: url(flags/1x1/vc.svg);
}

.flag-icon-ve {
  background-image: url(flags/4x3/ve.svg);
}

.flag-icon-ve.flag-icon-squared {
  background-image: url(flags/1x1/ve.svg);
}

.flag-icon-vg {
  background-image: url(flags/4x3/vg.svg);
}

.flag-icon-vg.flag-icon-squared {
  background-image: url(flags/1x1/vg.svg);
}

.flag-icon-vi {
  background-image: url(flags/4x3/vi.svg);
}

.flag-icon-vi.flag-icon-squared {
  background-image: url(flags/1x1/vi.svg);
}

.flag-icon-vn {
  background-image: url(flags/4x3/vn.svg);
}

.flag-icon-vn.flag-icon-squared {
  background-image: url(flags/1x1/vn.svg);
}

.flag-icon-vu {
  background-image: url(flags/4x3/vu.svg);
}

.flag-icon-vu.flag-icon-squared {
  background-image: url(flags/1x1/vu.svg);
}

.flag-icon-wf {
  background-image: url(flags/4x3/wf.svg);
}

.flag-icon-wf.flag-icon-squared {
  background-image: url(flags/1x1/wf.svg);
}

.flag-icon-ws {
  background-image: url(flags/4x3/ws.svg);
}

.flag-icon-ws.flag-icon-squared {
  background-image: url(flags/1x1/ws.svg);
}

.flag-icon-ye {
  background-image: url(flags/4x3/ye.svg);
}

.flag-icon-ye.flag-icon-squared {
  background-image: url(flags/1x1/ye.svg);
}

.flag-icon-yt {
  background-image: url(flags/4x3/yt.svg);
}

.flag-icon-yt.flag-icon-squared {
  background-image: url(flags/1x1/yt.svg);
}

.flag-icon-za {
  background-image: url(flags/4x3/za.svg);
}

.flag-icon-za.flag-icon-squared {
  background-image: url(flags/1x1/za.svg);
}

.flag-icon-zm {
  background-image: url(flags/4x3/zm.svg);
}

.flag-icon-zm.flag-icon-squared {
  background-image: url(flags/1x1/zm.svg);
}

.flag-icon-zw {
  background-image: url(flags/4x3/zw.svg);
}

.flag-icon-zw.flag-icon-squared {
  background-image: url(flags/1x1/zw.svg);
}

.flag-icon-eu {
  background-image: url(flags/4x3/eu.svg);
}

.flag-icon-eu.flag-icon-squared {
  background-image: url(flags/1x1/eu.svg);
}

.flag-icon-gb-eng {
  background-image: url(flags/4x3/gb-eng.svg);
}

.flag-icon-gb-eng.flag-icon-squared {
  background-image: url(flags/1x1/gb-eng.svg);
}

.flag-icon-gb-nir {
  background-image: url(flags/4x3/gb-nir.svg);
}

.flag-icon-gb-nir.flag-icon-squared {
  background-image: url(flags/1x1/gb-nir.svg);
}

.flag-icon-gb-sct {
  background-image: url(flags/4x3/gb-sct.svg);
}

.flag-icon-gb-sct.flag-icon-squared {
  background-image: url(flags/1x1/gb-sct.svg);
}

.flag-icon-gb-wls {
  background-image: url(flags/4x3/gb-wls.svg);
}

.flag-icon-gb-wls.flag-icon-squared {
  background-image: url(flags/1x1/gb-wls.svg);
}

.flag-icon-un {
  background-image: url(flags/4x3/un.svg);
}

.flag-icon-un.flag-icon-squared {
  background-image: url(flags/1x1/un.svg);
}

.introjs-overlay {
  position: absolute;
  z-index: 10000;
  background-color: #000;
  opacity: 0;
  transition: all .3s ease-out;
}

.introjs-fixParent {
  position: absolute !important;
  z-index: auto !important;
  opacity: 1 !important;
  -webkit-transform: none !important;
  transform: none !important;
}

.introjs-showElement, tr.introjs-showElement > td, tr.introjs-showElement > th {
  z-index: 10100 !important;
}

.introjs-disableInteraction {
  position: absolute;
  z-index: 10300 !important;
}

.introjs-relativePosition, tr.introjs-showElement > td, tr.introjs-showElement > th {
  position: relative;
}

.introjs-helperLayer {
  position: absolute;
  z-index: 10099;
  background-color: #3e8ef7;
  border: 1px solid #777;
  border: 1px solid rgba(0, 0, 0, .5);
  box-shadow: 0 2px 15px rgba(0, 0, 0, .4);
  transition: all .3s ease-out;
}

.introjs-tooltipReferenceLayer {
  position: absolute;
  z-index: 10101;
  background-color: transparent;
  transition: all .3s ease-out;
}

.introjs-helperLayer *, .introjs-helperLayer *:before, .introjs-helperLayer *:after {
  -ms-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box;
}

.introjs-helperNumberLayer {
  position: absolute;
  top: -16px;
  left: -16px;
  z-index: 10400 !important;
  width: 20px;
  height: 20px;
  padding: 4px;
  font-family: Arial, verdana, tahoma;
  font-size: 13px;
  font-weight: 500;
  line-height: 1;
  color: white;
  text-align: center;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, .3);
  background: #3e8ef7;
  /* Old browsers */
  border-radius: 50%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, .4);
}

.introjs-arrow {
  position: absolute;
  display: none !important;
  content: "";
  border: 5px solid white;
}

.introjs-arrow.top {
  top: -10px;
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: white;
  border-left-color: transparent;
}

.introjs-arrow.top-right {
  top: -10px;
  right: 10px;
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: white;
  border-left-color: transparent;
}

.introjs-arrow.top-middle {
  top: -10px;
  left: 50%;
  margin-left: -5px;
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: white;
  border-left-color: transparent;
}

.introjs-arrow.right {
  top: 10px;
  right: -10px;
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: white;
}

.introjs-arrow.right-bottom {
  right: -10px;
  bottom: 10px;
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: white;
}

.introjs-arrow.bottom {
  bottom: -10px;
  border-top-color: white;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
}

.introjs-arrow.left {
  top: 10px;
  left: -10px;
  border-top-color: transparent;
  border-right-color: white;
  border-bottom-color: transparent;
  border-left-color: transparent;
}

.introjs-arrow.left-bottom {
  bottom: 10px;
  left: -10px;
  border-top-color: transparent;
  border-right-color: white;
  border-bottom-color: transparent;
  border-left-color: transparent;
}

.introjs-tooltip {
  position: absolute;
  min-width: 300px;
  max-width: 500px;
  padding: 10px;
  font-size: 30px;
  color: #fff;
  background-color: transparent;
  transition: opacity .1s ease-out;
}

.introjs-tooltip .content {
  display: block;
  margin-top: 20px;
  font-size: 14px;
}

.introjs-tooltipbuttons {
  margin-top: 10px;
  text-align: right;
  white-space: nowrap;
}

/*
 Buttons style by http://nicolasgallagher.com/lab/css3-github-buttons/
 Changed by Afshin Mehrabani
*/
.introjs-skipbutton {
  position: fixed;
  right: 50px;
  bottom: 50px;
  width: 56px;
  height: 56px;
  padding: 0;
  margin: 0;
  margin-right: 5px;
  font-size: 24px;
  color: #fff !important;
  text-align: center;
  text-shadow: none;
  background: #ff4c52 !important;
  border: none;
  border-radius: 100%;
  box-shadow: 0 6px 10px rgba(0, 0, 0, .15);
}

.introjs-skipbutton i {
  line-height: 56px;
}

.introjs-skipbutton:hover {
  color: #fff;
  border: none;
  box-shadow: 0 6px 10px rgba(0, 0, 0, .15);
}

.introjs-skipbutton:active, .introjs-skipbutton:focus {
  background: #ff4c52;
}

.introjs-prevbutton, .introjs-nextbutton {
  padding: 8px 18px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #fff;
  text-decoration: none;
  background-color: #3e8ef7;
  border: 1px solid transparent;
  border-radius: 1000px;
  transition: border .2s linear, color .2s linear, width .2s linear, background-color .2s linear;
  -webkit-font-smoothing: subpixel-antialiased;
}

.introjs-prevbutton:hover, .introjs-prevbutton:active, .introjs-prevbutton:focus, .introjs-nextbutton:hover, .introjs-nextbutton:active, .introjs-nextbutton:focus {
  color: #fff;
  text-decoration: none;
}

.introjs-prevbutton.introjs-disabled, .introjs-nextbutton.introjs-disabled {
  color: #a3afb7 !important;
  cursor: not-allowed;
  background: transparent !important;
  border: 1px solid;
}

.introjs-prevbutton {
  float: left;
}

.introjs-prevbutton i {
  margin-right: 10px;
}

.introjs-nextbutton {
  float: right;
}

.introjs-nextbutton i {
  margin-left: 10px;
}

.introjs-disabled, .introjs-disabled:hover, .introjs-disabled:focus {
  text-decoration: none;
}

.introjs-bullets {
  text-align: center;
}

.introjs-bullets ul {
  display: inline-block;
  padding: 0;
  margin: 15px auto 0;
  clear: both;
}

.introjs-bullets ul li {
  float: left;
  margin: 0 2px;
  list-style: none;
}

.introjs-bullets ul li a {
  display: block;
  width: 6px;
  height: 6px;
  text-decoration: none;
  background: #ccc;
  border-radius: 10px;
}

.introjs-bullets ul li a:hover {
  background: #999;
}

.introjs-bullets ul li a.active {
  background: #999;
}

.introjs-progress {
  height: 10px;
  margin: 10px 0 5px;
  overflow: hidden;
  background-color: #ecf0f1;
  border-radius: 4px;
}

.introjs-progressbar {
  float: left;
  width: 0;
  height: 100%;
  font-size: 10px;
  line-height: 10px;
  text-align: center;
  background-color: #08c;
}

.introjsFloatingElement {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
}

.introjs-fixedTooltip {
  position: fixed;
}

.introjs-hint {
  position: absolute;
  width: 20px;
  height: 15px;
  background: 0 0;
}

.introjs-hidehint {
  display: none;
}

.introjs-fixedhint {
  position: fixed;
}

.introjs-hint:hover > .introjs-hint-pulse {
  border: 5px solid rgba(60, 60, 60, .57);
}

.introjs-hint-pulse {
  position: absolute;
  z-index: 10;
  width: 10px;
  height: 10px;
  background-color: rgba(136, 136, 136, .24);
  border: 5px solid rgba(60, 60, 60, .27);
  border-radius: 30px;
  transition: all .2s ease-out;
}

.introjs-hint-dot {
  position: absolute;
  top: -25px;
  left: -25px;
  z-index: 1;
  width: 50px;
  height: 50px;
  background: 0 0;
  border: 10px solid rgba(146, 146, 146, .36);
  border-radius: 60px;
  opacity: 0;
  -webkit-animation: introjspulse 3s ease-out;
  animation: introjspulse 3s ease-out;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

@-webkit-keyframes introjspulse {
  0% {
    opacity: 0;
    -webkit-transform: scale(0);
  }

  25% {
    opacity: .1;
    -webkit-transform: scale(0);
  }

  50% {
    opacity: .3;
    -webkit-transform: scale(.1);
  }

  75% {
    opacity: .5;
    -webkit-transform: scale(.5);
  }

  100% {
    opacity: 0;
    -webkit-transform: scale(1);
  }
}

.ct-label {
  font-size: .858rem;
  line-height: 1;
  color: #a3afb7;
  fill: #a3afb7;
}

.ct-chart-line .ct-label, .ct-chart-bar .ct-label {
  display: block;
  display: -ms-flexbox;
  display: flex;
}

.ct-label.ct-horizontal.ct-start {
  -ms-flex-align: flex-end;
  -ms-flex-pack: flex-start;
  align-items: flex-end;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-label.ct-horizontal.ct-end {
  -ms-flex-align: flex-start;
  -ms-flex-pack: flex-start;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-label.ct-vertical.ct-start {
  -ms-flex-align: flex-end;
  -ms-flex-pack: flex-end;
  align-items: flex-end;
  justify-content: flex-end;
  text-align: right;
  text-anchor: end;
}

.ct-label.ct-vertical.ct-end {
  -ms-flex-align: flex-end;
  -ms-flex-pack: flex-start;
  align-items: flex-end;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-chart-bar .ct-label.ct-horizontal.ct-start {
  -ms-flex-align: flex-end;
  -ms-flex-pack: center;
  align-items: flex-end;
  justify-content: center;
  text-align: center;
  text-anchor: start;
}

.ct-chart-bar .ct-label.ct-horizontal.ct-end {
  -ms-flex-align: flex-start;
  -ms-flex-pack: center;
  align-items: flex-start;
  justify-content: center;
  text-align: center;
  text-anchor: start;
}

.ct-chart-bar.ct-horizontal-bars .ct-label.ct-horizontal.ct-start {
  -ms-flex-align: flex-end;
  -ms-flex-pack: flex-start;
  align-items: flex-end;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-chart-bar.ct-horizontal-bars .ct-label.ct-horizontal.ct-end {
  -ms-flex-align: flex-start;
  -ms-flex-pack: flex-start;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-chart-bar.ct-horizontal-bars .ct-label.ct-vertical.ct-start {
  -ms-flex-align: center;
  -ms-flex-pack: flex-end;
  align-items: center;
  justify-content: flex-end;
  text-align: right;
  text-anchor: end;
}

.ct-chart-bar.ct-horizontal-bars .ct-label.ct-vertical.ct-end {
  -ms-flex-align: center;
  -ms-flex-pack: flex-start;
  align-items: center;
  justify-content: flex-start;
  text-align: left;
  text-anchor: end;
}

.ct-grid {
  stroke: rgba(0, 0, 0, .1);
  stroke-width: 1px;
  stroke-dasharray: 2px;
}

.ct-point {
  stroke-width: 8px;
  stroke-linecap: round;
}

.ct-line {
  fill: none;
  stroke-width: 3px;
}

.ct-area {
  stroke: none;
  fill-opacity: .15;
}

.ct-bar {
  fill: none;
  stroke-width: 10px;
}

.ct-slice-donut {
  fill: none;
  stroke-width: 60px;
}

.ct-series-a .ct-point, .ct-series-a .ct-line, .ct-series-a .ct-bar, .ct-series-a .ct-slice-donut {
  stroke: #3e8ef7;
}

.ct-series-a .ct-slice-pie, .ct-series-a .ct-area {
  fill: #3e8ef7;
}

.ct-series-b .ct-point, .ct-series-b .ct-line, .ct-series-b .ct-bar, .ct-series-b .ct-slice-donut {
  stroke: #49de94;
}

.ct-series-b .ct-slice-pie, .ct-series-b .ct-area {
  fill: #49de94;
}

.ct-series-c .ct-point, .ct-series-c .ct-line, .ct-series-c .ct-bar, .ct-series-c .ct-slice-donut {
  stroke: #ff666b;
}

.ct-series-c .ct-slice-pie, .ct-series-c .ct-area {
  fill: #ff666b;
}

.ct-series-d .ct-point, .ct-series-d .ct-line, .ct-series-d .ct-bar, .ct-series-d .ct-slice-donut {
  stroke: #9463f7;
}

.ct-series-d .ct-slice-pie, .ct-series-d .ct-area {
  fill: #9463f7;
}

.ct-series-e .ct-point, .ct-series-e .ct-line, .ct-series-e .ct-bar, .ct-series-e .ct-slice-donut {
  stroke: #ffcd17;
}

.ct-series-e .ct-slice-pie, .ct-series-e .ct-area {
  fill: #ffcd17;
}

.ct-series-f .ct-point, .ct-series-f .ct-line, .ct-series-f .ct-bar, .ct-series-f .ct-slice-donut {
  stroke: #f74584;
}

.ct-series-f .ct-slice-pie, .ct-series-f .ct-area {
  fill: #f74584;
}

.ct-series-g .ct-point, .ct-series-g .ct-line, .ct-series-g .ct-bar, .ct-series-g .ct-slice-donut {
  stroke: #96a3fa;
}

.ct-series-g .ct-slice-pie, .ct-series-g .ct-area {
  fill: #96a3fa;
}

.ct-series-h .ct-point, .ct-series-h .ct-line, .ct-series-h .ct-bar, .ct-series-h .ct-slice-donut {
  stroke: #5a9101;
}

.ct-series-h .ct-slice-pie, .ct-series-h .ct-area {
  fill: #5a9101;
}

.ct-series-i .ct-point, .ct-series-i .ct-line, .ct-series-i .ct-bar, .ct-series-i .ct-slice-donut {
  stroke: #fa983c;
}

.ct-series-i .ct-slice-pie, .ct-series-i .ct-area {
  fill: #fa983c;
}

.ct-series-j .ct-point, .ct-series-j .ct-line, .ct-series-j .ct-bar, .ct-series-j .ct-slice-donut {
  stroke: #54cbe3;
}

.ct-series-j .ct-slice-pie, .ct-series-j .ct-area {
  fill: #54cbe3;
}

.ct-series-k .ct-point, .ct-series-k .ct-line, .ct-series-k .ct-bar, .ct-series-k .ct-slice-donut {
  stroke: #17b3a3;
}

.ct-series-k .ct-slice-pie, .ct-series-k .ct-area {
  fill: #17b3a3;
}

.ct-series-l .ct-point, .ct-series-l .ct-line, .ct-series-l .ct-bar, .ct-series-l .ct-slice-donut {
  stroke: #ab8c82;
}

.ct-series-l .ct-slice-pie, .ct-series-l .ct-area {
  fill: #ab8c82;
}

.ct-series-m .ct-point, .ct-series-m .ct-line, .ct-series-m .ct-bar, .ct-series-m .ct-slice-donut {
  stroke: #bdbdbd;
}

.ct-series-m .ct-slice-pie, .ct-series-m .ct-area {
  fill: #bdbdbd;
}

.ct-series-n .ct-point, .ct-series-n .ct-line, .ct-series-n .ct-bar, .ct-series-n .ct-slice-donut {
  stroke: #76838f;
}

.ct-series-n .ct-slice-pie, .ct-series-n .ct-area {
  fill: #76838f;
}

.ct-square {
  position: relative;
  display: block;
  width: 100%;
}

.ct-square:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 100%;
  content: "";
}

.ct-square:after {
  display: table;
  clear: both;
  content: "";
}

.ct-square > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-minor-second {
  position: relative;
  display: block;
  width: 100%;
}

.ct-minor-second:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 93.75%;
  content: "";
}

.ct-minor-second:after {
  display: table;
  clear: both;
  content: "";
}

.ct-minor-second > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-second {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-second:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 88.8888888889%;
  content: "";
}

.ct-major-second:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-second > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-minor-third {
  position: relative;
  display: block;
  width: 100%;
}

.ct-minor-third:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 83.3333333333%;
  content: "";
}

.ct-minor-third:after {
  display: table;
  clear: both;
  content: "";
}

.ct-minor-third > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-third {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-third:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 80%;
  content: "";
}

.ct-major-third:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-third > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-perfect-fourth {
  position: relative;
  display: block;
  width: 100%;
}

.ct-perfect-fourth:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 75%;
  content: "";
}

.ct-perfect-fourth:after {
  display: table;
  clear: both;
  content: "";
}

.ct-perfect-fourth > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-perfect-fifth {
  position: relative;
  display: block;
  width: 100%;
}

.ct-perfect-fifth:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 66.6666666667%;
  content: "";
}

.ct-perfect-fifth:after {
  display: table;
  clear: both;
  content: "";
}

.ct-perfect-fifth > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-minor-sixth {
  position: relative;
  display: block;
  width: 100%;
}

.ct-minor-sixth:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 62.5%;
  content: "";
}

.ct-minor-sixth:after {
  display: table;
  clear: both;
  content: "";
}

.ct-minor-sixth > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-golden-section {
  position: relative;
  display: block;
  width: 100%;
}

.ct-golden-section:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 61.804697157%;
  content: "";
}

.ct-golden-section:after {
  display: table;
  clear: both;
  content: "";
}

.ct-golden-section > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-sixth {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-sixth:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 60%;
  content: "";
}

.ct-major-sixth:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-sixth > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-minor-seventh {
  position: relative;
  display: block;
  width: 100%;
}

.ct-minor-seventh:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 56.25%;
  content: "";
}

.ct-minor-seventh:after {
  display: table;
  clear: both;
  content: "";
}

.ct-minor-seventh > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-seventh {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-seventh:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 53.3333333333%;
  content: "";
}

.ct-major-seventh:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-seventh > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-octave {
  position: relative;
  display: block;
  width: 100%;
}

.ct-octave:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 50%;
  content: "";
}

.ct-octave:after {
  display: table;
  clear: both;
  content: "";
}

.ct-octave > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-tenth {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-tenth:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 40%;
  content: "";
}

.ct-major-tenth:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-tenth > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-eleventh {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-eleventh:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 37.5%;
  content: "";
}

.ct-major-eleventh:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-eleventh > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-major-twelfth {
  position: relative;
  display: block;
  width: 100%;
}

.ct-major-twelfth:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 33.3333333333%;
  content: "";
}

.ct-major-twelfth:after {
  display: table;
  clear: both;
  content: "";
}

.ct-major-twelfth > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.ct-double-octave {
  position: relative;
  display: block;
  width: 100%;
}

.ct-double-octave:before {
  display: block;
  float: left;
  width: 0;
  height: 0;
  padding-bottom: 25%;
  content: "";
}

.ct-double-octave:after {
  display: table;
  clear: both;
  content: "";
}

.ct-double-octave > svg {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}

.chartist-tooltip-wrap, .ct-chart {
  position: relative;
}

.chartist-tooltip {
  position: absolute;
  z-index: 1;
  display: inline-block;
  min-width: 5em;
  padding: 3px 8px;
  font-size: 12px;
  font-weight: 400;
  color: #fff;
  text-align: center;
  pointer-events: none;
  background: rgba(0, 0, 0, .7);
  border-radius: 3px;
  opacity: 0;
  transition: opacity .2s linear;
}

.chartist-tooltip.tooltip-show {
  opacity: 1;
}

.ct-area, .ct-line {
  pointer-events: none;
}

.pie-progress {
  position: relative;
  margin-right: auto;
  margin-left: auto;
  text-align: center;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

.pie-progress-content, .pie-progress-number, .pie-progress-label, .pie-progress .pie-progress-icon {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.pie-progress .pie-progress-icon {
  font-size: 36px;
  font-style: normal;
}

.pie-progress-number {
  font-size: 36px;
}

.pie-progress-label {
  margin-top: 28px;
  font-size: 14px;
}

.pie-progress-label .icon {
  top: .1em;
  font-size: 2em;
}

.pie-progress-content {
  font-size: 36px;
}

.pie-progress-content .pie-progress-number, .pie-progress-content .pie-progress-label {
  position: static;
  top: auto;
  left: auto;
  line-height: 1;
  -webkit-transform: translateY(0);
  transform: translateY(0);
}

.pie-progress-content .pie-progress-label {
  margin-top: 5px !important;
}

.pie-progress-svg {
  position: relative;
  display: inline-block;
  width: 100%;
  padding-bottom: 100%;
  overflow: hidden;
  vertical-align: middle;
}

.pie-progress-svg svg {
  position: absolute;
  top: 0;
  left: 0;
  display: inline-block;
}

.pie-progress-xs .pie-progress-icon {
  font-size: 16px;
  font-style: normal;
}

.pie-progress-xs .pie-progress-content {
  font-size: 16px;
}

.pie-progress-xs .pie-progress-number {
  font-size: 16px;
}

.pie-progress-xs .pie-progress-label {
  display: none;
  margin-top: 10px;
  font-size: 10px;
}

.pie-progress-sm .pie-progress-icon {
  font-size: 26px;
  font-style: normal;
}

.pie-progress-sm .pie-progress-content {
  font-size: 26px;
}

.pie-progress-sm .pie-progress-number {
  font-size: 26px;
}

.pie-progress-sm .pie-progress-label {
  margin-top: 19px;
  font-size: 11px;
}

.pie-progress-lg .pie-progress-icon {
  font-size: 44px;
  font-style: normal;
}

.pie-progress-lg .pie-progress-content {
  font-size: 44px;
}

.pie-progress-lg .pie-progress-number {
  font-size: 44px;
}

.pie-progress-lg .pie-progress-label {
  margin-top: 36px;
  font-size: 18px;
}

.datepicker {
  padding: 4px;
  margin: 0;
  border-radius: .215rem;
  direction: ltr;
}

.datepicker-inline {
  display: block !important;
  width: 220px;
}

.datepicker-rtl {
  direction: rtl;
}

.datepicker-rtl table tr td span {
  float: right;
}

.datepicker-dropdown {
  top: 0;
  left: 0;
  padding: 4px;
}

.datepicker-dropdown:before {
  position: absolute;
  display: inline-block;
  content: "";
  border-top: 0;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #e4eaec;
  border-bottom-color: rgba(0, 0, 0, .2);
  border-left: 7px solid transparent;
}

.datepicker-dropdown:after {
  position: absolute;
  display: inline-block;
  content: "";
  border-top: 0;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #fff;
  border-left: 6px solid transparent;
}

.datepicker-dropdown.datepicker-orient-left:before {
  left: 6px;
}

.datepicker-dropdown.datepicker-orient-left:after {
  left: 7px;
}

.datepicker-dropdown.datepicker-orient-right:before {
  right: 6px;
}

.datepicker-dropdown.datepicker-orient-right:after {
  right: 7px;
}

.datepicker-dropdown.datepicker-orient-bottom:before {
  top: -7px;
}

.datepicker-dropdown.datepicker-orient-bottom:after {
  top: -6px;
}

.datepicker-dropdown.datepicker-orient-top:before {
  bottom: -7px;
  border-top: 7px solid #e4eaec;
  border-bottom: 0;
}

.datepicker-dropdown.datepicker-orient-top:after {
  bottom: -6px;
  border-top: 6px solid #fff;
  border-bottom: 0;
}

.datepicker table {
  margin: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

.datepicker table tr td, .datepicker table tr th {
  width: 30px;
  height: 30px;
  text-align: center;
  border: none;
  border-radius: .215rem;
}

.table-striped .datepicker table tr td, .table-striped .datepicker table tr th {
  background-color: transparent;
}

.datepicker table tr td.old, .datepicker table tr td.new {
  color: #a3afb7;
}

.datepicker table tr td.day:hover, .datepicker table tr td.focused {
  cursor: pointer;
  background: #e4eaec;
}

.datepicker table tr td.disabled, .datepicker table tr td.disabled:hover {
  color: #a3afb7;
  cursor: default;
  background: none;
}

.datepicker table tr td.highlighted {
  color: #fff;
  background-color: #000;
  border-color: #0bb2d4;
  border-radius: 0;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.datepicker table tr td.highlighted:hover {
  color: #fff;
  background-color: #066173;
  border-color: #0889a4;
}

.datepicker table tr td.highlighted:focus, .datepicker table tr td.highlighted.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(11, 178, 212, .5);
}

.datepicker table tr td.highlighted.disabled, .datepicker table tr td.highlighted:disabled {
  background-color: #000;
  border-color: #0bb2d4;
}

.datepicker table tr td.highlighted:not([disabled]):not(.disabled):active, .datepicker table tr td.highlighted:not([disabled]):not(.disabled).active, .show > .datepicker table tr td.highlighted.dropdown-toggle {
  color: #fff;
  background-color: black;
  border-color: #087f97;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(11, 178, 212, .5);
}

.datepicker table tr td.highlighted.focused {
  background: #0889a4;
}

.datepicker table tr td.highlighted.disabled, .datepicker table tr td.highlighted.disabled:active {
  color: #a3afb7;
  background: #0bb2d4;
}

.datepicker table tr td.today {
  color: #fff;
  background-color: #000;
  border-color: #ffdb99;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.datepicker table tr td.today:hover {
  color: #111;
  background-color: #ffb733;
  border-color: #ffc966;
}

.datepicker table tr td.today:focus, .datepicker table tr td.today.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(255, 219, 153, .5);
}

.datepicker table tr td.today.disabled, .datepicker table tr td.today:disabled {
  background-color: #000;
  border-color: #ffdb99;
}

.datepicker table tr td.today:not([disabled]):not(.disabled):active, .datepicker table tr td.today:not([disabled]):not(.disabled).active, .show > .datepicker table tr td.today.dropdown-toggle {
  color: #fff;
  background-color: black;
  border-color: #ffc559;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(255, 219, 153, .5);
}

.datepicker table tr td.today.focused {
  background: #ffc966;
}

.datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:active {
  color: #a3afb7;
  background: #ffdb99;
}

.datepicker table tr td.range {
  color: #fff;
  background-color: #000;
  border-color: #e4eaec;
  border-radius: 0;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.datepicker table tr td.range:hover {
  color: #111;
  background-color: #a8bbc2;
  border-color: #c6d3d7;
}

.datepicker table tr td.range:focus, .datepicker table tr td.range.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(228, 234, 236, .5);
}

.datepicker table tr td.range.disabled, .datepicker table tr td.range:disabled {
  background-color: #000;
  border-color: #e4eaec;
}

.datepicker table tr td.range:not([disabled]):not(.disabled):active, .datepicker table tr td.range:not([disabled]):not(.disabled).active, .show > .datepicker table tr td.range.dropdown-toggle {
  color: #fff;
  background-color: black;
  border-color: #bfcdd2;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(228, 234, 236, .5);
}

.datepicker table tr td.range.focused {
  background: #c6d3d7;
}

.datepicker table tr td.range.disabled, .datepicker table tr td.range.disabled:active {
  color: #a3afb7;
  background: #e4eaec;
}

.datepicker table tr td.range.highlighted {
  color: #fff;
  background-color: #000;
  border-color: #78cee0;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.datepicker table tr td.range.highlighted:hover {
  color: #fff;
  background-color: #2dabc5;
  border-color: #4fbfd6;
}

.datepicker table tr td.range.highlighted:focus, .datepicker table tr td.range.highlighted.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(120, 206, 224, .5);
}

.datepicker table tr td.range.highlighted.disabled, .datepicker table tr td.range.highlighted:disabled {
  background-color: #000;
  border-color: #78cee0;
}

.datepicker table tr td.range.highlighted:not([disabled]):not(.disabled):active, .datepicker table tr td.range.highlighted:not([disabled]):not(.disabled).active, .show > .datepicker table tr td.range.highlighted.dropdown-toggle {
  color: #fff;
  background-color: black;
  border-color: #44bbd4;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(120, 206, 224, .5);
}

.datepicker table tr td.range.highlighted.focused {
  background: #4fbfd6;
}

.datepicker table tr td.range.highlighted.disabled, .datepicker table tr td.range.highlighted.disabled:active {
  color: #a3afb7;
  background: #78cee0;
}

.datepicker table tr td.range.today {
  color: #fff;
  background-color: #000;
  border-color: #f2c876;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.datepicker table tr td.range.today:hover {
  color: #111;
  background-color: #e9a319;
  border-color: #eeb547;
}

.datepicker table tr td.range.today:focus, .datepicker table tr td.range.today.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(242, 200, 118, .5);
}

.datepicker table tr td.range.today.disabled, .datepicker table tr td.range.today:disabled {
  background-color: #000;
  border-color: #f2c876;
}

.datepicker table tr td.range.today:not([disabled]):not(.disabled):active, .datepicker table tr td.range.today:not([disabled]):not(.disabled).active, .show > .datepicker table tr td.range.today.dropdown-toggle {
  color: #fff;
  background-color: black;
  border-color: #ecb13c;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(242, 200, 118, .5);
}

.datepicker table tr td.range.today.disabled, .datepicker table tr td.range.today.disabled:active {
  color: #a3afb7;
  background: #f2c876;
}

.datepicker table tr td.selected, .datepicker table tr td.selected.highlighted {
  color: #111;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, .25);
  background-color: #fff;
  border-color: #ccd5db;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.datepicker table tr td.selected:hover, .datepicker table tr td.selected.highlighted:hover {
  color: #111;
  background-color: #a3afb7;
  border-color: #aebcc6;
}

.datepicker table tr td.selected:focus, .datepicker table tr td.selected.focus, .datepicker table tr td.selected.highlighted:focus, .datepicker table tr td.selected.highlighted.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(204, 213, 219, .5);
}

.datepicker table tr td.selected.disabled, .datepicker table tr td.selected:disabled, .datepicker table tr td.selected.highlighted.disabled, .datepicker table tr td.selected.highlighted:disabled {
  background-color: #fff;
  border-color: #ccd5db;
}

.datepicker table tr td.selected:not([disabled]):not(.disabled):active, .datepicker table tr td.selected:not([disabled]):not(.disabled).active, .show > .datepicker table tr td.selected.dropdown-toggle, .datepicker table tr td.selected.highlighted:not([disabled]):not(.disabled):active, .datepicker table tr td.selected.highlighted:not([disabled]):not(.disabled).active, .show > .datepicker table tr td.selected.highlighted.dropdown-toggle {
  color: #111;
  background-color: #e6e5e5;
  border-color: #a7b6c1;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(204, 213, 219, .5);
}

.datepicker table tr td.active, .datepicker table tr td.active.highlighted {
  color: #111;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, .25);
  background-color: #fff;
  border-color: #3e8ef7;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.datepicker table tr td.active:hover, .datepicker table tr td.active.highlighted:hover {
  color: #fff;
  background-color: #3e8ef7;
  border-color: #0d71f5;
}

.datepicker table tr td.active:focus, .datepicker table tr td.active.focus, .datepicker table tr td.active.highlighted:focus, .datepicker table tr td.active.highlighted.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(62, 142, 247, .5);
}

.datepicker table tr td.active.disabled, .datepicker table tr td.active:disabled, .datepicker table tr td.active.highlighted.disabled, .datepicker table tr td.active.highlighted:disabled {
  background-color: #fff;
  border-color: #3e8ef7;
}

.datepicker table tr td.active:not([disabled]):not(.disabled):active, .datepicker table tr td.active:not([disabled]):not(.disabled).active, .show > .datepicker table tr td.active.dropdown-toggle, .datepicker table tr td.active.highlighted:not([disabled]):not(.disabled):active, .datepicker table tr td.active.highlighted:not([disabled]):not(.disabled).active, .show > .datepicker table tr td.active.highlighted.dropdown-toggle {
  color: #111;
  background-color: #e6e5e5;
  border-color: #0a6beb;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(62, 142, 247, .5);
}

.datepicker table tr td span {
  display: block;
  float: left;
  width: 23%;
  height: 54px;
  margin: 1%;
  line-height: 54px;
  cursor: pointer;
  border-radius: .215rem;
}

.datepicker table tr td span:hover, .datepicker table tr td span.focused {
  background: #e4eaec;
}

.datepicker table tr td span.disabled, .datepicker table tr td span.disabled:hover {
  color: #a3afb7;
  cursor: default;
  background: none;
}

.datepicker table tr td span.active, .datepicker table tr td span.active:hover, .datepicker table tr td span.active.disabled, .datepicker table tr td span.active.disabled:hover {
  color: #111;
  background-color: #fff;
  border-color: #3e8ef7;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.datepicker table tr td span.active:hover, .datepicker table tr td span.active:hover:hover, .datepicker table tr td span.active.disabled:hover, .datepicker table tr td span.active.disabled:hover:hover {
  color: #fff;
  background-color: #3e8ef7;
  border-color: #0d71f5;
}

.datepicker table tr td span.active:focus, .datepicker table tr td span.active.focus, .datepicker table tr td span.active:hover:focus, .datepicker table tr td span.active:hover.focus, .datepicker table tr td span.active.disabled:focus, .datepicker table tr td span.active.disabled.focus, .datepicker table tr td span.active.disabled:hover:focus, .datepicker table tr td span.active.disabled:hover.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(62, 142, 247, .5);
}

.datepicker table tr td span.active.disabled, .datepicker table tr td span.active:disabled, .datepicker table tr td span.active:hover.disabled, .datepicker table tr td span.active:hover:disabled, .datepicker table tr td span.active.disabled.disabled, .datepicker table tr td span.active.disabled:disabled, .datepicker table tr td span.active.disabled:hover.disabled, .datepicker table tr td span.active.disabled:hover:disabled {
  background-color: #fff;
  border-color: #3e8ef7;
}

.datepicker table tr td span.active:not([disabled]):not(.disabled):active, .datepicker table tr td span.active:not([disabled]):not(.disabled).active, .show > .datepicker table tr td span.active.dropdown-toggle, .datepicker table tr td span.active:hover:not([disabled]):not(.disabled):active, .datepicker table tr td span.active:hover:not([disabled]):not(.disabled).active, .show > .datepicker table tr td span.active:hover.dropdown-toggle, .datepicker table tr td span.active.disabled:not([disabled]):not(.disabled):active, .datepicker table tr td span.active.disabled:not([disabled]):not(.disabled).active, .show > .datepicker table tr td span.active.disabled.dropdown-toggle, .datepicker table tr td span.active.disabled:hover:not([disabled]):not(.disabled):active, .datepicker table tr td span.active.disabled:hover:not([disabled]):not(.disabled).active, .show > .datepicker table tr td span.active.disabled:hover.dropdown-toggle {
  color: #111;
  background-color: #e6e5e5;
  border-color: #0a6beb;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(62, 142, 247, .5);
}

.datepicker table tr td span.old, .datepicker table tr td span.new {
  color: #a3afb7;
}

.datepicker .datepicker-switch {
  width: 145px;
}

.datepicker .datepicker-switch, .datepicker .prev, .datepicker .next, .datepicker tfoot tr th {
  cursor: pointer;
}

.datepicker .datepicker-switch:hover, .datepicker .prev:hover, .datepicker .next:hover, .datepicker tfoot tr th:hover {
  background: #e4eaec;
}

.datepicker .cw {
  width: 12px;
  padding: 0 2px 0 5px;
  font-size: 10px;
  vertical-align: middle;
}

.input-group.date .input-group-addon {
  cursor: pointer;
}

.input-daterange {
  width: 100%;
}

.input-daterange input {
  text-align: center;
}

.input-daterange .input-group {
  float: left;
  width: 50%;
}

.input-daterange .input-group:first-child input {
  border-radius: 0;
}

.input-daterange .input-group:not(:last-child) input {
  border-radius: 0;
}

.input-daterange .input-group:not(:first-child):not(:last-child) .input-group-addon {
  border-right-width: 0;
  border-left-width: 0;
  border-radius: 0;
}

.input-daterange .input-group:last-child .input-group-addon {
  border-left-width: 0;
  border-radius: 0;
}

.input-daterange .input-group:last-child input {
  border-radius: 0 3px 3px 0;
}

.input-daterange .input-group-addon {
  width: auto;
  min-width: 16px;
  font-weight: 300;
  line-height: 1.57142857;
  text-align: center;
  vertical-align: middle;
  background-color: #e4eaec;
  border: solid #e4eaec;
  border-width: 1px 0;
}

.input-daterange .input-group-addon:first-child {
  border-left-width: 1px;
}

.input-daterange .input-group-addon:last-child {
  border-right-width: 1px;
}

.btn-select {
  height: 2.573rem;
  padding: .429rem 1rem;
  font-size: 1rem;
  line-height: 1.57142857;
  color: #76838f;
  background-color: #fff;
  background-image: none;
  border: 1px solid #e4eaec;
  border-radius: .215rem;
}

.btn-group.open .btn-select, .btn-select:focus {
  border-color: #3e8ef7 !important;
  box-shadow: none;
}

select.bs-select-hidden, select.selectpicker {
  display: none !important;
}

.bootstrap-select {
  width: 220px \0;
  /*IE9 and below*/
}

.bootstrap-select.show .open > .dropdown-menu {
  display: block;
}

.bootstrap-select > .dropdown-toggle {
  z-index: 1;
  width: 100%;
  padding-right: 25px;
}

.bootstrap-select > .dropdown-toggle.bs-placeholder, .bootstrap-select > .dropdown-toggle.bs-placeholder:hover, .bootstrap-select > .dropdown-toggle.bs-placeholder:focus, .bootstrap-select > .dropdown-toggle.bs-placeholder:active {
  color: #a3afb7;
}

.bootstrap-select .icon {
  width: 1em;
  text-align: center;
}

.bootstrap-select > select {
  position: absolute !important;
  bottom: 0;
  left: 50%;
  display: block !important;
  width: .5px !important;
  height: 100% !important;
  padding: 0 !important;
  border: none;
  opacity: 0 !important;
}

.bootstrap-select > select.mobile-device {
  top: 0;
  left: 0;
  z-index: 2;
  display: block !important;
  width: 100% !important;
}

.has-error .bootstrap-select .dropdown-toggle, .error .bootstrap-select .dropdown-toggle {
  border-color: #ff3d64;
}

.bootstrap-select.fit-width {
  width: auto !important;
}

.bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
  width: 220px;
}

.bootstrap-select > .dropdown-toggle:after {
  display: none;
  margin: 0;
  border: none;
}

.bootstrap-select > .dropdown-toggle .caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: .3em;
  vertical-align: middle;
  border-top: 4px solid;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  transition: .25s;
  -webkit-transform: scale(1.001);
  transform: scale(1.001);
}

.bootstrap-select.dropup .caret {
  border-top: 0;
  border-bottom: 4px solid;
}

.bootstrap-select .dropdown-menu > li {
  padding: 0 5px;
  margin: 2px 0;
}

.bootstrap-select .dropdown-menu > .dropdown-header {
  padding: 8px 20px 6px;
}

.bootstrap-select .dropdown-menu > li.divider {
  height: 1px;
  margin: 10px 0;
  overflow: hidden;
  background-color: #e4eaec;
}

.bootstrap-select .dropdown-menu > li > a {
  display: block;
  padding: .572rem 1.072rem;
  font-weight: normal;
  line-height: 1.57142857;
  color: #76838f;
  white-space: nowrap;
  border-radius: .215rem;
  transition: background-color .25s;
}

.bootstrap-select .dropdown-menu > li > a:hover, .bootstrap-select .dropdown-menu > li > a:focus {
  color: #76838f;
  text-decoration: none;
  background-color: #f3f7f9;
}

.bootstrap-select .dropdown-menu > .active > a, .bootstrap-select .dropdown-menu > .active > a:hover, .bootstrap-select .dropdown-menu > .active > a:focus {
  color: #76838f;
  text-decoration: none;
  background-color: #f3f7f9;
  outline: 0;
}

.bootstrap-select .dropdown-menu > .disabled > a, .bootstrap-select .dropdown-menu > .disabled > a:hover, .bootstrap-select .dropdown-menu > .disabled > a:focus {
  color: #e4eaec;
}

.bootstrap-select .dropdown-menu > .disabled > a:hover, .bootstrap-select .dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  cursor: not-allowed;
  background-color: transparent;
  background-image: none;
}

.bootstrap-select.form-control {
  padding: 0;
  margin-bottom: 0;
  border: none;
}

.bootstrap-select.form-control:not([class*="col-"]) {
  width: 100%;
}

.bootstrap-select.form-control.input-group-btn {
  z-index: auto;
}

.bootstrap-select.form-control.input-group-btn:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}

.bootstrap-select.btn-group:not(.input-group-btn), .bootstrap-select.btn-group[class*="col-"] {
  display: inline-block;
  float: none;
  margin-left: 0;
}

.bootstrap-select.btn-group.dropdown-menu-right, .bootstrap-select.btn-group[class*="col-"].dropdown-menu-right, .row .bootstrap-select.btn-group[class*="col-"].dropdown-menu-right {
  float: right;
}

.form-inline .bootstrap-select.btn-group, .form-horizontal .bootstrap-select.btn-group, .form-group .bootstrap-select.btn-group {
  margin-bottom: 0;
}

.form-group-lg .bootstrap-select.btn-group.form-control, .form-group-sm .bootstrap-select.btn-group.form-control {
  padding: 0;
}

.form-group-lg .bootstrap-select.btn-group.form-control .dropdown-toggle, .form-group-sm .bootstrap-select.btn-group.form-control .dropdown-toggle {
  height: 100%;
  font-size: inherit;
  line-height: inherit;
  border-radius: inherit;
}

.form-inline .bootstrap-select.btn-group .form-control {
  width: 100%;
}

.bootstrap-select.btn-group.disabled, .bootstrap-select.btn-group > .disabled {
  cursor: not-allowed;
}

.bootstrap-select.btn-group.disabled:focus, .bootstrap-select.btn-group > .disabled:focus {
  outline: none !important;
}

.bootstrap-select.btn-group.bs-container {
  position: absolute;
  height: 0 !important;
  padding: 0 !important;
}

.bootstrap-select.btn-group.bs-container .dropdown-menu {
  z-index: 1450;
}

.bootstrap-select.btn-group .dropdown-toggle .filter-option {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-align: left;
}

.bootstrap-select.btn-group .dropdown-toggle .filter-option > .icon {
  margin-right: .3em;
}

.bootstrap-select.btn-group .dropdown-toggle .caret {
  position: absolute;
  top: 50%;
  right: 12px;
  margin-top: -2px;
  vertical-align: middle;
}

.bootstrap-select.btn-group[class*="col-"] .dropdown-toggle {
  width: 100%;
}

.bootstrap-select.btn-group .dropdown-menu {
  box-sizing: border-box;
  min-width: 100%;
  margin-top: 7px;
}

.bootstrap-select.btn-group .dropdown-menu.inner {
  position: static;
  float: none;
  padding: 0;
  margin: 0;
  border: 0;
  border-radius: 0;
  box-shadow: none;
}

.bootstrap-select.btn-group .dropdown-menu li {
  position: relative;
}

.bootstrap-select.btn-group .dropdown-menu li.active small {
  color: #fff;
}

.bootstrap-select.btn-group .dropdown-menu li.disabled a {
  cursor: not-allowed;
}

.bootstrap-select.btn-group .dropdown-menu li a {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.bootstrap-select.btn-group .dropdown-menu li a > .icon {
  margin-right: .3em;
}

.bootstrap-select.btn-group .dropdown-menu li a.opt {
  position: relative;
  padding-left: 2.25em;
}

.bootstrap-select.btn-group .dropdown-menu li a span.check-mark {
  display: none;
}

.bootstrap-select.btn-group .dropdown-menu li a span.text {
  display: inline-block;
}

.bootstrap-select.btn-group .dropdown-menu li small {
  padding-left: .5em;
}

.bootstrap-select.btn-group .dropdown-menu .notify {
  position: absolute;
  bottom: 6px;
  box-sizing: border-box;
  width: 94%;
  min-height: 26px;
  padding: 3px 5px;
  margin: 0 3%;
  color: #76838f;
  pointer-events: none;
  background-color: #f3f7f9;
  border: 1px solid #e4eaec;
  border-radius: .215rem;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
  opacity: .9;
}

.bootstrap-select.btn-group .no-results {
  padding: 5px 10px;
  margin: 0 8px;
  color: #76838f;
  white-space: nowrap;
  background-color: #f3f7f9;
  border-radius: .215rem;
}

.bootstrap-select.btn-group.fit-width .dropdown-toggle .filter-option {
  position: static;
}

.bootstrap-select.btn-group.fit-width .dropdown-toggle .caret {
  position: static;
  top: auto;
  margin-top: -1px;
}

.bootstrap-select.btn-group.show-tick .dropdown-menu li.selected a span.check-mark {
  position: absolute;
  right: 15px;
  display: inline-block;
  margin-top: 5px;
}

.bootstrap-select.btn-group.show-tick .dropdown-menu li a span.text {
  margin-right: 34px;
}

.bootstrap-select.show-menu-arrow.open > .dropdown-toggle {
  z-index: 1451;
}

.bootstrap-select.show-menu-arrow .dropdown-toggle:before {
  position: absolute;
  bottom: -9px;
  left: 9px;
  display: none;
  content: "";
  border-right: 8px solid transparent;
  border-bottom: 8px solid #e4eaec;
  border-left: 8px solid transparent;
}

.bootstrap-select.show-menu-arrow .dropdown-toggle:after {
  position: absolute;
  bottom: -9px;
  left: 10px;
  display: none;
  content: "";
  border-right: 7px solid transparent;
  border-bottom: 7px solid white;
  border-left: 7px solid transparent;
}

.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle:before {
  top: -8px;
  bottom: auto;
  border-top: 8px solid #e4eaec;
  border-bottom: 0;
}

.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle:after {
  top: -8px;
  bottom: auto;
  border-top: 7px solid white;
  border-bottom: 0;
}

.bootstrap-select.show-menu-arrow.float-right .dropdown-toggle:before {
  right: 12px;
  left: auto;
}

.bootstrap-select.show-menu-arrow.float-right .dropdown-toggle:after {
  right: 13px;
  left: auto;
}

.bootstrap-select.show-menu-arrow.open > .dropdown-toggle:before, .bootstrap-select.show-menu-arrow.open > .dropdown-toggle:after {
  display: block;
}

.bs-searchbox, .bs-actionsbox, .bs-donebutton {
  padding: 4px 8px;
}

.bs-actionsbox {
  box-sizing: border-box;
  width: 100%;
}

.bs-actionsbox .btn-group button {
  width: 50%;
}

.bs-donebutton {
  box-sizing: border-box;
  float: left;
  width: 100%;
}

.bs-donebutton .btn-group button {
  width: 100%;
}

.bs-searchbox + .bs-actionsbox {
  padding: 0 8px 4px;
}

.bs-searchbox .form-control {
  float: none;
  width: 100%;
  margin-bottom: 0;
}

.filter-option.pull-left {
  float: left !important;
}

.select2-container {
  position: relative;
  box-sizing: border-box;
  display: inline-block;
  margin: 0;
  vertical-align: middle;
}

.select2-container .select2-selection--single {
  box-sizing: border-box;
  display: block;
  height: 28px;
  cursor: pointer;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}

.select2-container .select2-selection--single .select2-selection__rendered {
  display: block;
  padding-right: 20px;
  padding-left: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.select2-container .select2-selection--single .select2-selection__clear {
  position: relative;
}

.select2-container[dir="rtl"] .select2-selection--single .select2-selection__rendered {
  padding-right: 8px;
  padding-left: 20px;
}

.select2-container .select2-selection--multiple {
  box-sizing: border-box;
  display: block;
  min-height: 32px;
  cursor: pointer;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}

.select2-container .select2-selection--multiple .select2-selection__rendered {
  display: inline-block;
  padding-left: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.select2-container .select2-search--inline {
  float: left;
}

.select2-container .select2-search--inline .select2-search__field {
  box-sizing: border-box;
  padding: 0;
  margin-top: 5px;
  font-size: 100%;
  border: none;
}

.select2-container .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
  -webkit-appearance: none;
}

.select2-container .select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice, .select2-container .select2-results__otption .select2-results__otption--highlighted {
  color: #fff;
  background: #3e8ef7;
  border-color: #3e8ef7;
}

.select2-container .select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove, .select2-container .select2-results__otption .select2-results__otption--highlighted .select2-selection__choice__remove {
  color: #fff;
}

.select2-container .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice {
  color: #fff;
  background: #11c26d;
  border-color: #11c26d;
}

.select2-container .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
  color: #fff;
}

.select2-container .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice {
  color: #fff;
  background: #0bb2d4;
  border-color: #0bb2d4;
}

.select2-container .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
  color: #fff;
}

.select2-container .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {
  color: #fff;
  background: #eb6709;
  border-color: #eb6709;
}

.select2-container .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
  color: #fff;
}

.select2-container .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {
  color: #fff;
  background: #ff4c52;
  border-color: #ff4c52;
}

.select2-container .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
  color: #fff;
}

.select2-dropdown {
  position: absolute;
  left: -100000px;
  z-index: 1200;
  box-sizing: border-box;
  display: block;
  width: 100%;
  background-color: white;
  border: 1px solid #e4eaec;
  border-radius: .215rem;
}

.select2-results {
  display: block;
}

.select2-results__options {
  padding: 0;
  margin: 0;
  list-style: none;
}

.select2-results__option {
  padding: 6px;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}

.select2-results__option[aria-selected] {
  cursor: pointer;
}

.select2-container--open .select2-dropdown {
  left: 0;
}

.select2-container--open .select2-dropdown--above {
  border-bottom: none;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.select2-container--open .select2-dropdown--below {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.select2-search--dropdown {
  display: block;
  padding: 4px;
}

.select2-search--dropdown .select2-search__field {
  box-sizing: border-box;
  width: 100%;
  padding: 4px;
}

.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
  -webkit-appearance: none;
}

.select2-search--dropdown.select2-search--hide {
  display: none;
}

.select2-close-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1800;
  display: block;
  width: auto;
  min-width: 100%;
  height: auto;
  min-height: 100%;
  padding: 0;
  margin: 0;
  background-color: #fff;
  filter: alpha(opacity=0);
  border: 0;
  opacity: 0;
}

.select2-hidden-accessible {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  overflow: hidden !important;
  clip: rect(0 0 0 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
  -webkit-clip-path: inset(50%) !important;
  clip-path: inset(50%) !important;
}

.select2-container--default {
  display: block;
}

.select2-container--default .select2-selection {
  font-family: "Roboto", sans-serif;
  font-size: 1rem;
  color: #76838f;
  background-color: #fff;
  border: 1px solid #e4eaec;
  border-radius: .215rem;
  outline: 0;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  font-family: "Roboto", sans-serif;
  font-size: 1rem;
  color: #76838f;
  background-color: #fff;
  border: 1px solid #e4eaec;
  border-radius: .215rem;
}

.select2-container--default .select2-search__field {
  outline: 0;
}

.select2-container--default .select2-search__field::-webkit-input-placeholder {
  color: #a3afb7;
}

.select2-container--default .select2-search__field:-moz-placeholder {
  color: #a3afb7;
}

.select2-container--default .select2-search__field::-moz-placeholder {
  color: #a3afb7;
  opacity: 1;
}

.select2-container--default .select2-search__field:-ms-input-placeholder {
  color: #a3afb7;
}

.select2-container--default .select2-results__option[role=group] {
  padding: 0;
}

.select2-container--default .select2-results__option[aria-disabled=true] {
  color: #e4eaec;
  cursor: not-allowed;
}

.select2-container--default .select2-results__option[aria-selected=true] {
  color: #76838f;
  background-color: #f3f7f9;
}

.select2-container--default .select2-results__option .select2-results__option {
  padding: .429rem 1rem;
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__group {
  padding-left: 0;
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option {
  padding-left: 2rem;
  margin-left: -1rem;
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  padding-left: 3rem;
  margin-left: -2rem;
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  padding-left: 4rem;
  margin-left: -3rem;
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  padding-left: 5rem;
  margin-left: -4rem;
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
  padding-left: 6rem;
  margin-left: -5rem;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  color: #76838f;
  background-color: #f3f7f9;
}

.select2-container--default .select2-results__group {
  display: block;
  padding: .429rem 1rem;
  font-size: .858rem;
  line-height: 1.57142857;
  color: #37474f;
  white-space: nowrap;
  cursor: default;
}

.select2-container--default.select2-container--open .select2-selection {
  border-color: #3e8ef7;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px fade(#3e8ef7, .6);
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}

.select2-container--default.select2-container--open .select2-selection .select2-selection__arrow b {
  border-color: transparent transparent #a3afb7;
  border-width: 0 .286rem .286rem;
}

.select2-container--default.select2-container--open.select2-container--above .select2-selection {
  border-top-color: transparent;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.select2-container--default.select2-container--open.select2-container--below .select2-selection {
  border-bottom-color: transparent;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.select2-container--default.input-sm, .select2-container--default.input-lg {
  height: auto;
  padding: 0;
  font-size: 12px;
  line-height: 1;
  border-radius: 0;
}

.select2-container--default .select2-dropdown {
  margin-top: -1px;
  overflow-x: hidden;
  border-color: #3e8ef7;
  box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
}

.select2-container--default .select2-dropdown--above {
  margin-top: 1px;
}

.select2-container--default .select2-results > .select2-results__options {
  max-height: 200px;
  overflow-y: auto;
}

.select2-container--default .select2-selection--single {
  height: 2.573rem;
  padding: .429rem 1.858rem .429rem 1rem;
  line-height: 1.57142857;
  background-color: #fff;
  border: 1px solid #e4eaec;
  border-radius: .215rem;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  padding: 0;
  color: #76838f;
}

.select2-container--default .select2-selection--single .select2-selection__clear {
  float: right;
  margin-right: 10px;
  font-weight: 500;
  color: #a3afb7;
  cursor: pointer;
}

.select2-container--default .select2-selection--single .select2-selection__clear:hover {
  color: #333;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #a3afb7;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  position: absolute;
  top: 0;
  right: 1rem;
  bottom: 0;
  width: .286rem;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  position: absolute;
  top: 50%;
  left: 0;
  width: 0;
  height: 0;
  margin-top: -0.143rem;
  margin-left: -0.286rem;
  border-color: #a3afb7 transparent transparent;
  border-style: solid;
  border-width: .286rem .286rem 0;
}

.select2-container--default[dir="rtl"] .select2-selection--single {
  padding-right: 1rem;
  padding-left: 1.858rem;
}

.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__clear {
  float: left;
}

.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__arrow {
  right: auto;
  left: 1rem;
}

.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__rendered {
  padding-right: 0;
  padding-left: 0;
  text-align: right;
}

.select2-container--default.select2-container--disabled .select2-selection--single {
  cursor: not-allowed;
  background-color: #f3f7f9;
}

.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {
  display: none;
}

.select2-container--default.select2-container--disabled .select2-search__field {
  background-color: #f3f7f9;
}

.select2-container--default.input-sm .select2-selection--single, .input-group-sm .select2-container--default .select2-selection--single, .form-group-sm .select2-container--default .select2-selection--single {
  height: 2.288rem;
  padding: .429rem 1.716rem .429rem .858rem;
  font-size: .858rem;
  line-height: 1.5;
  border-radius: .143rem;
}

.select2-container--default.input-sm .select2-selection--single .select2-selection__arrow b, .input-group-sm .select2-container--default .select2-selection--single .select2-selection__arrow b, .form-group-sm .select2-container--default .select2-selection--single .select2-selection__arrow b {
  margin-left: -0.429rem;
}

.select2-container--default.input-lg .select2-selection--single, .input-group-lg .select2-container--default .select2-selection--single, .form-group-lg .select2-container--default .select2-selection--single {
  height: 3.2876666667rem;
  padding: .715rem 2.36rem .715rem 1.286rem;
  font-size: 1.286rem;
  line-height: 1.3333333333;
  border-radius: .286rem;
}

.select2-container--default.input-lg .select2-selection--single .select2-selection__arrow, .input-group-lg .select2-container--default .select2-selection--single .select2-selection__arrow, .form-group-lg .select2-container--default .select2-selection--single .select2-selection__arrow {
  width: .358rem;
}

.select2-container--default.input-lg .select2-selection--single .select2-selection__arrow b, .input-group-lg .select2-container--default .select2-selection--single .select2-selection__arrow b, .form-group-lg .select2-container--default .select2-selection--single .select2-selection__arrow b {
  margin-top: -0.179rem;
  margin-left: -0.358rem;
  margin-left: -0.715rem;
  border-width: .358rem .358rem 0;
}

.select2-container--default .select2-selection--multiple {
  cursor: text;
  background-color: white;
  border: 1px solid #e4eaec;
  border-radius: .215rem;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
  box-sizing: border-box;
  width: 100%;
  padding: 0 5px;
  margin: 0;
  list-style: none;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
  list-style: none;
}

.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
  float: left;
  margin-top: 5px;
  color: #a3afb7;
}

.select2-container--default .select2-selection--multiple .select2-selection__clear {
  float: right;
  margin-top: 5px;
  margin-right: 10px;
  font-weight: bold;
  cursor: pointer;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  float: left;
  padding: 0 .429rem;
  margin: .3rem 0 0 .5rem;
  color: #76838f;
  cursor: default;
  background: #e4eaec;
  border: 1px solid #e4eaec;
  border-radius: .215rem;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  display: inline-block;
  margin-right: .2145rem;
  font-weight: 500;
  color: #999;
  cursor: pointer;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #333;
}

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice, .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__placeholder, .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-search--inline {
  float: right;
}

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
  margin-right: .5rem;
  margin-left: 0;
}

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
  margin-right: auto;
  margin-left: 2px;
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
  border: solid #3e8ef7 1px;
  outline: 0;
}

.select2-container--default.select2-container--disabled .select2-selection--multiple {
  cursor: not-allowed;
  background-color: #f3f7f9;
}

.select2-container--default.select2-container--disabled .select2-selection__choice__remove {
  display: none;
}

.select2-container--default.input-sm .select2-selection--multiple, .input-group-sm .select2-container--default .select2-selection--multiple, .form-group-sm .select2-container--default .select2-selection--multiple {
  min-height: 2.288rem;
}

.select2-container--default.input-sm .select2-selection--multiple .select2-selection__choice, .input-group-sm .select2-container--default .select2-selection--multiple .select2-selection__choice, .form-group-sm .select2-container--default .select2-selection--multiple .select2-selection__choice {
  padding: 0 .429rem;
  margin: .357rem 0 0 .429rem;
  font-size: .858rem;
  line-height: 1.5;
}

.select2-container--default.input-sm .select2-selection--multiple .select2-search--inline .select2-search__field, .input-group-sm .select2-container--default .select2-selection--multiple .select2-search--inline .select2-search__field, .form-group-sm .select2-container--default .select2-selection--multiple .select2-search--inline .select2-search__field {
  height: 2.145rem;
  padding: 0 .858rem;
  font-size: .858rem;
  line-height: 1.5;
}

.select2-container--default.input-lg.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #a3afb7;
  border-width: 0 .358rem .358rem;
}

.input-group-lg .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #a3afb7;
  border-width: 0 .358rem .358rem;
}

.input-group .select2-container--default {
  position: relative;
  z-index: 2;
  display: table;
  float: left;
  width: 100%;
  margin-bottom: 0;
  table-layout: fixed;
}

.has-warning .select2-dropdown, .has-warning .select2-selection {
  border-color: #eb6709;
}

.has-warning .select2-container--open .select2-selection {
  border-color: #eb6709;
}

.has-warning.select2-drop-active {
  border-color: #eb6709;
}

.has-warning.select2-drop-active.select2-drop.select2-drop-above {
  border-top-color: #eb6709;
}

.has-error .select2-dropdown, .has-error .select2-selection {
  border-color: #ff4c52;
}

.has-error .select2-container--open .select2-selection {
  border-color: #ff4c52;
}

.has-error.select2-drop-active {
  border-color: #ff4c52;
}

.has-error.select2-drop-active.select2-drop.select2-drop-above {
  border-top-color: #ff4c52;
}

.has-success .select2-dropdown, .has-success .select2-selection {
  border-color: #11c26d;
}

.has-success .select2-container--open .select2-selection {
  border-color: #11c26d;
}

.has-success.select2-drop-active {
  border-color: #11c26d;
}

.has-success.select2-drop-active.select2-drop.select2-drop-above {
  border-top-color: #11c26d;
}

.modal-open .select2-container {
  z-index: 1701;
}

.wizard-pane {
  position: absolute;
  top: 0;
  visibility: hidden;
  opacity: 0;
  transition: opacity .2s ease;
}

.wizard-pane.active {
  position: relative;
  visibility: visible;
  opacity: 1;
}

.wizard-pane.activing {
  visibility: visible;
}

.wizard-content {
  position: relative;
  padding: 0;
  transition: height .2s ease;
}

.panel-body > .wizard-buttons, .tab-content > .wizard-buttons {
  margin-top: 22px;
}

.color-active {
  color: #3e8ef7 !important;
}

.color-error {
  color: #ff4c52 !important;
}

.color-done {
  color: #11c26d !important;
}

.has-warning .twitter-typeahead .tt-hint,.has-warning .twitter-typeahead .tt-input{border-color:#fff;box-shadow:inset 0 1px 1px rgba(0,0,0,.075)}.has-warning .twitter-typeahead .tt-hint:focus,.has-warning .twitter-typeahead .tt-input:focus{border-color:#e6e5e5;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #fff}.has-error .twitter-typeahead .tt-hint,.has-error .twitter-typeahead .tt-input{border-color:#fff;box-shadow:inset 0 1px 1px rgba(0,0,0,.075)}.has-error .twitter-typeahead .tt-hint:focus,.has-error .twitter-typeahead .tt-input:focus{border-color:#e6e5e5;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #fff}.has-success .twitter-typeahead .tt-hint,.has-success .twitter-typeahead .tt-input{border-color:#fff;box-shadow:inset 0 1px 1px rgba(0,0,0,.075)}.has-success .twitter-typeahead .tt-hint:focus,.has-success .twitter-typeahead .tt-input:focus{border-color:#e6e5e5;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #fff}.input-group .twitter-typeahead:first-child .tt-hint,.input-group .twitter-typeahead:first-child .tt-input{width:100%;border-top-left-radius:.215rem;border-bottom-left-radius:.215rem}.input-group .twitter-typeahead:last-child .tt-hint,.input-group .twitter-typeahead:last-child .tt-input{width:100%;border-top-right-radius:.215rem;border-bottom-right-radius:.215rem}select .input-group.input-group-sm .twitter-typeahead .tt-hint,select .input-group.input-group-sm .twitter-typeahead .tt-input{padding-top:0;padding-bottom:0}.input-group.input-group-sm .twitter-typeahead:not(:first-child):not(:last-child) .tt-hint,.input-group.input-group-sm .twitter-typeahead:not(:first-child):not(:last-child) .tt-input{border-radius:0}.input-group.input-group-sm .twitter-typeahead:first-child .tt-hint,.input-group.input-group-sm .twitter-typeahead:first-child .tt-input{border-top-left-radius:.143rem;border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:.143rem}.input-group.input-group-sm .twitter-typeahead:last-child .tt-hint,.input-group.input-group-sm .twitter-typeahead:last-child .tt-input{border-top-left-radius:0;border-top-right-radius:.143rem;border-bottom-right-radius:.143rem;border-bottom-left-radius:0}select .input-group.input-group-lg .twitter-typeahead .tt-hint,select .input-group.input-group-lg .twitter-typeahead .tt-input{padding-top:0;padding-bottom:0}.input-group.input-group-lg .twitter-typeahead:not(:first-child):not(:last-child) .tt-hint,.input-group.input-group-lg .twitter-typeahead:not(:first-child):not(:last-child) .tt-input{border-radius:0}.input-group.input-group-lg .twitter-typeahead:first-child .tt-hint,.input-group.input-group-lg .twitter-typeahead:first-child .tt-input{border-top-left-radius:.286rem;border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:.286rem}.input-group.input-group-lg .twitter-typeahead:last-child .tt-hint,.input-group.input-group-lg .twitter-typeahead:last-child .tt-input{border-top-left-radius:0;border-top-right-radius:.286rem;border-bottom-right-radius:.286rem;border-bottom-left-radius:0}.twitter-typeahead{float:left;width:100%}.input-group .twitter-typeahead{display:table-cell!important}.twitter-typeahead .tt-hint{color:#a3afb7}.twitter-typeahead .tt-input{z-index:2}.twitter-typeahead .tt-input[disabled],.twitter-typeahead .tt-input[readonly],fieldset[disabled] .twitter-typeahead .tt-input{cursor:not-allowed;background-color:#f3f7f9!important}.tt-dropdown-menu,.tt-menu{position:absolute;top:100%;left:0;z-index:1200;width:100%;min-width:160px;padding:5px 0;margin:2px 0 0;font-size:1rem;list-style:none;background-color:#fff;background-clip:padding-box;border:1px solid #ccc;border:1px solid #e4eaec;border-radius:.215rem;box-shadow:0 3px 12px rgba(0,0,0,.05)}.tt-dropdown-menu .tt-suggestion,.tt-menu .tt-suggestion{display:block;padding:3px 20px;clear:both;font-weight:300;line-height:1.57142857;color:#76838f}.tt-dropdown-menu .tt-suggestion.tt-cursor,.tt-dropdown-menu .tt-suggestion:hover,.tt-menu .tt-suggestion.tt-cursor,.tt-menu .tt-suggestion:hover{color:#76838f;text-decoration:none;cursor:pointer;background-color:#f3f7f9;outline:0}.tt-dropdown-menu .tt-suggestion.tt-cursor a,.tt-dropdown-menu .tt-suggestion:hover a,.tt-menu .tt-suggestion.tt-cursor a,.tt-menu .tt-suggestion:hover a{color:#76838f}.tt-dropdown-menu .tt-suggestion p,.tt-menu .tt-suggestion p{margin:0}