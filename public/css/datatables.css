@charset "utf-8";

.dataTables_wrapper.dt-bootstrap4.container-fluid, .dataTables_wrapper.dt-bootstrap4.container {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

table.dataTable {
  max-width: none !important;
  margin-top: 6px !important;
  margin-bottom: 6px !important;
  clear: both;
}

table.dataTable td, table.dataTable th {
  box-sizing: content-box;
}

table.dataTable td.dataTables_empty, table.dataTable th.dataTables_empty {
  text-align: center;
}

table.dataTable.nowrap th, table.dataTable.nowrap td {
  white-space: nowrap;
}

div.dataTables_wrapper div.dataTables_length label {
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
}

div.dataTables_wrapper div.dataTables_length select {
  display: inline-block;
  width: 75px;
}

div.dataTables_wrapper div.dataTables_filter {
  text-align: right;
}

div.dataTables_wrapper div.dataTables_filter label {
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
}

div.dataTables_wrapper div.dataTables_filter input {
  display: inline-block;
  width: auto;
  margin-left: .5em;
}

div.dataTables_wrapper div.dataTables_info {
  padding-top: .85em;
  white-space: nowrap;
}

div.dataTables_wrapper div.dataTables_paginate {
  margin: 0;
  text-align: right;
  white-space: nowrap;
}

div.dataTables_wrapper div.dataTables_paginate ul.pagination {
  -ms-flex-pack: end;
  justify-content: flex-end;
  margin: 2px 0;
  white-space: nowrap;
}

div.dataTables_wrapper div.dataTables_processing {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200px;
  padding: 1em 0;
  margin-top: -26px;
  margin-left: -100px;
  text-align: center;
}

table.dataTable thead > tr > th.sorting_asc, table.dataTable thead > tr > th.sorting_desc, table.dataTable thead > tr > th.sorting, table.dataTable thead > tr > td.sorting_asc, table.dataTable thead > tr > td.sorting_desc, table.dataTable thead > tr > td.sorting {
  padding-right: 30px;
}

table.dataTable thead > tr > th:active, table.dataTable thead > tr > td:active {
  outline: none;
}

table.dataTable thead .sorting, table.dataTable thead .sorting_asc, table.dataTable thead .sorting_desc, table.dataTable thead .sorting_asc_disabled, table.dataTable thead .sorting_desc_disabled {
  position: relative;
  cursor: pointer;
}

table.dataTable thead .sorting:before, table.dataTable thead .sorting:after, table.dataTable thead .sorting_asc:before, table.dataTable thead .sorting_asc:after, table.dataTable thead .sorting_desc:before, table.dataTable thead .sorting_desc:after, table.dataTable thead .sorting_asc_disabled:before, table.dataTable thead .sorting_asc_disabled:after, table.dataTable thead .sorting_desc_disabled:before, table.dataTable thead .sorting_desc_disabled:after {
  position: absolute;
  top: 50%;
  right: 1em;
  font-family: "Web Icons";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  opacity: .3;
  text-rendering: auto;
}

table.dataTable thead .sorting:before, table.dataTable thead .sorting_asc:before, table.dataTable thead .sorting_desc:before, table.dataTable thead .sorting_asc_disabled:before, table.dataTable thead .sorting_desc_disabled:before {
  margin-top: -5px;
  content: "";
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

table.dataTable thead .sorting:after, table.dataTable thead .sorting_asc:after, table.dataTable thead .sorting_desc:after, table.dataTable thead .sorting_asc_disabled:after, table.dataTable thead .sorting_desc_disabled:after {
  margin-top: 5px;
  content: "";
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

table.dataTable thead .sorting_asc:before, table.dataTable thead .sorting_desc:after {
  opacity: 1;
}

table.dataTable thead .sorting_asc_disabled:before, table.dataTable thead .sorting_desc_disabled:after {
  opacity: 0;
}

div.dataTables_scrollHead table.dataTable {
  margin-bottom: 0 !important;
}

div.dataTables_scrollBody table {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  border-top: none;
}

div.dataTables_scrollBody table thead .sorting:after, div.dataTables_scrollBody table thead .sorting_asc:after, div.dataTables_scrollBody table thead .sorting_desc:after {
  display: none;
}

div.dataTables_scrollBody table tbody tr:first-child th, div.dataTables_scrollBody table tbody tr:first-child td {
  border-top: none;
}

div.dataTables_scrollFoot table {
  margin-top: 0 !important;
  border-top: none;
}

@media screen and (max-width: 767px) {
  div.dataTables_wrapper div.dataTables_length, div.dataTables_wrapper div.dataTables_info, div.dataTables_wrapper div.dataTables_paginate {
    text-align: center;
  }
}

table.dataTable.table-condensed > thead > tr > th {
  padding-right: 20px;
}

table.dataTable.table-condensed .sorting:after, table.dataTable.table-condensed .sorting_asc:after, table.dataTable.table-condensed .sorting_desc:after {
  top: 6px;
  right: 6px;
}

table.table-bordered.dataTable th, table.table-bordered.dataTable td {
  border-left-width: 0;
}

table.table-bordered.dataTable th:last-child, table.table-bordered.dataTable th:last-child, table.table-bordered.dataTable td:last-child, table.table-bordered.dataTable td:last-child {
  border-right-width: 0;
}

table.table-bordered.dataTable tbody th, table.table-bordered.dataTable tbody td {
  border-bottom-width: 0;
}

div.dataTables_scrollHead table.table-bordered {
  border-bottom-width: 0;
}

div.table-responsive > div.dataTables_wrapper > div.row {
  margin: 0;
}

div.table-responsive > div.dataTables_wrapper > div.row > div[class^="col-"]:first-child {
  padding-left: 0;
}

div.table-responsive > div.dataTables_wrapper > div.row > div[class^="col-"]:last-child {
  padding-right: 0;
}

table.dataTable.fixedHeader-floating, table.dataTable.fixedHeader-locked {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  background-color: white;
}

table.dataTable.fixedHeader-floating {
  position: fixed !important;
}

table.dataTable.fixedHeader-locked {
  position: absolute !important;
}

@media print {
  table.fixedHeader-floating {
    display: none;
  }
}

table.DTFC_Cloned tr {
  margin-bottom: 0;
  background-color: white;
}

div.DTFC_LeftHeadWrapper table, div.DTFC_RightHeadWrapper table {
  margin-bottom: 0 !important;
  background-color: white;
  border-bottom: none !important;
}

div.DTFC_LeftBodyWrapper table, div.DTFC_RightBodyWrapper table {
  margin: 0 !important;
  border-top: none;
}

div.DTFC_LeftBodyWrapper table thead .sorting:after, div.DTFC_LeftBodyWrapper table thead .sorting_asc:after, div.DTFC_LeftBodyWrapper table thead .sorting_desc:after, div.DTFC_LeftBodyWrapper table thead .sorting:after, div.DTFC_LeftBodyWrapper table thead .sorting_asc:after, div.DTFC_LeftBodyWrapper table thead .sorting_desc:after, div.DTFC_RightBodyWrapper table thead .sorting:after, div.DTFC_RightBodyWrapper table thead .sorting_asc:after, div.DTFC_RightBodyWrapper table thead .sorting_desc:after, div.DTFC_RightBodyWrapper table thead .sorting:after, div.DTFC_RightBodyWrapper table thead .sorting_asc:after, div.DTFC_RightBodyWrapper table thead .sorting_desc:after {
  display: none;
}

div.DTFC_LeftBodyWrapper table tbody tr:first-child th, div.DTFC_LeftBodyWrapper table tbody tr:first-child td, div.DTFC_RightBodyWrapper table tbody tr:first-child th, div.DTFC_RightBodyWrapper table tbody tr:first-child td {
  border-top: none;
}

div.DTFC_LeftFootWrapper table, div.DTFC_RightFootWrapper table {
  margin-top: 0 !important;
  background-color: white;
  border-top: none;
}

table.dataTable tr.group td {
  font-weight: bold;
  background-color: #e0e0e0;
}

div.DTS {
  display: block !important;
}

div.DTS tbody th, div.DTS tbody td {
  white-space: nowrap;
}

div.DTS div.DTS_Loading {
  z-index: 1;
}

div.DTS div.dataTables_scrollBody {
  background: repeating-linear-gradient(45deg, #edeeff, #edeeff 10px, white 10px, white 20px);
}

div.DTS div.dataTables_scrollBody table {
  z-index: 2;
}

div.DTS div.dataTables_paginate, div.DTS div.dataTables_length {
  display: none;
}

div.DTS tbody tr.even {
  background-color: white;
}

table.dataTable tbody > tr.selected, table.dataTable tbody > tr > .selected {
  background-color: #0275d8;
}

table.dataTable.stripe tbody > tr.odd.selected, table.dataTable.stripe tbody > tr.odd > .selected, table.dataTable.display tbody > tr.odd.selected, table.dataTable.display tbody > tr.odd > .selected {
  background-color: #0172d2;
}

table.dataTable.hover tbody > tr.selected:hover, table.dataTable.hover tbody > tr > .selected:hover, table.dataTable.display tbody > tr.selected:hover, table.dataTable.display tbody > tr > .selected:hover {
  background-color: #0170d0;
}

table.dataTable.order-column tbody > tr.selected > .sorting_1, table.dataTable.order-column tbody > tr.selected > .sorting_2, table.dataTable.order-column tbody > tr.selected > .sorting_3, table.dataTable.order-column tbody > tr > .selected, table.dataTable.display tbody > tr.selected > .sorting_1, table.dataTable.display tbody > tr.selected > .sorting_2, table.dataTable.display tbody > tr.selected > .sorting_3, table.dataTable.display tbody > tr > .selected {
  background-color: #0172d3;
}

table.dataTable.display tbody > tr.odd.selected > .sorting_1, table.dataTable.order-column.stripe tbody > tr.odd.selected > .sorting_1 {
  background-color: #016ecc;
}

table.dataTable.display tbody > tr.odd.selected > .sorting_2, table.dataTable.order-column.stripe tbody > tr.odd.selected > .sorting_2 {
  background-color: #016fcd;
}

table.dataTable.display tbody > tr.odd.selected > .sorting_3, table.dataTable.order-column.stripe tbody > tr.odd.selected > .sorting_3 {
  background-color: #0170cf;
}

table.dataTable.display tbody > tr.even.selected > .sorting_1, table.dataTable.order-column.stripe tbody > tr.even.selected > .sorting_1 {
  background-color: #0172d3;
}

table.dataTable.display tbody > tr.even.selected > .sorting_2, table.dataTable.order-column.stripe tbody > tr.even.selected > .sorting_2 {
  background-color: #0173d5;
}

table.dataTable.display tbody > tr.even.selected > .sorting_3, table.dataTable.order-column.stripe tbody > tr.even.selected > .sorting_3 {
  background-color: #0174d7;
}

table.dataTable.display tbody > tr.odd > .selected, table.dataTable.order-column.stripe tbody > tr.odd > .selected {
  background-color: #016ecc;
}

table.dataTable.display tbody > tr.even > .selected, table.dataTable.order-column.stripe tbody > tr.even > .selected {
  background-color: #0172d3;
}

table.dataTable.display tbody > tr.selected:hover > .sorting_1, table.dataTable.order-column.hover tbody > tr.selected:hover > .sorting_1 {
  background-color: #016bc6;
}

table.dataTable.display tbody > tr.selected:hover > .sorting_2, table.dataTable.order-column.hover tbody > tr.selected:hover > .sorting_2 {
  background-color: #016cc7;
}

table.dataTable.display tbody > tr.selected:hover > .sorting_3, table.dataTable.order-column.hover tbody > tr.selected:hover > .sorting_3 {
  background-color: #016dca;
}

table.dataTable.display tbody > tr:hover > .selected, table.dataTable.display tbody > tr > .selected:hover, table.dataTable.order-column.hover tbody > tr:hover > .selected, table.dataTable.order-column.hover tbody > tr > .selected:hover {
  background-color: #016bc6;
}

table.dataTable tbody td.select-checkbox, table.dataTable tbody th.select-checkbox {
  position: relative;
}

table.dataTable tbody td.select-checkbox:before, table.dataTable tbody td.select-checkbox:after, table.dataTable tbody th.select-checkbox:before, table.dataTable tbody th.select-checkbox:after {
  position: absolute;
  top: 1.2em;
  left: 50%;
  box-sizing: border-box;
  display: block;
  width: 12px;
  height: 12px;
}

table.dataTable tbody td.select-checkbox:before, table.dataTable tbody th.select-checkbox:before {
  margin-top: -6px;
  margin-left: -6px;
  content: " ";
  border: 1px solid black;
  border-radius: 3px;
}

table.dataTable tr.selected td.select-checkbox:after, table.dataTable tr.selected th.select-checkbox:after {
  margin-top: -11px;
  margin-left: -4px;
  text-align: center;
  text-shadow: 1px 1px #b0bed9, -1px -1px #b0bed9, 1px -1px #b0bed9, -1px 1px #b0bed9;
  content: "\2714";
}

div.dataTables_wrapper span.select-info, div.dataTables_wrapper span.select-item {
  margin-left: .5em;
}

@media screen and (max-width: 640px) {
  div.dataTables_wrapper span.select-info, div.dataTables_wrapper span.select-item {
    display: block;
    margin-left: 0;
  }
}

table.dataTable tbody tr.selected, table.dataTable tbody th.selected, table.dataTable tbody td.selected {
  color: white;
}

table.dataTable tbody tr.selected a, table.dataTable tbody th.selected a, table.dataTable tbody td.selected a {
  color: #a2d4ed;
}

@charset "utf-8";

table.dataTable.dtr-inline.collapsed > tbody > tr > td.child, table.dataTable.dtr-inline.collapsed > tbody > tr > th.child, table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty {
  cursor: default !important;
}

table.dataTable.dtr-inline.collapsed > tbody > tr > td.child:before, table.dataTable.dtr-inline.collapsed > tbody > tr > th.child:before, table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty:before {
  display: none !important;
}

table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child, table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
}

table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child:before {
  position: absolute;
  top: 6px;
  left: 10px;
  font-family: "Web Icons";
  font-style: normal;
  font-weight: 400;
  content: "";
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  text-rendering: auto;
}

table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {
  content: "";
}

table.dataTable.dtr-inline.collapsed > tbody > tr.child td:before {
  display: none;
}

table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td:first-child, table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th:first-child {
  padding-left: 27px;
}

table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td:first-child:before, table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th:first-child:before {
  top: 5px;
  left: 4px;
  width: 14px;
  height: 14px;
  line-height: 14px;
  text-indent: 3px;
  border-radius: 14px;
}

table.dataTable.dtr-column > tbody > tr > td.control, table.dataTable.dtr-column > tbody > tr > th.control {
  position: relative;
  cursor: pointer;
}

table.dataTable.dtr-column > tbody > tr > td.control:before, table.dataTable.dtr-column > tbody > tr > th.control:before {
  position: absolute;
  top: 50%;
  left: 50%;
  box-sizing: content-box;
  display: block;
  width: 16px;
  height: 16px;
  margin-top: -10px;
  margin-left: -10px;
  font-family: "Courier New", Courier, monospace;
  line-height: 14px;
  color: white;
  text-align: center;
  content: "+";
  background-color: #0275d8;
  border: 2px solid white;
  border-radius: 14px;
  box-shadow: 0 0 3px #444;
}

table.dataTable.dtr-column > tbody > tr.parent td.control:before, table.dataTable.dtr-column > tbody > tr.parent th.control:before {
  content: "-";
  background-color: #d33333;
}

table.dataTable > tbody > tr.child {
  padding: .5em 1em;
}

table.dataTable > tbody > tr.child:hover {
  background: transparent !important;
}

table.dataTable > tbody > tr.child ul.dtr-details {
  display: inline-block;
  padding: 0;
  margin: 0;
  list-style-type: none;
}

table.dataTable > tbody > tr.child ul.dtr-details li {
  padding: .5em 0;
  border-bottom: 1px solid #efefef;
}

table.dataTable > tbody > tr.child ul.dtr-details li:first-child {
  padding-top: 0;
}

table.dataTable > tbody > tr.child ul.dtr-details li:last-child {
  border-bottom: none;
}

table.dataTable > tbody > tr.child span.dtr-title {
  display: inline-block;
  min-width: 75px;
  font-weight: bold;
}

div.dtr-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 10em 1em;
}

div.dtr-modal div.dtr-modal-display {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 102;
  width: 50%;
  height: 50%;
  margin: auto;
  overflow: auto;
  overflow: auto;
  background-color: #f5f5f7;
  border: 1px solid black;
  border-radius: .5em;
  box-shadow: 0 12px 30px rgba(0, 0, 0, .6);
}

div.dtr-modal div.dtr-modal-content {
  position: relative;
  padding: 1em;
}

div.dtr-modal div.dtr-modal-close {
  position: absolute;
  top: 6px;
  right: 6px;
  z-index: 12;
  width: 22px;
  height: 22px;
  text-align: center;
  cursor: pointer;
  background-color: #f9f9f9;
  border: 1px solid #eaeaea;
  border-radius: 3px;
}

div.dtr-modal div.dtr-modal-close:hover {
  background-color: #eaeaea;
}

div.dtr-modal div.dtr-modal-background {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 101;
  background: rgba(0, 0, 0, .6);
}

@media screen and (max-width: 767px) {
  div.dtr-modal div.dtr-modal-display {
    width: 95%;
  }
}

div.dtr-bs-modal table.table tr:first-child td {
  border-top: none;
}

@keyframes dtb-spinner {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-webkit-keyframes dtb-spinner {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

div.dt-button-info {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 21;
  width: 400px;
  margin-top: -100px;
  margin-left: -200px;
  text-align: center;
  background-color: white;
  border: 2px solid #111;
  border-radius: 3px;
  box-shadow: 3px 3px 8px rgba(0, 0, 0, .3);
}

div.dt-button-info h2 {
  padding: .5em;
  margin: 0;
  font-weight: normal;
  background-color: #f3f3f3;
  border-bottom: 1px solid #ddd;
}

div.dt-button-info > div {
  padding: 1em;
}

ul.dt-button-collection.dropdown-menu {
  z-index: 2002;
  display: block;
  -webkit-column-gap: 8px;
  -ms-column-gap: 8px;
  -o-column-gap: 8px;
  column-gap: 8px;
}

ul.dt-button-collection.dropdown-menu.fixed {
  position: fixed;
  top: 50%;
  left: 50%;
  margin-left: -75px;
  border-radius: 0;
}

ul.dt-button-collection.dropdown-menu.fixed.two-column {
  margin-left: -150px;
}

ul.dt-button-collection.dropdown-menu.fixed.three-column {
  margin-left: -225px;
}

ul.dt-button-collection.dropdown-menu.fixed.four-column {
  margin-left: -300px;
}

ul.dt-button-collection.dropdown-menu > * {
  -webkit-column-break-inside: avoid;
  break-inside: avoid;
}

ul.dt-button-collection.dropdown-menu.two-column {
  width: 300px;
  padding-bottom: 1px;
  -webkit-column-count: 2;
  -ms-column-count: 2;
  -o-column-count: 2;
  column-count: 2;
}

ul.dt-button-collection.dropdown-menu.three-column {
  width: 450px;
  padding-bottom: 1px;
  -webkit-column-count: 3;
  -ms-column-count: 3;
  -o-column-count: 3;
  column-count: 3;
}

ul.dt-button-collection.dropdown-menu.four-column {
  width: 600px;
  padding-bottom: 1px;
  -webkit-column-count: 4;
  -ms-column-count: 4;
  -o-column-count: 4;
  column-count: 4;
}

ul.dt-button-collection {
  -webkit-column-gap: 8px;
  -ms-column-gap: 8px;
  -o-column-gap: 8px;
  column-gap: 8px;
}

ul.dt-button-collection.fixed {
  position: fixed;
  top: 50%;
  left: 50%;
  margin-left: -75px;
  border-radius: 0;
}

ul.dt-button-collection.fixed.two-column {
  margin-left: -150px;
}

ul.dt-button-collection.fixed.three-column {
  margin-left: -225px;
}

ul.dt-button-collection.fixed.four-column {
  margin-left: -300px;
}

ul.dt-button-collection > * {
  -webkit-column-break-inside: avoid;
  break-inside: avoid;
}

ul.dt-button-collection.two-column {
  width: 300px;
  padding-bottom: 1px;
  -webkit-column-count: 2;
  -ms-column-count: 2;
  -o-column-count: 2;
  column-count: 2;
}

ul.dt-button-collection.three-column {
  width: 450px;
  padding-bottom: 1px;
  -webkit-column-count: 3;
  -ms-column-count: 3;
  -o-column-count: 3;
  column-count: 3;
}

ul.dt-button-collection.four-column {
  width: 600px;
  padding-bottom: 1px;
  -webkit-column-count: 4;
  -ms-column-count: 4;
  -o-column-count: 4;
  column-count: 4;
}

ul.dt-button-collection.fixed {
  max-width: none;
}

ul.dt-button-collection.fixed:before, ul.dt-button-collection.fixed:after {
  display: none;
}

div.dt-button-background {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
}

@media screen and (max-width: 767px) {
  div.dt-buttons {
    float: none;
    width: 100%;
    margin-bottom: .5em;
    text-align: center;
  }

  div.dt-buttons a.btn {
    float: none;
  }
}

div.dt-buttons button.btn.processing, div.dt-buttons div.btn.processing, div.dt-buttons a.btn.processing {
  color: rgba(0, 0, 0, .2);
}

div.dt-buttons button.btn.processing:after, div.dt-buttons div.btn.processing:after, div.dt-buttons a.btn.processing:after {
  position: absolute;
  top: 50%;
  left: 50%;
  box-sizing: border-box;
  display: block;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  content: " ";
  border: 2px solid #282828;
  border-right-color: transparent;
  border-left-color: transparent;
  border-radius: 50%;
  -webkit-animation: dtb-spinner 1500ms infinite linear;
  animation: dtb-spinner 1500ms infinite linear;
}
