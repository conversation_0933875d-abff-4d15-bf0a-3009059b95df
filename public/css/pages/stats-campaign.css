#campaignDropdown {
    padding: .715rem 1.072rem;
}

#prospects-table tfoot {
    display: table-header-group;
}
div.dataTables_wrapper div.dataTables_filter {
    text-align: left;
}

.chart-pie-right .ct-series.ct-series-b .ct-slice-donut {
    stroke: #e4eaec;
}
#interested-donut-chart .chart-pie-right .ct-series.ct-series-a .ct-slice-donut {
    stroke: #11c26d;
}
#maybe-later-donut-chart .chart-pie-right .ct-series.ct-series-a .ct-slice-donut {
    stroke: #62a8ea;
}
#not-interested-donut-chart .chart-pie-right .ct-series.ct-series-a .ct-slice-donut {
    stroke: #eb6709;
}

#interest-bar-chart .ct-chart-bar .ct-bar {
    /*stroke: #79b2fc;*/
    stroke-width: 25px;
}

#unsubscribe-bar-chart .ct-chart-bar .ct-bar {
    stroke: #ff8589; /* #98c3fb;*/
    /*stroke-width: 20%;*/
    stroke-width: 25px;
}

#cadence-steps-line-chart .ct-chart-line .ct-series-a .ct-area {
    fill: #b8d7ff;
}
#cadence-steps-line-chart .ct-chart-line .ct-series-a .ct-line,
#cadence-steps-line-chart .ct-chart-line .ct-series-a .ct-point {
    stroke: #b8d7ff;
}

#cadence-steps-line-chart .ct-chart-line .ct-series-b .ct-area {
    fill: #d9c7fc;
}
#cadence-steps-line-chart .ct-chart-line .ct-series-b .ct-line,
#cadence-steps-line-chart .ct-chart-line .ct-series-b .ct-point {
    stroke: #d9c7fc;
}

#cadence-steps-line-chart .ct-chart-line .ct-series-c .ct-area {
    fill: #92f0e6;
    fill-opacity: 0.85;
}
#cadence-steps-line-chart .ct-chart-line .ct-series-c .ct-line,
#cadence-steps-line-chart .ct-chart-line .ct-series-c .ct-point {
    stroke: #76ecdf;
}

#cadence-steps-line-chart .ct-chart-line .ct-area {
    fill-opacity: 0.5;
}
#cadence-steps-line-chart .ct-label {
    white-space: nowrap;
}

#positives-by-day-bar-chart .ct-chart-bar .ct-bar {
    stroke: #49de94; /* #98c3fb;*/
    stroke-width: 10%;
}

/* for the inbox threads */
.font13 {
    font-size: 13px;
    line-height: 13px;
}
.bg-grey-150 {
  background-color: #f4f4f4 !important;
}
.btn-default.btn-outline:hover .badge,
.btn-default.btn-outline:focus .badge,
.btn-default.btn-outline:active .badge {
    color: #fff !important;
}
.list-campaign-thread.list-group-item.active 
.list-campaign-thread.list-group-item.active .btn-default{
    color: #fff !important;
}
.list-campaign-thread {
    cursor: pointer;
}

.loader-wrapper {
    position: relative;
}
.loader-box {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0px;
    left: 0px;
    background: rgba(255,255,255,0.5);
    display: none;
}
.loader-wrapper.loader-wrapper-loading .loader-box {
    display: block;
} 

.filter-group-btn-md,
.filter-group-header,
.filter-group-btn-md {
    display: none;
} 

.tabbed-inbox-wrap {
    position: relative;
    padding-left: 250px;
    overflow: hidden;
}
.tabbed-inbox-filters {
    position: absolute;
    top: 0;
    left: 0;
    width: 250px;
}
.tabbed-inbox-threads {
    border-left: 1px solid #e4eaec;
    min-height: 600px;
}

.tabbed-inbox-filters h4 {
    color: #526069;
    font-size: 14px;
    padding: 0 12px;
    margin: 5px 0;
}


.tabbed-inbox-filter-btn {
    border-top: 1px solid #e4eaec;
}
.tabbed-inbox-filter-btn:first-child {
    border-top: none;
}
.tabbed-inbox-filter-btn .btn {
    text-align: left;
    background: #fff;
    border-color: #fff;
    margin: 0px;
    padding: 5px 12px;
    color: #76838f;
}
.tabbed-inbox-filter-btn .btn:hover {
    background-color: #f3f7f9;
}
.tabbed-inbox-filter-btn .btn.btn-active {
    color: #eb6709;
    background-color: #e4eaec;
    font-weight: bold;
}

.tabbed-inbox-scrollable {
    overflow-y: scroll;
}
.tabbed-inbox-scrollable::-webkit-scrollbar-track
{
    /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);*/
    border-radius: 10px;
    background-color: #F5F5F5;
}

.tabbed-inbox-scrollable::-webkit-scrollbar
{
    width: 4px;
    background-color: #F5F5F5;
}

.tabbed-inbox-scrollable::-webkit-scrollbar-thumb
{
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.2);
    background-color: #c2ccd2;
}

.card-group-stats .card.card-block {
    padding: 30px;
    border-left: 1px solid rgba(102,102,102,0.1);
    border-bottom: none;
}
.card-group-stats .card.card-block:first-child {
    border-left: none;
}

.counter-bounced {
    position: relative;
}
.counter-bounced-close {
    position: absolute;
    bottom: 11px;
    right: 20px;
    font-size: 10px; 
    color: #fff;
    background: #3e8ef7;
    line-height: 20px; 
    border-radius: 50%;
    width: 20px;
    height: 20px;
    text-align: center;
}
.yahoo_quoted, .gmail_extra, 
.gmail_quote, .unsrb {
  display: none;
}

.display-quoted .yahoo_quoted, .display-quoted .gmail_extra, 
.display-quoted .gmail_quote, .display-quoted .unsrb, .display-quoted .quote {
  display: block !important;
}

.swal-text,
.swal-footer {
    text-align: center;
}

.stats-contact-dt .datatable-box {
    min-height: 400px;
}

.contact-activity-label {
    display: inline-block;
    width: 120px;
    font-size: 12px;
}

.contact-activity-detail {
    border-left: 1px solid #ccc; 
}

@media (min-width: 1600px) {
    .card-group-stats {
        flex-flow: column wrap;
    }
    .card-group-stats .card.card-block {
        padding-top: 20px;
        padding-bottom: 20px;
        border-top: 1px solid rgba(102,102,102,0.1);
        border-left: none;
    }
    .card-group-stats .card.card-block:first-child {
        border-top: none;
    }
}

@media (max-width: 780px) {
    .filter-group-header,
    .filter-group-btn-md {
        display: block;
    }
    .filter-group-btn-md .form-control {
        
    } 

    .tabbed-inbox-wrap { padding-left: 0px; }
    .tabbed-inbox-filters { display: none;}
    .tabbed-inbox-threads { border-left: none; }
}