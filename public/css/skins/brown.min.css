:root {
  --blue: #007bff;
  --indigo: #6610f2;
  --purple: #6f42c1;
  --pink: #e83e8c;
  --red: #dc3545;
  --orange: #fd7e14;
  --yellow: #ffc107;
  --green: #28a745;
  --teal: #20c997;
  --cyan: #17a2b8;
  --white: #fff;
  --gray: #868e96;
  --gray-dark: #343a40;
  --primary: #007bff;
  --secondary: #868e96;
  --success: #28a745;
  --info: #17a2b8;
  --warning: #ffc107;
  --danger: #dc3545;
  --light: #f8f9fa;
  --dark: #343a40;
  --breakpoint-xs: 0;
  --breakpoint-sm: 480px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1600px;
}

@media print {
  *, *::before, *::after {
    text-shadow: none !important;
    box-shadow: none !important;
  }

  pre, blockquote {
    border: 1px solid #999;
  }

  .badge {
    border: 1px solid #000;
  }

  .table td, .table th {
    background-color: #fff !important;
  }

  .table-bordered th, .table-bordered td {
    border: 1px solid #ddd !important;
  }
}

html {
  -webkit-tap-highlight-color: transparent;
}

body {
  color: #76838f;
  background-color: #fff;
}

a {
  color: #997b71;
  background-color: transparent;
}

a:hover {
  color: #ab8c82;
}

a:not([href]):not([tabindex]) {
  color: inherit;
}

a:not([href]):not([tabindex]):focus, a:not([href]):not([tabindex]):hover {
  color: inherit;
}

caption {
  color: #a3afb7;
}

button:focus {
  outline: 5px auto -webkit-focus-ring-color;
}

legend {
  color: inherit;
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
  color: #37474f;
}

hr {
  border: 0;
  border-top: 1px solid #e4eaec;
}

mark, .mark {
  background-color: #eb6709;
}

.blockquote-footer {
  color: #a3afb7;
}

.img-thumbnail {
  background-color: #fff;
  border: 1px solid #ddd;
  box-shadow: 0 1px 2px rgba(0, 0, 0, .075);
}

.figure-caption {
  color: #ccd5db;
}

code {
  color: #716b6e;
  background-color: rgba(245, 226, 218, .1);
}

a > code {
  color: inherit;
  background-color: inherit;
}

kbd {
  color: #fff;
  background-color: #997b71;
  box-shadow: inset 0 -0.1rem 0 rgba(0, 0, 0, .25);
}

kbd kbd {
  box-shadow: none;
}

pre {
  color: inherit;
}

pre code {
  color: inherit;
  background-color: transparent;
}

.table {
  background-color: transparent;
}

.table th, .table td {
  border-top: 1px solid #e4eaec;
}

.table thead th {
  border-bottom: 2px solid #e4eaec;
}

.table tbody + tbody {
  border-top: 2px solid #e4eaec;
}

.table .table {
  background-color: #fff;
}

.table-bordered {
  border: 1px solid #e4eaec;
}

.table-bordered th, .table-bordered td {
  border: 1px solid #e4eaec;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(243, 247, 249, .3);
}

.table-hover tbody tr:hover {
  background-color: #f3f7f9;
}

.table-primary, .table-primary > th, .table-primary > td {
  background-color: #b8daff;
}

.table-hover .table-primary:hover {
  background-color: #9fcdff;
}

.table-hover .table-primary:hover > td, .table-hover .table-primary:hover > th {
  background-color: #9fcdff;
}

.table-secondary, .table-secondary > th, .table-secondary > td {
  background-color: #dddfe2;
}

.table-hover .table-secondary:hover {
  background-color: #cfd2d6;
}

.table-hover .table-secondary:hover > td, .table-hover .table-secondary:hover > th {
  background-color: #cfd2d6;
}

.table-success, .table-success > th, .table-success > td {
  background-color: #c3e6cb;
}

.table-hover .table-success:hover {
  background-color: #b1dfbb;
}

.table-hover .table-success:hover > td, .table-hover .table-success:hover > th {
  background-color: #b1dfbb;
}

.table-info, .table-info > th, .table-info > td {
  background-color: #bee5eb;
}

.table-hover .table-info:hover {
  background-color: #abdde5;
}

.table-hover .table-info:hover > td, .table-hover .table-info:hover > th {
  background-color: #abdde5;
}

.table-warning, .table-warning > th, .table-warning > td {
  background-color: #ffeeba;
}

.table-hover .table-warning:hover {
  background-color: #ffe8a1;
}

.table-hover .table-warning:hover > td, .table-hover .table-warning:hover > th {
  background-color: #ffe8a1;
}

.table-danger, .table-danger > th, .table-danger > td {
  background-color: #f5c6cb;
}

.table-hover .table-danger:hover {
  background-color: #f1b0b7;
}

.table-hover .table-danger:hover > td, .table-hover .table-danger:hover > th {
  background-color: #f1b0b7;
}

.table-light, .table-light > th, .table-light > td {
  background-color: #fdfdfe;
}

.table-hover .table-light:hover {
  background-color: #ececf6;
}

.table-hover .table-light:hover > td, .table-hover .table-light:hover > th {
  background-color: #ececf6;
}

.table-dark, .table-dark > th, .table-dark > td {
  background-color: #c6c8ca;
}

.table-hover .table-dark:hover {
  background-color: #b9bbbe;
}

.table-hover .table-dark:hover > td, .table-hover .table-dark:hover > th {
  background-color: #b9bbbe;
}

.table-active, .table-active > th, .table-active > td {
  background-color: #f3f7f9;
}

.table-hover .table-active:hover {
  background-color: #e2ecf1;
}

.table-hover .table-active:hover > td, .table-hover .table-active:hover > th {
  background-color: #e2ecf1;
}

.table .thead-dark th {
  color: #fff;
  background-color: #3e4854;
  border-color: #4e5b6a;
}

.table .thead-light th {
  color: #a3afb7;
  background-color: #e4eaec;
  border-color: #e4eaec;
}

.table-dark {
  color: #fff;
  background-color: #3e4854;
}

.table-dark th, .table-dark td, .table-dark thead th {
  border-color: #4e5b6a;
}

.table-dark.table-bordered {
  border: 0;
}

.table-dark.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, .05);
}

.table-dark.table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, .075);
}

@media (max-width: 479px) {
  .table-responsive-sm.table-bordered {
    border: 0;
  }
}

@media (max-width: 767px) {
  .table-responsive-md.table-bordered {
    border: 0;
  }
}

@media (max-width: 991px) {
  .table-responsive-lg.table-bordered {
    border: 0;
  }
}

@media (max-width: 1199px) {
  .table-responsive-xl.table-bordered {
    border: 0;
  }
}

@media (max-width: 1599px) {
  .table-responsive-xxl.table-bordered {
    border: 0;
  }
}

.table-responsive.table-bordered {
  border: 0;
}

.form-control {
  color: #76838f;
  background-color: #fff;
  border: 1px solid #e4eaec;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}

.form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}

.form-control.focus, .form-control:focus {
  border-color: #997b71;
  box-shadow: none;
}

.form-control::-webkit-input-placeholder {
  color: #a3afb7;
}

.form-control:-ms-input-placeholder {
  color: #a3afb7;
}

.form-control::-ms-input-placeholder {
  color: #a3afb7;
}

.form-control::placeholder {
  color: #a3afb7;
}

.form-control:disabled, .form-control[readonly] {
  background-color: #f3f7f9;
}

select.form-control:focus::-ms-value {
  color: #76838f;
  background-color: #fff;
}

.form-control-plaintext {
  background-color: transparent;
  border: solid transparent;
}

.form-check.disabled .form-check-label {
  color: #a3afb7;
}

.valid-feedback {
  display: none;
  margin-top: .25rem;
  font-size: .875rem;
  color: #11c26d;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  width: 250px;
  padding: .5rem;
  margin-top: .1rem;
  font-size: .875rem;
  line-height: 1;
  color: #fff;
  background-color: rgba(17, 194, 109, .8);
  border-radius: .2rem;
}

.was-validated .form-control:valid, .form-control.is-valid, .was-validated
.custom-select:valid, .custom-select.is-valid {
  border-color: #11c26d;
}

.was-validated .form-control:valid:focus, .form-control.is-valid:focus, .was-validated
  .custom-select:valid:focus, .custom-select.is-valid:focus {
  box-shadow: 0 0 0 .2rem rgba(17, 194, 109, .25);
}

.was-validated .form-control:valid ~ .valid-feedback, .was-validated .form-control:valid ~ .valid-tooltip, .form-control.is-valid ~ .valid-feedback, .form-control.is-valid ~ .valid-tooltip, .was-validated
  .custom-select:valid ~ .valid-feedback, .was-validated
  .custom-select:valid ~ .valid-tooltip, .custom-select.is-valid ~ .valid-feedback, .custom-select.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-check-input:valid + .form-check-label, .form-check-input.is-valid + .form-check-label {
  color: #11c26d;
}

.was-validated .custom-control-input:valid ~ .custom-control-indicator, .custom-control-input.is-valid ~ .custom-control-indicator {
  background-color: rgba(17, 194, 109, .25);
}

.was-validated .custom-control-input:valid ~ .custom-control-description, .custom-control-input.is-valid ~ .custom-control-description {
  color: #11c26d;
}

.was-validated .custom-file-input:valid ~ .custom-file-control, .custom-file-input.is-valid ~ .custom-file-control {
  border-color: #11c26d;
}

.was-validated .custom-file-input:valid ~ .custom-file-control::before, .custom-file-input.is-valid ~ .custom-file-control::before {
  border-color: inherit;
}

.was-validated .custom-file-input:valid:focus, .custom-file-input.is-valid:focus {
  box-shadow: 0 0 0 .2rem rgba(17, 194, 109, .25);
}

.invalid-feedback {
  display: none;
  margin-top: .25rem;
  font-size: .875rem;
  color: #ff4c52;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  width: 250px;
  padding: .5rem;
  margin-top: .1rem;
  font-size: .875rem;
  line-height: 1;
  color: #fff;
  background-color: rgba(255, 76, 82, .8);
  border-radius: .2rem;
}

.was-validated .form-control:invalid, .form-control.is-invalid, .was-validated
.custom-select:invalid, .custom-select.is-invalid {
  border-color: #ff4c52;
}

.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus, .was-validated
  .custom-select:invalid:focus, .custom-select.is-invalid:focus {
  box-shadow: 0 0 0 .2rem rgba(255, 76, 82, .25);
}

.was-validated .form-control:invalid ~ .invalid-feedback, .was-validated .form-control:invalid ~ .invalid-tooltip, .form-control.is-invalid ~ .invalid-feedback, .form-control.is-invalid ~ .invalid-tooltip, .was-validated
  .custom-select:invalid ~ .invalid-feedback, .was-validated
  .custom-select:invalid ~ .invalid-tooltip, .custom-select.is-invalid ~ .invalid-feedback, .custom-select.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-check-input:invalid + .form-check-label, .form-check-input.is-invalid + .form-check-label {
  color: #ff4c52;
}

.was-validated .custom-control-input:invalid ~ .custom-control-indicator, .custom-control-input.is-invalid ~ .custom-control-indicator {
  background-color: rgba(255, 76, 82, .25);
}

.was-validated .custom-control-input:invalid ~ .custom-control-description, .custom-control-input.is-invalid ~ .custom-control-description {
  color: #ff4c52;
}

.was-validated .custom-file-input:invalid ~ .custom-file-control, .custom-file-input.is-invalid ~ .custom-file-control {
  border-color: #ff4c52;
}

.was-validated .custom-file-input:invalid ~ .custom-file-control::before, .custom-file-input.is-invalid ~ .custom-file-control::before {
  border-color: inherit;
}

.was-validated .custom-file-input:invalid:focus, .custom-file-input.is-invalid:focus {
  box-shadow: 0 0 0 .2rem rgba(255, 76, 82, .25);
}

.btn {
  border: 1px solid transparent;
}

.btn:focus, .btn.focus {
  box-shadow: 0 0 0 2px rgba(153, 123, 113, .25);
}

.btn.disabled, .btn:disabled {
  box-shadow: none;
}

.btn:active, .btn.active {
  box-shadow: 0 0 0 2px rgba(153, 123, 113, .25), inset 0 3px 5px rgba(0, 0, 0, .125);
}

.btn:not([disabled]):not(.disabled):active, .btn:not([disabled]):not(.disabled).active {
  box-shadow: 0 0 0 2px rgba(153, 123, 113, .25), inset 0 3px 5px rgba(0, 0, 0, .125);
}

.btn-primary {
  color: color-yiq(#007bff);
  background-color: #007bff;
  border-color: #007bff;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.btn-primary:hover {
  color: color-yiq(#0069d9);
  background-color: #0069d9;
  border-color: #0062cc;
}

.btn-primary:focus, .btn-primary.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(0, 123, 255, .5);
}

.btn-primary.disabled, .btn-primary:disabled {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:not([disabled]):not(.disabled):active, .btn-primary:not([disabled]):not(.disabled).active, .show > .btn-primary.dropdown-toggle {
  color: color-yiq(#0062cc);
  background-color: #0062cc;
  border-color: #005cbf;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(0, 123, 255, .5);
}

.btn-secondary {
  color: color-yiq(#868e96);
  background-color: #868e96;
  border-color: #868e96;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.btn-secondary:hover {
  color: color-yiq(#727b84);
  background-color: #727b84;
  border-color: #6c757d;
}

.btn-secondary:focus, .btn-secondary.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(134, 142, 150, .5);
}

.btn-secondary.disabled, .btn-secondary:disabled {
  background-color: #868e96;
  border-color: #868e96;
}

.btn-secondary:not([disabled]):not(.disabled):active, .btn-secondary:not([disabled]):not(.disabled).active, .show > .btn-secondary.dropdown-toggle {
  color: color-yiq(#6c757d);
  background-color: #6c757d;
  border-color: #666e76;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(134, 142, 150, .5);
}

.btn-success {
  color: color-yiq(#28a745);
  background-color: #28a745;
  border-color: #28a745;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.btn-success:hover {
  color: color-yiq(#218838);
  background-color: #218838;
  border-color: #1e7e34;
}

.btn-success:focus, .btn-success.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(40, 167, 69, .5);
}

.btn-success.disabled, .btn-success:disabled {
  background-color: #28a745;
  border-color: #28a745;
}

.btn-success:not([disabled]):not(.disabled):active, .btn-success:not([disabled]):not(.disabled).active, .show > .btn-success.dropdown-toggle {
  color: color-yiq(#1e7e34);
  background-color: #1e7e34;
  border-color: #1c7430;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(40, 167, 69, .5);
}

.btn-info {
  color: color-yiq(#17a2b8);
  background-color: #17a2b8;
  border-color: #17a2b8;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.btn-info:hover {
  color: color-yiq(#138496);
  background-color: #138496;
  border-color: #117a8b;
}

.btn-info:focus, .btn-info.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(23, 162, 184, .5);
}

.btn-info.disabled, .btn-info:disabled {
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-info:not([disabled]):not(.disabled):active, .btn-info:not([disabled]):not(.disabled).active, .show > .btn-info.dropdown-toggle {
  color: color-yiq(#117a8b);
  background-color: #117a8b;
  border-color: #10707f;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(23, 162, 184, .5);
}

.btn-warning {
  color: color-yiq(#ffc107);
  background-color: #ffc107;
  border-color: #ffc107;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.btn-warning:hover {
  color: color-yiq(#e0a800);
  background-color: #e0a800;
  border-color: #d39e00;
}

.btn-warning:focus, .btn-warning.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(255, 193, 7, .5);
}

.btn-warning.disabled, .btn-warning:disabled {
  background-color: #ffc107;
  border-color: #ffc107;
}

.btn-warning:not([disabled]):not(.disabled):active, .btn-warning:not([disabled]):not(.disabled).active, .show > .btn-warning.dropdown-toggle {
  color: color-yiq(#d39e00);
  background-color: #d39e00;
  border-color: #c69500;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(255, 193, 7, .5);
}

.btn-danger {
  color: color-yiq(#dc3545);
  background-color: #dc3545;
  border-color: #dc3545;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.btn-danger:hover {
  color: color-yiq(#c82333);
  background-color: #c82333;
  border-color: #bd2130;
}

.btn-danger:focus, .btn-danger.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(220, 53, 69, .5);
}

.btn-danger.disabled, .btn-danger:disabled {
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-danger:not([disabled]):not(.disabled):active, .btn-danger:not([disabled]):not(.disabled).active, .show > .btn-danger.dropdown-toggle {
  color: color-yiq(#bd2130);
  background-color: #bd2130;
  border-color: #b21f2d;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(220, 53, 69, .5);
}

.btn-light {
  color: color-yiq(#f8f9fa);
  background-color: #f8f9fa;
  border-color: #f8f9fa;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.btn-light:hover {
  color: color-yiq(#e2e6ea);
  background-color: #e2e6ea;
  border-color: #dae0e5;
}

.btn-light:focus, .btn-light.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(248, 249, 250, .5);
}

.btn-light.disabled, .btn-light:disabled {
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-light:not([disabled]):not(.disabled):active, .btn-light:not([disabled]):not(.disabled).active, .show > .btn-light.dropdown-toggle {
  color: color-yiq(#dae0e5);
  background-color: #dae0e5;
  border-color: #d3d9df;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(248, 249, 250, .5);
}

.btn-dark {
  color: color-yiq(#343a40);
  background-color: #343a40;
  border-color: #343a40;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}

.btn-dark:hover {
  color: color-yiq(#23272b);
  background-color: #23272b;
  border-color: #1d2124;
}

.btn-dark:focus, .btn-dark.focus {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075), 0 0 0 2px rgba(52, 58, 64, .5);
}

.btn-dark.disabled, .btn-dark:disabled {
  background-color: #343a40;
  border-color: #343a40;
}

.btn-dark:not([disabled]):not(.disabled):active, .btn-dark:not([disabled]):not(.disabled).active, .show > .btn-dark.dropdown-toggle {
  color: color-yiq(#1d2124);
  background-color: #1d2124;
  border-color: #171a1d;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125), 0 0 0 2px rgba(52, 58, 64, .5);
}

.btn-outline-primary {
  color: #007bff;
  background-color: transparent;
  background-image: none;
  border-color: #007bff;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-outline-primary:focus, .btn-outline-primary.focus {
  box-shadow: 0 0 0 2px rgba(0, 123, 255, .5);
}

.btn-outline-primary.disabled, .btn-outline-primary:disabled {
  color: #007bff;
  background-color: transparent;
}

.btn-outline-primary:not([disabled]):not(.disabled):active, .btn-outline-primary:not([disabled]):not(.disabled).active, .show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, .5);
}

.btn-outline-secondary {
  color: #868e96;
  background-color: transparent;
  background-image: none;
  border-color: #868e96;
}

.btn-outline-secondary:hover {
  color: #fff;
  background-color: #868e96;
  border-color: #868e96;
}

.btn-outline-secondary:focus, .btn-outline-secondary.focus {
  box-shadow: 0 0 0 2px rgba(134, 142, 150, .5);
}

.btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
  color: #868e96;
  background-color: transparent;
}

.btn-outline-secondary:not([disabled]):not(.disabled):active, .btn-outline-secondary:not([disabled]):not(.disabled).active, .show > .btn-outline-secondary.dropdown-toggle {
  color: #fff;
  background-color: #868e96;
  border-color: #868e96;
  box-shadow: 0 0 0 2px rgba(134, 142, 150, .5);
}

.btn-outline-success {
  color: #28a745;
  background-color: transparent;
  background-image: none;
  border-color: #28a745;
}

.btn-outline-success:hover {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.btn-outline-success:focus, .btn-outline-success.focus {
  box-shadow: 0 0 0 2px rgba(40, 167, 69, .5);
}

.btn-outline-success.disabled, .btn-outline-success:disabled {
  color: #28a745;
  background-color: transparent;
}

.btn-outline-success:not([disabled]):not(.disabled):active, .btn-outline-success:not([disabled]):not(.disabled).active, .show > .btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
  box-shadow: 0 0 0 2px rgba(40, 167, 69, .5);
}

.btn-outline-info {
  color: #17a2b8;
  background-color: transparent;
  background-image: none;
  border-color: #17a2b8;
}

.btn-outline-info:hover {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-outline-info:focus, .btn-outline-info.focus {
  box-shadow: 0 0 0 2px rgba(23, 162, 184, .5);
}

.btn-outline-info.disabled, .btn-outline-info:disabled {
  color: #17a2b8;
  background-color: transparent;
}

.btn-outline-info:not([disabled]):not(.disabled):active, .btn-outline-info:not([disabled]):not(.disabled).active, .show > .btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
  box-shadow: 0 0 0 2px rgba(23, 162, 184, .5);
}

.btn-outline-warning {
  color: #ffc107;
  background-color: transparent;
  background-image: none;
  border-color: #ffc107;
}

.btn-outline-warning:hover {
  color: #fff;
  background-color: #ffc107;
  border-color: #ffc107;
}

.btn-outline-warning:focus, .btn-outline-warning.focus {
  box-shadow: 0 0 0 2px rgba(255, 193, 7, .5);
}

.btn-outline-warning.disabled, .btn-outline-warning:disabled {
  color: #ffc107;
  background-color: transparent;
}

.btn-outline-warning:not([disabled]):not(.disabled):active, .btn-outline-warning:not([disabled]):not(.disabled).active, .show > .btn-outline-warning.dropdown-toggle {
  color: #fff;
  background-color: #ffc107;
  border-color: #ffc107;
  box-shadow: 0 0 0 2px rgba(255, 193, 7, .5);
}

.btn-outline-danger {
  color: #dc3545;
  background-color: transparent;
  background-image: none;
  border-color: #dc3545;
}

.btn-outline-danger:hover {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-outline-danger:focus, .btn-outline-danger.focus {
  box-shadow: 0 0 0 2px rgba(220, 53, 69, .5);
}

.btn-outline-danger.disabled, .btn-outline-danger:disabled {
  color: #dc3545;
  background-color: transparent;
}

.btn-outline-danger:not([disabled]):not(.disabled):active, .btn-outline-danger:not([disabled]):not(.disabled).active, .show > .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, .5);
}

.btn-outline-light {
  color: #f8f9fa;
  background-color: transparent;
  background-image: none;
  border-color: #f8f9fa;
}

.btn-outline-light:hover {
  color: #3e4854;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-outline-light:focus, .btn-outline-light.focus {
  box-shadow: 0 0 0 2px rgba(248, 249, 250, .5);
}

.btn-outline-light.disabled, .btn-outline-light:disabled {
  color: #f8f9fa;
  background-color: transparent;
}

.btn-outline-light:not([disabled]):not(.disabled):active, .btn-outline-light:not([disabled]):not(.disabled).active, .show > .btn-outline-light.dropdown-toggle {
  color: #3e4854;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
  box-shadow: 0 0 0 2px rgba(248, 249, 250, .5);
}

.btn-outline-dark {
  color: #343a40;
  background-color: transparent;
  background-image: none;
  border-color: #343a40;
}

.btn-outline-dark:hover {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-outline-dark:focus, .btn-outline-dark.focus {
  box-shadow: 0 0 0 2px rgba(52, 58, 64, .5);
}

.btn-outline-dark.disabled, .btn-outline-dark:disabled {
  color: #343a40;
  background-color: transparent;
}

.btn-outline-dark:not([disabled]):not(.disabled):active, .btn-outline-dark:not([disabled]):not(.disabled).active, .show > .btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
  box-shadow: 0 0 0 2px rgba(52, 58, 64, .5);
}

.btn-link {
  color: #997b71;
  background-color: transparent;
}

.btn-link:hover {
  color: #ab8c82;
  background-color: transparent;
  border-color: transparent;
}

.btn-link:focus, .btn-link.focus {
  border-color: transparent;
  box-shadow: none;
}

.btn-link:disabled, .btn-link.disabled {
  color: #a3afb7;
}

.dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: .2431rem;
  vertical-align: .2431rem;
  content: "";
  border-top: .286rem solid;
  border-right: .286rem solid transparent;
  border-bottom: 0;
  border-left: .286rem solid transparent;
}

.dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-menu {
  color: #76838f;
  background-color: #fff;
  border: 1px solid #e4eaec;
  box-shadow: 0 3px 12px rgba(0, 0, 0, .05);
}

.dropup .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: .2431rem;
  vertical-align: .2431rem;
  content: "";
  border-top: 0;
  border-right: .286rem solid transparent;
  border-bottom: .286rem solid;
  border-left: .286rem solid transparent;
}

.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-divider {
  height: 0;
  margin: .5715rem 0;
  overflow: hidden;
  border-top: 1px solid #e4eaec;
}

.dropdown-item {
  color: #76838f;
  background: none;
  border: 0;
}

.dropdown-item:focus, .dropdown-item:hover {
  color: #76838f;
  background-color: #f3f7f9;
}

.dropdown-item.active, .dropdown-item:active {
  color: #76838f;
  background-color: #f3f7f9;
}

.dropdown-item.disabled, .dropdown-item:disabled {
  color: #e4eaec;
  background-color: transparent;
}

.dropdown-header {
  color: #37474f;
}

.btn-group.show .dropdown-toggle {
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
}

.btn-group.show .dropdown-toggle.btn-link {
  box-shadow: none;
}

.input-group-addon {
  color: #76838f;
  background-color: #e4eaec;
  border: 1px solid #e4eaec;
}

.custom-control-input:checked ~ .custom-control-indicator {
  color: #fff;
  background-color: #007bff;
  box-shadow: none;
}

.custom-control-input:focus ~ .cutom-control-indicator {
  box-shadow: 0 0 0 1px #fff, 0 0 0 .2rem rgba(0, 123, 255, .25);
}

.custom-control-input:active ~ .custom-control-indicator {
  color: #fff;
  background-color: #b3d7ff;
  box-shadow: none;
}

.custom-control-input:disabled ~ .custom-control-indicator {
  background-color: #e9ecef;
}

.custom-control-input:disabled ~ .custom-control-description {
  color: #868e96;
}

.custom-control-indicator {
  background-color: #ddd;
  box-shadow: inset 0 .25rem .25rem rgba(0, 0, 0, .1);
}

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-indicator {
  background-color: #007bff;
  box-shadow: none;
}

.custom-select {
  color: #495057;
  background: #fff url("data:image/svg+xml;charset=utf8, %3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"%3E%3Cpath fill="%23333" d="M2 0L0 2h4zm0 5L0 3h4z"/%3E%3C/svg%3E") no-repeat right .75rem center;
  border: 1px solid #ced4da;
}

.custom-select:focus {
  border-color: #80bdff;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, .075), 0 0 5px rgba(128, 189, 255, .5);
}

.custom-select:focus::-ms-value {
  color: #76838f;
  background-color: #fff;
}

.custom-select:disabled {
  color: #868e96;
  background-color: #e9ecef;
}

.custom-file-input:focus ~ .custom-file-control {
  box-shadow: 0 0 0 .075rem #fff, 0 0 0 .2rem #007bff;
}

.custom-file-control {
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}

.custom-file-control::before {
  color: #495057;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
}

.nav-link.disabled {
  color: #a3afb7;
}

.nav-tabs {
  border-bottom: 1px solid #e4eaec;
}

.nav-tabs .nav-link {
  border: 1px solid transparent;
}

.nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover {
  border-color: transparent transparent #e4eaec;
}

.nav-tabs .nav-link.disabled {
  color: #a3afb7;
  background-color: transparent;
  border-color: transparent;
}

.nav-tabs .nav-link.active, .nav-tabs .nav-item.show .nav-link {
  color: #fff;
  background-color: #997b71;
  border-color: #e4eaec #e4eaec #997b71;
}

.nav-pills .nav-link.active, .nav-pills .show > .nav-link {
  color: #fff;
  background-color: #997b71;
}

.navbar-toggler {
  background: transparent;
  border: 1px solid transparent;
}

.navbar-light .navbar-brand {
  color: rgba(0, 0, 0, .8);
}

.navbar-light .navbar-brand:focus, .navbar-light .navbar-brand:hover {
  color: rgba(0, 0, 0, .8);
}

.navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 0, .3);
}

.navbar-light .navbar-nav .nav-link:focus, .navbar-light .navbar-nav .nav-link:hover {
  color: rgba(0, 0, 0, .6);
}

.navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 0, .15);
}

.navbar-light .navbar-nav .show > .nav-link, .navbar-light .navbar-nav .active > .nav-link, .navbar-light .navbar-nav .nav-link.show, .navbar-light .navbar-nav .nav-link.active {
  color: rgba(0, 0, 0, .8);
}

.navbar-light .navbar-toggler {
  color: rgba(0, 0, 0, .3);
  border-color: rgba(0, 0, 0, .1);
}

.navbar-light .navbar-text {
  color: rgba(0, 0, 0, .3);
}

.navbar-light .navbar-text a {
  color: rgba(0, 0, 0, .8);
}

.navbar-light .navbar-text a:focus, .navbar-light .navbar-text a:hover {
  color: rgba(0, 0, 0, .8);
}

.navbar-dark .navbar-brand {
  color: #fff;
}

.navbar-dark .navbar-brand:focus, .navbar-dark .navbar-brand:hover {
  color: #fff;
}

.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, .5);
}

.navbar-dark .navbar-nav .nav-link:focus, .navbar-dark .navbar-nav .nav-link:hover {
  color: rgba(255, 255, 255, .75);
}

.navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, .25);
}

.navbar-dark .navbar-nav .show > .nav-link, .navbar-dark .navbar-nav .active > .nav-link, .navbar-dark .navbar-nav .nav-link.show, .navbar-dark .navbar-nav .nav-link.active {
  color: #fff;
}

.navbar-dark .navbar-toggler {
  color: rgba(255, 255, 255, .5);
  border-color: rgba(255, 255, 255, .1);
}

.navbar-dark .navbar-text {
  color: rgba(255, 255, 255, .5);
}

.navbar-dark .navbar-text a {
  color: #fff;
}

.navbar-dark .navbar-text a:focus, .navbar-dark .navbar-text a:hover {
  color: #fff;
}

.card {
  background-color: #fff;
  border: 1px solid #e4eaec;
}

.card-header {
  background-color: #f3f7f9;
  border-bottom: 1px solid #e4eaec;
}

.card-footer {
  background-color: #f3f7f9;
  border-top: 1px solid #e4eaec;
}

.breadcrumb {
  background-color: transparent;
}

.breadcrumb-item + .breadcrumb-item::before {
  color: #ccd5db;
}

.breadcrumb-item.active {
  color: #76838f;
}

.page-item.active .page-link {
  color: #fff;
  background-color: #997b71;
  border-color: #997b71;
}

.page-item.disabled .page-link {
  color: #ccd5db;
  background-color: transparent;
  border-color: #e4eaec;
}

.page-link {
  color: #76838f;
  background-color: transparent;
  border: 1px solid #e4eaec;
}

.page-link:focus, .page-link:hover {
  color: #ab8c82;
  background-color: #f3f7f9;
  border-color: #e4eaec;
}

.badge-primary {
  color: color-yiq(#007bff);
  background-color: #007bff;
}

.badge-primary[href]:focus, .badge-primary[href]:hover {
  color: color-yiq(#007bff);
  text-decoration: none;
  background-color: #0062cc;
}

.badge-secondary {
  color: color-yiq(#868e96);
  background-color: #868e96;
}

.badge-secondary[href]:focus, .badge-secondary[href]:hover {
  color: color-yiq(#868e96);
  text-decoration: none;
  background-color: #6c757d;
}

.badge-success {
  color: color-yiq(#28a745);
  background-color: #28a745;
}

.badge-success[href]:focus, .badge-success[href]:hover {
  color: color-yiq(#28a745);
  text-decoration: none;
  background-color: #1e7e34;
}

.badge-info {
  color: color-yiq(#17a2b8);
  background-color: #17a2b8;
}

.badge-info[href]:focus, .badge-info[href]:hover {
  color: color-yiq(#17a2b8);
  text-decoration: none;
  background-color: #117a8b;
}

.badge-warning {
  color: color-yiq(#ffc107);
  background-color: #ffc107;
}

.badge-warning[href]:focus, .badge-warning[href]:hover {
  color: color-yiq(#ffc107);
  text-decoration: none;
  background-color: #d39e00;
}

.badge-danger {
  color: color-yiq(#dc3545);
  background-color: #dc3545;
}

.badge-danger[href]:focus, .badge-danger[href]:hover {
  color: color-yiq(#dc3545);
  text-decoration: none;
  background-color: #bd2130;
}

.badge-light {
  color: color-yiq(#f8f9fa);
  background-color: #f8f9fa;
}

.badge-light[href]:focus, .badge-light[href]:hover {
  color: color-yiq(#f8f9fa);
  text-decoration: none;
  background-color: #dae0e5;
}

.badge-dark {
  color: color-yiq(#343a40);
  background-color: #343a40;
}

.badge-dark[href]:focus, .badge-dark[href]:hover {
  color: color-yiq(#343a40);
  text-decoration: none;
  background-color: #1d2124;
}

.jumbotron {
  background-color: #e4eaec;
}

.alert {
  border: 1px solid transparent;
}

.alert-heading {
  color: inherit;
}

.alert-dismissible .close {
  color: inherit;
}

.alert-primary {
  color: #004085;
  background-color: #cce5ff;
  border-color: #b8daff;
}

.alert-primary .close {
  color: #004085;
}

.alert-primary .close:hover, .alert-primary .close:focus {
  color: #004085;
}

.alert-secondary {
  color: #464a4e;
  background-color: #e7e8ea;
  border-color: #dddfe2;
}

.alert-secondary .close {
  color: #464a4e;
}

.alert-secondary .close:hover, .alert-secondary .close:focus {
  color: #464a4e;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.alert-success .close {
  color: #155724;
}

.alert-success .close:hover, .alert-success .close:focus {
  color: #155724;
}

.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

.alert-info .close {
  color: #0c5460;
}

.alert-info .close:hover, .alert-info .close:focus {
  color: #0c5460;
}

.alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}

.alert-warning .close {
  color: #856404;
}

.alert-warning .close:hover, .alert-warning .close:focus {
  color: #856404;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-danger .close {
  color: #721c24;
}

.alert-danger .close:hover, .alert-danger .close:focus {
  color: #721c24;
}

.alert-light {
  color: #818182;
  background-color: #fefefe;
  border-color: #fdfdfe;
}

.alert-light .close {
  color: #818182;
}

.alert-light .close:hover, .alert-light .close:focus {
  color: #818182;
}

.alert-dark {
  color: #1b1e21;
  background-color: #d6d8d9;
  border-color: #c6c8ca;
}

.alert-dark .close {
  color: #1b1e21;
}

.alert-dark .close:hover, .alert-dark .close:focus {
  color: #1b1e21;
}

.list-group-item-action {
  color: #76838f;
}

.list-group-item-action:focus, .list-group-item-action:hover {
  color: #76838f;
  background-color: #f3f7f9;
}

.list-group-item-action:active {
  color: #76838f;
  background-color: #e4eaec;
}

.list-group-item {
  background-color: #fff;
  border: 1px solid transparent;
}

.list-group-item.disabled, .list-group-item:disabled {
  color: #e4eaec;
  background-color: transparent;
}

.list-group-item.active {
  color: #997b71;
  background-color: transparent;
  border-color: transparent;
}

.list-group-flush .list-group-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}

.list-group-flush:first-child .list-group-item:first-child {
  border-top: 0;
}

.list-group-flush:last-child .list-group-item:last-child {
  border-bottom: 0;
}

.list-group-item-primary {
  color: #004085;
  background-color: #b8daff;
}

a.list-group-item-primary, button.list-group-item-primary {
  color: #004085;
}

a.list-group-item-primary:focus, a.list-group-item-primary:hover, button.list-group-item-primary:focus, button.list-group-item-primary:hover {
  color: #004085;
  background-color: #9fcdff;
}

a.list-group-item-primary.active, button.list-group-item-primary.active {
  color: #fff;
  background-color: #004085;
  border-color: #004085;
}

.list-group-item-secondary {
  color: #464a4e;
  background-color: #dddfe2;
}

a.list-group-item-secondary, button.list-group-item-secondary {
  color: #464a4e;
}

a.list-group-item-secondary:focus, a.list-group-item-secondary:hover, button.list-group-item-secondary:focus, button.list-group-item-secondary:hover {
  color: #464a4e;
  background-color: #cfd2d6;
}

a.list-group-item-secondary.active, button.list-group-item-secondary.active {
  color: #fff;
  background-color: #464a4e;
  border-color: #464a4e;
}

.list-group-item-success {
  color: #155724;
  background-color: #c3e6cb;
}

a.list-group-item-success, button.list-group-item-success {
  color: #155724;
}

a.list-group-item-success:focus, a.list-group-item-success:hover, button.list-group-item-success:focus, button.list-group-item-success:hover {
  color: #155724;
  background-color: #b1dfbb;
}

a.list-group-item-success.active, button.list-group-item-success.active {
  color: #fff;
  background-color: #155724;
  border-color: #155724;
}

.list-group-item-info {
  color: #0c5460;
  background-color: #bee5eb;
}

a.list-group-item-info, button.list-group-item-info {
  color: #0c5460;
}

a.list-group-item-info:focus, a.list-group-item-info:hover, button.list-group-item-info:focus, button.list-group-item-info:hover {
  color: #0c5460;
  background-color: #abdde5;
}

a.list-group-item-info.active, button.list-group-item-info.active {
  color: #fff;
  background-color: #0c5460;
  border-color: #0c5460;
}

.list-group-item-warning {
  color: #856404;
  background-color: #ffeeba;
}

a.list-group-item-warning, button.list-group-item-warning {
  color: #856404;
}

a.list-group-item-warning:focus, a.list-group-item-warning:hover, button.list-group-item-warning:focus, button.list-group-item-warning:hover {
  color: #856404;
  background-color: #ffe8a1;
}

a.list-group-item-warning.active, button.list-group-item-warning.active {
  color: #fff;
  background-color: #856404;
  border-color: #856404;
}

.list-group-item-danger {
  color: #721c24;
  background-color: #f5c6cb;
}

a.list-group-item-danger, button.list-group-item-danger {
  color: #721c24;
}

a.list-group-item-danger:focus, a.list-group-item-danger:hover, button.list-group-item-danger:focus, button.list-group-item-danger:hover {
  color: #721c24;
  background-color: #f1b0b7;
}

a.list-group-item-danger.active, button.list-group-item-danger.active {
  color: #fff;
  background-color: #721c24;
  border-color: #721c24;
}

.list-group-item-light {
  color: #818182;
  background-color: #fdfdfe;
}

a.list-group-item-light, button.list-group-item-light {
  color: #818182;
}

a.list-group-item-light:focus, a.list-group-item-light:hover, button.list-group-item-light:focus, button.list-group-item-light:hover {
  color: #818182;
  background-color: #ececf6;
}

a.list-group-item-light.active, button.list-group-item-light.active {
  color: #fff;
  background-color: #818182;
  border-color: #818182;
}

.list-group-item-dark {
  color: #1b1e21;
  background-color: #c6c8ca;
}

a.list-group-item-dark, button.list-group-item-dark {
  color: #1b1e21;
}

a.list-group-item-dark:focus, a.list-group-item-dark:hover, button.list-group-item-dark:focus, button.list-group-item-dark:hover {
  color: #1b1e21;
  background-color: #b9bbbe;
}

a.list-group-item-dark.active, button.list-group-item-dark.active {
  color: #fff;
  background-color: #1b1e21;
  border-color: #1b1e21;
}

.close {
  color: #000;
  text-shadow: none;
}

.close:focus, .close:hover {
  color: #000;
}

button.close {
  background: transparent;
  border: 0;
}

.modal-content {
  background-color: #fff;
  border: 1px solid transparent;
  box-shadow: 0 2px 12px rgba(0, 0, 0, .2);
}

.modal-backdrop {
  background-color: #000;
}

.modal-header {
  border-bottom: 1px solid #e4eaec;
}

.modal-footer {
  border-top: 1px solid #e4eaec;
}

@media (min-width: 480px) {
  .modal-content {
    box-shadow: 0 2px 12px rgba(0, 0, 0, .2);
  }
}

.tooltip .arrow::before {
  border-color: transparent;
}

.tooltip.bs-tooltip-top .arrow::before {
  border-top-color: rgba(0, 0, 0, .8);
}

.tooltip.bs-tooltip-right .arrow::before {
  border-right-color: rgba(0, 0, 0, .8);
}

.tooltip.bs-tooltip-bottom .arrow::before {
  border-bottom-color: rgba(0, 0, 0, .8);
}

.tooltip.bs-tooltip-left .arrow::before {
  border-left-color: rgba(0, 0, 0, .8);
}

.tooltip-inner {
  color: #fff;
  background-color: rgba(0, 0, 0, .8);
}

.popover {
  background-color: #fff;
  border: 1px solid rgba(204, 213, 219, .8);
  border-radius: .286rem;
  box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
}

.popover .arrow::before, .popover .arrow::after {
  border-color: transparent;
}

.popover.bs-popover-top .arrow::before {
  border-top-color: rgba(204, 213, 219, .8);
}

.popover.bs-popover-top .arrow::after {
  border-top-color: #fff;
}

.popover.bs-popover-right .arrow::before {
  border-right-color: rgba(204, 213, 219, .8);
}

.popover.bs-popover-right .arrow::after {
  border-right-color: #fff;
}

.popover.bs-popover-bottom .arrow::before {
  border-bottom-color: rgba(204, 213, 219, .8);
}

.popover.bs-popover-bottom .arrow::after {
  border-bottom-color: #fff;
}

.popover.bs-popover-bottom .popover-header::before {
  border-bottom: 1px solid #f3f7f9;
}

.popover.bs-popover-left .arrow::before {
  border-left-color: rgba(204, 213, 219, .8);
}

.popover.bs-popover-left .arrow::after {
  border-left-color: #fff;
}

.popover-header {
  color: #37474f;
  background-color: #f3f7f9;
  border-bottom: 1px solid #e2ecf1;
}

.popover-body {
  color: #76838f;
}

.carousel-control-prev, .carousel-control-next {
  color: #fff;
}

.carousel-control-prev:focus, .carousel-control-prev:hover, .carousel-control-next:focus, .carousel-control-next:hover {
  color: #fff;
}

.carousel-control-prev-icon, .carousel-control-next-icon {
  background: transparent no-repeat center center;
}

.carousel-indicators li {
  background-color: rgba(255, 255, 255, .5);
}

.carousel-indicators .active {
  background-color: #fff;
}

.carousel-caption {
  color: #fff;
}

.bg-primary {
  background-color: #007bff !important;
}

a.bg-primary:focus, a.bg-primary:hover {
  background-color: #0062cc !important;
}

.bg-secondary {
  background-color: #868e96 !important;
}

a.bg-secondary:focus, a.bg-secondary:hover {
  background-color: #6c757d !important;
}

.bg-success {
  background-color: #28a745 !important;
}

a.bg-success:focus, a.bg-success:hover {
  background-color: #1e7e34 !important;
}

.bg-info {
  background-color: #17a2b8 !important;
}

a.bg-info:focus, a.bg-info:hover {
  background-color: #117a8b !important;
}

.bg-warning {
  background-color: #ffc107 !important;
}

a.bg-warning:focus, a.bg-warning:hover {
  background-color: #d39e00 !important;
}

.bg-danger {
  background-color: #dc3545 !important;
}

a.bg-danger:focus, a.bg-danger:hover {
  background-color: #bd2130 !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

a.bg-light:focus, a.bg-light:hover {
  background-color: #dae0e5 !important;
}

.bg-dark {
  background-color: #343a40 !important;
}

a.bg-dark:focus, a.bg-dark:hover {
  background-color: #1d2124 !important;
}

.bg-white {
  background-color: #fff !important;
}

.bg-transparent {
  background-color: transparent !important;
}

.border {
  border: 1px solid #e4eaec !important;
}

.border-0 {
  border: 0 !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-right-0 {
  border-right: 0 !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-left-0 {
  border-left: 0 !important;
}

.border-primary {
  border-color: #997b71 !important;
}

.border-secondary {
  border-color: #868e96 !important;
}

.border-success {
  border-color: #28a745 !important;
}

.border-info {
  border-color: #17a2b8 !important;
}

.border-warning {
  border-color: #ffc107 !important;
}

.border-danger {
  border-color: #dc3545 !important;
}

.border-light {
  border-color: #f8f9fa !important;
}

.border-dark {
  border-color: #343a40 !important;
}

.border-white {
  border-color: #fff !important;
}

.embed-responsive .embed-responsive-item, .embed-responsive iframe, .embed-responsive embed, .embed-responsive object, .embed-responsive video {
  border: 0;
}

.text-white {
  color: #fff !important;
}

.text-primary {
  color: #007bff !important;
}

a.text-primary:focus, a.text-primary:hover {
  color: #0062cc !important;
}

.text-secondary {
  color: #868e96 !important;
}

a.text-secondary:focus, a.text-secondary:hover {
  color: #6c757d !important;
}

.text-success {
  color: #28a745 !important;
}

a.text-success:focus, a.text-success:hover {
  color: #1e7e34 !important;
}

.text-info {
  color: #17a2b8 !important;
}

a.text-info:focus, a.text-info:hover {
  color: #117a8b !important;
}

.text-warning {
  color: #ffc107 !important;
}

a.text-warning:focus, a.text-warning:hover {
  color: #d39e00 !important;
}

.text-danger {
  color: #dc3545 !important;
}

a.text-danger:focus, a.text-danger:hover {
  color: #bd2130 !important;
}

.text-light {
  color: #f8f9fa !important;
}

a.text-light:focus, a.text-light:hover {
  color: #dae0e5 !important;
}

.text-dark {
  color: #343a40 !important;
}

a.text-dark:focus, a.text-dark:hover {
  color: #1d2124 !important;
}

.text-muted {
  color: #a3afb7 !important;
}

button {
  color: inherit;
}

a.text-body {
  color: #76838f;
}

a.text-action {
  color: #a3afb7;
}

a.text-action:hover, a.text-action:focus {
  color: #ccd5db;
}

a.text-like {
  color: #a3afb7 !important;
}

a.text-like.active, a.text-like:hover, a.text-like:focus {
  color: #ff4c52 !important;
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
  text-shadow: rgba(0, 0, 0, .15) 0 0 1px;
}

mark, .mark {
  color: #fff;
}

.drop-cap {
  color: #263238;
}

.drop-cap-reversed {
  color: #fff;
  background-color: #263238;
}

.blockquote {
  color: #526069;
}

.blockquote-success {
  background-color: rgba(17, 194, 109, .1);
  border-color: #11c26d;
}

.blockquote-info {
  background-color: rgba(11, 178, 212, .1);
  border-color: #0bb2d4;
}

.blockquote-warning {
  background-color: rgba(235, 103, 9, .1);
  border-color: #eb6709;
}

.blockquote-danger {
  background-color: rgba(255, 76, 82, .1);
  border-color: #ff4c52;
}

.blockquote-reverse {
  border-right: 2px solid #e4eaec;
  border-left: none;
}

.img-bordered {
  border: 1px solid #e4eaec;
}

.img-bordered-primary {
  border-color: #997b71 !important;
}

.img-bordered-purple {
  border-color: #9463f7 !important;
}

.img-bordered-red {
  border-color: #ff4c52 !important;
}

.img-bordered-green {
  border-color: #49de94 !important;
}

.img-bordered-orange {
  border-color: #eb6709 !important;
}

code {
  border: 1px solid #cfb8b0;
}

pre {
  color: inherit;
  border: 1px solid #f3efee;
}

.table {
  color: #76838f;
}

.table thead th, .table tfoot th {
  color: #526069;
}

.table thead:first-child th {
  border-top: none;
}

.table-active, .table-active > th, .table-active > td {
  color: inherit;
  background-color: #f3f7f9;
}

.table-active a {
  color: inherit;
}

.table-hover .table-active:hover {
  background-color: #e2ecf1;
}

.table-hover .table-active:hover > td, .table-hover .table-active:hover > th {
  color: inherit;
  background-color: #e2ecf1;
}

.table-success, .table-success > th, .table-success > td {
  color: #fff;
  background-color: #11c26d;
}

.table-success a {
  color: #fff;
}

.table-hover .table-success:hover {
  background-color: #0fab60;
}

.table-hover .table-success:hover > td, .table-hover .table-success:hover > th {
  color: #fff;
  background-color: #0fab60;
}

.table-info, .table-info > th, .table-info > td {
  color: #fff;
  background-color: #0bb2d4;
}

.table-info a {
  color: #fff;
}

.table-hover .table-info:hover {
  background-color: #0a9ebc;
}

.table-hover .table-info:hover > td, .table-hover .table-info:hover > th {
  color: #fff;
  background-color: #0a9ebc;
}

.table-warning, .table-warning > th, .table-warning > td {
  color: #fff;
  background-color: #eb6709;
}

.table-warning a {
  color: #fff;
}

.table-hover .table-warning:hover {
  background-color: #d25c08;
}

.table-hover .table-warning:hover > td, .table-hover .table-warning:hover > th {
  color: #fff;
  background-color: #d25c08;
}

.table-danger, .table-danger > th, .table-danger > td {
  color: #fff;
  background-color: #ff4c52;
}

.table-danger a {
  color: #fff;
}

.table-hover .table-danger:hover {
  background-color: #ff3339;
}

.table-hover .table-danger:hover > td, .table-hover .table-danger:hover > th {
  color: #fff;
  background-color: #ff3339;
}

.table .thead-default th {
  color: inherit;
  background-color: #f3f7f9;
}

.table .thead-primary th {
  color: #fff;
  background-color: #997b71;
}

.table .thead-success th {
  color: #fff;
  background-color: #11c26d;
}

.table .thead-info th {
  color: #fff;
  background-color: #0bb2d4;
}

.table .thead-warning th {
  color: #fff;
  background-color: #eb6709;
}

.table .thead-danger th {
  color: #fff;
  background-color: #ff4c52;
}

.table .thead-dark th {
  color: #fff;
  background-color: #526069;
}

.table .thead-gray th {
  color: #526069;
  background-color: #ccd5db;
}

.table-section.active tr {
  background-color: #f3f7f9;
}

.form-control {
  border-color: #e4eaec;
  box-shadow: none;
}

select.form-control {
  background: #fff url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAFCAYAAABB9hwOAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA4RpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDpiNWZkMzNlMC0zNTcxLTI4NDgtYjA3NC01ZTRhN2RjMWVmNjEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RTUxRUI3MDdEQjk4MTFFNUI1NDA5QTcyNTlFQzRERTYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RTUxRUI3MDZEQjk4MTFFNUI1NDA5QTcyNTlFQzRERTYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6ZWNiNjQzMjYtNDc1Yi01OTQxLWIxYjItNDVkZjU5YjZlODA2IiBzdFJlZjpkb2N1bWVudElEPSJhZG9iZTpkb2NpZDpwaG90b3Nob3A6N2RlYzI2YWMtZGI5OC0xMWU1LWIwMjgtY2ZhNDhhOGNjNWY1Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+AXTIGgAAAFRJREFUeNpidI1KSWFgYDBlwASngXjOrqWzGcgBTEC8DIjfo4m/h4qTDUAGfwPi+UD8Hyr2H8r/RqnBIHATiPdC2XuhfIoACxJ7PRDzQmmKAUCAAQDxOxHyb4DjOAAAAABJRU5ErkJggg==) no-repeat center right;
}

select[multiple].form-control {
  background-image: none;
}

.input-group-file input[type="text"] {
  background-color: #fff;
}

.input-group-file .btn-file.btn-outline {
  border: 1px solid #e4eaec;
  border-left: none;
}

.input-group-file .btn-file.btn-outline:hover {
  border-left: none;
}

.text-help {
  color: #bcc2c8;
}

.input-search-close {
  color: #000;
  text-shadow: none;
}

.input-search-close:hover, .input-search-close:focus {
  color: #000;
}

button.input-search-close {
  background: transparent;
  border: 0;
}

.input-search .input-search-icon {
  color: #a3afb7;
}

.input-search-btn {
  background: transparent;
  border: none;
}

.input-search-dark .input-search-icon {
  color: #76838f;
}

.input-search-dark .form-control {
  background: #f3f7f9;
  box-shadow: none;
}

.input-search-dark .form-control:focus {
  background-color: #fff;
}

/*$btn-floating-xs-padding:                10px !default;*/
/*$btn-floating-sm-padding:                13px !default;*/
/*$btn-floating-lg-padding:                15px !default;*/
.btn:active, .btn.active, .btn:focus, .btn.focus {
  box-shadow: none;
}

.btn:not([disabled]):not(.disabled):active, .btn:not([disabled]):not(.disabled).active {
  box-shadow: none;
}

.btn-outline-default, .btn-outline.btn-default {
  color: #76838f;
  background-color: transparent;
  border-color: #e4eaec;
}

.btn-outline-default.btn-up:before, .btn-outline.btn-default.btn-up:before {
  border-bottom-color: rgba(118, 131, 143, .1);
}

.btn-outline-default.btn-up:hover:before, .btn-outline-default.btn-up.hover:before, .btn-outline-default.btn-up:focus:before, .btn-outline-default.btn-up.focus:before, .btn-outline.btn-default.btn-up:hover:before, .btn-outline.btn-default.btn-up.hover:before, .btn-outline.btn-default.btn-up:focus:before, .btn-outline.btn-default.btn-up.focus:before {
  border-bottom-color: rgba(118, 131, 143, .1);
}

.btn-outline-default.btn-up.active:before, .btn-outline-default.btn-up:active:before, .open > .btn-outline-default.btn-up.dropdown-toggle:before, .show > .btn-outline-default.btn-up.dropdown-toggle:before, .btn-outline.btn-default.btn-up.active:before, .btn-outline.btn-default.btn-up:active:before, .open > .btn-outline.btn-default.btn-up.dropdown-toggle:before, .show > .btn-outline.btn-default.btn-up.dropdown-toggle:before {
  border-bottom-color: #ccd5db;
}

.btn-outline-default.btn-right:before, .btn-outline.btn-default.btn-right:before {
  border-left-color: rgba(118, 131, 143, .1);
}

.btn-outline-default.btn-right:hover:before, .btn-outline-default.btn-right.hover:before, .btn-outline-default.btn-right:focus:before, .btn-outline-default.btn-right.focus:before, .btn-outline.btn-default.btn-right:hover:before, .btn-outline.btn-default.btn-right.hover:before, .btn-outline.btn-default.btn-right:focus:before, .btn-outline.btn-default.btn-right.focus:before {
  border-left-color: rgba(118, 131, 143, .1);
}

.btn-outline-default.btn-right.active:before, .btn-outline-default.btn-right:active:before, .open > .btn-outline-default.btn-right.dropdown-toggle:before, .show > .btn-outline-default.btn-right.dropdown-toggle:before, .btn-outline.btn-default.btn-right.active:before, .btn-outline.btn-default.btn-right:active:before, .open > .btn-outline.btn-default.btn-right.dropdown-toggle:before, .show > .btn-outline.btn-default.btn-right.dropdown-toggle:before {
  border-left-color: #ccd5db;
}

.btn-outline-default.btn-bottom:before, .btn-outline.btn-default.btn-bottom:before {
  border-top-color: rgba(118, 131, 143, .1);
}

.btn-outline-default.btn-bottom:hover:before, .btn-outline-default.btn-bottom.hover:before, .btn-outline-default.btn-bottom:focus:before, .btn-outline-default.btn-bottom.focus:before, .btn-outline.btn-default.btn-bottom:hover:before, .btn-outline.btn-default.btn-bottom.hover:before, .btn-outline.btn-default.btn-bottom:focus:before, .btn-outline.btn-default.btn-bottom.focus:before {
  border-top-color: rgba(118, 131, 143, .1);
}

.btn-outline-default.btn-bottom.active:before, .btn-outline-default.btn-bottom:active:before, .open > .btn-outline-default.btn-bottom.dropdown-toggle:before, .show > .btn-outline-default.btn-bottom.dropdown-toggle:before, .btn-outline.btn-default.btn-bottom.active:before, .btn-outline.btn-default.btn-bottom:active:before, .open > .btn-outline.btn-default.btn-bottom.dropdown-toggle:before, .show > .btn-outline.btn-default.btn-bottom.dropdown-toggle:before {
  border-top-color: #ccd5db;
}

.btn-outline-default.btn-left:before, .btn-outline.btn-default.btn-left:before {
  border-right-color: rgba(118, 131, 143, .1);
}

.btn-outline-default.btn-left:hover:before, .btn-outline-default.btn-left.hover:before, .btn-outline-default.btn-left:focus:before, .btn-outline-default.btn-left.focus:before, .btn-outline.btn-default.btn-left:hover:before, .btn-outline.btn-default.btn-left.hover:before, .btn-outline.btn-default.btn-left:focus:before, .btn-outline.btn-default.btn-left.focus:before {
  border-right-color: rgba(118, 131, 143, .1);
}

.btn-outline-default.btn-left.active:before, .btn-outline-default.btn-left:active:before, .open > .btn-outline-default.btn-left.dropdown-toggle:before, .show > .btn-outline-default.btn-left.dropdown-toggle:before, .btn-outline.btn-default.btn-left.active:before, .btn-outline.btn-default.btn-left:active:before, .open > .btn-outline.btn-default.btn-left.dropdown-toggle:before, .show > .btn-outline.btn-default.btn-left.dropdown-toggle:before {
  border-right-color: #ccd5db;
}

.btn-outline-default:hover, .btn-outline-default.hover, .btn-outline-default:focus, .btn-outline-default.focus, .btn-outline-default:not([disabled]):not(.disabled):active, .btn-outline-default:not([disabled]):not(.disabled).active, .open > .btn-outline-default.dropdown-toggle, .show > .btn-outline-default.dropdown-toggle, .btn-outline.btn-default:hover, .btn-outline.btn-default.hover, .btn-outline.btn-default:focus, .btn-outline.btn-default.focus, .btn-outline.btn-default:not([disabled]):not(.disabled):active, .btn-outline.btn-default:not([disabled]):not(.disabled).active, .open > .btn-outline.btn-default.dropdown-toggle, .show > .btn-outline.btn-default.dropdown-toggle {
  color: #76838f;
  background-color: rgba(118, 131, 143, .1);
  border-color: #e4eaec;
}

.btn-outline-default:hover .badge-pill, .btn-outline-default.hover .badge-pill, .btn-outline-default:focus .badge-pill, .btn-outline-default.focus .badge-pill, .btn-outline-default:not([disabled]):not(.disabled):active .badge-pill, .btn-outline-default:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline-default.dropdown-toggle .badge-pill, .show > .btn-outline-default.dropdown-toggle .badge-pill, .btn-outline.btn-default:hover .badge-pill, .btn-outline.btn-default.hover .badge-pill, .btn-outline.btn-default:focus .badge-pill, .btn-outline.btn-default.focus .badge-pill, .btn-outline.btn-default:not([disabled]):not(.disabled):active .badge-pill, .btn-outline.btn-default:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline.btn-default.dropdown-toggle .badge-pill, .show > .btn-outline.btn-default.dropdown-toggle .badge-pill {
  color: #76838f;
  background-color: #76838f;
}

.btn-outline-primary, .btn-outline.btn-primary {
  color: #997b71;
  background-color: transparent;
  border-color: #997b71;
}

.btn-outline-primary.btn-up:before, .btn-outline.btn-primary.btn-up:before {
  border-bottom-color: #997b71;
}

.btn-outline-primary.btn-up:hover:before, .btn-outline-primary.btn-up.hover:before, .btn-outline-primary.btn-up:focus:before, .btn-outline-primary.btn-up.focus:before, .btn-outline.btn-primary.btn-up:hover:before, .btn-outline.btn-primary.btn-up.hover:before, .btn-outline.btn-primary.btn-up:focus:before, .btn-outline.btn-primary.btn-up.focus:before {
  border-bottom-color: #997b71;
}

.btn-outline-primary.btn-up.active:before, .btn-outline-primary.btn-up:active:before, .open > .btn-outline-primary.btn-up.dropdown-toggle:before, .show > .btn-outline-primary.btn-up.dropdown-toggle:before, .btn-outline.btn-primary.btn-up.active:before, .btn-outline.btn-primary.btn-up:active:before, .open > .btn-outline.btn-primary.btn-up.dropdown-toggle:before, .show > .btn-outline.btn-primary.btn-up.dropdown-toggle:before {
  border-bottom-color: #82675f;
}

.btn-outline-primary.btn-right:before, .btn-outline.btn-primary.btn-right:before {
  border-left-color: #997b71;
}

.btn-outline-primary.btn-right:hover:before, .btn-outline-primary.btn-right.hover:before, .btn-outline-primary.btn-right:focus:before, .btn-outline-primary.btn-right.focus:before, .btn-outline.btn-primary.btn-right:hover:before, .btn-outline.btn-primary.btn-right.hover:before, .btn-outline.btn-primary.btn-right:focus:before, .btn-outline.btn-primary.btn-right.focus:before {
  border-left-color: #997b71;
}

.btn-outline-primary.btn-right.active:before, .btn-outline-primary.btn-right:active:before, .open > .btn-outline-primary.btn-right.dropdown-toggle:before, .show > .btn-outline-primary.btn-right.dropdown-toggle:before, .btn-outline.btn-primary.btn-right.active:before, .btn-outline.btn-primary.btn-right:active:before, .open > .btn-outline.btn-primary.btn-right.dropdown-toggle:before, .show > .btn-outline.btn-primary.btn-right.dropdown-toggle:before {
  border-left-color: #82675f;
}

.btn-outline-primary.btn-bottom:before, .btn-outline.btn-primary.btn-bottom:before {
  border-top-color: #997b71;
}

.btn-outline-primary.btn-bottom:hover:before, .btn-outline-primary.btn-bottom.hover:before, .btn-outline-primary.btn-bottom:focus:before, .btn-outline-primary.btn-bottom.focus:before, .btn-outline.btn-primary.btn-bottom:hover:before, .btn-outline.btn-primary.btn-bottom.hover:before, .btn-outline.btn-primary.btn-bottom:focus:before, .btn-outline.btn-primary.btn-bottom.focus:before {
  border-top-color: #997b71;
}

.btn-outline-primary.btn-bottom.active:before, .btn-outline-primary.btn-bottom:active:before, .open > .btn-outline-primary.btn-bottom.dropdown-toggle:before, .show > .btn-outline-primary.btn-bottom.dropdown-toggle:before, .btn-outline.btn-primary.btn-bottom.active:before, .btn-outline.btn-primary.btn-bottom:active:before, .open > .btn-outline.btn-primary.btn-bottom.dropdown-toggle:before, .show > .btn-outline.btn-primary.btn-bottom.dropdown-toggle:before {
  border-top-color: #82675f;
}

.btn-outline-primary.btn-left:before, .btn-outline.btn-primary.btn-left:before {
  border-right-color: #997b71;
}

.btn-outline-primary.btn-left:hover:before, .btn-outline-primary.btn-left.hover:before, .btn-outline-primary.btn-left:focus:before, .btn-outline-primary.btn-left.focus:before, .btn-outline.btn-primary.btn-left:hover:before, .btn-outline.btn-primary.btn-left.hover:before, .btn-outline.btn-primary.btn-left:focus:before, .btn-outline.btn-primary.btn-left.focus:before {
  border-right-color: #997b71;
}

.btn-outline-primary.btn-left.active:before, .btn-outline-primary.btn-left:active:before, .open > .btn-outline-primary.btn-left.dropdown-toggle:before, .show > .btn-outline-primary.btn-left.dropdown-toggle:before, .btn-outline.btn-primary.btn-left.active:before, .btn-outline.btn-primary.btn-left:active:before, .open > .btn-outline.btn-primary.btn-left.dropdown-toggle:before, .show > .btn-outline.btn-primary.btn-left.dropdown-toggle:before {
  border-right-color: #82675f;
}

.btn-outline-primary:hover, .btn-outline-primary.hover, .btn-outline-primary:focus, .btn-outline-primary.focus, .btn-outline-primary:not([disabled]):not(.disabled):active, .btn-outline-primary:not([disabled]):not(.disabled).active, .open > .btn-outline-primary.dropdown-toggle, .show > .btn-outline-primary.dropdown-toggle, .btn-outline.btn-primary:hover, .btn-outline.btn-primary.hover, .btn-outline.btn-primary:focus, .btn-outline.btn-primary.focus, .btn-outline.btn-primary:not([disabled]):not(.disabled):active, .btn-outline.btn-primary:not([disabled]):not(.disabled).active, .open > .btn-outline.btn-primary.dropdown-toggle, .show > .btn-outline.btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #997b71;
  border-color: #997b71;
}

.btn-outline-primary:hover .badge-pill, .btn-outline-primary.hover .badge-pill, .btn-outline-primary:focus .badge-pill, .btn-outline-primary.focus .badge-pill, .btn-outline-primary:not([disabled]):not(.disabled):active .badge-pill, .btn-outline-primary:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline-primary.dropdown-toggle .badge-pill, .show > .btn-outline-primary.dropdown-toggle .badge-pill, .btn-outline.btn-primary:hover .badge-pill, .btn-outline.btn-primary.hover .badge-pill, .btn-outline.btn-primary:focus .badge-pill, .btn-outline.btn-primary.focus .badge-pill, .btn-outline.btn-primary:not([disabled]):not(.disabled):active .badge-pill, .btn-outline.btn-primary:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline.btn-primary.dropdown-toggle .badge-pill, .show > .btn-outline.btn-primary.dropdown-toggle .badge-pill {
  color: #997b71;
  background-color: #fff;
}

.btn-outline-success, .btn-outline.btn-success {
  color: #11c26d;
  background-color: transparent;
  border-color: #11c26d;
}

.btn-outline-success.btn-up:before, .btn-outline.btn-success.btn-up:before {
  border-bottom-color: #11c26d;
}

.btn-outline-success.btn-up:hover:before, .btn-outline-success.btn-up.hover:before, .btn-outline-success.btn-up:focus:before, .btn-outline-success.btn-up.focus:before, .btn-outline.btn-success.btn-up:hover:before, .btn-outline.btn-success.btn-up.hover:before, .btn-outline.btn-success.btn-up:focus:before, .btn-outline.btn-success.btn-up.focus:before {
  border-bottom-color: #11c26d;
}

.btn-outline-success.btn-up.active:before, .btn-outline-success.btn-up:active:before, .open > .btn-outline-success.btn-up.dropdown-toggle:before, .show > .btn-outline-success.btn-up.dropdown-toggle:before, .btn-outline.btn-success.btn-up.active:before, .btn-outline.btn-success.btn-up:active:before, .open > .btn-outline.btn-success.btn-up.dropdown-toggle:before, .show > .btn-outline.btn-success.btn-up.dropdown-toggle:before {
  border-bottom-color: #05a85c;
}

.btn-outline-success.btn-right:before, .btn-outline.btn-success.btn-right:before {
  border-left-color: #11c26d;
}

.btn-outline-success.btn-right:hover:before, .btn-outline-success.btn-right.hover:before, .btn-outline-success.btn-right:focus:before, .btn-outline-success.btn-right.focus:before, .btn-outline.btn-success.btn-right:hover:before, .btn-outline.btn-success.btn-right.hover:before, .btn-outline.btn-success.btn-right:focus:before, .btn-outline.btn-success.btn-right.focus:before {
  border-left-color: #11c26d;
}

.btn-outline-success.btn-right.active:before, .btn-outline-success.btn-right:active:before, .open > .btn-outline-success.btn-right.dropdown-toggle:before, .show > .btn-outline-success.btn-right.dropdown-toggle:before, .btn-outline.btn-success.btn-right.active:before, .btn-outline.btn-success.btn-right:active:before, .open > .btn-outline.btn-success.btn-right.dropdown-toggle:before, .show > .btn-outline.btn-success.btn-right.dropdown-toggle:before {
  border-left-color: #05a85c;
}

.btn-outline-success.btn-bottom:before, .btn-outline.btn-success.btn-bottom:before {
  border-top-color: #11c26d;
}

.btn-outline-success.btn-bottom:hover:before, .btn-outline-success.btn-bottom.hover:before, .btn-outline-success.btn-bottom:focus:before, .btn-outline-success.btn-bottom.focus:before, .btn-outline.btn-success.btn-bottom:hover:before, .btn-outline.btn-success.btn-bottom.hover:before, .btn-outline.btn-success.btn-bottom:focus:before, .btn-outline.btn-success.btn-bottom.focus:before {
  border-top-color: #11c26d;
}

.btn-outline-success.btn-bottom.active:before, .btn-outline-success.btn-bottom:active:before, .open > .btn-outline-success.btn-bottom.dropdown-toggle:before, .show > .btn-outline-success.btn-bottom.dropdown-toggle:before, .btn-outline.btn-success.btn-bottom.active:before, .btn-outline.btn-success.btn-bottom:active:before, .open > .btn-outline.btn-success.btn-bottom.dropdown-toggle:before, .show > .btn-outline.btn-success.btn-bottom.dropdown-toggle:before {
  border-top-color: #05a85c;
}

.btn-outline-success.btn-left:before, .btn-outline.btn-success.btn-left:before {
  border-right-color: #11c26d;
}

.btn-outline-success.btn-left:hover:before, .btn-outline-success.btn-left.hover:before, .btn-outline-success.btn-left:focus:before, .btn-outline-success.btn-left.focus:before, .btn-outline.btn-success.btn-left:hover:before, .btn-outline.btn-success.btn-left.hover:before, .btn-outline.btn-success.btn-left:focus:before, .btn-outline.btn-success.btn-left.focus:before {
  border-right-color: #11c26d;
}

.btn-outline-success.btn-left.active:before, .btn-outline-success.btn-left:active:before, .open > .btn-outline-success.btn-left.dropdown-toggle:before, .show > .btn-outline-success.btn-left.dropdown-toggle:before, .btn-outline.btn-success.btn-left.active:before, .btn-outline.btn-success.btn-left:active:before, .open > .btn-outline.btn-success.btn-left.dropdown-toggle:before, .show > .btn-outline.btn-success.btn-left.dropdown-toggle:before {
  border-right-color: #05a85c;
}

.btn-outline-success:hover, .btn-outline-success.hover, .btn-outline-success:focus, .btn-outline-success.focus, .btn-outline-success:not([disabled]):not(.disabled):active, .btn-outline-success:not([disabled]):not(.disabled).active, .open > .btn-outline-success.dropdown-toggle, .show > .btn-outline-success.dropdown-toggle, .btn-outline.btn-success:hover, .btn-outline.btn-success.hover, .btn-outline.btn-success:focus, .btn-outline.btn-success.focus, .btn-outline.btn-success:not([disabled]):not(.disabled):active, .btn-outline.btn-success:not([disabled]):not(.disabled).active, .open > .btn-outline.btn-success.dropdown-toggle, .show > .btn-outline.btn-success.dropdown-toggle {
  color: #fff;
  background-color: #11c26d;
  border-color: #11c26d;
}

.btn-outline-success:hover .badge-pill, .btn-outline-success.hover .badge-pill, .btn-outline-success:focus .badge-pill, .btn-outline-success.focus .badge-pill, .btn-outline-success:not([disabled]):not(.disabled):active .badge-pill, .btn-outline-success:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline-success.dropdown-toggle .badge-pill, .show > .btn-outline-success.dropdown-toggle .badge-pill, .btn-outline.btn-success:hover .badge-pill, .btn-outline.btn-success.hover .badge-pill, .btn-outline.btn-success:focus .badge-pill, .btn-outline.btn-success.focus .badge-pill, .btn-outline.btn-success:not([disabled]):not(.disabled):active .badge-pill, .btn-outline.btn-success:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline.btn-success.dropdown-toggle .badge-pill, .show > .btn-outline.btn-success.dropdown-toggle .badge-pill {
  color: #11c26d;
  background-color: #fff;
}

.btn-outline-info, .btn-outline.btn-info {
  color: #0bb2d4;
  background-color: transparent;
  border-color: #0bb2d4;
}

.btn-outline-info.btn-up:before, .btn-outline.btn-info.btn-up:before {
  border-bottom-color: #0bb2d4;
}

.btn-outline-info.btn-up:hover:before, .btn-outline-info.btn-up.hover:before, .btn-outline-info.btn-up:focus:before, .btn-outline-info.btn-up.focus:before, .btn-outline.btn-info.btn-up:hover:before, .btn-outline.btn-info.btn-up.hover:before, .btn-outline.btn-info.btn-up:focus:before, .btn-outline.btn-info.btn-up.focus:before {
  border-bottom-color: #0bb2d4;
}

.btn-outline-info.btn-up.active:before, .btn-outline-info.btn-up:active:before, .open > .btn-outline-info.btn-up.dropdown-toggle:before, .show > .btn-outline-info.btn-up.dropdown-toggle:before, .btn-outline.btn-info.btn-up.active:before, .btn-outline.btn-info.btn-up:active:before, .open > .btn-outline.btn-info.btn-up.dropdown-toggle:before, .show > .btn-outline.btn-info.btn-up.dropdown-toggle:before {
  border-bottom-color: #0099b8;
}

.btn-outline-info.btn-right:before, .btn-outline.btn-info.btn-right:before {
  border-left-color: #0bb2d4;
}

.btn-outline-info.btn-right:hover:before, .btn-outline-info.btn-right.hover:before, .btn-outline-info.btn-right:focus:before, .btn-outline-info.btn-right.focus:before, .btn-outline.btn-info.btn-right:hover:before, .btn-outline.btn-info.btn-right.hover:before, .btn-outline.btn-info.btn-right:focus:before, .btn-outline.btn-info.btn-right.focus:before {
  border-left-color: #0bb2d4;
}

.btn-outline-info.btn-right.active:before, .btn-outline-info.btn-right:active:before, .open > .btn-outline-info.btn-right.dropdown-toggle:before, .show > .btn-outline-info.btn-right.dropdown-toggle:before, .btn-outline.btn-info.btn-right.active:before, .btn-outline.btn-info.btn-right:active:before, .open > .btn-outline.btn-info.btn-right.dropdown-toggle:before, .show > .btn-outline.btn-info.btn-right.dropdown-toggle:before {
  border-left-color: #0099b8;
}

.btn-outline-info.btn-bottom:before, .btn-outline.btn-info.btn-bottom:before {
  border-top-color: #0bb2d4;
}

.btn-outline-info.btn-bottom:hover:before, .btn-outline-info.btn-bottom.hover:before, .btn-outline-info.btn-bottom:focus:before, .btn-outline-info.btn-bottom.focus:before, .btn-outline.btn-info.btn-bottom:hover:before, .btn-outline.btn-info.btn-bottom.hover:before, .btn-outline.btn-info.btn-bottom:focus:before, .btn-outline.btn-info.btn-bottom.focus:before {
  border-top-color: #0bb2d4;
}

.btn-outline-info.btn-bottom.active:before, .btn-outline-info.btn-bottom:active:before, .open > .btn-outline-info.btn-bottom.dropdown-toggle:before, .show > .btn-outline-info.btn-bottom.dropdown-toggle:before, .btn-outline.btn-info.btn-bottom.active:before, .btn-outline.btn-info.btn-bottom:active:before, .open > .btn-outline.btn-info.btn-bottom.dropdown-toggle:before, .show > .btn-outline.btn-info.btn-bottom.dropdown-toggle:before {
  border-top-color: #0099b8;
}

.btn-outline-info.btn-left:before, .btn-outline.btn-info.btn-left:before {
  border-right-color: #0bb2d4;
}

.btn-outline-info.btn-left:hover:before, .btn-outline-info.btn-left.hover:before, .btn-outline-info.btn-left:focus:before, .btn-outline-info.btn-left.focus:before, .btn-outline.btn-info.btn-left:hover:before, .btn-outline.btn-info.btn-left.hover:before, .btn-outline.btn-info.btn-left:focus:before, .btn-outline.btn-info.btn-left.focus:before {
  border-right-color: #0bb2d4;
}

.btn-outline-info.btn-left.active:before, .btn-outline-info.btn-left:active:before, .open > .btn-outline-info.btn-left.dropdown-toggle:before, .show > .btn-outline-info.btn-left.dropdown-toggle:before, .btn-outline.btn-info.btn-left.active:before, .btn-outline.btn-info.btn-left:active:before, .open > .btn-outline.btn-info.btn-left.dropdown-toggle:before, .show > .btn-outline.btn-info.btn-left.dropdown-toggle:before {
  border-right-color: #0099b8;
}

.btn-outline-info:hover, .btn-outline-info.hover, .btn-outline-info:focus, .btn-outline-info.focus, .btn-outline-info:not([disabled]):not(.disabled):active, .btn-outline-info:not([disabled]):not(.disabled).active, .open > .btn-outline-info.dropdown-toggle, .show > .btn-outline-info.dropdown-toggle, .btn-outline.btn-info:hover, .btn-outline.btn-info.hover, .btn-outline.btn-info:focus, .btn-outline.btn-info.focus, .btn-outline.btn-info:not([disabled]):not(.disabled):active, .btn-outline.btn-info:not([disabled]):not(.disabled).active, .open > .btn-outline.btn-info.dropdown-toggle, .show > .btn-outline.btn-info.dropdown-toggle {
  color: #fff;
  background-color: #0bb2d4;
  border-color: #0bb2d4;
}

.btn-outline-info:hover .badge-pill, .btn-outline-info.hover .badge-pill, .btn-outline-info:focus .badge-pill, .btn-outline-info.focus .badge-pill, .btn-outline-info:not([disabled]):not(.disabled):active .badge-pill, .btn-outline-info:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline-info.dropdown-toggle .badge-pill, .show > .btn-outline-info.dropdown-toggle .badge-pill, .btn-outline.btn-info:hover .badge-pill, .btn-outline.btn-info.hover .badge-pill, .btn-outline.btn-info:focus .badge-pill, .btn-outline.btn-info.focus .badge-pill, .btn-outline.btn-info:not([disabled]):not(.disabled):active .badge-pill, .btn-outline.btn-info:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline.btn-info.dropdown-toggle .badge-pill, .show > .btn-outline.btn-info.dropdown-toggle .badge-pill {
  color: #0bb2d4;
  background-color: #fff;
}

.btn-outline-warning, .btn-outline.btn-warning {
  color: #eb6709;
  background-color: transparent;
  border-color: #eb6709;
}

.btn-outline-warning.btn-up:before, .btn-outline.btn-warning.btn-up:before {
  border-bottom-color: #eb6709;
}

.btn-outline-warning.btn-up:hover:before, .btn-outline-warning.btn-up.hover:before, .btn-outline-warning.btn-up:focus:before, .btn-outline-warning.btn-up.focus:before, .btn-outline.btn-warning.btn-up:hover:before, .btn-outline.btn-warning.btn-up.hover:before, .btn-outline.btn-warning.btn-up:focus:before, .btn-outline.btn-warning.btn-up.focus:before {
  border-bottom-color: #eb6709;
}

.btn-outline-warning.btn-up.active:before, .btn-outline-warning.btn-up:active:before, .open > .btn-outline-warning.btn-up.dropdown-toggle:before, .show > .btn-outline-warning.btn-up.dropdown-toggle:before, .btn-outline.btn-warning.btn-up.active:before, .btn-outline.btn-warning.btn-up:active:before, .open > .btn-outline.btn-warning.btn-up.dropdown-toggle:before, .show > .btn-outline.btn-warning.btn-up.dropdown-toggle:before {
  border-bottom-color: #de4e00;
}

.btn-outline-warning.btn-right:before, .btn-outline.btn-warning.btn-right:before {
  border-left-color: #eb6709;
}

.btn-outline-warning.btn-right:hover:before, .btn-outline-warning.btn-right.hover:before, .btn-outline-warning.btn-right:focus:before, .btn-outline-warning.btn-right.focus:before, .btn-outline.btn-warning.btn-right:hover:before, .btn-outline.btn-warning.btn-right.hover:before, .btn-outline.btn-warning.btn-right:focus:before, .btn-outline.btn-warning.btn-right.focus:before {
  border-left-color: #eb6709;
}

.btn-outline-warning.btn-right.active:before, .btn-outline-warning.btn-right:active:before, .open > .btn-outline-warning.btn-right.dropdown-toggle:before, .show > .btn-outline-warning.btn-right.dropdown-toggle:before, .btn-outline.btn-warning.btn-right.active:before, .btn-outline.btn-warning.btn-right:active:before, .open > .btn-outline.btn-warning.btn-right.dropdown-toggle:before, .show > .btn-outline.btn-warning.btn-right.dropdown-toggle:before {
  border-left-color: #de4e00;
}

.btn-outline-warning.btn-bottom:before, .btn-outline.btn-warning.btn-bottom:before {
  border-top-color: #eb6709;
}

.btn-outline-warning.btn-bottom:hover:before, .btn-outline-warning.btn-bottom.hover:before, .btn-outline-warning.btn-bottom:focus:before, .btn-outline-warning.btn-bottom.focus:before, .btn-outline.btn-warning.btn-bottom:hover:before, .btn-outline.btn-warning.btn-bottom.hover:before, .btn-outline.btn-warning.btn-bottom:focus:before, .btn-outline.btn-warning.btn-bottom.focus:before {
  border-top-color: #eb6709;
}

.btn-outline-warning.btn-bottom.active:before, .btn-outline-warning.btn-bottom:active:before, .open > .btn-outline-warning.btn-bottom.dropdown-toggle:before, .show > .btn-outline-warning.btn-bottom.dropdown-toggle:before, .btn-outline.btn-warning.btn-bottom.active:before, .btn-outline.btn-warning.btn-bottom:active:before, .open > .btn-outline.btn-warning.btn-bottom.dropdown-toggle:before, .show > .btn-outline.btn-warning.btn-bottom.dropdown-toggle:before {
  border-top-color: #de4e00;
}

.btn-outline-warning.btn-left:before, .btn-outline.btn-warning.btn-left:before {
  border-right-color: #eb6709;
}

.btn-outline-warning.btn-left:hover:before, .btn-outline-warning.btn-left.hover:before, .btn-outline-warning.btn-left:focus:before, .btn-outline-warning.btn-left.focus:before, .btn-outline.btn-warning.btn-left:hover:before, .btn-outline.btn-warning.btn-left.hover:before, .btn-outline.btn-warning.btn-left:focus:before, .btn-outline.btn-warning.btn-left.focus:before {
  border-right-color: #eb6709;
}

.btn-outline-warning.btn-left.active:before, .btn-outline-warning.btn-left:active:before, .open > .btn-outline-warning.btn-left.dropdown-toggle:before, .show > .btn-outline-warning.btn-left.dropdown-toggle:before, .btn-outline.btn-warning.btn-left.active:before, .btn-outline.btn-warning.btn-left:active:before, .open > .btn-outline.btn-warning.btn-left.dropdown-toggle:before, .show > .btn-outline.btn-warning.btn-left.dropdown-toggle:before {
  border-right-color: #de4e00;
}

.btn-outline-warning:hover, .btn-outline-warning.hover, .btn-outline-warning:focus, .btn-outline-warning.focus, .btn-outline-warning:not([disabled]):not(.disabled):active, .btn-outline-warning:not([disabled]):not(.disabled).active, .open > .btn-outline-warning.dropdown-toggle, .show > .btn-outline-warning.dropdown-toggle, .btn-outline.btn-warning:hover, .btn-outline.btn-warning.hover, .btn-outline.btn-warning:focus, .btn-outline.btn-warning.focus, .btn-outline.btn-warning:not([disabled]):not(.disabled):active, .btn-outline.btn-warning:not([disabled]):not(.disabled).active, .open > .btn-outline.btn-warning.dropdown-toggle, .show > .btn-outline.btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #eb6709;
  border-color: #eb6709;
}

.btn-outline-warning:hover .badge-pill, .btn-outline-warning.hover .badge-pill, .btn-outline-warning:focus .badge-pill, .btn-outline-warning.focus .badge-pill, .btn-outline-warning:not([disabled]):not(.disabled):active .badge-pill, .btn-outline-warning:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline-warning.dropdown-toggle .badge-pill, .show > .btn-outline-warning.dropdown-toggle .badge-pill, .btn-outline.btn-warning:hover .badge-pill, .btn-outline.btn-warning.hover .badge-pill, .btn-outline.btn-warning:focus .badge-pill, .btn-outline.btn-warning.focus .badge-pill, .btn-outline.btn-warning:not([disabled]):not(.disabled):active .badge-pill, .btn-outline.btn-warning:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline.btn-warning.dropdown-toggle .badge-pill, .show > .btn-outline.btn-warning.dropdown-toggle .badge-pill {
  color: #eb6709;
  background-color: #fff;
}

.btn-outline-danger, .btn-outline.btn-danger {
  color: #ff4c52;
  background-color: transparent;
  border-color: #ff4c52;
}

.btn-outline-danger.btn-up:before, .btn-outline.btn-danger.btn-up:before {
  border-bottom-color: #ff4c52;
}

.btn-outline-danger.btn-up:hover:before, .btn-outline-danger.btn-up.hover:before, .btn-outline-danger.btn-up:focus:before, .btn-outline-danger.btn-up.focus:before, .btn-outline.btn-danger.btn-up:hover:before, .btn-outline.btn-danger.btn-up.hover:before, .btn-outline.btn-danger.btn-up:focus:before, .btn-outline.btn-danger.btn-up.focus:before {
  border-bottom-color: #ff4c52;
}

.btn-outline-danger.btn-up.active:before, .btn-outline-danger.btn-up:active:before, .open > .btn-outline-danger.btn-up.dropdown-toggle:before, .show > .btn-outline-danger.btn-up.dropdown-toggle:before, .btn-outline.btn-danger.btn-up.active:before, .btn-outline.btn-danger.btn-up:active:before, .open > .btn-outline.btn-danger.btn-up.dropdown-toggle:before, .show > .btn-outline.btn-danger.btn-up.dropdown-toggle:before {
  border-bottom-color: #f2353c;
}

.btn-outline-danger.btn-right:before, .btn-outline.btn-danger.btn-right:before {
  border-left-color: #ff4c52;
}

.btn-outline-danger.btn-right:hover:before, .btn-outline-danger.btn-right.hover:before, .btn-outline-danger.btn-right:focus:before, .btn-outline-danger.btn-right.focus:before, .btn-outline.btn-danger.btn-right:hover:before, .btn-outline.btn-danger.btn-right.hover:before, .btn-outline.btn-danger.btn-right:focus:before, .btn-outline.btn-danger.btn-right.focus:before {
  border-left-color: #ff4c52;
}

.btn-outline-danger.btn-right.active:before, .btn-outline-danger.btn-right:active:before, .open > .btn-outline-danger.btn-right.dropdown-toggle:before, .show > .btn-outline-danger.btn-right.dropdown-toggle:before, .btn-outline.btn-danger.btn-right.active:before, .btn-outline.btn-danger.btn-right:active:before, .open > .btn-outline.btn-danger.btn-right.dropdown-toggle:before, .show > .btn-outline.btn-danger.btn-right.dropdown-toggle:before {
  border-left-color: #f2353c;
}

.btn-outline-danger.btn-bottom:before, .btn-outline.btn-danger.btn-bottom:before {
  border-top-color: #ff4c52;
}

.btn-outline-danger.btn-bottom:hover:before, .btn-outline-danger.btn-bottom.hover:before, .btn-outline-danger.btn-bottom:focus:before, .btn-outline-danger.btn-bottom.focus:before, .btn-outline.btn-danger.btn-bottom:hover:before, .btn-outline.btn-danger.btn-bottom.hover:before, .btn-outline.btn-danger.btn-bottom:focus:before, .btn-outline.btn-danger.btn-bottom.focus:before {
  border-top-color: #ff4c52;
}

.btn-outline-danger.btn-bottom.active:before, .btn-outline-danger.btn-bottom:active:before, .open > .btn-outline-danger.btn-bottom.dropdown-toggle:before, .show > .btn-outline-danger.btn-bottom.dropdown-toggle:before, .btn-outline.btn-danger.btn-bottom.active:before, .btn-outline.btn-danger.btn-bottom:active:before, .open > .btn-outline.btn-danger.btn-bottom.dropdown-toggle:before, .show > .btn-outline.btn-danger.btn-bottom.dropdown-toggle:before {
  border-top-color: #f2353c;
}

.btn-outline-danger.btn-left:before, .btn-outline.btn-danger.btn-left:before {
  border-right-color: #ff4c52;
}

.btn-outline-danger.btn-left:hover:before, .btn-outline-danger.btn-left.hover:before, .btn-outline-danger.btn-left:focus:before, .btn-outline-danger.btn-left.focus:before, .btn-outline.btn-danger.btn-left:hover:before, .btn-outline.btn-danger.btn-left.hover:before, .btn-outline.btn-danger.btn-left:focus:before, .btn-outline.btn-danger.btn-left.focus:before {
  border-right-color: #ff4c52;
}

.btn-outline-danger.btn-left.active:before, .btn-outline-danger.btn-left:active:before, .open > .btn-outline-danger.btn-left.dropdown-toggle:before, .show > .btn-outline-danger.btn-left.dropdown-toggle:before, .btn-outline.btn-danger.btn-left.active:before, .btn-outline.btn-danger.btn-left:active:before, .open > .btn-outline.btn-danger.btn-left.dropdown-toggle:before, .show > .btn-outline.btn-danger.btn-left.dropdown-toggle:before {
  border-right-color: #f2353c;
}

.btn-outline-danger:hover, .btn-outline-danger.hover, .btn-outline-danger:focus, .btn-outline-danger.focus, .btn-outline-danger:not([disabled]):not(.disabled):active, .btn-outline-danger:not([disabled]):not(.disabled).active, .open > .btn-outline-danger.dropdown-toggle, .show > .btn-outline-danger.dropdown-toggle, .btn-outline.btn-danger:hover, .btn-outline.btn-danger.hover, .btn-outline.btn-danger:focus, .btn-outline.btn-danger.focus, .btn-outline.btn-danger:not([disabled]):not(.disabled):active, .btn-outline.btn-danger:not([disabled]):not(.disabled).active, .open > .btn-outline.btn-danger.dropdown-toggle, .show > .btn-outline.btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #ff4c52;
  border-color: #ff4c52;
}

.btn-outline-danger:hover .badge-pill, .btn-outline-danger.hover .badge-pill, .btn-outline-danger:focus .badge-pill, .btn-outline-danger.focus .badge-pill, .btn-outline-danger:not([disabled]):not(.disabled):active .badge-pill, .btn-outline-danger:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline-danger.dropdown-toggle .badge-pill, .show > .btn-outline-danger.dropdown-toggle .badge-pill, .btn-outline.btn-danger:hover .badge-pill, .btn-outline.btn-danger.hover .badge-pill, .btn-outline.btn-danger:focus .badge-pill, .btn-outline.btn-danger.focus .badge-pill, .btn-outline.btn-danger:not([disabled]):not(.disabled):active .badge-pill, .btn-outline.btn-danger:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline.btn-danger.dropdown-toggle .badge-pill, .show > .btn-outline.btn-danger.dropdown-toggle .badge-pill {
  color: #ff4c52;
  background-color: #fff;
}

.btn-outline-dark, .btn-outline.btn-dark {
  color: #526069;
  background-color: transparent;
  border-color: #526069;
}

.btn-outline-dark.btn-up:before, .btn-outline.btn-dark.btn-up:before {
  border-bottom-color: #526069;
}

.btn-outline-dark.btn-up:hover:before, .btn-outline-dark.btn-up.hover:before, .btn-outline-dark.btn-up:focus:before, .btn-outline-dark.btn-up.focus:before, .btn-outline.btn-dark.btn-up:hover:before, .btn-outline.btn-dark.btn-up.hover:before, .btn-outline.btn-dark.btn-up:focus:before, .btn-outline.btn-dark.btn-up.focus:before {
  border-bottom-color: #526069;
}

.btn-outline-dark.btn-up.active:before, .btn-outline-dark.btn-up:active:before, .open > .btn-outline-dark.btn-up.dropdown-toggle:before, .show > .btn-outline-dark.btn-up.dropdown-toggle:before, .btn-outline.btn-dark.btn-up.active:before, .btn-outline.btn-dark.btn-up:active:before, .open > .btn-outline.btn-dark.btn-up.dropdown-toggle:before, .show > .btn-outline.btn-dark.btn-up.dropdown-toggle:before {
  border-bottom-color: #37474f;
}

.btn-outline-dark.btn-right:before, .btn-outline.btn-dark.btn-right:before {
  border-left-color: #526069;
}

.btn-outline-dark.btn-right:hover:before, .btn-outline-dark.btn-right.hover:before, .btn-outline-dark.btn-right:focus:before, .btn-outline-dark.btn-right.focus:before, .btn-outline.btn-dark.btn-right:hover:before, .btn-outline.btn-dark.btn-right.hover:before, .btn-outline.btn-dark.btn-right:focus:before, .btn-outline.btn-dark.btn-right.focus:before {
  border-left-color: #526069;
}

.btn-outline-dark.btn-right.active:before, .btn-outline-dark.btn-right:active:before, .open > .btn-outline-dark.btn-right.dropdown-toggle:before, .show > .btn-outline-dark.btn-right.dropdown-toggle:before, .btn-outline.btn-dark.btn-right.active:before, .btn-outline.btn-dark.btn-right:active:before, .open > .btn-outline.btn-dark.btn-right.dropdown-toggle:before, .show > .btn-outline.btn-dark.btn-right.dropdown-toggle:before {
  border-left-color: #37474f;
}

.btn-outline-dark.btn-bottom:before, .btn-outline.btn-dark.btn-bottom:before {
  border-top-color: #526069;
}

.btn-outline-dark.btn-bottom:hover:before, .btn-outline-dark.btn-bottom.hover:before, .btn-outline-dark.btn-bottom:focus:before, .btn-outline-dark.btn-bottom.focus:before, .btn-outline.btn-dark.btn-bottom:hover:before, .btn-outline.btn-dark.btn-bottom.hover:before, .btn-outline.btn-dark.btn-bottom:focus:before, .btn-outline.btn-dark.btn-bottom.focus:before {
  border-top-color: #526069;
}

.btn-outline-dark.btn-bottom.active:before, .btn-outline-dark.btn-bottom:active:before, .open > .btn-outline-dark.btn-bottom.dropdown-toggle:before, .show > .btn-outline-dark.btn-bottom.dropdown-toggle:before, .btn-outline.btn-dark.btn-bottom.active:before, .btn-outline.btn-dark.btn-bottom:active:before, .open > .btn-outline.btn-dark.btn-bottom.dropdown-toggle:before, .show > .btn-outline.btn-dark.btn-bottom.dropdown-toggle:before {
  border-top-color: #37474f;
}

.btn-outline-dark.btn-left:before, .btn-outline.btn-dark.btn-left:before {
  border-right-color: #526069;
}

.btn-outline-dark.btn-left:hover:before, .btn-outline-dark.btn-left.hover:before, .btn-outline-dark.btn-left:focus:before, .btn-outline-dark.btn-left.focus:before, .btn-outline.btn-dark.btn-left:hover:before, .btn-outline.btn-dark.btn-left.hover:before, .btn-outline.btn-dark.btn-left:focus:before, .btn-outline.btn-dark.btn-left.focus:before {
  border-right-color: #526069;
}

.btn-outline-dark.btn-left.active:before, .btn-outline-dark.btn-left:active:before, .open > .btn-outline-dark.btn-left.dropdown-toggle:before, .show > .btn-outline-dark.btn-left.dropdown-toggle:before, .btn-outline.btn-dark.btn-left.active:before, .btn-outline.btn-dark.btn-left:active:before, .open > .btn-outline.btn-dark.btn-left.dropdown-toggle:before, .show > .btn-outline.btn-dark.btn-left.dropdown-toggle:before {
  border-right-color: #37474f;
}

.btn-outline-dark:hover, .btn-outline-dark.hover, .btn-outline-dark:focus, .btn-outline-dark.focus, .btn-outline-dark:not([disabled]):not(.disabled):active, .btn-outline-dark:not([disabled]):not(.disabled).active, .open > .btn-outline-dark.dropdown-toggle, .show > .btn-outline-dark.dropdown-toggle, .btn-outline.btn-dark:hover, .btn-outline.btn-dark.hover, .btn-outline.btn-dark:focus, .btn-outline.btn-dark.focus, .btn-outline.btn-dark:not([disabled]):not(.disabled):active, .btn-outline.btn-dark:not([disabled]):not(.disabled).active, .open > .btn-outline.btn-dark.dropdown-toggle, .show > .btn-outline.btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #526069;
  border-color: #526069;
}

.btn-outline-dark:hover .badge-pill, .btn-outline-dark.hover .badge-pill, .btn-outline-dark:focus .badge-pill, .btn-outline-dark.focus .badge-pill, .btn-outline-dark:not([disabled]):not(.disabled):active .badge-pill, .btn-outline-dark:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline-dark.dropdown-toggle .badge-pill, .show > .btn-outline-dark.dropdown-toggle .badge-pill, .btn-outline.btn-dark:hover .badge-pill, .btn-outline.btn-dark.hover .badge-pill, .btn-outline.btn-dark:focus .badge-pill, .btn-outline.btn-dark.focus .badge-pill, .btn-outline.btn-dark:not([disabled]):not(.disabled):active .badge-pill, .btn-outline.btn-dark:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline.btn-dark.dropdown-toggle .badge-pill, .show > .btn-outline.btn-dark.dropdown-toggle .badge-pill {
  color: #526069;
  background-color: #fff;
}

.btn-outline-inverse, .btn-outline.btn-inverse {
  color: #fff;
  background-color: transparent;
  border-color: #fff;
}

.btn-outline-inverse.btn-up:before, .btn-outline.btn-inverse.btn-up:before {
  border-bottom-color: #fff;
}

.btn-outline-inverse.btn-up:hover:before, .btn-outline-inverse.btn-up.hover:before, .btn-outline-inverse.btn-up:focus:before, .btn-outline-inverse.btn-up.focus:before, .btn-outline.btn-inverse.btn-up:hover:before, .btn-outline.btn-inverse.btn-up.hover:before, .btn-outline.btn-inverse.btn-up:focus:before, .btn-outline.btn-inverse.btn-up.focus:before {
  border-bottom-color: #fff;
}

.btn-outline-inverse.btn-up.active:before, .btn-outline-inverse.btn-up:active:before, .open > .btn-outline-inverse.btn-up.dropdown-toggle:before, .show > .btn-outline-inverse.btn-up.dropdown-toggle:before, .btn-outline.btn-inverse.btn-up.active:before, .btn-outline.btn-inverse.btn-up:active:before, .open > .btn-outline.btn-inverse.btn-up.dropdown-toggle:before, .show > .btn-outline.btn-inverse.btn-up.dropdown-toggle:before {
  border-bottom-color: #fff;
}

.btn-outline-inverse.btn-right:before, .btn-outline.btn-inverse.btn-right:before {
  border-left-color: #fff;
}

.btn-outline-inverse.btn-right:hover:before, .btn-outline-inverse.btn-right.hover:before, .btn-outline-inverse.btn-right:focus:before, .btn-outline-inverse.btn-right.focus:before, .btn-outline.btn-inverse.btn-right:hover:before, .btn-outline.btn-inverse.btn-right.hover:before, .btn-outline.btn-inverse.btn-right:focus:before, .btn-outline.btn-inverse.btn-right.focus:before {
  border-left-color: #fff;
}

.btn-outline-inverse.btn-right.active:before, .btn-outline-inverse.btn-right:active:before, .open > .btn-outline-inverse.btn-right.dropdown-toggle:before, .show > .btn-outline-inverse.btn-right.dropdown-toggle:before, .btn-outline.btn-inverse.btn-right.active:before, .btn-outline.btn-inverse.btn-right:active:before, .open > .btn-outline.btn-inverse.btn-right.dropdown-toggle:before, .show > .btn-outline.btn-inverse.btn-right.dropdown-toggle:before {
  border-left-color: #fff;
}

.btn-outline-inverse.btn-bottom:before, .btn-outline.btn-inverse.btn-bottom:before {
  border-top-color: #fff;
}

.btn-outline-inverse.btn-bottom:hover:before, .btn-outline-inverse.btn-bottom.hover:before, .btn-outline-inverse.btn-bottom:focus:before, .btn-outline-inverse.btn-bottom.focus:before, .btn-outline.btn-inverse.btn-bottom:hover:before, .btn-outline.btn-inverse.btn-bottom.hover:before, .btn-outline.btn-inverse.btn-bottom:focus:before, .btn-outline.btn-inverse.btn-bottom.focus:before {
  border-top-color: #fff;
}

.btn-outline-inverse.btn-bottom.active:before, .btn-outline-inverse.btn-bottom:active:before, .open > .btn-outline-inverse.btn-bottom.dropdown-toggle:before, .show > .btn-outline-inverse.btn-bottom.dropdown-toggle:before, .btn-outline.btn-inverse.btn-bottom.active:before, .btn-outline.btn-inverse.btn-bottom:active:before, .open > .btn-outline.btn-inverse.btn-bottom.dropdown-toggle:before, .show > .btn-outline.btn-inverse.btn-bottom.dropdown-toggle:before {
  border-top-color: #fff;
}

.btn-outline-inverse.btn-left:before, .btn-outline.btn-inverse.btn-left:before {
  border-right-color: #fff;
}

.btn-outline-inverse.btn-left:hover:before, .btn-outline-inverse.btn-left.hover:before, .btn-outline-inverse.btn-left:focus:before, .btn-outline-inverse.btn-left.focus:before, .btn-outline.btn-inverse.btn-left:hover:before, .btn-outline.btn-inverse.btn-left.hover:before, .btn-outline.btn-inverse.btn-left:focus:before, .btn-outline.btn-inverse.btn-left.focus:before {
  border-right-color: #fff;
}

.btn-outline-inverse.btn-left.active:before, .btn-outline-inverse.btn-left:active:before, .open > .btn-outline-inverse.btn-left.dropdown-toggle:before, .show > .btn-outline-inverse.btn-left.dropdown-toggle:before, .btn-outline.btn-inverse.btn-left.active:before, .btn-outline.btn-inverse.btn-left:active:before, .open > .btn-outline.btn-inverse.btn-left.dropdown-toggle:before, .show > .btn-outline.btn-inverse.btn-left.dropdown-toggle:before {
  border-right-color: #fff;
}

.btn-outline-inverse:hover, .btn-outline-inverse.hover, .btn-outline-inverse:focus, .btn-outline-inverse.focus, .btn-outline-inverse:not([disabled]):not(.disabled):active, .btn-outline-inverse:not([disabled]):not(.disabled).active, .open > .btn-outline-inverse.dropdown-toggle, .show > .btn-outline-inverse.dropdown-toggle, .btn-outline.btn-inverse:hover, .btn-outline.btn-inverse.hover, .btn-outline.btn-inverse:focus, .btn-outline.btn-inverse.focus, .btn-outline.btn-inverse:not([disabled]):not(.disabled):active, .btn-outline.btn-inverse:not([disabled]):not(.disabled).active, .open > .btn-outline.btn-inverse.dropdown-toggle, .show > .btn-outline.btn-inverse.dropdown-toggle {
  color: #76838f;
  background-color: #fff;
  border-color: #fff;
}

.btn-outline-inverse:hover .badge-pill, .btn-outline-inverse.hover .badge-pill, .btn-outline-inverse:focus .badge-pill, .btn-outline-inverse.focus .badge-pill, .btn-outline-inverse:not([disabled]):not(.disabled):active .badge-pill, .btn-outline-inverse:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline-inverse.dropdown-toggle .badge-pill, .show > .btn-outline-inverse.dropdown-toggle .badge-pill, .btn-outline.btn-inverse:hover .badge-pill, .btn-outline.btn-inverse.hover .badge-pill, .btn-outline.btn-inverse:focus .badge-pill, .btn-outline.btn-inverse.focus .badge-pill, .btn-outline.btn-inverse:not([disabled]):not(.disabled):active .badge-pill, .btn-outline.btn-inverse:not([disabled]):not(.disabled).active .badge-pill, .open > .btn-outline.btn-inverse.dropdown-toggle .badge-pill, .show > .btn-outline.btn-inverse.dropdown-toggle .badge-pill {
  color: #fff;
  background-color: #76838f;
}

.btn-primary {
  color: color-yiq(#997b71);
  background-color: #997b71;
  border-color: #997b71;
  box-shadow: none;
}

.btn-primary.btn-up:before {
  border-bottom-color: #997b71;
}

.btn-primary.btn-up:hover:before, .btn-primary.btn-up.hover:before, .btn-primary.btn-up:focus:before, .btn-primary.btn-up.focus:before {
  border-bottom-color: #ab8c82;
}

.btn-primary.btn-up.active:before, .btn-primary.btn-up:active:before, .open > .btn-primary.btn-up.dropdown-toggle:before, .show > .btn-primary.btn-up.dropdown-toggle:before {
  border-bottom-color: #82675f;
}

.btn-primary.btn-right:before {
  border-left-color: #997b71;
}

.btn-primary.btn-right:hover:before, .btn-primary.btn-right.hover:before, .btn-primary.btn-right:focus:before, .btn-primary.btn-right.focus:before {
  border-left-color: #ab8c82;
}

.btn-primary.btn-right.active:before, .btn-primary.btn-right:active:before, .open > .btn-primary.btn-right.dropdown-toggle:before, .show > .btn-primary.btn-right.dropdown-toggle:before {
  border-left-color: #82675f;
}

.btn-primary.btn-bottom:before {
  border-top-color: #997b71;
}

.btn-primary.btn-bottom:hover:before, .btn-primary.btn-bottom.hover:before, .btn-primary.btn-bottom:focus:before, .btn-primary.btn-bottom.focus:before {
  border-top-color: #ab8c82;
}

.btn-primary.btn-bottom.active:before, .btn-primary.btn-bottom:active:before, .open > .btn-primary.btn-bottom.dropdown-toggle:before, .show > .btn-primary.btn-bottom.dropdown-toggle:before {
  border-top-color: #82675f;
}

.btn-primary.btn-left:before {
  border-right-color: #997b71;
}

.btn-primary.btn-left:hover:before, .btn-primary.btn-left.hover:before, .btn-primary.btn-left:focus:before, .btn-primary.btn-left.focus:before {
  border-right-color: #ab8c82;
}

.btn-primary.btn-left.active:before, .btn-primary.btn-left:active:before, .open > .btn-primary.btn-left.dropdown-toggle:before, .show > .btn-primary.btn-left.dropdown-toggle:before {
  border-right-color: #82675f;
}

.btn-primary:hover, .btn-primary.hover, .btn-primary:focus, .btn-primary.focus {
  color: #fff;
  background-color: #ab8c82;
  border-color: #ab8c82;
  box-shadow: none;
}

.btn-primary:not([disabled]):not(.disabled):active, .btn-primary:not([disabled]):not(.disabled).active, .open > .btn-primary.dropdown-toggle, .show > .btn-primary.dropdown-toggle {
  background-color: #82675f;
  border-color: #82675f;
  box-shadow: none;
}

.btn-primary:not([disabled]):not(.disabled):active:hover, .btn-primary:not([disabled]):not(.disabled):active.hover, .btn-primary:not([disabled]):not(.disabled):active:focus, .btn-primary:not([disabled]):not(.disabled):active.focus, .btn-primary:not([disabled]):not(.disabled).active:hover, .btn-primary:not([disabled]):not(.disabled).active.hover, .btn-primary:not([disabled]):not(.disabled).active:focus, .btn-primary:not([disabled]):not(.disabled).active.focus, .open > .btn-primary.dropdown-toggle:hover, .open > .btn-primary.dropdown-toggle.hover, .open > .btn-primary.dropdown-toggle:focus, .open > .btn-primary.dropdown-toggle.focus, .show > .btn-primary.dropdown-toggle:hover, .show > .btn-primary.dropdown-toggle.hover, .show > .btn-primary.dropdown-toggle:focus, .show > .btn-primary.dropdown-toggle.focus {
  background-color: #82675f;
  border-color: #82675f;
}

.btn-primary.disabled, .btn-primary.disabled:hover, .btn-primary.disabled.hover, .btn-primary.disabled:focus, .btn-primary.disabled.focus, .btn-primary.disabled:active, .btn-primary.disabled.active, .btn-primary[disabled], .btn-primary[disabled]:hover, .btn-primary[disabled].hover, .btn-primary[disabled]:focus, .btn-primary[disabled].focus, .btn-primary[disabled]:active, .btn-primary[disabled].active, fieldset[disabled] .btn-primary, fieldset[disabled] .btn-primary:hover, fieldset[disabled] .btn-primary.hover, fieldset[disabled] .btn-primary:focus, fieldset[disabled] .btn-primary.focus, fieldset[disabled] .btn-primary:active, fieldset[disabled] .btn-primary.active {
  color: #fff;
  background-color: #bda299;
  border-color: #bda299;
}

.btn-primary .badge-pill {
  color: #997b71;
  background-color: #fff;
}

.btn-primary.btn-flat {
  color: #997b71;
}

.btn-primary.btn-flat:hover, .btn-primary.btn-flat.hover, .btn-primary.btn-flat:focus, .btn-primary.btn-flat.focus {
  color: #fff;
}

.btn-success {
  color: color-yiq(#11c26d);
  background-color: #11c26d;
  border-color: #11c26d;
  box-shadow: none;
}

.btn-success.btn-up:before {
  border-bottom-color: #11c26d;
}

.btn-success.btn-up:hover:before, .btn-success.btn-up.hover:before, .btn-success.btn-up:focus:before, .btn-success.btn-up.focus:before {
  border-bottom-color: #28d17c;
}

.btn-success.btn-up.active:before, .btn-success.btn-up:active:before, .open > .btn-success.btn-up.dropdown-toggle:before, .show > .btn-success.btn-up.dropdown-toggle:before {
  border-bottom-color: #05a85c;
}

.btn-success.btn-right:before {
  border-left-color: #11c26d;
}

.btn-success.btn-right:hover:before, .btn-success.btn-right.hover:before, .btn-success.btn-right:focus:before, .btn-success.btn-right.focus:before {
  border-left-color: #28d17c;
}

.btn-success.btn-right.active:before, .btn-success.btn-right:active:before, .open > .btn-success.btn-right.dropdown-toggle:before, .show > .btn-success.btn-right.dropdown-toggle:before {
  border-left-color: #05a85c;
}

.btn-success.btn-bottom:before {
  border-top-color: #11c26d;
}

.btn-success.btn-bottom:hover:before, .btn-success.btn-bottom.hover:before, .btn-success.btn-bottom:focus:before, .btn-success.btn-bottom.focus:before {
  border-top-color: #28d17c;
}

.btn-success.btn-bottom.active:before, .btn-success.btn-bottom:active:before, .open > .btn-success.btn-bottom.dropdown-toggle:before, .show > .btn-success.btn-bottom.dropdown-toggle:before {
  border-top-color: #05a85c;
}

.btn-success.btn-left:before {
  border-right-color: #11c26d;
}

.btn-success.btn-left:hover:before, .btn-success.btn-left.hover:before, .btn-success.btn-left:focus:before, .btn-success.btn-left.focus:before {
  border-right-color: #28d17c;
}

.btn-success.btn-left.active:before, .btn-success.btn-left:active:before, .open > .btn-success.btn-left.dropdown-toggle:before, .show > .btn-success.btn-left.dropdown-toggle:before {
  border-right-color: #05a85c;
}

.btn-success:hover, .btn-success.hover, .btn-success:focus, .btn-success.focus {
  color: #fff;
  background-color: #28d17c;
  border-color: #28d17c;
  box-shadow: none;
}

.btn-success:not([disabled]):not(.disabled):active, .btn-success:not([disabled]):not(.disabled).active, .open > .btn-success.dropdown-toggle, .show > .btn-success.dropdown-toggle {
  background-color: #05a85c;
  border-color: #05a85c;
  box-shadow: none;
}

.btn-success:not([disabled]):not(.disabled):active:hover, .btn-success:not([disabled]):not(.disabled):active.hover, .btn-success:not([disabled]):not(.disabled):active:focus, .btn-success:not([disabled]):not(.disabled):active.focus, .btn-success:not([disabled]):not(.disabled).active:hover, .btn-success:not([disabled]):not(.disabled).active.hover, .btn-success:not([disabled]):not(.disabled).active:focus, .btn-success:not([disabled]):not(.disabled).active.focus, .open > .btn-success.dropdown-toggle:hover, .open > .btn-success.dropdown-toggle.hover, .open > .btn-success.dropdown-toggle:focus, .open > .btn-success.dropdown-toggle.focus, .show > .btn-success.dropdown-toggle:hover, .show > .btn-success.dropdown-toggle.hover, .show > .btn-success.dropdown-toggle:focus, .show > .btn-success.dropdown-toggle.focus {
  background-color: #05a85c;
  border-color: #05a85c;
}

.btn-success.disabled, .btn-success.disabled:hover, .btn-success.disabled.hover, .btn-success.disabled:focus, .btn-success.disabled.focus, .btn-success.disabled:active, .btn-success.disabled.active, .btn-success[disabled], .btn-success[disabled]:hover, .btn-success[disabled].hover, .btn-success[disabled]:focus, .btn-success[disabled].focus, .btn-success[disabled]:active, .btn-success[disabled].active, fieldset[disabled] .btn-success, fieldset[disabled] .btn-success:hover, fieldset[disabled] .btn-success.hover, fieldset[disabled] .btn-success:focus, fieldset[disabled] .btn-success.focus, fieldset[disabled] .btn-success:active, fieldset[disabled] .btn-success.active {
  color: #fff;
  background-color: #49de94;
  border-color: #49de94;
}

.btn-success .badge-pill {
  color: #11c26d;
  background-color: #fff;
}

.btn-success.btn-flat {
  color: #11c26d;
}

.btn-success.btn-flat:hover, .btn-success.btn-flat.hover, .btn-success.btn-flat:focus, .btn-success.btn-flat.focus {
  color: #fff;
}

.btn-info {
  color: color-yiq(#0bb2d4);
  background-color: #0bb2d4;
  border-color: #0bb2d4;
  box-shadow: none;
}

.btn-info.btn-up:before {
  border-bottom-color: #0bb2d4;
}

.btn-info.btn-up:hover:before, .btn-info.btn-up.hover:before, .btn-info.btn-up:focus:before, .btn-info.btn-up.focus:before {
  border-bottom-color: #28c0de;
}

.btn-info.btn-up.active:before, .btn-info.btn-up:active:before, .open > .btn-info.btn-up.dropdown-toggle:before, .show > .btn-info.btn-up.dropdown-toggle:before {
  border-bottom-color: #0099b8;
}

.btn-info.btn-right:before {
  border-left-color: #0bb2d4;
}

.btn-info.btn-right:hover:before, .btn-info.btn-right.hover:before, .btn-info.btn-right:focus:before, .btn-info.btn-right.focus:before {
  border-left-color: #28c0de;
}

.btn-info.btn-right.active:before, .btn-info.btn-right:active:before, .open > .btn-info.btn-right.dropdown-toggle:before, .show > .btn-info.btn-right.dropdown-toggle:before {
  border-left-color: #0099b8;
}

.btn-info.btn-bottom:before {
  border-top-color: #0bb2d4;
}

.btn-info.btn-bottom:hover:before, .btn-info.btn-bottom.hover:before, .btn-info.btn-bottom:focus:before, .btn-info.btn-bottom.focus:before {
  border-top-color: #28c0de;
}

.btn-info.btn-bottom.active:before, .btn-info.btn-bottom:active:before, .open > .btn-info.btn-bottom.dropdown-toggle:before, .show > .btn-info.btn-bottom.dropdown-toggle:before {
  border-top-color: #0099b8;
}

.btn-info.btn-left:before {
  border-right-color: #0bb2d4;
}

.btn-info.btn-left:hover:before, .btn-info.btn-left.hover:before, .btn-info.btn-left:focus:before, .btn-info.btn-left.focus:before {
  border-right-color: #28c0de;
}

.btn-info.btn-left.active:before, .btn-info.btn-left:active:before, .open > .btn-info.btn-left.dropdown-toggle:before, .show > .btn-info.btn-left.dropdown-toggle:before {
  border-right-color: #0099b8;
}

.btn-info:hover, .btn-info.hover, .btn-info:focus, .btn-info.focus {
  color: #fff;
  background-color: #28c0de;
  border-color: #28c0de;
  box-shadow: none;
}

.btn-info:not([disabled]):not(.disabled):active, .btn-info:not([disabled]):not(.disabled).active, .open > .btn-info.dropdown-toggle, .show > .btn-info.dropdown-toggle {
  background-color: #0099b8;
  border-color: #0099b8;
  box-shadow: none;
}

.btn-info:not([disabled]):not(.disabled):active:hover, .btn-info:not([disabled]):not(.disabled):active.hover, .btn-info:not([disabled]):not(.disabled):active:focus, .btn-info:not([disabled]):not(.disabled):active.focus, .btn-info:not([disabled]):not(.disabled).active:hover, .btn-info:not([disabled]):not(.disabled).active.hover, .btn-info:not([disabled]):not(.disabled).active:focus, .btn-info:not([disabled]):not(.disabled).active.focus, .open > .btn-info.dropdown-toggle:hover, .open > .btn-info.dropdown-toggle.hover, .open > .btn-info.dropdown-toggle:focus, .open > .btn-info.dropdown-toggle.focus, .show > .btn-info.dropdown-toggle:hover, .show > .btn-info.dropdown-toggle.hover, .show > .btn-info.dropdown-toggle:focus, .show > .btn-info.dropdown-toggle.focus {
  background-color: #0099b8;
  border-color: #0099b8;
}

.btn-info.disabled, .btn-info.disabled:hover, .btn-info.disabled.hover, .btn-info.disabled:focus, .btn-info.disabled.focus, .btn-info.disabled:active, .btn-info.disabled.active, .btn-info[disabled], .btn-info[disabled]:hover, .btn-info[disabled].hover, .btn-info[disabled]:focus, .btn-info[disabled].focus, .btn-info[disabled]:active, .btn-info[disabled].active, fieldset[disabled] .btn-info, fieldset[disabled] .btn-info:hover, fieldset[disabled] .btn-info.hover, fieldset[disabled] .btn-info:focus, fieldset[disabled] .btn-info.focus, fieldset[disabled] .btn-info:active, fieldset[disabled] .btn-info.active {
  color: #fff;
  background-color: #54cbe3;
  border-color: #54cbe3;
}

.btn-info .badge-pill {
  color: #0bb2d4;
  background-color: #fff;
}

.btn-info.btn-flat {
  color: #0bb2d4;
}

.btn-info.btn-flat:hover, .btn-info.btn-flat.hover, .btn-info.btn-flat:focus, .btn-info.btn-flat.focus {
  color: #fff;
}

.btn-warning {
  color: color-yiq(#eb6709);
  background-color: #eb6709;
  border-color: #eb6709;
  box-shadow: none;
}

.btn-warning.btn-up:before {
  border-bottom-color: #eb6709;
}

.btn-warning.btn-up:hover:before, .btn-warning.btn-up.hover:before, .btn-warning.btn-up:focus:before, .btn-warning.btn-up.focus:before {
  border-bottom-color: #f57d1b;
}

.btn-warning.btn-up.active:before, .btn-warning.btn-up:active:before, .open > .btn-warning.btn-up.dropdown-toggle:before, .show > .btn-warning.btn-up.dropdown-toggle:before {
  border-bottom-color: #de4e00;
}

.btn-warning.btn-right:before {
  border-left-color: #eb6709;
}

.btn-warning.btn-right:hover:before, .btn-warning.btn-right.hover:before, .btn-warning.btn-right:focus:before, .btn-warning.btn-right.focus:before {
  border-left-color: #f57d1b;
}

.btn-warning.btn-right.active:before, .btn-warning.btn-right:active:before, .open > .btn-warning.btn-right.dropdown-toggle:before, .show > .btn-warning.btn-right.dropdown-toggle:before {
  border-left-color: #de4e00;
}

.btn-warning.btn-bottom:before {
  border-top-color: #eb6709;
}

.btn-warning.btn-bottom:hover:before, .btn-warning.btn-bottom.hover:before, .btn-warning.btn-bottom:focus:before, .btn-warning.btn-bottom.focus:before {
  border-top-color: #f57d1b;
}

.btn-warning.btn-bottom.active:before, .btn-warning.btn-bottom:active:before, .open > .btn-warning.btn-bottom.dropdown-toggle:before, .show > .btn-warning.btn-bottom.dropdown-toggle:before {
  border-top-color: #de4e00;
}

.btn-warning.btn-left:before {
  border-right-color: #eb6709;
}

.btn-warning.btn-left:hover:before, .btn-warning.btn-left.hover:before, .btn-warning.btn-left:focus:before, .btn-warning.btn-left.focus:before {
  border-right-color: #f57d1b;
}

.btn-warning.btn-left.active:before, .btn-warning.btn-left:active:before, .open > .btn-warning.btn-left.dropdown-toggle:before, .show > .btn-warning.btn-left.dropdown-toggle:before {
  border-right-color: #de4e00;
}

.btn-warning:hover, .btn-warning.hover, .btn-warning:focus, .btn-warning.focus {
  color: #fff;
  background-color: #f57d1b;
  border-color: #f57d1b;
  box-shadow: none;
}

.btn-warning:not([disabled]):not(.disabled):active, .btn-warning:not([disabled]):not(.disabled).active, .open > .btn-warning.dropdown-toggle, .show > .btn-warning.dropdown-toggle {
  background-color: #de4e00;
  border-color: #de4e00;
  box-shadow: none;
}

.btn-warning:not([disabled]):not(.disabled):active:hover, .btn-warning:not([disabled]):not(.disabled):active.hover, .btn-warning:not([disabled]):not(.disabled):active:focus, .btn-warning:not([disabled]):not(.disabled):active.focus, .btn-warning:not([disabled]):not(.disabled).active:hover, .btn-warning:not([disabled]):not(.disabled).active.hover, .btn-warning:not([disabled]):not(.disabled).active:focus, .btn-warning:not([disabled]):not(.disabled).active.focus, .open > .btn-warning.dropdown-toggle:hover, .open > .btn-warning.dropdown-toggle.hover, .open > .btn-warning.dropdown-toggle:focus, .open > .btn-warning.dropdown-toggle.focus, .show > .btn-warning.dropdown-toggle:hover, .show > .btn-warning.dropdown-toggle.hover, .show > .btn-warning.dropdown-toggle:focus, .show > .btn-warning.dropdown-toggle.focus {
  background-color: #de4e00;
  border-color: #de4e00;
}

.btn-warning.disabled, .btn-warning.disabled:hover, .btn-warning.disabled.hover, .btn-warning.disabled:focus, .btn-warning.disabled.focus, .btn-warning.disabled:active, .btn-warning.disabled.active, .btn-warning[disabled], .btn-warning[disabled]:hover, .btn-warning[disabled].hover, .btn-warning[disabled]:focus, .btn-warning[disabled].focus, .btn-warning[disabled]:active, .btn-warning[disabled].active, fieldset[disabled] .btn-warning, fieldset[disabled] .btn-warning:hover, fieldset[disabled] .btn-warning.hover, fieldset[disabled] .btn-warning:focus, fieldset[disabled] .btn-warning.focus, fieldset[disabled] .btn-warning:active, fieldset[disabled] .btn-warning.active {
  color: #fff;
  background-color: #fa983c;
  border-color: #fa983c;
}

.btn-warning .badge-pill {
  color: #eb6709;
  background-color: #fff;
}

.btn-warning.btn-flat {
  color: #eb6709;
}

.btn-warning.btn-flat:hover, .btn-warning.btn-flat.hover, .btn-warning.btn-flat:focus, .btn-warning.btn-flat.focus {
  color: #fff;
}

.btn-danger {
  color: color-yiq(#ff4c52);
  background-color: #ff4c52;
  border-color: #ff4c52;
  box-shadow: none;
}

.btn-danger.btn-up:before {
  border-bottom-color: #ff4c52;
}

.btn-danger.btn-up:hover:before, .btn-danger.btn-up.hover:before, .btn-danger.btn-up:focus:before, .btn-danger.btn-up.focus:before {
  border-bottom-color: #ff666b;
}

.btn-danger.btn-up.active:before, .btn-danger.btn-up:active:before, .open > .btn-danger.btn-up.dropdown-toggle:before, .show > .btn-danger.btn-up.dropdown-toggle:before {
  border-bottom-color: #f2353c;
}

.btn-danger.btn-right:before {
  border-left-color: #ff4c52;
}

.btn-danger.btn-right:hover:before, .btn-danger.btn-right.hover:before, .btn-danger.btn-right:focus:before, .btn-danger.btn-right.focus:before {
  border-left-color: #ff666b;
}

.btn-danger.btn-right.active:before, .btn-danger.btn-right:active:before, .open > .btn-danger.btn-right.dropdown-toggle:before, .show > .btn-danger.btn-right.dropdown-toggle:before {
  border-left-color: #f2353c;
}

.btn-danger.btn-bottom:before {
  border-top-color: #ff4c52;
}

.btn-danger.btn-bottom:hover:before, .btn-danger.btn-bottom.hover:before, .btn-danger.btn-bottom:focus:before, .btn-danger.btn-bottom.focus:before {
  border-top-color: #ff666b;
}

.btn-danger.btn-bottom.active:before, .btn-danger.btn-bottom:active:before, .open > .btn-danger.btn-bottom.dropdown-toggle:before, .show > .btn-danger.btn-bottom.dropdown-toggle:before {
  border-top-color: #f2353c;
}

.btn-danger.btn-left:before {
  border-right-color: #ff4c52;
}

.btn-danger.btn-left:hover:before, .btn-danger.btn-left.hover:before, .btn-danger.btn-left:focus:before, .btn-danger.btn-left.focus:before {
  border-right-color: #ff666b;
}

.btn-danger.btn-left.active:before, .btn-danger.btn-left:active:before, .open > .btn-danger.btn-left.dropdown-toggle:before, .show > .btn-danger.btn-left.dropdown-toggle:before {
  border-right-color: #f2353c;
}

.btn-danger:hover, .btn-danger.hover, .btn-danger:focus, .btn-danger.focus {
  color: #fff;
  background-color: #ff666b;
  border-color: #ff666b;
  box-shadow: none;
}

.btn-danger:not([disabled]):not(.disabled):active, .btn-danger:not([disabled]):not(.disabled).active, .open > .btn-danger.dropdown-toggle, .show > .btn-danger.dropdown-toggle {
  background-color: #f2353c;
  border-color: #f2353c;
  box-shadow: none;
}

.btn-danger:not([disabled]):not(.disabled):active:hover, .btn-danger:not([disabled]):not(.disabled):active.hover, .btn-danger:not([disabled]):not(.disabled):active:focus, .btn-danger:not([disabled]):not(.disabled):active.focus, .btn-danger:not([disabled]):not(.disabled).active:hover, .btn-danger:not([disabled]):not(.disabled).active.hover, .btn-danger:not([disabled]):not(.disabled).active:focus, .btn-danger:not([disabled]):not(.disabled).active.focus, .open > .btn-danger.dropdown-toggle:hover, .open > .btn-danger.dropdown-toggle.hover, .open > .btn-danger.dropdown-toggle:focus, .open > .btn-danger.dropdown-toggle.focus, .show > .btn-danger.dropdown-toggle:hover, .show > .btn-danger.dropdown-toggle.hover, .show > .btn-danger.dropdown-toggle:focus, .show > .btn-danger.dropdown-toggle.focus {
  background-color: #f2353c;
  border-color: #f2353c;
}

.btn-danger.disabled, .btn-danger.disabled:hover, .btn-danger.disabled.hover, .btn-danger.disabled:focus, .btn-danger.disabled.focus, .btn-danger.disabled:active, .btn-danger.disabled.active, .btn-danger[disabled], .btn-danger[disabled]:hover, .btn-danger[disabled].hover, .btn-danger[disabled]:focus, .btn-danger[disabled].focus, .btn-danger[disabled]:active, .btn-danger[disabled].active, fieldset[disabled] .btn-danger, fieldset[disabled] .btn-danger:hover, fieldset[disabled] .btn-danger.hover, fieldset[disabled] .btn-danger:focus, fieldset[disabled] .btn-danger.focus, fieldset[disabled] .btn-danger:active, fieldset[disabled] .btn-danger.active {
  color: #fff;
  background-color: #ff8589;
  border-color: #ff8589;
}

.btn-danger .badge-pill {
  color: #ff4c52;
  background-color: #fff;
}

.btn-danger.btn-flat {
  color: #ff4c52;
}

.btn-danger.btn-flat:hover, .btn-danger.btn-flat.hover, .btn-danger.btn-flat:focus, .btn-danger.btn-flat.focus {
  color: #fff;
}

.btn-default {
  color: color-yiq(#e4eaec);
  background-color: #e4eaec;
  border-color: #e4eaec;
  box-shadow: none;
}

.btn-default.btn-up:before {
  border-bottom-color: #e4eaec;
}

.btn-default.btn-up:hover:before, .btn-default.btn-up.hover:before, .btn-default.btn-up:focus:before, .btn-default.btn-up.focus:before {
  border-bottom-color: #f3f7f9;
}

.btn-default.btn-up.active:before, .btn-default.btn-up:active:before, .open > .btn-default.btn-up.dropdown-toggle:before, .show > .btn-default.btn-up.dropdown-toggle:before {
  border-bottom-color: #ccd5db;
}

.btn-default.btn-right:before {
  border-left-color: #e4eaec;
}

.btn-default.btn-right:hover:before, .btn-default.btn-right.hover:before, .btn-default.btn-right:focus:before, .btn-default.btn-right.focus:before {
  border-left-color: #f3f7f9;
}

.btn-default.btn-right.active:before, .btn-default.btn-right:active:before, .open > .btn-default.btn-right.dropdown-toggle:before, .show > .btn-default.btn-right.dropdown-toggle:before {
  border-left-color: #ccd5db;
}

.btn-default.btn-bottom:before {
  border-top-color: #e4eaec;
}

.btn-default.btn-bottom:hover:before, .btn-default.btn-bottom.hover:before, .btn-default.btn-bottom:focus:before, .btn-default.btn-bottom.focus:before {
  border-top-color: #f3f7f9;
}

.btn-default.btn-bottom.active:before, .btn-default.btn-bottom:active:before, .open > .btn-default.btn-bottom.dropdown-toggle:before, .show > .btn-default.btn-bottom.dropdown-toggle:before {
  border-top-color: #ccd5db;
}

.btn-default.btn-left:before {
  border-right-color: #e4eaec;
}

.btn-default.btn-left:hover:before, .btn-default.btn-left.hover:before, .btn-default.btn-left:focus:before, .btn-default.btn-left.focus:before {
  border-right-color: #f3f7f9;
}

.btn-default.btn-left.active:before, .btn-default.btn-left:active:before, .open > .btn-default.btn-left.dropdown-toggle:before, .show > .btn-default.btn-left.dropdown-toggle:before {
  border-right-color: #ccd5db;
}

.btn-default:hover, .btn-default.hover, .btn-default:focus, .btn-default.focus {
  color: #76838f;
  background-color: #f3f7f9;
  border-color: #f3f7f9;
  box-shadow: none;
}

.btn-default:not([disabled]):not(.disabled):active, .btn-default:not([disabled]):not(.disabled).active, .open > .btn-default.dropdown-toggle, .show > .btn-default.dropdown-toggle {
  background-color: #ccd5db;
  border-color: #ccd5db;
  box-shadow: none;
}

.btn-default:not([disabled]):not(.disabled):active:hover, .btn-default:not([disabled]):not(.disabled):active.hover, .btn-default:not([disabled]):not(.disabled):active:focus, .btn-default:not([disabled]):not(.disabled):active.focus, .btn-default:not([disabled]):not(.disabled).active:hover, .btn-default:not([disabled]):not(.disabled).active.hover, .btn-default:not([disabled]):not(.disabled).active:focus, .btn-default:not([disabled]):not(.disabled).active.focus, .open > .btn-default.dropdown-toggle:hover, .open > .btn-default.dropdown-toggle.hover, .open > .btn-default.dropdown-toggle:focus, .open > .btn-default.dropdown-toggle.focus, .show > .btn-default.dropdown-toggle:hover, .show > .btn-default.dropdown-toggle.hover, .show > .btn-default.dropdown-toggle:focus, .show > .btn-default.dropdown-toggle.focus {
  background-color: #ccd5db;
  border-color: #ccd5db;
}

.btn-default.disabled, .btn-default.disabled:hover, .btn-default.disabled.hover, .btn-default.disabled:focus, .btn-default.disabled.focus, .btn-default.disabled:active, .btn-default.disabled.active, .btn-default[disabled], .btn-default[disabled]:hover, .btn-default[disabled].hover, .btn-default[disabled]:focus, .btn-default[disabled].focus, .btn-default[disabled]:active, .btn-default[disabled].active, fieldset[disabled] .btn-default, fieldset[disabled] .btn-default:hover, fieldset[disabled] .btn-default.hover, fieldset[disabled] .btn-default:focus, fieldset[disabled] .btn-default.focus, fieldset[disabled] .btn-default:active, fieldset[disabled] .btn-default.active {
  color: #76838f;
  background-color: #f3f7f9;
  border-color: #f3f7f9;
}

.btn-default .badge-pill {
  color: #e4eaec;
  background-color: #76838f;
}

.btn-default.btn-flat {
  color: #e4eaec;
}

.btn-default.btn-flat:hover, .btn-default.btn-flat.hover, .btn-default.btn-flat:focus, .btn-default.btn-flat.focus {
  color: #76838f;
}

.btn-inverse {
  color: color-yiq(#fff);
  background-color: #fff;
  border-color: #e4eaec;
  box-shadow: none;
}

.btn-inverse.btn-up:before {
  border-bottom-color: #fff;
}

.btn-inverse.btn-up:hover:before, .btn-inverse.btn-up.hover:before, .btn-inverse.btn-up:focus:before, .btn-inverse.btn-up.focus:before {
  border-bottom-color: #fff;
}

.btn-inverse.btn-up.active:before, .btn-inverse.btn-up:active:before, .open > .btn-inverse.btn-up.dropdown-toggle:before, .show > .btn-inverse.btn-up.dropdown-toggle:before {
  border-bottom-color: #fff;
}

.btn-inverse.btn-right:before {
  border-left-color: #fff;
}

.btn-inverse.btn-right:hover:before, .btn-inverse.btn-right.hover:before, .btn-inverse.btn-right:focus:before, .btn-inverse.btn-right.focus:before {
  border-left-color: #fff;
}

.btn-inverse.btn-right.active:before, .btn-inverse.btn-right:active:before, .open > .btn-inverse.btn-right.dropdown-toggle:before, .show > .btn-inverse.btn-right.dropdown-toggle:before {
  border-left-color: #fff;
}

.btn-inverse.btn-bottom:before {
  border-top-color: #fff;
}

.btn-inverse.btn-bottom:hover:before, .btn-inverse.btn-bottom.hover:before, .btn-inverse.btn-bottom:focus:before, .btn-inverse.btn-bottom.focus:before {
  border-top-color: #fff;
}

.btn-inverse.btn-bottom.active:before, .btn-inverse.btn-bottom:active:before, .open > .btn-inverse.btn-bottom.dropdown-toggle:before, .show > .btn-inverse.btn-bottom.dropdown-toggle:before {
  border-top-color: #fff;
}

.btn-inverse.btn-left:before {
  border-right-color: #fff;
}

.btn-inverse.btn-left:hover:before, .btn-inverse.btn-left.hover:before, .btn-inverse.btn-left:focus:before, .btn-inverse.btn-left.focus:before {
  border-right-color: #fff;
}

.btn-inverse.btn-left.active:before, .btn-inverse.btn-left:active:before, .open > .btn-inverse.btn-left.dropdown-toggle:before, .show > .btn-inverse.btn-left.dropdown-toggle:before {
  border-right-color: #fff;
}

.btn-inverse:hover, .btn-inverse.hover, .btn-inverse:focus, .btn-inverse.focus {
  color: #76838f;
  background-color: #fff;
  border-color: #f3f7f9;
  box-shadow: none;
}

.btn-inverse:not([disabled]):not(.disabled):active, .btn-inverse:not([disabled]):not(.disabled).active, .open > .btn-inverse.dropdown-toggle, .show > .btn-inverse.dropdown-toggle {
  background-color: #fff;
  border-color: #ccd5db;
  box-shadow: none;
}

.btn-inverse:not([disabled]):not(.disabled):active:hover, .btn-inverse:not([disabled]):not(.disabled):active.hover, .btn-inverse:not([disabled]):not(.disabled):active:focus, .btn-inverse:not([disabled]):not(.disabled):active.focus, .btn-inverse:not([disabled]):not(.disabled).active:hover, .btn-inverse:not([disabled]):not(.disabled).active.hover, .btn-inverse:not([disabled]):not(.disabled).active:focus, .btn-inverse:not([disabled]):not(.disabled).active.focus, .open > .btn-inverse.dropdown-toggle:hover, .open > .btn-inverse.dropdown-toggle.hover, .open > .btn-inverse.dropdown-toggle:focus, .open > .btn-inverse.dropdown-toggle.focus, .show > .btn-inverse.dropdown-toggle:hover, .show > .btn-inverse.dropdown-toggle.hover, .show > .btn-inverse.dropdown-toggle:focus, .show > .btn-inverse.dropdown-toggle.focus {
  background-color: #fff;
  border-color: #ccd5db;
}

.btn-inverse.disabled, .btn-inverse.disabled:hover, .btn-inverse.disabled.hover, .btn-inverse.disabled:focus, .btn-inverse.disabled.focus, .btn-inverse.disabled:active, .btn-inverse.disabled.active, .btn-inverse[disabled], .btn-inverse[disabled]:hover, .btn-inverse[disabled].hover, .btn-inverse[disabled]:focus, .btn-inverse[disabled].focus, .btn-inverse[disabled]:active, .btn-inverse[disabled].active, fieldset[disabled] .btn-inverse, fieldset[disabled] .btn-inverse:hover, fieldset[disabled] .btn-inverse.hover, fieldset[disabled] .btn-inverse:focus, fieldset[disabled] .btn-inverse.focus, fieldset[disabled] .btn-inverse:active, fieldset[disabled] .btn-inverse.active {
  color: #ccd5db;
  background-color: #fff;
  border-color: #a3afb7;
}

.btn-inverse .badge-pill {
  color: #fff;
  background-color: #76838f;
}

.btn-inverse.btn-flat {
  color: #fff;
}

.btn-inverse.btn-flat:hover, .btn-inverse.btn-flat.hover, .btn-inverse.btn-flat:focus, .btn-inverse.btn-flat.focus {
  color: #76838f;
}

.btn-dark {
  color: color-yiq(#526069);
  background-color: #526069;
  border-color: #526069;
  box-shadow: none;
}

.btn-dark.btn-up:before {
  border-bottom-color: #526069;
}

.btn-dark.btn-up:hover:before, .btn-dark.btn-up.hover:before, .btn-dark.btn-up:focus:before, .btn-dark.btn-up.focus:before {
  border-bottom-color: #76838f;
}

.btn-dark.btn-up.active:before, .btn-dark.btn-up:active:before, .open > .btn-dark.btn-up.dropdown-toggle:before, .show > .btn-dark.btn-up.dropdown-toggle:before {
  border-bottom-color: #37474f;
}

.btn-dark.btn-right:before {
  border-left-color: #526069;
}

.btn-dark.btn-right:hover:before, .btn-dark.btn-right.hover:before, .btn-dark.btn-right:focus:before, .btn-dark.btn-right.focus:before {
  border-left-color: #76838f;
}

.btn-dark.btn-right.active:before, .btn-dark.btn-right:active:before, .open > .btn-dark.btn-right.dropdown-toggle:before, .show > .btn-dark.btn-right.dropdown-toggle:before {
  border-left-color: #37474f;
}

.btn-dark.btn-bottom:before {
  border-top-color: #526069;
}

.btn-dark.btn-bottom:hover:before, .btn-dark.btn-bottom.hover:before, .btn-dark.btn-bottom:focus:before, .btn-dark.btn-bottom.focus:before {
  border-top-color: #76838f;
}

.btn-dark.btn-bottom.active:before, .btn-dark.btn-bottom:active:before, .open > .btn-dark.btn-bottom.dropdown-toggle:before, .show > .btn-dark.btn-bottom.dropdown-toggle:before {
  border-top-color: #37474f;
}

.btn-dark.btn-left:before {
  border-right-color: #526069;
}

.btn-dark.btn-left:hover:before, .btn-dark.btn-left.hover:before, .btn-dark.btn-left:focus:before, .btn-dark.btn-left.focus:before {
  border-right-color: #76838f;
}

.btn-dark.btn-left.active:before, .btn-dark.btn-left:active:before, .open > .btn-dark.btn-left.dropdown-toggle:before, .show > .btn-dark.btn-left.dropdown-toggle:before {
  border-right-color: #37474f;
}

.btn-dark:hover, .btn-dark.hover, .btn-dark:focus, .btn-dark.focus {
  color: #fff;
  background-color: #76838f;
  border-color: #76838f;
  box-shadow: none;
}

.btn-dark:not([disabled]):not(.disabled):active, .btn-dark:not([disabled]):not(.disabled).active, .open > .btn-dark.dropdown-toggle, .show > .btn-dark.dropdown-toggle {
  background-color: #37474f;
  border-color: #37474f;
  box-shadow: none;
}

.btn-dark:not([disabled]):not(.disabled):active:hover, .btn-dark:not([disabled]):not(.disabled):active.hover, .btn-dark:not([disabled]):not(.disabled):active:focus, .btn-dark:not([disabled]):not(.disabled):active.focus, .btn-dark:not([disabled]):not(.disabled).active:hover, .btn-dark:not([disabled]):not(.disabled).active.hover, .btn-dark:not([disabled]):not(.disabled).active:focus, .btn-dark:not([disabled]):not(.disabled).active.focus, .open > .btn-dark.dropdown-toggle:hover, .open > .btn-dark.dropdown-toggle.hover, .open > .btn-dark.dropdown-toggle:focus, .open > .btn-dark.dropdown-toggle.focus, .show > .btn-dark.dropdown-toggle:hover, .show > .btn-dark.dropdown-toggle.hover, .show > .btn-dark.dropdown-toggle:focus, .show > .btn-dark.dropdown-toggle.focus {
  background-color: #37474f;
  border-color: #37474f;
}

.btn-dark.disabled, .btn-dark.disabled:hover, .btn-dark.disabled.hover, .btn-dark.disabled:focus, .btn-dark.disabled.focus, .btn-dark.disabled:active, .btn-dark.disabled.active, .btn-dark[disabled], .btn-dark[disabled]:hover, .btn-dark[disabled].hover, .btn-dark[disabled]:focus, .btn-dark[disabled].focus, .btn-dark[disabled]:active, .btn-dark[disabled].active, fieldset[disabled] .btn-dark, fieldset[disabled] .btn-dark:hover, fieldset[disabled] .btn-dark.hover, fieldset[disabled] .btn-dark:focus, fieldset[disabled] .btn-dark.focus, fieldset[disabled] .btn-dark:active, fieldset[disabled] .btn-dark.active {
  color: #fff;
  background-color: #a3afb7;
  border-color: #a3afb7;
}

.btn-dark .badge-pill {
  color: #526069;
  background-color: #fff;
}

.btn-dark.btn-flat {
  color: #526069;
}

.btn-dark.btn-flat:hover, .btn-dark.btn-flat.hover, .btn-dark.btn-flat:focus, .btn-dark.btn-flat.focus {
  color: #fff;
}

.btn-flat {
  background: none;
  border: none;
  box-shadow: none;
}

.btn-flat.disabled {
  color: #a3afb7;
}

.btn-icon.disabled, .btn.icon.disabled {
  color: #a3afb7;
}

.btn-raised {
  box-shadow: 0 0 2px rgba(0, 0, 0, .18), 0 2px 4px rgba(0, 0, 0, .21);
  transition: box-shadow .25s cubic-bezier(.4, 0, .2, 1);
}

.btn-raised:hover, .btn-raised.hover, .btn-raised:active, .btn-raised.active, .open > .btn-raised.dropdown-toggle, .show > .btn-raised.dropdown-toggle {
  box-shadow: 0 0 3px rgba(0, 0, 0, .15), 0 3px 6px rgba(0, 0, 0, .2);
}

.btn-raised.disabled, .btn-raised[disabled], fieldset[disabled] .btn-raised {
  box-shadow: none;
}

.btn-floating {
  box-shadow: 0 6px 10px rgba(0, 0, 0, .15);
}

.btn-tag {
  background-color: rgba(0, 0, 0, .15);
}

.btn-direction:before {
  border: 8px solid transparent;
}

.btn-up:before {
  border-bottom-color: #e4eaec;
}

.btn-right:before {
  border-left-color: #e4eaec;
}

.btn-bottom:before {
  border-top-color: #e4eaec;
}

.btn-left:before {
  border-right-color: #e4eaec;
}

.btn-pure, .btn-pure:hover, .btn-pure:focus, .btn-pure:active, .btn-pure.active, .open > .btn-pure.dropdown-toggle, .btn-pure[disabled], fieldset[disabled] .btn-pure {
  background-color: transparent;
  border-color: transparent;
  box-shadow: none;
}

.btn-pure:hover, .btn-pure:focus, .btn-pure.focus, .btn-pure:hover:hover, .btn-pure:hover:focus, .btn-pure:hover.focus, .btn-pure:focus:hover, .btn-pure:focus:focus, .btn-pure:focus.focus, .btn-pure:active:hover, .btn-pure:active:focus, .btn-pure:active.focus, .btn-pure.active:hover, .btn-pure.active:focus, .btn-pure.active.focus, .open > .btn-pure.dropdown-toggle:hover, .open > .btn-pure.dropdown-toggle:focus, .open > .btn-pure.dropdown-toggle.focus, .btn-pure[disabled]:hover, .btn-pure[disabled]:focus, .btn-pure[disabled].focus, fieldset[disabled] .btn-pure:hover, fieldset[disabled] .btn-pure:focus, fieldset[disabled] .btn-pure.focus {
  background-color: transparent;
  border-color: transparent;
  box-shadow: none;
}

.btn-pure.btn-default {
  color: #a3afb7;
}

.btn-pure.btn-default:hover, .btn-pure.btn-default.hover, .btn-pure.btn-default:focus, .btn-pure.btn-default.focus, .btn-pure.btn-default:active, .btn-pure.btn-default.active, .open > .btn-pure.btn-default.dropdown-toggle, .show > .btn-pure.btn-default.dropdown-toggle {
  color: #ccd5db;
}

.btn-pure.btn-default:hover:hover, .btn-pure.btn-default:hover.hover, .btn-pure.btn-default:hover:focus, .btn-pure.btn-default:hover.focus, .btn-pure.btn-default.hover:hover, .btn-pure.btn-default.hover.hover, .btn-pure.btn-default.hover:focus, .btn-pure.btn-default.hover.focus, .btn-pure.btn-default:focus:hover, .btn-pure.btn-default:focus.hover, .btn-pure.btn-default:focus:focus, .btn-pure.btn-default:focus.focus, .btn-pure.btn-default.focus:hover, .btn-pure.btn-default.focus.hover, .btn-pure.btn-default.focus:focus, .btn-pure.btn-default.focus.focus, .btn-pure.btn-default:active:hover, .btn-pure.btn-default:active.hover, .btn-pure.btn-default:active:focus, .btn-pure.btn-default:active.focus, .btn-pure.btn-default.active:hover, .btn-pure.btn-default.active.hover, .btn-pure.btn-default.active:focus, .btn-pure.btn-default.active.focus, .open > .btn-pure.btn-default.dropdown-toggle:hover, .open > .btn-pure.btn-default.dropdown-toggle.hover, .open > .btn-pure.btn-default.dropdown-toggle:focus, .open > .btn-pure.btn-default.dropdown-toggle.focus, .show > .btn-pure.btn-default.dropdown-toggle:hover, .show > .btn-pure.btn-default.dropdown-toggle.hover, .show > .btn-pure.btn-default.dropdown-toggle:focus, .show > .btn-pure.btn-default.dropdown-toggle.focus {
  color: #ccd5db;
}

.btn-pure.btn-default:hover .badge-pill, .btn-pure.btn-default.hover .badge-pill, .btn-pure.btn-default:focus .badge-pill, .btn-pure.btn-default.focus .badge-pill, .btn-pure.btn-default:active .badge-pill, .btn-pure.btn-default.active .badge-pill, .open > .btn-pure.btn-default.dropdown-toggle .badge-pill, .show > .btn-pure.btn-default.dropdown-toggle .badge-pill {
  color: #ccd5db;
}

.btn-pure.btn-primary {
  color: #997b71;
}

.btn-pure.btn-primary:hover, .btn-pure.btn-primary.hover, .btn-pure.btn-primary:focus, .btn-pure.btn-primary.focus, .btn-pure.btn-primary:active, .btn-pure.btn-primary.active, .open > .btn-pure.btn-primary.dropdown-toggle, .show > .btn-pure.btn-primary.dropdown-toggle {
  color: #bda299;
}

.btn-pure.btn-primary:hover:hover, .btn-pure.btn-primary:hover.hover, .btn-pure.btn-primary:hover:focus, .btn-pure.btn-primary:hover.focus, .btn-pure.btn-primary.hover:hover, .btn-pure.btn-primary.hover.hover, .btn-pure.btn-primary.hover:focus, .btn-pure.btn-primary.hover.focus, .btn-pure.btn-primary:focus:hover, .btn-pure.btn-primary:focus.hover, .btn-pure.btn-primary:focus:focus, .btn-pure.btn-primary:focus.focus, .btn-pure.btn-primary.focus:hover, .btn-pure.btn-primary.focus.hover, .btn-pure.btn-primary.focus:focus, .btn-pure.btn-primary.focus.focus, .btn-pure.btn-primary:active:hover, .btn-pure.btn-primary:active.hover, .btn-pure.btn-primary:active:focus, .btn-pure.btn-primary:active.focus, .btn-pure.btn-primary.active:hover, .btn-pure.btn-primary.active.hover, .btn-pure.btn-primary.active:focus, .btn-pure.btn-primary.active.focus, .open > .btn-pure.btn-primary.dropdown-toggle:hover, .open > .btn-pure.btn-primary.dropdown-toggle.hover, .open > .btn-pure.btn-primary.dropdown-toggle:focus, .open > .btn-pure.btn-primary.dropdown-toggle.focus, .show > .btn-pure.btn-primary.dropdown-toggle:hover, .show > .btn-pure.btn-primary.dropdown-toggle.hover, .show > .btn-pure.btn-primary.dropdown-toggle:focus, .show > .btn-pure.btn-primary.dropdown-toggle.focus {
  color: #bda299;
}

.btn-pure.btn-primary:hover .badge-pill, .btn-pure.btn-primary.hover .badge-pill, .btn-pure.btn-primary:focus .badge-pill, .btn-pure.btn-primary.focus .badge-pill, .btn-pure.btn-primary:active .badge-pill, .btn-pure.btn-primary.active .badge-pill, .open > .btn-pure.btn-primary.dropdown-toggle .badge-pill, .show > .btn-pure.btn-primary.dropdown-toggle .badge-pill {
  color: #bda299;
}

.btn-pure.btn-success {
  color: #11c26d;
}

.btn-pure.btn-success:hover, .btn-pure.btn-success.hover, .btn-pure.btn-success:focus, .btn-pure.btn-success.focus, .btn-pure.btn-success:active, .btn-pure.btn-success.active, .open > .btn-pure.btn-success.dropdown-toggle, .show > .btn-pure.btn-success.dropdown-toggle {
  color: #49de94;
}

.btn-pure.btn-success:hover:hover, .btn-pure.btn-success:hover.hover, .btn-pure.btn-success:hover:focus, .btn-pure.btn-success:hover.focus, .btn-pure.btn-success.hover:hover, .btn-pure.btn-success.hover.hover, .btn-pure.btn-success.hover:focus, .btn-pure.btn-success.hover.focus, .btn-pure.btn-success:focus:hover, .btn-pure.btn-success:focus.hover, .btn-pure.btn-success:focus:focus, .btn-pure.btn-success:focus.focus, .btn-pure.btn-success.focus:hover, .btn-pure.btn-success.focus.hover, .btn-pure.btn-success.focus:focus, .btn-pure.btn-success.focus.focus, .btn-pure.btn-success:active:hover, .btn-pure.btn-success:active.hover, .btn-pure.btn-success:active:focus, .btn-pure.btn-success:active.focus, .btn-pure.btn-success.active:hover, .btn-pure.btn-success.active.hover, .btn-pure.btn-success.active:focus, .btn-pure.btn-success.active.focus, .open > .btn-pure.btn-success.dropdown-toggle:hover, .open > .btn-pure.btn-success.dropdown-toggle.hover, .open > .btn-pure.btn-success.dropdown-toggle:focus, .open > .btn-pure.btn-success.dropdown-toggle.focus, .show > .btn-pure.btn-success.dropdown-toggle:hover, .show > .btn-pure.btn-success.dropdown-toggle.hover, .show > .btn-pure.btn-success.dropdown-toggle:focus, .show > .btn-pure.btn-success.dropdown-toggle.focus {
  color: #49de94;
}

.btn-pure.btn-success:hover .badge-pill, .btn-pure.btn-success.hover .badge-pill, .btn-pure.btn-success:focus .badge-pill, .btn-pure.btn-success.focus .badge-pill, .btn-pure.btn-success:active .badge-pill, .btn-pure.btn-success.active .badge-pill, .open > .btn-pure.btn-success.dropdown-toggle .badge-pill, .show > .btn-pure.btn-success.dropdown-toggle .badge-pill {
  color: #49de94;
}

.btn-pure.btn-info {
  color: #0bb2d4;
}

.btn-pure.btn-info:hover, .btn-pure.btn-info.hover, .btn-pure.btn-info:focus, .btn-pure.btn-info.focus, .btn-pure.btn-info:active, .btn-pure.btn-info.active, .open > .btn-pure.btn-info.dropdown-toggle, .show > .btn-pure.btn-info.dropdown-toggle {
  color: #54cbe3;
}

.btn-pure.btn-info:hover:hover, .btn-pure.btn-info:hover.hover, .btn-pure.btn-info:hover:focus, .btn-pure.btn-info:hover.focus, .btn-pure.btn-info.hover:hover, .btn-pure.btn-info.hover.hover, .btn-pure.btn-info.hover:focus, .btn-pure.btn-info.hover.focus, .btn-pure.btn-info:focus:hover, .btn-pure.btn-info:focus.hover, .btn-pure.btn-info:focus:focus, .btn-pure.btn-info:focus.focus, .btn-pure.btn-info.focus:hover, .btn-pure.btn-info.focus.hover, .btn-pure.btn-info.focus:focus, .btn-pure.btn-info.focus.focus, .btn-pure.btn-info:active:hover, .btn-pure.btn-info:active.hover, .btn-pure.btn-info:active:focus, .btn-pure.btn-info:active.focus, .btn-pure.btn-info.active:hover, .btn-pure.btn-info.active.hover, .btn-pure.btn-info.active:focus, .btn-pure.btn-info.active.focus, .open > .btn-pure.btn-info.dropdown-toggle:hover, .open > .btn-pure.btn-info.dropdown-toggle.hover, .open > .btn-pure.btn-info.dropdown-toggle:focus, .open > .btn-pure.btn-info.dropdown-toggle.focus, .show > .btn-pure.btn-info.dropdown-toggle:hover, .show > .btn-pure.btn-info.dropdown-toggle.hover, .show > .btn-pure.btn-info.dropdown-toggle:focus, .show > .btn-pure.btn-info.dropdown-toggle.focus {
  color: #54cbe3;
}

.btn-pure.btn-info:hover .badge-pill, .btn-pure.btn-info.hover .badge-pill, .btn-pure.btn-info:focus .badge-pill, .btn-pure.btn-info.focus .badge-pill, .btn-pure.btn-info:active .badge-pill, .btn-pure.btn-info.active .badge-pill, .open > .btn-pure.btn-info.dropdown-toggle .badge-pill, .show > .btn-pure.btn-info.dropdown-toggle .badge-pill {
  color: #54cbe3;
}

.btn-pure.btn-warning {
  color: #eb6709;
}

.btn-pure.btn-warning:hover, .btn-pure.btn-warning.hover, .btn-pure.btn-warning:focus, .btn-pure.btn-warning.focus, .btn-pure.btn-warning:active, .btn-pure.btn-warning.active, .open > .btn-pure.btn-warning.dropdown-toggle, .show > .btn-pure.btn-warning.dropdown-toggle {
  color: #fa983c;
}

.btn-pure.btn-warning:hover:hover, .btn-pure.btn-warning:hover.hover, .btn-pure.btn-warning:hover:focus, .btn-pure.btn-warning:hover.focus, .btn-pure.btn-warning.hover:hover, .btn-pure.btn-warning.hover.hover, .btn-pure.btn-warning.hover:focus, .btn-pure.btn-warning.hover.focus, .btn-pure.btn-warning:focus:hover, .btn-pure.btn-warning:focus.hover, .btn-pure.btn-warning:focus:focus, .btn-pure.btn-warning:focus.focus, .btn-pure.btn-warning.focus:hover, .btn-pure.btn-warning.focus.hover, .btn-pure.btn-warning.focus:focus, .btn-pure.btn-warning.focus.focus, .btn-pure.btn-warning:active:hover, .btn-pure.btn-warning:active.hover, .btn-pure.btn-warning:active:focus, .btn-pure.btn-warning:active.focus, .btn-pure.btn-warning.active:hover, .btn-pure.btn-warning.active.hover, .btn-pure.btn-warning.active:focus, .btn-pure.btn-warning.active.focus, .open > .btn-pure.btn-warning.dropdown-toggle:hover, .open > .btn-pure.btn-warning.dropdown-toggle.hover, .open > .btn-pure.btn-warning.dropdown-toggle:focus, .open > .btn-pure.btn-warning.dropdown-toggle.focus, .show > .btn-pure.btn-warning.dropdown-toggle:hover, .show > .btn-pure.btn-warning.dropdown-toggle.hover, .show > .btn-pure.btn-warning.dropdown-toggle:focus, .show > .btn-pure.btn-warning.dropdown-toggle.focus {
  color: #fa983c;
}

.btn-pure.btn-warning:hover .badge-pill, .btn-pure.btn-warning.hover .badge-pill, .btn-pure.btn-warning:focus .badge-pill, .btn-pure.btn-warning.focus .badge-pill, .btn-pure.btn-warning:active .badge-pill, .btn-pure.btn-warning.active .badge-pill, .open > .btn-pure.btn-warning.dropdown-toggle .badge-pill, .show > .btn-pure.btn-warning.dropdown-toggle .badge-pill {
  color: #fa983c;
}

.btn-pure.btn-danger {
  color: #ff4c52;
}

.btn-pure.btn-danger:hover, .btn-pure.btn-danger.hover, .btn-pure.btn-danger:focus, .btn-pure.btn-danger.focus, .btn-pure.btn-danger:active, .btn-pure.btn-danger.active, .open > .btn-pure.btn-danger.dropdown-toggle, .show > .btn-pure.btn-danger.dropdown-toggle {
  color: #ff8589;
}

.btn-pure.btn-danger:hover:hover, .btn-pure.btn-danger:hover.hover, .btn-pure.btn-danger:hover:focus, .btn-pure.btn-danger:hover.focus, .btn-pure.btn-danger.hover:hover, .btn-pure.btn-danger.hover.hover, .btn-pure.btn-danger.hover:focus, .btn-pure.btn-danger.hover.focus, .btn-pure.btn-danger:focus:hover, .btn-pure.btn-danger:focus.hover, .btn-pure.btn-danger:focus:focus, .btn-pure.btn-danger:focus.focus, .btn-pure.btn-danger.focus:hover, .btn-pure.btn-danger.focus.hover, .btn-pure.btn-danger.focus:focus, .btn-pure.btn-danger.focus.focus, .btn-pure.btn-danger:active:hover, .btn-pure.btn-danger:active.hover, .btn-pure.btn-danger:active:focus, .btn-pure.btn-danger:active.focus, .btn-pure.btn-danger.active:hover, .btn-pure.btn-danger.active.hover, .btn-pure.btn-danger.active:focus, .btn-pure.btn-danger.active.focus, .open > .btn-pure.btn-danger.dropdown-toggle:hover, .open > .btn-pure.btn-danger.dropdown-toggle.hover, .open > .btn-pure.btn-danger.dropdown-toggle:focus, .open > .btn-pure.btn-danger.dropdown-toggle.focus, .show > .btn-pure.btn-danger.dropdown-toggle:hover, .show > .btn-pure.btn-danger.dropdown-toggle.hover, .show > .btn-pure.btn-danger.dropdown-toggle:focus, .show > .btn-pure.btn-danger.dropdown-toggle.focus {
  color: #ff8589;
}

.btn-pure.btn-danger:hover .badge-pill, .btn-pure.btn-danger.hover .badge-pill, .btn-pure.btn-danger:focus .badge-pill, .btn-pure.btn-danger.focus .badge-pill, .btn-pure.btn-danger:active .badge-pill, .btn-pure.btn-danger.active .badge-pill, .open > .btn-pure.btn-danger.dropdown-toggle .badge-pill, .show > .btn-pure.btn-danger.dropdown-toggle .badge-pill {
  color: #ff8589;
}

.btn-pure.btn-dark {
  color: #526069;
}

.btn-pure.btn-dark:hover, .btn-pure.btn-dark.hover, .btn-pure.btn-dark:focus, .btn-pure.btn-dark.focus, .btn-pure.btn-dark:active, .btn-pure.btn-dark.active, .open > .btn-pure.btn-dark.dropdown-toggle, .show > .btn-pure.btn-dark.dropdown-toggle {
  color: #76838f;
}

.btn-pure.btn-dark:hover:hover, .btn-pure.btn-dark:hover.hover, .btn-pure.btn-dark:hover:focus, .btn-pure.btn-dark:hover.focus, .btn-pure.btn-dark.hover:hover, .btn-pure.btn-dark.hover.hover, .btn-pure.btn-dark.hover:focus, .btn-pure.btn-dark.hover.focus, .btn-pure.btn-dark:focus:hover, .btn-pure.btn-dark:focus.hover, .btn-pure.btn-dark:focus:focus, .btn-pure.btn-dark:focus.focus, .btn-pure.btn-dark.focus:hover, .btn-pure.btn-dark.focus.hover, .btn-pure.btn-dark.focus:focus, .btn-pure.btn-dark.focus.focus, .btn-pure.btn-dark:active:hover, .btn-pure.btn-dark:active.hover, .btn-pure.btn-dark:active:focus, .btn-pure.btn-dark:active.focus, .btn-pure.btn-dark.active:hover, .btn-pure.btn-dark.active.hover, .btn-pure.btn-dark.active:focus, .btn-pure.btn-dark.active.focus, .open > .btn-pure.btn-dark.dropdown-toggle:hover, .open > .btn-pure.btn-dark.dropdown-toggle.hover, .open > .btn-pure.btn-dark.dropdown-toggle:focus, .open > .btn-pure.btn-dark.dropdown-toggle.focus, .show > .btn-pure.btn-dark.dropdown-toggle:hover, .show > .btn-pure.btn-dark.dropdown-toggle.hover, .show > .btn-pure.btn-dark.dropdown-toggle:focus, .show > .btn-pure.btn-dark.dropdown-toggle.focus {
  color: #76838f;
}

.btn-pure.btn-dark:hover .badge-pill, .btn-pure.btn-dark.hover .badge-pill, .btn-pure.btn-dark:focus .badge-pill, .btn-pure.btn-dark.focus .badge-pill, .btn-pure.btn-dark:active .badge-pill, .btn-pure.btn-dark.active .badge-pill, .open > .btn-pure.btn-dark.dropdown-toggle .badge-pill, .show > .btn-pure.btn-dark.dropdown-toggle .badge-pill {
  color: #76838f;
}

.btn-pure.btn-inverse {
  color: #fff;
}

.btn-pure.btn-inverse:hover, .btn-pure.btn-inverse.hover, .btn-pure.btn-inverse:focus, .btn-pure.btn-inverse.focus, .btn-pure.btn-inverse:active, .btn-pure.btn-inverse.active, .open > .btn-pure.btn-inverse.dropdown-toggle, .show > .btn-pure.btn-inverse.dropdown-toggle {
  color: #fff;
}

.btn-pure.btn-inverse:hover:hover, .btn-pure.btn-inverse:hover.hover, .btn-pure.btn-inverse:hover:focus, .btn-pure.btn-inverse:hover.focus, .btn-pure.btn-inverse.hover:hover, .btn-pure.btn-inverse.hover.hover, .btn-pure.btn-inverse.hover:focus, .btn-pure.btn-inverse.hover.focus, .btn-pure.btn-inverse:focus:hover, .btn-pure.btn-inverse:focus.hover, .btn-pure.btn-inverse:focus:focus, .btn-pure.btn-inverse:focus.focus, .btn-pure.btn-inverse.focus:hover, .btn-pure.btn-inverse.focus.hover, .btn-pure.btn-inverse.focus:focus, .btn-pure.btn-inverse.focus.focus, .btn-pure.btn-inverse:active:hover, .btn-pure.btn-inverse:active.hover, .btn-pure.btn-inverse:active:focus, .btn-pure.btn-inverse:active.focus, .btn-pure.btn-inverse.active:hover, .btn-pure.btn-inverse.active.hover, .btn-pure.btn-inverse.active:focus, .btn-pure.btn-inverse.active.focus, .open > .btn-pure.btn-inverse.dropdown-toggle:hover, .open > .btn-pure.btn-inverse.dropdown-toggle.hover, .open > .btn-pure.btn-inverse.dropdown-toggle:focus, .open > .btn-pure.btn-inverse.dropdown-toggle.focus, .show > .btn-pure.btn-inverse.dropdown-toggle:hover, .show > .btn-pure.btn-inverse.dropdown-toggle.hover, .show > .btn-pure.btn-inverse.dropdown-toggle:focus, .show > .btn-pure.btn-inverse.dropdown-toggle.focus {
  color: #fff;
}

.btn-pure.btn-inverse:hover .badge-pill, .btn-pure.btn-inverse.hover .badge-pill, .btn-pure.btn-inverse:focus .badge-pill, .btn-pure.btn-inverse.focus .badge-pill, .btn-pure.btn-inverse:active .badge-pill, .btn-pure.btn-inverse.active .badge-pill, .open > .btn-pure.btn-inverse.dropdown-toggle .badge-pill, .show > .btn-pure.btn-inverse.dropdown-toggle .badge-pill {
  color: #fff;
}

.dropdown-menu.dropdown-menu-bullet:before, .dropdown-menu.dropdown-menu-bullet:after {
  border: 7px solid transparent;
}

.dropdown-menu.dropdown-menu-bullet:before {
  border-bottom-color: #e4eaec;
}

.dropdown-menu.dropdown-menu-bullet:after {
  border-bottom-color: #fff;
}

.dropup .dropdown-menu, .navbar-fixed-bottom .dropdown .dropdown-menu {
  box-shadow: 0 -3px 12px rgba(0, 0, 0, .05);
}

.dropup .dropdown-menu.dropdown-menu-bullet:before, .navbar-fixed-bottom .dropdown .dropdown-menu.dropdown-menu-bullet:before {
  border-top-color: #e4eaec;
}

.dropup .dropdown-menu.dropdown-menu-bullet:after, .navbar-fixed-bottom .dropdown .dropdown-menu.dropdown-menu-bullet:after {
  border-top-color: #fff;
}

.dropdown-menu > .dropdown-submenu > .dropdown-item:after {
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 4px dashed;
}

.dropdown-menu-media .dropdown-menu-header {
  background-color: #fff;
  border-bottom: 1px solid #e4eaec;
}

.dropdown-menu-media .list-group-item {
  border: none;
}

.dropdown-menu-media .list-group-item .media {
  border-top: 1px solid #e4eaec;
}

.dropdown-menu-media .list-group-item:first-child .media {
  border-top: none;
}

.dropdown-menu-media > .dropdown-menu-footer {
  background-color: #f3f7f9;
  border-top: 1px solid #e4eaec;
}

.dropdown-menu-media > .dropdown-menu-footer > a {
  color: #a3afb7 !important;
}

.dropdown-menu-media > .dropdown-menu-footer > a:hover {
  color: #ab8c82 !important;
  background-color: transparent !important;
}

.dropdown-menu-media > .dropdown-menu-footer > .dropdown-menu-footer-btn:hover {
  color: #ab8c82 !important;
  background-color: transparent !important;
}

.dropdown-menu-primary .dropdown-iten.active, .dropdown-menu-primary .dropdown-iten.active:hover, .dropdown-menu-primary .dropdown-iten.active:focus {
  color: #fff;
  background-color: #997b71;
}

.dropdown-menu-success .dropdown-iten.active, .dropdown-menu-success .dropdown-iten.active:hover, .dropdown-menu-success .dropdown-iten.active:focus {
  color: #fff;
  background-color: #11c26d;
}

.dropdown-menu-info .dropdown-iten.active, .dropdown-menu-info .dropdown-iten.active:hover, .dropdown-menu-info .dropdown-iten.active:focus {
  color: #fff;
  background-color: #0bb2d4;
}

.dropdown-menu-warning .dropdown-iten.active, .dropdown-menu-warning .dropdown-iten.active:hover, .dropdown-menu-warning .dropdown-iten.active:focus {
  color: #fff;
  background-color: #eb6709;
}

.dropdown-menu-danger .dropdown-iten.active, .dropdown-menu-danger .dropdown-iten.active:hover, .dropdown-menu-danger .dropdown-iten.active:focus {
  color: #fff;
  background-color: #ff4c52;
}

.dropdown-menu-dark .dropdown-iten.active, .dropdown-menu-dark .dropdown-iten.active:hover, .dropdown-menu-dark .dropdown-iten.active:focus {
  color: #fff;
  background-color: #526069;
}

.btn-group.open .dropdown-toggle {
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, .05);
}

.nav-link:focus, .nav-link:hover {
  background-color: #f3f7f9;
}

.nav-link.disabled:focus, .nav-link.disabled:hover {
  background-color: transparent;
}

.nav-quick {
  background-color: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
}

.nav-quick .nav-link {
  color: #76838f;
}

.nav-quick-bordered {
  border-top: 1px solid #e4eaec;
  border-left: 1px solid #e4eaec;
}

.nav-quick-bordered .nav-item {
  border-right: 1px solid #e4eaec;
  border-bottom: 1px solid #e4eaec;
}

.nav-tabs .nav-link {
  color: #76838f;
}

.nav-tabs .nav-link.active, .nav-tabs .nav-link.active:focus, .nav-tabs .nav-link.active:hover, .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-item.show .nav-link:focus, .nav-tabs .nav-item.show .nav-link:hover, .nav-tabs .nav-item.open .nav-link, .nav-tabs .nav-item.open .nav-link:focus, .nav-tabs .nav-item.open .nav-link:hover {
  border-color: transparent;
  border-bottom-color: #997b71;
}

.nav-tabs.nav-tabs-bottom {
  border-top: 1px solid #e4eaec;
  border-bottom: none;
}

.nav-tabs.nav-tabs-bottom .nav-link:hover, .nav-tabs.nav-tabs-bottom .nav-link:focus {
  border-top-color: #e4eaec;
  border-bottom-color: transparent;
}

.nav-tabs-solid {
  border-bottom-color: #f3f7f9;
}

.nav-tabs-solid .nav-link:hover {
  border-color: transparent;
}

.nav-tabs-solid .nav-link.active, .nav-tabs-solid .nav-link.active:focus, .nav-tabs-solid .nav-link.active:hover, .nav-tabs-solid .nav-item.show .nav-link, .nav-tabs-solid .nav-item.show .nav-link:focus, .nav-tabs-solid .nav-item.show .nav-link:hover, .nav-tabs-solid .nav-item.open .nav-link, .nav-tabs-solid .nav-item.open .nav-link:focus, .nav-tabs-solid .nav-item.open .nav-link:hover {
  color: #76838f;
  background-color: #f3f7f9;
  border-color: transparent;
}

.nav-tabs-solid ~ .tab-content {
  background-color: #f3f7f9;
}

.nav-tabs-solid.nav-tabs-bottom .nav-link.active, .nav-tabs-solid.nav-tabs-bottom .nav-link.active:hover, .nav-tabs-solid.nav-tabs-bottom .nav-link.active:focus {
  border: none;
}

.nav-tabs-line .nav-link {
  border-bottom: 2px solid transparent;
}

.nav-tabs-line .nav-link:hover, .nav-tabs-line .nav-link:focus {
  background-color: transparent;
}

.nav-tabs-line .nav-link:hover {
  border-bottom-color: #ccd5db;
}

.nav-tabs-line .nav-link.active, .nav-tabs-line .nav-link.active:focus, .nav-tabs-line .nav-link.active:hover, .nav-tabs-line .nav-item.show .nav-link, .nav-tabs-line .nav-item.show .nav-link:focus, .nav-tabs-line .nav-item.show .nav-link:hover, .nav-tabs-line .nav-item.open .nav-link, .nav-tabs-line .nav-item.open .nav-link:focus, .nav-tabs-line .nav-item.open .nav-link:hover {
  color: #997b71;
  background-color: transparent;
  border-bottom: 2px solid #997b71;
}

.nav-tabs-line .nav-item.open > .nav-link, .nav-tabs-line .nav-item.open > .nav-link:focus, .nav-tabs-line .nav-item.open > .nav-link:hover, .nav-tabs-line .nav-item.show > .nav-link, .nav-tabs-line .nav-item.show > .nav-link:focus, .nav-tabs-line .nav-item.show > .nav-link:hover {
  border-bottom-color: #997b71;
}

.nav-tabs-line.nav-tabs-bottom .nav-link {
  border-top: 2px solid transparent;
  border-bottom: none;
}

.nav-tabs-line.nav-tabs-bottom .nav-link:hover {
  border-top-color: #ccd5db;
  border-bottom-color: transparent;
}

.nav-tabs-line.nav-tabs-bottom .nav-item.open > .nav-link, .nav-tabs-line.nav-tabs-bottom .nav-item.open > .nav-link:focus, .nav-tabs-line.nav-tabs-bottom .nav-item.open > .nav-link:hover, .nav-tabs-line.nav-tabs-bottom .nav-item.show > .nav-link, .nav-tabs-line.nav-tabs-bottom .nav-item.show > .nav-link:focus, .nav-tabs-line.nav-tabs-bottom .nav-item.show > .nav-link:hover {
  border-top-color: #997b71;
}

.tabs-line-top {
  border-bottom: 1px solid #e4eaec;
}

.tabs-line-top .nav-link, .tabs-line-top .nav-item .nav-link {
  border-top: 2px solid transparent;
  border-bottom-color: transparent;
}

.tabs-line-top .nav-link:hover, .tabs-line-top .nav-item .nav-link:hover {
  border-top: 2px solid #ccd5db;
  border-bottom-color: transparent;
}

.tabs-line-top .nav-link.active, .tabs-line-top .nav-link.active:focus, .tabs-line-top .nav-link.active:hover, .tabs-line-top .nav-item.open .nav-link, .tabs-line-top .nav-item.open .nav-link:focus, .tabs-line-top .nav-item.open .nav-link:hover, .tabs-line-top .nav-item.show .nav-link, .tabs-line-top .nav-item.show .nav-link:focus, .tabs-line-top .nav-item.show .nav-link:hover {
  color: #997b71;
  background-color: transparent;
  border-top: 2px solid #997b71;
  border-right: 1px solid #e4eaec;
  border-bottom: 1px solid #fff;
  border-left: 1px solid #e4eaec;
}

.nav-tabs-vertical .nav-tabs {
  border-right: 1px solid #e4eaec;
  border-bottom: none;
}

.nav-tabs-vertical .nav-tabs .nav-link:hover {
  border-right-color: #e4eaec;
  border-bottom-color: transparent;
}

.nav-tabs-vertical .nav-tabs .nav-link.active, .nav-tabs-vertical .nav-tabs .nav-link.active:focus, .nav-tabs-vertical .nav-tabs .nav-link.active:hover {
  border-right-color: #997b71;
  border-bottom-color: transparent;
}

.nav-tabs-vertical .nav-tabs-reverse {
  border-right: none;
  border-left: 1px solid #e4eaec;
}

.nav-tabs-vertical .nav-tabs-reverse .nav-link:hover {
  border-right-color: transparent;
  border-left-color: #e4eaec;
}

.nav-tabs-vertical .nav-tabs-reverse .nav-link.active, .nav-tabs-vertical .nav-tabs-reverse .nav-link.active:focus, .nav-tabs-vertical .nav-tabs-reverse .nav-link.active:hover {
  border-right-color: transparent;
  border-left-color: #997b71;
}

.nav-tabs-vertical .nav-tabs-solid {
  border-right-color: #f3f7f9;
}

.nav-tabs-vertical .nav-tabs-solid .nav-link:hover {
  border-color: transparent;
}

.nav-tabs-vertical .nav-tabs-solid .nav-link.active, .nav-tabs-vertical .nav-tabs-solid .nav-link.active:focus, .nav-tabs-vertical .nav-tabs-solid .nav-link.active:hover {
  border-color: transparent;
}

.nav-tabs-vertical .nav-tabs-solid.nav-tabs-reverse {
  border-left-color: #f3f7f9;
}

.nav-tabs-vertical .nav-tabs-line .nav-link {
  border-right: 2px solid transparent;
  border-bottom: none;
}

.nav-tabs-vertical .nav-tabs-line .nav-link:hover {
  border-right-color: #ccd5db;
}

.nav-tabs-vertical .nav-tabs-line .nav-link.active, .nav-tabs-vertical .nav-tabs-line .nav-link.active:hover, .nav-tabs-vertical .nav-tabs-line .nav-link.active:focus {
  border-right: 2px solid #997b71;
  border-bottom: none;
}

.nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse .nav-link {
  border-left: 2px solid transparent;
}

.nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse .nav-link:hover {
  border-color: transparent;
  border-left-color: #ccd5db;
}

.nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse .nav-link.active, .nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse .nav-link.active:hover, .nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse .nav-link.active:focus {
  border-right: 1px solid transparent;
  border-left: 2px solid #997b71;
}

.nav-tabs-inverse .nav-link.active, .nav-tabs-inverse .nav-link.active:hover, .nav-tabs-inverse .nav-link.active:focus {
  border-color: transparent;
}

.nav-tabs-inverse .nav-tabs-solid {
  border-bottom-color: #fff;
}

.nav-tabs-inverse .nav-tabs-solid .nav-link.active, .nav-tabs-inverse .nav-tabs-solid .nav-link.active:hover, .nav-tabs-inverse .nav-tabs-solid .nav-link.active:focus {
  color: #76838f;
  background-color: #fff;
}

.nav-tabs-inverse.nav-tabs-vertical .nav-link.active, .nav-tabs-inverse.nav-tabs-vertical .nav-link.active:hover, .nav-tabs-inverse.nav-tabs-vertical .nav-link.active:focus {
  border-color: transparent;
}

.nav-tabs-inverse.nav-tabs-vertical .nav-tabs-solid {
  border-right-color: #fff;
}

.nav-tabs-inverse.nav-tabs-vertical .nav-tabs-solid.nav-tabs-reverse {
  border-left-color: #fff;
}

.nav-tabs-inverse .tab-content {
  background: #fff;
}

.navbar {
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .08);
}

@media (max-width: 767px) {
  .navbar-nav .open .dropdown-menu, .navbar-nav .show .dropdown-menu {
    background-color: transparent;
    border: 0;
  }
}

.navbar-toggler {
  background: transparent !important;
  border: 1px solid transparent;
}

.navbar-toggler:hover {
  background: transparent !important;
}

.navbar-collapse {
  border-top: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1);
}

@media (min-width: 768px) {
  .navbar-collapse {
    border-top: 0;
    box-shadow: none;
  }
}

.navbar-form {
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1), 0 1px 0 rgba(255, 255, 255, .1);
}

@media (min-width: 768px) {
  .navbar-form {
    border: 0;
    box-shadow: none;
  }
}

.navbar-form .icon {
  color: rgba(55, 71, 79, .4);
}

.navbar-form .form-control {
  background-color: #f3f7f9;
  border: none;
}

@media (max-width: 767px) {
  .navbar-search .navbar-form {
    border-bottom: none;
  }
}

.navbar-search-overlap {
  background-color: #fff;
}

.navbar-search-overlap .form-control {
  background-color: transparent !important;
}

.navbar-search-overlap .form-control:focus {
  border-color: transparent;
}

.navbar-default {
  background-color: #fff;
  border-color: #e4eaec;
}

.navbar-default .navbar-brand {
  color: #37474f;
}

.navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus {
  color: #37474f;
  background-color: none;
}

.navbar-default .navbar-text {
  color: #76838f;
}

.navbar-default .navbar-nav .nav-link {
  color: #76838f;
}

.navbar-default .navbar-nav .nav-link:hover, .navbar-default .navbar-nav .nav-link:focus {
  color: #526069;
  background-color: rgba(243, 247, 249, .3);
}

.navbar-default .navbar-nav > .active > .nav-link, .navbar-default .navbar-nav > .active > .nav-link:hover, .navbar-default .navbar-nav > .active > .nav-link:focus, .navbar-default .navbar-nav .nav-link.active, .navbar-default .navbar-nav .nav-link.active:hover, .navbar-default .navbar-nav .nav-link.active:focus {
  color: #526069;
  background-color: rgba(243, 247, 249, .6);
}

.navbar-default .navbar-nav > .disabled > .nav-link, .navbar-default .navbar-nav > .disabled > .nav-link:hover, .navbar-default .navbar-nav > .disabled > .nav-link:focus, .navbar-default .navbar-nav .nav-link.disabled, .navbar-default .navbar-nav .nav-link.disabled:hover, .navbar-default .navbar-nav .nav-link.disabled:focus {
  color: #e4eaec;
  background-color: transparent;
}

.navbar-default .navbar-toggler {
  border-color: transparent;
}

.navbar-default .navbar-toggler:hover, .navbar-default .navbar-toggler:focus {
  background-color: rgba(243, 247, 249, .3);
}

.navbar-default .navbar-toggler .icon-bar {
  background-color: #76838f;
}

.navbar-default .navbar-collapse, .navbar-default .navbar-form {
  border-color: #e4eaec;
}

@media (max-width: 767px) {
  .navbar-default .navbar-nav .open .dropdown-menu .dropdown-item, .navbar-default .navbar-nav .show .dropdown-menu .dropdown-item {
    color: #76838f;
  }

  .navbar-default .navbar-nav .open .dropdown-menu .dropdown-item:hover, .navbar-default .navbar-nav .open .dropdown-menu .dropdown-item:focus, .navbar-default .navbar-nav .show .dropdown-menu .dropdown-item:hover, .navbar-default .navbar-nav .show .dropdown-menu .dropdown-item:focus {
    color: #526069;
    background-color: rgba(243, 247, 249, .3);
  }

  .navbar-default .navbar-nav .open .dropdown-menu .dropdown-item.active, .navbar-default .navbar-nav .open .dropdown-menu .dropdown-item.active:hover, .navbar-default .navbar-nav .open .dropdown-menu .dropdown-item.active:focus, .navbar-default .navbar-nav .show .dropdown-menu .dropdown-item.active, .navbar-default .navbar-nav .show .dropdown-menu .dropdown-item.active:hover, .navbar-default .navbar-nav .show .dropdown-menu .dropdown-item.active:focus {
    color: #526069;
    background-color: rgba(243, 247, 249, .6);
  }

  .navbar-default .navbar-nav .open .dropdown-menu .dropdown-item.disabled, .navbar-default .navbar-nav .open .dropdown-menu .dropdown-item.disabled:hover, .navbar-default .navbar-nav .open .dropdown-menu .dropdown-item.disabled:focus, .navbar-default .navbar-nav .show .dropdown-menu .dropdown-item.disabled, .navbar-default .navbar-nav .show .dropdown-menu .dropdown-item.disabled:hover, .navbar-default .navbar-nav .show .dropdown-menu .dropdown-item.disabled:focus {
    color: #e4eaec;
    background-color: transparent;
  }
}

.navbar-default .navbar-nav > .open > .nav-link, .navbar-default .navbar-nav > .open > .nav-link:hover, .navbar-default .navbar-nav > .open > .nav-link:focus, .navbar-default .navbar-nav > .show > .nav-link, .navbar-default .navbar-nav > .show > .nav-link:hover, .navbar-default .navbar-nav > .show > .nav-link:focus {
  color: #526069;
  background-color: rgba(243, 247, 249, .6);
}

.navbar-default .navbar-link {
  color: #76838f;
}

.navbar-default .navbar-link:hover {
  color: #526069;
}

.navbar-default .navbar-toolbar .nav-link {
  display: block;
  color: #76838f;
}

.navbar-default .navbar-toolbar .nav-link:hover, .navbar-default .navbar-toolbar .nav-link:focus {
  color: #526069;
  background-color: rgba(243, 247, 249, .3);
}

.navbar-default .navbar-toolbar > .active > .nav-link, .navbar-default .navbar-toolbar > .active > .nav-link:hover, .navbar-default .navbar-toolbar > .active > .nav-link:focus, .navbar-default .navbar-toolbar .nav-link.active, .navbar-default .navbar-toolbar .nav-link.active:hover, .navbar-default .navbar-toolbar .nav-link.active:focus {
  color: #526069;
  background-color: rgba(243, 247, 249, .6);
}

.navbar-default .navbar-toolbar > .disabled > .nav-link, .navbar-default .navbar-toolbar > .disabled > .nav-link:hover, .navbar-default .navbar-toolbar > .disabled > .nav-link:focus, .navbar-default .navbar-toolbar .nav-link.disabled, .navbar-default .navbar-toolbar .nav-link.disabled:hover, .navbar-default .navbar-toolbar .nav-link.disabled:focus {
  color: #e4eaec;
  background-color: transparent;
}

.navbar-default .navbar-toggler {
  color: #76838f;
}

.navbar-default .navbar-toolbar > .open > .nav-link, .navbar-default .navbar-toolbar > .open > .nav-link:hover, .navbar-default .navbar-toolbar > .open > .nav-link:focus, .navbar-default .navbar-toolbar > .show > .nav-link, .navbar-default .navbar-toolbar > .show > .nav-link:hover, .navbar-default .navbar-toolbar > .show > .nav-link:focus {
  color: #526069;
  background-color: rgba(243, 247, 249, .6);
}

.navbar-inverse {
  background-color: #997b71;
  border-color: rgba(0, 0, 0, .1);
}

.navbar-inverse .navbar-brand {
  color: #fff;
}

.navbar-inverse .navbar-brand:hover, .navbar-inverse .navbar-brand:focus {
  color: #fff;
  background-color: none;
}

.navbar-inverse .navbar-text {
  color: #fff;
}

.navbar-inverse .navbar-nav .nav-link {
  color: #fff;
}

.navbar-inverse .navbar-nav .nav-link:hover, .navbar-inverse .navbar-nav .nav-link:focus {
  color: #fff;
  background-color: rgba(0, 0, 0, .1);
}

.navbar-inverse .navbar-nav > .active > .nav-link, .navbar-inverse .navbar-nav > .active > .nav-link:hover, .navbar-inverse .navbar-nav > .active > .nav-link:focus, .navbar-inverse .navbar-nav .nav-link.active, .navbar-inverse .navbar-nav .nav-link.active:hover, .navbar-inverse .navbar-nav .nav-link.active:focus {
  color: #fff;
  background-color: rgba(0, 0, 0, .1);
}

.navbar-inverse .navbar-nav > .disabled > .nav-link, .navbar-inverse .navbar-nav > .disabled > .nav-link:hover, .navbar-inverse .navbar-nav > .disabled > .nav-link:focus, .navbar-inverse .navbar-nav .nav-link.disabled, .navbar-inverse .navbar-nav .nav-link.disabled:hover, .navbar-inverse .navbar-nav .nav-link.disabled:focus {
  color: #fff;
  background-color: transparent;
}

.navbar-inverse .navbar-toggler {
  color: #fff;
  border-color: transparent;
}

.navbar-inverse .navbar-toggler:hover, .navbar-inverse .navbar-toggler:focus {
  background-color: rgba(0, 0, 0, .1);
}

.navbar-inverse .navbar-toggler .icon-bar {
  background-color: #fff;
}

.navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
  border-color: #866a60;
}

@media (max-width: 767px) {
  .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header, .navbar-inverse .navbar-nav .show .dropdown-menu > .dropdown-header {
    border-color: rgba(0, 0, 0, .1);
  }

  .navbar-inverse .navbar-nav .open .dropdown-menu .dropdown-divider, .navbar-inverse .navbar-nav .show .dropdown-menu .dropdown-divider {
    background-color: rgba(0, 0, 0, .1);
  }

  .navbar-inverse .navbar-nav .open .dropdown-menu .dropdown-item, .navbar-inverse .navbar-nav .show .dropdown-menu .dropdown-item {
    color: #fff;
  }

  .navbar-inverse .navbar-nav .open .dropdown-menu .dropdown-item:hover, .navbar-inverse .navbar-nav .open .dropdown-menu .dropdown-item:focus, .navbar-inverse .navbar-nav .show .dropdown-menu .dropdown-item:hover, .navbar-inverse .navbar-nav .show .dropdown-menu .dropdown-item:focus {
    color: #fff;
    background-color: rgba(0, 0, 0, .1);
  }

  .navbar-inverse .navbar-nav .open .dropdown-menu .dropdown-item.active, .navbar-inverse .navbar-nav .open .dropdown-menu .dropdown-item.active:hover, .navbar-inverse .navbar-nav .open .dropdown-menu .dropdown-item.active:focus, .navbar-inverse .navbar-nav .show .dropdown-menu .dropdown-item.active, .navbar-inverse .navbar-nav .show .dropdown-menu .dropdown-item.active:hover, .navbar-inverse .navbar-nav .show .dropdown-menu .dropdown-item.active:focus {
    color: #fff;
    background-color: rgba(0, 0, 0, .1);
  }

  .navbar-inverse .navbar-nav .open .dropdown-menu .dropdown-item.disabled, .navbar-inverse .navbar-nav .open .dropdown-menu .dropdown-item.disabled:hover, .navbar-inverse .navbar-nav .open .dropdown-menu .dropdown-item.disabled:focus, .navbar-inverse .navbar-nav .show .dropdown-menu .dropdown-item.disabled, .navbar-inverse .navbar-nav .show .dropdown-menu .dropdown-item.disabled:hover, .navbar-inverse .navbar-nav .show .dropdown-menu .dropdown-item.disabled:focus {
    color: #fff;
    background-color: transparent;
  }
}

.navbar-inverse .navbar-nav > .open > .nav-link, .navbar-inverse .navbar-nav > .open > .nav-link:hover, .navbar-inverse .navbar-nav > .open > .nav-link:focus, .navbar-inverse .navbar-nav > .show > .nav-link, .navbar-inverse .navbar-nav > .show > .nav-link:hover, .navbar-inverse .navbar-nav > .show > .nav-link:focus {
  color: #fff;
  background-color: rgba(0, 0, 0, .1);
}

.navbar-inverse .navbar-link {
  color: #fff;
}

.navbar-inverse .navbar-link:hover {
  color: #fff;
}

.navbar-inverse .navbar-toolbar .nav-link {
  color: #fff;
}

.navbar-inverse .navbar-toolbar .nav-link:hover, .navbar-inverse .navbar-toolbar .nav-link:focus {
  color: #fff;
  background-color: rgba(0, 0, 0, .1);
}

.navbar-inverse .navbar-toolbar > .active > .nav-link, .navbar-inverse .navbar-toolbar > .active > .nav-link:hover, .navbar-inverse .navbar-toolbar > .active > .nav-link:focus, .navbar-inverse .navbar-toolbar .nav-link.active, .navbar-inverse .navbar-toolbar .nav-link.active:hover, .navbar-inverse .navbar-toolbar .nav-link.active:focus {
  color: #fff;
  background-color: rgba(0, 0, 0, .1);
}

.navbar-inverse .navbar-toolbar > .disabled > .nav-link, .navbar-inverse .navbar-toolbar > .disabled > .nav-link:hover, .navbar-inverse .navbar-toolbar > .disabled > .nav-link:focus, .navbar-inverse .navbar-toolbar .nav-link.disabled, .navbar-inverse .navbar-toolbar .nav-link.disabled:hover, .navbar-inverse .navbar-toolbar .nav-link.disabled:focus {
  color: #fff;
  background-color: transparent;
}

.navbar-inverse .navbar-toolbar > .open > .nav-link, .navbar-inverse .navbar-toolbar > .open > .nav-link:hover, .navbar-inverse .navbar-toolbar > .open > .nav-link:focus, .navbar-inverse .navbar-toolbar > .show > .nav-link, .navbar-inverse .navbar-toolbar > .show > .nav-link:hover, .navbar-inverse .navbar-toolbar > .show > .nav-link:focus {
  color: #fff;
  background-color: rgba(0, 0, 0, .1);
}

.breadcrumb {
  background-color: transparent;
}

.page-item.disabled > span, .page-item.disabled > span:focus, .page-item.disabled > span:hover {
  color: #ccd5db;
  background-color: transparent;
  border-color: #e4eaec;
}

.pagination-gap .page-item.disabled .page-link:focus, .pagination-gap .page-item.disabled .page-link:hover {
  border-color: #e4eaec;
}

.pagination-gap .page-item.active .page-link {
  background-color: #997b71;
}

.pagination-gap .page-link:focus, .pagination-gap .page-link:hover {
  background-color: transparent;
  border-color: #997b71;
}

.pagination-no-border .page-link {
  border: none;
}

.badge.badge-outline {
  color: #f3f7f9;
  background-color: transparent;
  border-color: #f3f7f9;
}

.badge-outline {
  border: 1px solid transparent;
}

.badge-default {
  color: #76838f;
}

.badge-default[href]:hover, .badge-default[href]:focus {
  background-color: #f3f7f9;
}

.badge-default.badge-outline {
  color: #e4eaec;
  background-color: transparent;
  border-color: #e4eaec;
}

.badge-default[href]:hover, .badge-default[href]:focus {
  color: #a3afb7;
}

.badge-default.badge-outline {
  color: #76838f;
}

.badge-primary[href]:hover, .badge-primary[href]:focus {
  background-color: #ab8c82;
}

.badge-primary.badge-outline {
  color: #997b71;
  background-color: transparent;
  border-color: #997b71;
}

.badge-success[href]:hover, .badge-success[href]:focus {
  background-color: #28d17c;
}

.badge-success.badge-outline {
  color: #11c26d;
  background-color: transparent;
  border-color: #11c26d;
}

.badge-info[href]:hover, .badge-info[href]:focus {
  background-color: #28c0de;
}

.badge-info.badge-outline {
  color: #0bb2d4;
  background-color: transparent;
  border-color: #0bb2d4;
}

.badge-warning[href]:hover, .badge-warning[href]:focus {
  background-color: #f57d1b;
}

.badge-warning.badge-outline {
  color: #eb6709;
  background-color: transparent;
  border-color: #eb6709;
}

.badge-danger[href]:hover, .badge-danger[href]:focus {
  background-color: #ff666b;
}

.badge-danger.badge-outline {
  color: #ff4c52;
  background-color: transparent;
  border-color: #ff4c52;
}

.badge-dark {
  color: color-yiq(#526069);
  background-color: #526069;
}

.badge-dark[href]:hover, .badge-dark[href]:focus {
  background-color: #76838f;
}

.badge-dark.badge-outline {
  color: #526069;
  background-color: transparent;
  border-color: #526069;
}

.badge-dark[href]:focus, .badge-dark[href]:hover {
  color: color-yiq(#526069);
  text-decoration: none;
  background-color: #3c464c;
}

.alert h4 {
  color: inherit;
}

.alert-alt {
  color: #76838f;
  background-color: rgba(243, 247, 249, .8);
  border: none;
  border-left: 3px solid transparent;
}

.alert-dismissible.alert-alt .close {
  color: #a3afb7;
}

.alert-dismissible.alert-alt .close:hover, .alert-dismissible.alert-alt .close:focus {
  color: #a3afb7;
}

.alert-primary {
  color: #997b71;
  background-color: rgba(245, 226, 218, .8);
  border-color: #f5e2da;
}

.alert-primary .close {
  color: #997b71;
}

.alert-primary .close:hover, .alert-primary .close:focus {
  color: #997b71;
}

.alert-primary .alert-link {
  color: #82675f;
}

.alert-alt.alert-primary {
  border-color: #997b71;
}

.alert-alt.alert-primary a, .alert-alt.alert-primary .alert-link {
  color: #997b71;
}

.alert-success .alert-link {
  color: #05a85c;
}

.alert-alt.alert-success {
  border-color: #11c26d;
}

.alert-alt.alert-success a, .alert-alt.alert-success .alert-link {
  color: #11c26d;
}

.alert-info .alert-link {
  color: #0099b8;
}

.alert-alt.alert-info {
  border-color: #0bb2d4;
}

.alert-alt.alert-info a, .alert-alt.alert-info .alert-link {
  color: #0bb2d4;
}

.alert-warning .alert-link {
  color: #de4e00;
}

.alert-alt.alert-warning {
  border-color: #eb6709;
}

.alert-alt.alert-warning a, .alert-alt.alert-warning .alert-link {
  color: #eb6709;
}

.alert-danger .alert-link {
  color: #f2353c;
}

.alert-alt.alert-danger {
  border-color: #ff4c52;
}

.alert-alt.alert-danger a, .alert-alt.alert-danger .alert-link {
  color: #ff4c52;
}

.alert-facebook {
  color: #fff;
  background-color: #3b5998;
  border-color: #3b5998;
}

.alert-facebook .close {
  color: #fff;
}

.alert-facebook .close:hover, .alert-facebook .close:focus {
  color: #fff;
}

.alert-facebook .alert-link {
  color: #fff;
}

.alert-twitter {
  color: #fff;
  background-color: #55acee;
  border-color: #55acee;
}

.alert-twitter .close {
  color: #fff;
}

.alert-twitter .close:hover, .alert-twitter .close:focus {
  color: #fff;
}

.alert-twitter .alert-link {
  color: #fff;
}

.alert-google-plus {
  color: #fff;
  background-color: #dd4b39;
  border-color: #dd4b39;
}

.alert-google-plus .close {
  color: #fff;
}

.alert-google-plus .close:hover, .alert-google-plus .close:focus {
  color: #fff;
}

.alert-google-plus .alert-link {
  color: #fff;
}

.alert-linkedin {
  color: #fff;
  background-color: #0976b4;
  border-color: #0976b4;
}

.alert-linkedin .close {
  color: #fff;
}

.alert-linkedin .close:hover, .alert-linkedin .close:focus {
  color: #fff;
}

.alert-linkedin .alert-link {
  color: #fff;
}

.alert-flickr {
  color: #fff;
  background-color: #ff0084;
  border-color: #ff0084;
}

.alert-flickr .close {
  color: #fff;
}

.alert-flickr .close:hover, .alert-flickr .close:focus {
  color: #fff;
}

.alert-flickr .alert-link {
  color: #fff;
}

.alert-tumblr {
  color: #fff;
  background-color: #35465c;
  border-color: #35465c;
}

.alert-tumblr .close {
  color: #fff;
}

.alert-tumblr .close:hover, .alert-tumblr .close:focus {
  color: #fff;
}

.alert-tumblr .alert-link {
  color: #fff;
}

.alert-github {
  color: #fff;
  background-color: #4183c4;
  border-color: #4183c4;
}

.alert-github .close {
  color: #fff;
}

.alert-github .close:hover, .alert-github .close:focus {
  color: #fff;
}

.alert-github .alert-link {
  color: #fff;
}

.alert-dribbble {
  color: #fff;
  background-color: #c32361;
  border-color: #c32361;
}

.alert-dribbble .close {
  color: #fff;
}

.alert-dribbble .close:hover, .alert-dribbble .close:focus {
  color: #fff;
}

.alert-dribbble .alert-link {
  color: #fff;
}

.alert-youtube {
  color: #fff;
  background-color: #b31217;
  border-color: #b31217;
}

.alert-youtube .close {
  color: #fff;
}

.alert-youtube .close:hover, .alert-youtube .close:focus {
  color: #fff;
}

.alert-youtube .alert-link {
  color: #fff;
}

.alert.dark .alert-link {
  color: #fff !important;
}

.alert.dark .alert-left-border {
  border: none;
  border-left: 3px solid transparent;
}

.alert.dark.alert-dismissible.alert-alt .close {
  color: #fff;
}

.alert.dark.alert-dismissible.alert-alt .close:hover, .alert.dark.alert-dismissible.alert-alt .close:focus {
  color: #fff;
}

.alert.dark.alert-primary {
  color: #fff;
  background-color: #997b71;
  border-color: #997b71;
}

.alert.dark.alert-primary .close {
  color: #fff;
}

.alert.dark.alert-primary .close:hover, .alert.dark.alert-primary .close:focus {
  color: #fff;
}

.alert.dark.alert-primary.alert-alt {
  border-color: #56443f;
}

.alert.dark.alert-primary.alert-alt a, .alert.dark.alert-primary.alert-alt .alert-link {
  color: #fff;
}

.alert.dark.alert-success {
  color: #fff;
  background-color: #11c26d;
  border-color: #11c26d;
}

.alert.dark.alert-success .close {
  color: #fff;
}

.alert.dark.alert-success .close:hover, .alert.dark.alert-success .close:focus {
  color: #fff;
}

.alert.dark.alert-success.alert-alt {
  border-color: #035e33;
}

.alert.dark.alert-success.alert-alt a, .alert.dark.alert-success.alert-alt .alert-link {
  color: #fff;
}

.alert.dark.alert-info {
  color: #fff;
  background-color: #0bb2d4;
  border-color: #0bb2d4;
}

.alert.dark.alert-info .close {
  color: #fff;
}

.alert.dark.alert-info .close:hover, .alert.dark.alert-info .close:focus {
  color: #fff;
}

.alert.dark.alert-info.alert-alt {
  border-color: #00596c;
}

.alert.dark.alert-info.alert-alt a, .alert.dark.alert-info.alert-alt .alert-link {
  color: #fff;
}

.alert.dark.alert-warning {
  color: #fff;
  background-color: #eb6709;
  border-color: #eb6709;
}

.alert.dark.alert-warning .close {
  color: #fff;
}

.alert.dark.alert-warning .close:hover, .alert.dark.alert-warning .close:focus {
  color: #fff;
}

.alert.dark.alert-warning.alert-alt {
  border-color: #923300;
}

.alert.dark.alert-warning.alert-alt a, .alert.dark.alert-warning.alert-alt .alert-link {
  color: #fff;
}

.alert.dark.alert-danger {
  color: #fff;
  background-color: #ff4c52;
  border-color: #ff4c52;
}

.alert.dark.alert-danger .close {
  color: #fff;
}

.alert.dark.alert-danger .close:hover, .alert.dark.alert-danger .close:focus {
  color: #fff;
}

.alert.dark.alert-danger.alert-alt {
  border-color: #cd0d14;
}

.alert.dark.alert-danger.alert-alt a, .alert.dark.alert-danger.alert-alt .alert-link {
  color: #fff;
}

.progress {
  background-color: #e4eaec;
  border-radius: .215rem;
}

.progress-bar {
  color: #fff;
  background-color: #997b71;
}

.progress-bar-success {
  background-color: #11c26d;
}

.progress-striped .progress-bar-success {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
}

.progress-bar-info {
  background-color: #0bb2d4;
}

.progress-striped .progress-bar-info {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
}

.progress-bar-warning {
  background-color: #eb6709;
}

.progress-striped .progress-bar-warning {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
}

.progress-bar-danger {
  background-color: #ff4c52;
}

.progress-striped .progress-bar-danger {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
}

.progress-striped .progress-bar, .progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
}

.progress-bar-indicating.active:before {
  background-color: #fff;
}

.progress-skill .progress-bar > span {
  color: #526069;
}

.media .media {
  border-bottom: none;
}

.media-meta {
  color: #a3afb7;
}

a.list-group-item {
  color: #76838f;
}

a.list-group-item:focus, a.list-group-item:hover {
  color: #76838f;
  background-color: #f3f7f9;
}

a.list-group-item.disabled, a.list-group-item.disabled:focus, a.list-group-item.disabled:hover {
  color: #e4eaec;
  background-color: #f3f7f9;
}

a.list-group-item.active, a.list-group-item.active:focus, a.list-group-item.active:hover {
  color: #fff;
  background-color: #997b71;
}

.list-group.bg-inherit .list-group-item {
  background-color: transparent;
  border-bottom-color: rgba(0, 0, 0, .075);
}

.list-group.bg-inherit .list-group-item:last-child {
  border-bottom-color: transparent;
}

.list-group.bg-inherit .list-group-item:hover {
  background-color: rgba(0, 0, 0, .075);
  border-color: transparent;
}

.list-group-bordered .list-group-item {
  border-color: #e4eaec;
}

.list-group-bordered .list-group-item.active, .list-group-bordered .list-group-item.active:hover, .list-group-bordered .list-group-item.active:focus {
  color: #fff;
  background-color: #82675f;
  border-color: #82675f;
}

.list-group-dividered .list-group-item {
  border-top-color: #e4eaec;
}

.list-group-dividered .list-group-item.active:hover {
  border-top-color: #e4eaec;
}

.list-group-dividered .list-group-item:last-child {
  border-bottom-color: #e4eaec;
}

.list-group-dividered .list-group-item:first-child {
  border-top-color: transparent;
}

.list-group-dividered .list-group-item:first-child.active:hover {
  border-top-color: transparent;
}

.list-group-item {
  border: 1px solid transparent;
}

.list-group-item.disabled .list-group-item-heading, .list-group-item.disabled:focus .list-group-item-heading, .list-group-item.disabled:hover .list-group-item-heading {
  color: inherit;
}

.list-group-item.disabled .list-group-item-text, .list-group-item.disabled:focus .list-group-item-text, .list-group-item.disabled:hover .list-group-item-text {
  color: #e4eaec;
}

.list-group-item.active .list-group-item-heading, .list-group-item.active .list-group-item-heading > small, .list-group-item.active .list-group-item-heading > .small, .list-group-item.active:focus .list-group-item-heading, .list-group-item.active:focus .list-group-item-heading > small, .list-group-item.active:focus .list-group-item-heading > .small, .list-group-item.active:hover .list-group-item-heading, .list-group-item.active:hover .list-group-item-heading > small, .list-group-item.active:hover .list-group-item-heading > .small {
  color: inherit;
}

.list-group-item.active .list-group-item-text, .list-group-item.active:focus .list-group-item-text, .list-group-item.active:hover .list-group-item-text {
  color: #eee9e8;
}

.list-group-item.active, .list-group-item.active:focus, .list-group-item.active:hover {
  color: #997b71;
  background-color: transparent;
  border-color: transparent;
}

.list-group-item.active .list-group-item-heading, .list-group-item.active:focus .list-group-item-heading, .list-group-item.active:hover .list-group-item-heading {
  color: inherit;
}

.list-group-item-dark {
  color: #fff;
  background-color: #526069;
}

a.list-group-item-dark, button.list-group-item-dark {
  color: #fff;
}

a.list-group-item-dark:focus, a.list-group-item-dark:hover, button.list-group-item-dark:focus, button.list-group-item-dark:hover {
  color: #fff;
  background-color: #47535b;
}

a.list-group-item-dark.active, button.list-group-item-dark.active {
  color: #fff;
  background-color: #fff;
  border-color: #fff;
}

.list-group-hover .list-group-item:hover {
  background-color: #f3f7f9;
}

.list-group-hover .list-group-item-success, .list-group-hover a.list-group-item-success, .list-group-hover button.list-group-item-success {
  color: #fff;
}

.list-group-hover .list-group-item-success:focus, .list-group-hover .list-group-item-success:hover, .list-group-hover a.list-group-item-success:focus, .list-group-hover a.list-group-item-success:hover, .list-group-hover button.list-group-item-success:focus, .list-group-hover button.list-group-item-success:hover {
  color: #fff;
  background-color: #0fab60;
}

.list-group-hover .list-group-item-success.active, .list-group-hover a.list-group-item-success.active, .list-group-hover button.list-group-item-success.active {
  color: #fff;
  background-color: #fff;
  border-color: #fff;
}

.list-group-hover .list-group-item-info, .list-group-hover a.list-group-item-info, .list-group-hover button.list-group-item-info {
  color: #fff;
}

.list-group-hover .list-group-item-info:focus, .list-group-hover .list-group-item-info:hover, .list-group-hover a.list-group-item-info:focus, .list-group-hover a.list-group-item-info:hover, .list-group-hover button.list-group-item-info:focus, .list-group-hover button.list-group-item-info:hover {
  color: #fff;
  background-color: #0a9ebc;
}

.list-group-hover .list-group-item-info.active, .list-group-hover a.list-group-item-info.active, .list-group-hover button.list-group-item-info.active {
  color: #fff;
  background-color: #fff;
  border-color: #fff;
}

.list-group-hover .list-group-item-warning, .list-group-hover a.list-group-item-warning, .list-group-hover button.list-group-item-warning {
  color: #fff;
}

.list-group-hover .list-group-item-warning:focus, .list-group-hover .list-group-item-warning:hover, .list-group-hover a.list-group-item-warning:focus, .list-group-hover a.list-group-item-warning:hover, .list-group-hover button.list-group-item-warning:focus, .list-group-hover button.list-group-item-warning:hover {
  color: #fff;
  background-color: #d25c08;
}

.list-group-hover .list-group-item-warning.active, .list-group-hover a.list-group-item-warning.active, .list-group-hover button.list-group-item-warning.active {
  color: #fff;
  background-color: #fff;
  border-color: #fff;
}

.list-group-hover .list-group-item-danger, .list-group-hover a.list-group-item-danger, .list-group-hover button.list-group-item-danger {
  color: #fff;
}

.list-group-hover .list-group-item-danger:focus, .list-group-hover .list-group-item-danger:hover, .list-group-hover a.list-group-item-danger:focus, .list-group-hover a.list-group-item-danger:hover, .list-group-hover button.list-group-item-danger:focus, .list-group-hover button.list-group-item-danger:hover {
  color: #fff;
  background-color: #ff3339;
}

.list-group-hover .list-group-item-danger.active, .list-group-hover a.list-group-item-danger.active, .list-group-hover button.list-group-item-danger.active {
  color: #fff;
  background-color: #fff;
  border-color: #fff;
}

.list-group-hover .list-group-item-dark, .list-group-hover a.list-group-item-dark, .list-group-hover button.list-group-item-dark {
  color: #fff;
}

.list-group-hover .list-group-item-dark:focus, .list-group-hover .list-group-item-dark:hover, .list-group-hover a.list-group-item-dark:focus, .list-group-hover a.list-group-item-dark:hover, .list-group-hover button.list-group-item-dark:focus, .list-group-hover button.list-group-item-dark:hover {
  color: #fff;
  background-color: #47535b;
}

.list-group-hover .list-group-item-dark.active, .list-group-hover a.list-group-item-dark.active, .list-group-hover button.list-group-item-dark.active {
  color: #fff;
  background-color: #fff;
  border-color: #fff;
}

.card {
  border: none;
  box-shadow: none;
}

.card .cover {
  border-bottom: none;
}

.card-header, .card-footer {
  border: none;
  box-shadow: none;
}

.card-inverse.card-bordered, .card-inverse .card-header-bordered, .card-inverse .card-footer-bordered {
  border-color: rgba(255, 255, 255, .2);
}

.card-inverse .card-header, .card-inverse .card-footer, .card-inverse .card-title, .card-inverse .card-blockquote {
  color: #fff;
}

.card-inverse .card-link, .card-inverse .card-text, .card-inverse .card-blockquote > footer {
  color: rgba(255, 255, 255, .65);
}

.card-inverse .card-link:focus, .card-inverse .card-link:hover {
  color: #fff;
}

.card-bordered {
  border: 1px solid #e4eaec;
}

.card-header-bordered {
  border-bottom: 1px solid #e4eaec;
}

.card-footer-bordered {
  border-top: 1px solid #e4eaec;
}

.card-transparent {
  background-color: transparent;
}

.card-header-transparent {
  background-color: transparent;
}

.card-footer-transparent {
  background-color: transparent;
}

.card-shadow {
  box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
}

.card-text.type-link a {
  color: #a3afb7;
}

.card-text.type-link a:hover {
  color: #ccd5db;
}

.card-text.type-link a + a:before {
  background-color: #a3afb7;
}

.card-watermark.darker {
  color: black;
}

.card-watermark.lighter {
  color: white;
}

.card-divider:after {
  background-color: #fff;
}

.card-actions a {
  color: #a3afb7;
}

.card-actions a.active, .card-actions a:hover, .card-actions a:focus {
  color: #ccd5db;
}

.card-actions-sidebar a {
  border-right: 1px solid #e4eaec;
}

.card-actions-sidebar a + a {
  border-top: 1px solid #e4eaec;
}

.card-default {
  background-color: #e4eaec;
}

.card-default.card-bordered, .card-default .card-header-bordered, .card-default .card-footer-bordered {
  border-color: #e4eaec;
}

.modal-content {
  border: none;
}

.modal-header {
  border-bottom: none;
}

.modal-footer {
  border-top: none;
}

.modal-sidebar {
  background-color: #fff;
}

.modal-sidebar .modal-content {
  background-color: transparent;
  box-shadow: none;
}

.modal-sidebar .modal-header {
  border-bottom: none;
}

.modal-fill-in {
  background-color: transparent;
}

.modal-fill-in.show {
  background-color: rgba(255, 255, 255, .95);
}

.modal-fill-in .modal-content {
  background-color: transparent;
  box-shadow: none;
}

.modal-primary .modal-header {
  background-color: #997b71;
  border-radius: .286rem .286rem 0 0;
}

.modal-primary .modal-header * {
  color: #fff;
}

.modal-primary .modal-header .close {
  opacity: .6;
}

.modal-success .modal-header {
  background-color: #11c26d;
  border-radius: .286rem .286rem 0 0;
}

.modal-success .modal-header * {
  color: #fff;
}

.modal-success .modal-header .close {
  opacity: .6;
}

.modal-info .modal-header {
  background-color: #0bb2d4;
  border-radius: .286rem .286rem 0 0;
}

.modal-info .modal-header * {
  color: #fff;
}

.modal-info .modal-header .close {
  opacity: .6;
}

.modal-warning .modal-header {
  background-color: #eb6709;
  border-radius: .286rem .286rem 0 0;
}

.modal-warning .modal-header * {
  color: #fff;
}

.modal-warning .modal-header .close {
  opacity: .6;
}

.modal-danger .modal-header {
  background-color: #ff4c52;
  border-radius: .286rem .286rem 0 0;
}

.modal-danger .modal-header * {
  color: #fff;
}

.modal-danger .modal-header .close {
  opacity: .6;
}

.modal.modal-just-me .modal-backdrop {
  background-color: #fff;
}

.modal.modal-just-me.show {
  background: #fff;
}

.tooltip-primary .tooltip-inner {
  color: #fff;
  background-color: #997b71;
}

.tooltip-primary.bs-tooltip-top .arrow::before, .tooltip-primary.tooltip-top .arrow::before {
  border-top-color: #997b71;
}

.tooltip-primary.bs-tooltip-right .arrow::before, .tooltip-primary.tooltip-right .arrow::before {
  border-right-color: #997b71;
}

.tooltip-primary.bs-tooltip-bottom .arrow::before, .tooltip-primary.tooltip-bottom .arrow::before {
  border-bottom-color: #997b71;
}

.tooltip-primary.bs-tooltip-left .arrow::before, .tooltip-primary.tooltip-left .arrow::before {
  border-left-color: #997b71;
}

.tooltip-success .tooltip-inner {
  color: #fff;
  background-color: #11c26d;
}

.tooltip-success.bs-tooltip-top .arrow::before, .tooltip-success.tooltip-top .arrow::before {
  border-top-color: #11c26d;
}

.tooltip-success.bs-tooltip-right .arrow::before, .tooltip-success.tooltip-right .arrow::before {
  border-right-color: #11c26d;
}

.tooltip-success.bs-tooltip-bottom .arrow::before, .tooltip-success.tooltip-bottom .arrow::before {
  border-bottom-color: #11c26d;
}

.tooltip-success.bs-tooltip-left .arrow::before, .tooltip-success.tooltip-left .arrow::before {
  border-left-color: #11c26d;
}

.tooltip-info .tooltip-inner {
  color: #fff;
  background-color: #0bb2d4;
}

.tooltip-info.bs-tooltip-top .arrow::before, .tooltip-info.tooltip-top .arrow::before {
  border-top-color: #0bb2d4;
}

.tooltip-info.bs-tooltip-right .arrow::before, .tooltip-info.tooltip-right .arrow::before {
  border-right-color: #0bb2d4;
}

.tooltip-info.bs-tooltip-bottom .arrow::before, .tooltip-info.tooltip-bottom .arrow::before {
  border-bottom-color: #0bb2d4;
}

.tooltip-info.bs-tooltip-left .arrow::before, .tooltip-info.tooltip-left .arrow::before {
  border-left-color: #0bb2d4;
}

.tooltip-warning .tooltip-inner {
  color: #fff;
  background-color: #eb6709;
}

.tooltip-warning.bs-tooltip-top .arrow::before, .tooltip-warning.tooltip-top .arrow::before {
  border-top-color: #eb6709;
}

.tooltip-warning.bs-tooltip-right .arrow::before, .tooltip-warning.tooltip-right .arrow::before {
  border-right-color: #eb6709;
}

.tooltip-warning.bs-tooltip-bottom .arrow::before, .tooltip-warning.tooltip-bottom .arrow::before {
  border-bottom-color: #eb6709;
}

.tooltip-warning.bs-tooltip-left .arrow::before, .tooltip-warning.tooltip-left .arrow::before {
  border-left-color: #eb6709;
}

.tooltip-danger .tooltip-inner {
  color: #fff;
  background-color: #ff4c52;
}

.tooltip-danger.bs-tooltip-top .arrow::before, .tooltip-danger.tooltip-top .arrow::before {
  border-top-color: #ff4c52;
}

.tooltip-danger.bs-tooltip-right .arrow::before, .tooltip-danger.tooltip-right .arrow::before {
  border-right-color: #ff4c52;
}

.tooltip-danger.bs-tooltip-bottom .arrow::before, .tooltip-danger.tooltip-bottom .arrow::before {
  border-bottom-color: #ff4c52;
}

.tooltip-danger.bs-tooltip-left .arrow::before, .tooltip-danger.tooltip-left .arrow::before {
  border-left-color: #ff4c52;
}

.popover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, .05);
}

.popover-primary .popover-header {
  color: #fff;
  background-color: #997b71;
  border-color: #997b71;
}

.popover-primary.bs-popover-bottom .popover-header::before {
  border-color: #997b71;
}

.popover-primary.bs-popover-bottom .arrow::before, .popover-primary.popover-bottom .arrow::before {
  border-bottom-color: #997b71;
}

.popover-primary.bs-popover-bottom .arrow::after, .popover-primary.popover-bottom .arrow::after {
  border-bottom-color: #997b71;
}

.popover-success .popover-header {
  color: #fff;
  background-color: #11c26d;
  border-color: #11c26d;
}

.popover-success.bs-popover-bottom .popover-header::before {
  border-color: #11c26d;
}

.popover-success.bs-popover-bottom .arrow::before, .popover-success.popover-bottom .arrow::before {
  border-bottom-color: #11c26d;
}

.popover-success.bs-popover-bottom .arrow::after, .popover-success.popover-bottom .arrow::after {
  border-bottom-color: #11c26d;
}

.popover-info .popover-header {
  color: #fff;
  background-color: #0bb2d4;
  border-color: #0bb2d4;
}

.popover-info.bs-popover-bottom .popover-header::before {
  border-color: #0bb2d4;
}

.popover-info.bs-popover-bottom .arrow::before, .popover-info.popover-bottom .arrow::before {
  border-bottom-color: #0bb2d4;
}

.popover-info.bs-popover-bottom .arrow::after, .popover-info.popover-bottom .arrow::after {
  border-bottom-color: #0bb2d4;
}

.popover-warning .popover-header {
  color: #fff;
  background-color: #eb6709;
  border-color: #eb6709;
}

.popover-warning.bs-popover-bottom .popover-header::before {
  border-color: #eb6709;
}

.popover-warning.bs-popover-bottom .arrow::before, .popover-warning.popover-bottom .arrow::before {
  border-bottom-color: #eb6709;
}

.popover-warning.bs-popover-bottom .arrow::after, .popover-warning.popover-bottom .arrow::after {
  border-bottom-color: #eb6709;
}

.popover-danger .popover-header {
  color: #fff;
  background-color: #ff4c52;
  border-color: #ff4c52;
}

.popover-danger.bs-popover-bottom .popover-header::before {
  border-color: #ff4c52;
}

.popover-danger.bs-popover-bottom .arrow::before, .popover-danger.popover-bottom .arrow::before {
  border-bottom-color: #ff4c52;
}

.popover-danger.bs-popover-bottom .arrow::after, .popover-danger.popover-bottom .arrow::after {
  border-bottom-color: #ff4c52;
}

.carousel-caption h1, .carousel-caption h2, .carousel-caption h3, .carousel-caption h4, .carousel-caption h5, .carousel-caption h6 {
  color: inherit;
}

.carousel-indicators li {
  background-color: rgba(255, 255, 255, .3);
}

.carousel-indicators-scaleup li {
  border: none;
  transition: background-color .3s ease 0s, -webkit-transform .3s ease 0s;
  transition: transform .3s ease 0s, background-color .3s ease 0s;
  transition: transform .3s ease 0s, background-color .3s ease 0s, -webkit-transform .3s ease 0s;
}

.carousel-indicators-fillin li {
  background-color: transparent;
  box-shadow: 0 0 0 2px #fff inset;
}

.carousel-indicators-fillin .active {
  box-shadow: 0 0 0 8px #fff inset;
}

.carousel-indicators-fall li {
  transition: background-color .3s ease 0s, -webkit-transform .3s ease 0s;
  transition: transform .3s ease 0s, background-color .3s ease 0s;
  transition: transform .3s ease 0s, background-color .3s ease 0s, -webkit-transform .3s ease 0s;
}

.carousel-indicators-fall li:after {
  background-color: rgba(0, 0, 0, .3);
}

.carousel-indicators-fall .active {
  background-color: transparent;
}

.site-navbar.navbar-inverse .navbar-header .hamburger:before, .site-navbar.navbar-inverse .navbar-header .hamburger:after, .site-navbar.navbar-inverse .navbar-header .hamburger .hamburger-bar {
  background-color: #fff;
}

.site-menubar {
  color: rgba(163, 175, 183, .9);
  background: #263238;
}

.site-menubar.site-menubar-light {
  color: rgba(118, 131, 143, .9);
  background: #fff;
}

@media (max-width: 767px) {
  .site-menubar {
    color: rgba(163, 175, 183, .9);
    background: #263238;
  }
}

.css-menubar .site-menu-item:hover {
  background-color: rgba(0, 0, 0, .06);
}

.css-menubar .site-menu-item:hover > a {
  color: #fff;
}

.css-menubar .site-menu-item:hover > a:hover {
  background-color: transparent;
}

.css-menubar .site-menu-item.active:hover > a {
  background-color: transparent;
}

.css-menubar .site-menu-section:hover {
  background-color: transparent;
}

.css-menubar .site-menubar-light .site-menu-item:hover {
  background-color: rgba(107, 83, 76, .03);
}

.css-menubar .site-menubar-light .site-menu-item:hover > a {
  color: rgba(107, 83, 76, .9);
}

.css-menubar .site-menubar-light .site-menu-item:hover > a:hover {
  background-color: transparent;
}

.css-menubar .site-menubar-light .site-menu-item.active:hover > a {
  background-color: transparent;
}

.css-menubar .site-menubar-light .site-menu-section:hover {
  background-color: transparent;
}

.site-menu .dropdown-menu {
  background-color: #263238;
}

.site-menu-item.has-sub > a:focus {
  background-color: transparent;
}

.site-menu-item > a {
  color: inherit;
}

.site-menu-item > a:hover {
  color: rgba(255, 255, 255, .8);
  background-color: rgba(255, 255, 255, .02);
}

.site-menu-item.open {
  background-color: rgba(0, 0, 0, .06);
}

.site-menu-item.open > a {
  color: #fff;
}

.site-menu-item.open > a:hover {
  background-color: transparent;
}

.site-menu-item.active > a {
  color: #fff;
  background-color: rgba(0, 0, 0, .06);
}

.site-menu-item.active.open > a {
  background-color: transparent;
}

.site-menu-section > header {
  border-bottom-color: rgba(0, 0, 0, .06);
}

.site-menu-section.open {
  background-color: transparent;
}

.site-menubar-light .site-menu .dropdown-menu {
  background-color: #fff;
}

.site-menubar-light .site-menu-item > a {
  color: inherit;
}

.site-menubar-light .site-menu-item > a:hover {
  color: rgba(118, 131, 143, .9);
  background-color: rgba(107, 83, 76, .05);
}

.site-menubar-light .site-menu-item.open {
  background-color: rgba(107, 83, 76, .03);
}

.site-menubar-light .site-menu-item.open > a {
  color: rgba(107, 83, 76, .9);
}

.site-menubar-light .site-menu-item.open > a:hover {
  background-color: transparent;
}

.site-menubar-light .site-menu-item.active > a {
  color: rgba(107, 83, 76, .9);
  background-color: rgba(107, 83, 76, .03);
}

.site-menubar-light .site-menu-item.active.open > a {
  background-color: transparent;
}

.site-menubar-light .site-menu-section > header {
  border-bottom-color: rgba(0, 0, 0, .06);
}

.site-menubar-light .site-menu-section.open {
  background-color: transparent;
}

@media (max-width: 767px) {
  .site-menu-section > header:hover {
    color: rgba(255, 255, 255, .8);
    background-color: rgba(255, 255, 255, .02);
  }

  .site-menu-section.open {
    background-color: rgba(0, 0, 0, .06);
  }

  .site-menu-section.open > header {
    color: #fff;
  }

  .site-menu-section.open > header:hover {
    background-color: transparent;
  }

  .site-menu-section.active > header {
    color: #fff;
    background-color: rgba(0, 0, 0, .06);
  }

  .site-menu-section.active.open > header {
    background-color: transparent;
  }

  .site-menubar-light .site-menu .dropdown-menu {
    background-color: transparent;
  }

  .site-menubar-light .site-menu-section > header:hover {
    color: rgba(118, 131, 143, .9);
    background-color: rgba(107, 83, 76, .05);
  }

  .site-menubar-light .site-menu-section.open {
    background-color: rgba(107, 83, 76, .03);
  }

  .site-menubar-light .site-menu-section.open > header {
    color: rgba(107, 83, 76, .9);
  }

  .site-menubar-light .site-menu-section.open > header:hover {
    background-color: transparent;
  }

  .site-menubar-light .site-menu-section.active > header {
    color: rgba(107, 83, 76, .9);
    background-color: rgba(107, 83, 76, .03);
  }

  .site-menubar-light .site-menu-section.active.open > header {
    background-color: transparent;
  }
}

.site-sidebar .conversation-header {
  border-bottom: 1px solid #e4eaec;
}

.site-sidebar .conversation-more, .site-sidebar .conversation-return {
  color: rgba(55, 71, 79, .4);
}

.site-sidebar .conversation-more:hover, .site-sidebar .conversation-more:focus, .site-sidebar .conversation-return:hover, .site-sidebar .conversation-return:focus {
  color: rgba(55, 71, 79, .6);
}

.site-sidebar .conversation-more:active, .site-sidebar .conversation-return:active {
  color: #37474f;
}

.site-sidebar .conversation-title {
  color: #37474f;
}

.site-sidebar .conversation-reply {
  border-top: 1px solid #e4eaec;
}

.site-sidebar .conversation-reply .form-control {
  border-right: 1px solid #e4eaec;
}

.page {
  background: #f1f4f5;
}

.page-dark.layout-full {
  color: #fff;
}

.page-dark.layout-full:after {
  background-color: rgba(38, 50, 56, .6);
}

.page-dark.layout-full .brand-text {
  color: #fff;
}

.page-content-table .table > tbody > tr:hover > td {
  background-color: #f3f7f9;
}

.page-content-table .table > tbody > tr:last-child td {
  border-bottom: 1px solid #e4eaec;
}

.page-copyright {
  color: #37474f;
}

.page-copyright .social .icon {
  color: rgba(55, 71, 79, .6);
}

.page-copyright .social .icon:hover, .page-copyright .social .icon:focus {
  color: rgba(55, 71, 79, .8);
}

.page-copyright .social .icon.active, .page-copyright .social .icon:active {
  color: #37474f;
}

.page-copyright-inverse {
  color: #fff;
}

.page-copyright-inverse .social .icon {
  color: #fff;
}

.page-copyright-inverse .social .icon:hover, .page-copyright-inverse .social .icon:active {
  color: rgba(255, 255, 255, .8);
}

.page-description {
  color: #a3afb7;
}

.page-header {
  background: transparent;
}

.page-header-bordered {
  background-color: #fff;
  border-bottom: 1px solid transparent;
}

.page-aside {
  background: #fff;
  border-right: 1px solid #e4eaec;
}

.page-aside-right .page-aside .page-aside-inner {
  border-left: 1px solid #e4eaec;
}

.page-aside-section:after {
  border-bottom: 1px solid #e4eaec;
}

.page-aside-title {
  color: #526069;
}

.page-aside .list-group-item .icon {
  color: #a3afb7;
}

.page-aside .list-group-item:hover, .page-aside .list-group-item:focus {
  color: #997b71;
  background-color: #f3f7f9;
}

.page-aside .list-group-item:hover > .icon, .page-aside .list-group-item:focus > .icon {
  color: #997b71;
}

.page-aside .list-group-item.active {
  color: #997b71;
}

.page-aside .list-group-item.active > .icon {
  color: #997b71;
}

.page-aside .list-group-item.active:hover, .page-aside .list-group-item.active:focus {
  color: #997b71;
  background-color: #f3f7f9;
}

.page-aside .list-group-item.active:hover > .icon, .page-aside .list-group-item.active:focus > .icon {
  color: #997b71;
}

.page-aside .list-group.has-actions .list-group-item .item-actions .btn-icon:hover .icon {
  color: #997b71;
}

.page-aside .list-group.has-actions .list-group-item:hover .item-actions .icon {
  color: #76838f;
}

@media (max-width: 767px) {
  .page-aside .page-aside-inner {
    border-right: 1px solid #e4eaec;
  }
}

.site-footer {
  background-color: rgba(0, 0, 0, .02);
  border-top: 1px solid #e4eaec;
}

.site-footer .scroll-to-top {
  color: #76838f;
}

@media (min-width: 1200px) {
  .layout-boxed {
    background: #e4eaec;
  }
}

.site-print {
  padding-top: 0;
}

.site-print .site-navbar, .site-print .site-menubar, .site-print .site-gridmenu, .site-print .site-footer {
  display: none;
}

.site-print .page {
  margin: 0 !important;
}

@media (max-width: 767px) {
  .site-gridmenu {
    background: rgba(38, 50, 56, .9);
  }
}

.checkbox-custom label::before {
  background-color: #fff;
  border: 1px solid #e4eaec;
}

.checkbox-custom label::after {
  color: #76838f;
}

.checkbox-custom input[type="checkbox"]:checked + label::before, .checkbox-custom input[type="radio"]:checked + label::before {
  border-color: #e4eaec;
}

.checkbox-custom input[type="checkbox"]:disabled + label::before, .checkbox-custom input[type="radio"]:disabled + label::before {
  background-color: #f3f7f9;
  border-color: #e4eaec;
}

.checkbox-default input[type="checkbox"]:checked + label::before, .checkbox-default input[type="radio"]:checked + label::before {
  background-color: #fff;
  border-color: #e4eaec;
}

.checkbox-default input[type="checkbox"]:checked + label::after, .checkbox-default input[type="radio"]:checked + label::after {
  color: #997b71;
}

.checkbox-primary input[type="checkbox"]:checked + label::before, .checkbox-primary input[type="radio"]:checked + label::before {
  background-color: #997b71;
  border-color: #997b71;
}

.checkbox-primary input[type="checkbox"]:checked + label::after, .checkbox-primary input[type="radio"]:checked + label::after {
  color: #fff;
}

.checkbox-danger input[type="checkbox"]:checked + label::before, .checkbox-danger input[type="radio"]:checked + label::before {
  background-color: #ff4c52;
  border-color: #ff4c52;
}

.checkbox-danger input[type="checkbox"]:checked + label::after, .checkbox-danger input[type="radio"]:checked + label::after {
  color: #fff;
}

.checkbox-info input[type="checkbox"]:checked + label::before, .checkbox-info input[type="radio"]:checked + label::before {
  background-color: #0bb2d4;
  border-color: #0bb2d4;
}

.checkbox-info input[type="checkbox"]:checked + label::after, .checkbox-info input[type="radio"]:checked + label::after {
  color: #fff;
}

.checkbox-warning input[type="checkbox"]:checked + label::before, .checkbox-warning input[type="radio"]:checked + label::before {
  background-color: #eb6709;
  border-color: #eb6709;
}

.checkbox-warning input[type="checkbox"]:checked + label::after, .checkbox-warning input[type="radio"]:checked + label::after {
  color: #fff;
}

.checkbox-success input[type="checkbox"]:checked + label::before, .checkbox-success input[type="radio"]:checked + label::before {
  background-color: #11c26d;
  border-color: #11c26d;
}

.checkbox-success input[type="checkbox"]:checked + label::after, .checkbox-success input[type="radio"]:checked + label::after {
  color: #fff;
}

.radio-default input[type="radio"]:checked + label::before {
  background-color: #fff;
  border-color: #e4eaec;
}

.radio-default input[type="radio"]:checked + label::after {
  border-color: #997b71;
}

.radio-primary input[type="radio"]:checked + label::before {
  border-color: #997b71;
}

.radio-primary input[type="radio"]:checked + label::after {
  border-color: #fff;
}

.radio-danger input[type="radio"]:checked + label::before {
  border-color: #ff4c52;
}

.radio-danger input[type="radio"]:checked + label::after {
  border-color: #fff;
}

.radio-info input[type="radio"]:checked + label::before {
  border-color: #0bb2d4;
}

.radio-info input[type="radio"]:checked + label::after {
  border-color: #fff;
}

.radio-warning input[type="radio"]:checked + label::before {
  border-color: #eb6709;
}

.radio-warning input[type="radio"]:checked + label::after {
  border-color: #fff;
}

.radio-success input[type="radio"]:checked + label::before {
  border-color: #11c26d;
}

.radio-success input[type="radio"]:checked + label::after {
  border-color: #fff;
}

.form-material .form-control {
  background-color: transparent;
  box-shadow: none;
}

.form-material .form-control, .form-material .form-control:focus, .form-material .form-control.focus {
  background-image: linear-gradient(#997b71, #997b71), linear-gradient(#e4eaec, #e4eaec);
}

.no-cssgradients .form-material .form-control {
  border-bottom: 2px solid #e4eaec;
}

.form-material .form-control::-webkit-input-placeholder {
  color: #a3afb7;
}

.form-material .form-control::-moz-placeholder {
  color: #a3afb7;
}

.form-material .form-control:-ms-input-placeholder {
  color: #a3afb7;
}

.form-material .form-control:disabled::-webkit-input-placeholder {
  color: #ccd5db;
}

.form-material .form-control:disabled::-moz-placeholder {
  color: #ccd5db;
}

.form-material .form-control:disabled:-ms-input-placeholder {
  color: #ccd5db;
}

.no-cssgradients .form-material .form-control:focus, .no-cssgradients .form-material .form-control.focus {
  border-bottom: 2px solid #997b71;
}

.form-material .form-control:disabled, .form-material .form-control[disabled], fieldset[disabled] .form-material .form-control {
  border-bottom: 1px dashed #ccd5db;
}

.form-material .form-control:disabled ~ .floating-label, .form-material .form-control[disabled] ~ .floating-label, fieldset[disabled] .form-material .form-control ~ .floating-label {
  color: #ccd5db;
}

.form-material .floating-label {
  color: #76838f;
}

.form-material .form-control:focus ~ .floating-label, .form-material .form-control.focus ~ .floating-label {
  color: #997b71;
}

.form-material .form-control:not(.empty):invalid ~ .floating-label, .form-material .form-control.focus:invalid ~ .floating-label {
  color: #ff4c52;
}

.form-material .form-control:invalid {
  background-image: linear-gradient(#ff4c52, #ff4c52), linear-gradient(#e4eaec, #e4eaec);
}

.form-material.form-group.has-warning .form-control:focus, .form-material.form-group.has-warning .form-control.focus, .form-material.form-group.has-warning .form-control:not(.empty) {
  background-image: linear-gradient(#eb6709, #eb6709), linear-gradient(#e4eaec, #e4eaec);
}

.no-cssgradients .form-material.form-group.has-warning .form-control:focus, .no-cssgradients .form-material.form-group.has-warning .form-control.focus, .no-cssgradients .form-material.form-group.has-warning .form-control:not(.empty) {
  background: transparent;
  border-bottom: 2px solid #eb6709;
}

.form-material.form-group.has-warning .form-control:-webkit-autofill {
  background-image: linear-gradient(#eb6709, #eb6709), linear-gradient(#e4eaec, #e4eaec);
}

.no-cssgradients .form-material.form-group.has-warning .form-control:-webkit-autofill {
  background: transparent;
  border-bottom: 2px solid #eb6709;
}

.form-material.form-group.has-warning .form-control:not(.empty) {
  background-size: 100% 2px, 100% 1px;
}

.form-material.form-group.has-warning .form-control-label {
  color: #eb6709;
}

.form-material.form-group.has-warning .form-control:focus ~ .floating-label, .form-material.form-group.has-warning .form-control.focus ~ .floating-label, .form-material.form-group.has-warning .form-control:not(.empty) ~ .floating-label {
  color: #eb6709;
}

.form-material.form-group.has-warning .form-control:-webkit-autofill ~ .floating-label {
  color: #eb6709;
}

.form-material.form-group.has-danger .form-control:focus, .form-material.form-group.has-danger .form-control.focus, .form-material.form-group.has-danger .form-control:not(.empty) {
  background-image: linear-gradient(#ff4c52, #ff4c52), linear-gradient(#e4eaec, #e4eaec);
}

.no-cssgradients .form-material.form-group.has-danger .form-control:focus, .no-cssgradients .form-material.form-group.has-danger .form-control.focus, .no-cssgradients .form-material.form-group.has-danger .form-control:not(.empty) {
  background: transparent;
  border-bottom: 2px solid #ff4c52;
}

.form-material.form-group.has-danger .form-control:-webkit-autofill {
  background-image: linear-gradient(#ff4c52, #ff4c52), linear-gradient(#e4eaec, #e4eaec);
}

.no-cssgradients .form-material.form-group.has-danger .form-control:-webkit-autofill {
  background: transparent;
  border-bottom: 2px solid #ff4c52;
}

.form-material.form-group.has-danger .form-control:not(.empty) {
  background-size: 100% 2px, 100% 1px;
}

.form-material.form-group.has-danger .form-control-label {
  color: #ff4c52;
}

.form-material.form-group.has-danger .form-control:focus ~ .floating-label, .form-material.form-group.has-danger .form-control.focus ~ .floating-label, .form-material.form-group.has-danger .form-control:not(.empty) ~ .floating-label {
  color: #ff4c52;
}

.form-material.form-group.has-danger .form-control:-webkit-autofill ~ .floating-label {
  color: #ff4c52;
}

.form-material.form-group.has-success .form-control:focus, .form-material.form-group.has-success .form-control.focus, .form-material.form-group.has-success .form-control:not(.empty) {
  background-image: linear-gradient(#11c26d, #11c26d), linear-gradient(#e4eaec, #e4eaec);
}

.no-cssgradients .form-material.form-group.has-success .form-control:focus, .no-cssgradients .form-material.form-group.has-success .form-control.focus, .no-cssgradients .form-material.form-group.has-success .form-control:not(.empty) {
  background: transparent;
  border-bottom: 2px solid #11c26d;
}

.form-material.form-group.has-success .form-control:-webkit-autofill {
  background-image: linear-gradient(#11c26d, #11c26d), linear-gradient(#e4eaec, #e4eaec);
}

.no-cssgradients .form-material.form-group.has-success .form-control:-webkit-autofill {
  background: transparent;
  border-bottom: 2px solid #11c26d;
}

.form-material.form-group.has-success .form-control:not(.empty) {
  background-size: 100% 2px, 100% 1px;
}

.form-material.form-group.has-success .form-control-label {
  color: #11c26d;
}

.form-material.form-group.has-success .form-control:focus ~ .floating-label, .form-material.form-group.has-success .form-control.focus ~ .floating-label, .form-material.form-group.has-success .form-control:not(.empty) ~ .floating-label {
  color: #11c26d;
}

.form-material.form-group.has-success .form-control:-webkit-autofill ~ .floating-label {
  color: #11c26d;
}

.form-material.form-group.has-info .form-control:focus, .form-material.form-group.has-info .form-control.focus, .form-material.form-group.has-info .form-control:not(.empty) {
  background-image: linear-gradient(#0bb2d4, #0bb2d4), linear-gradient(#e4eaec, #e4eaec);
}

.no-cssgradients .form-material.form-group.has-info .form-control:focus, .no-cssgradients .form-material.form-group.has-info .form-control.focus, .no-cssgradients .form-material.form-group.has-info .form-control:not(.empty) {
  background: transparent;
  border-bottom: 2px solid #0bb2d4;
}

.form-material.form-group.has-info .form-control:-webkit-autofill {
  background-image: linear-gradient(#0bb2d4, #0bb2d4), linear-gradient(#e4eaec, #e4eaec);
}

.no-cssgradients .form-material.form-group.has-info .form-control:-webkit-autofill {
  background: transparent;
  border-bottom: 2px solid #0bb2d4;
}

.form-material.form-group.has-info .form-control:not(.empty) {
  background-size: 100% 2px, 100% 1px;
}

.form-material.form-group.has-info .form-control-label {
  color: #0bb2d4;
}

.form-material.form-group.has-info .form-control:focus ~ .floating-label, .form-material.form-group.has-info .form-control.focus ~ .floating-label, .form-material.form-group.has-info .form-control:not(.empty) ~ .floating-label {
  color: #0bb2d4;
}

.form-material.form-group.has-info .form-control:-webkit-autofill ~ .floating-label {
  color: #0bb2d4;
}

.loader-default {
  background-color: #a3afb7;
}

.loader-grill {
  background: #a3afb7;
}

.loader-grill:before, .loader-grill:after {
  background: #a3afb7;
}

.loader-circle {
  border-top: .125em solid rgba(163, 175, 183, .5);
  border-right: .125em solid rgba(163, 175, 183, .5);
  border-bottom: .125em solid rgba(163, 175, 183, .5);
  border-left: .125em solid #a3afb7;
}

.loader-dot:before, .loader-dot:after {
  background: #a3afb7;
}

.loader-bounce:before, .loader-bounce:after {
  background: #a3afb7;
}

.loader-cube:before, .loader-cube:after {
  background: #a3afb7;
}

.loader-rotate-plane {
  background: #a3afb7;
}

.loader-folding-cube:before, .loader-folding-cube:after {
  background: #a3afb7;
}

.loader-overlay {
  background: #997b71;
}

.loader-content h2 {
  color: #fff;
}

.loader-index > div {
  background: #fff;
}

@-webkit-keyframes default-grill {
  0%, 80%, 100% {
    box-shadow: 0 0 #a3afb7;
  }

  40% {
    box-shadow: 0 -0.25em #a3afb7;
  }
}

@keyframes default-grill {
  0%, 80%, 100% {
    box-shadow: 0 0 #a3afb7;
  }

  40% {
    box-shadow: 0 -0.25em #a3afb7;
  }
}

@-webkit-keyframes loader-round-circle {
  0%, 100% {
    box-shadow: 0 -3em 0 .2em #a3afb7, 2em -2em 0 0 #a3afb7, 3em 0 0 -0.5em #a3afb7, 2em 2em 0 -0.5em #a3afb7, 0 3em 0 -0.5em #a3afb7, -2em 2em 0 -0.5em #a3afb7, -3em 0 0 -0.5em #a3afb7, -2em -2em 0 0 #a3afb7;
  }

  12.5% {
    box-shadow: 0 -3em 0 0 #a3afb7, 2em -2em 0 .2em #a3afb7, 3em 0 0 0 #a3afb7, 2em 2em 0 -0.5em #a3afb7, 0 3em 0 -0.5em #a3afb7, -2em 2em 0 -0.5em #a3afb7, -3em 0 0 -0.5em #a3afb7, -2em -2em 0 -0.5em #a3afb7;
  }

  25% {
    box-shadow: 0 -3em 0 -0.5em #a3afb7, 2em -2em 0 0 #a3afb7, 3em 0 0 .2em #a3afb7, 2em 2em 0 0 #a3afb7, 0 3em 0 -0.5em #a3afb7, -2em 2em 0 -0.5em #a3afb7, -3em 0 0 -0.5em #a3afb7, -2em -2em 0 -0.5em #a3afb7;
  }

  37.5% {
    box-shadow: 0 -3em 0 -0.5em #a3afb7, 2em -2em 0 -0.5em #a3afb7, 3em 0 0 0 #a3afb7, 2em 2em 0 .2em #a3afb7, 0 3em 0 0 #a3afb7, -2em 2em 0 -0.5em #a3afb7, -3em 0 0 -0.5em #a3afb7, -2em -2em 0 -0.5em #a3afb7;
  }

  50% {
    box-shadow: 0 -3em 0 -0.5em #a3afb7, 2em -2em 0 -0.5em #a3afb7, 3em 0 0 -0.5em #a3afb7, 2em 2em 0 0 #a3afb7, 0 3em 0 .2em #a3afb7, -2em 2em 0 0 #a3afb7, -3em 0 0 -0.5em #a3afb7, -2em -2em 0 -0.5em #a3afb7;
  }

  62.5% {
    box-shadow: 0 -3em 0 -0.5em #a3afb7, 2em -2em 0 -0.5em #a3afb7, 3em 0 0 -0.5em #a3afb7, 2em 2em 0 -0.5em #a3afb7, 0 3em 0 0 #a3afb7, -2em 2em 0 .2em #a3afb7, -3em 0 0 0 #a3afb7, -2em -2em 0 -0.5em #a3afb7;
  }

  75% {
    box-shadow: 0 -3em 0 -0.5em #a3afb7, 2em -2em 0 -0.5em #a3afb7, 3em 0 0 -0.5em #a3afb7, 2em 2em 0 -0.5em #a3afb7, 0 3em 0 -0.5em #a3afb7, -2em 2em 0 0 #a3afb7, -3em 0 0 .2em #a3afb7, -2em -2em 0 0 #a3afb7;
  }

  87.5% {
    box-shadow: 0 -3em 0 0 #a3afb7, 2em -2em 0 -0.5em #a3afb7, 3em 0 0 -0.5em #a3afb7, 2em 2em 0 -0.5em #a3afb7, 0 3em 0 -0.5em #a3afb7, -2em 2em 0 0 #a3afb7, -3em 0 0 0 #a3afb7, -2em -2em 0 .2em #a3afb7;
  }
}

@keyframes loader-round-circle {
  0%, 100% {
    box-shadow: 0 -3em 0 .2em #a3afb7, 2em -2em 0 0 #a3afb7, 3em 0 0 -0.5em #a3afb7, 2em 2em 0 -0.5em #a3afb7, 0 3em 0 -0.5em #a3afb7, -2em 2em 0 -0.5em #a3afb7, -3em 0 0 -0.5em #a3afb7, -2em -2em 0 0 #a3afb7;
  }

  12.5% {
    box-shadow: 0 -3em 0 0 #a3afb7, 2em -2em 0 .2em #a3afb7, 3em 0 0 0 #a3afb7, 2em 2em 0 -0.5em #a3afb7, 0 3em 0 -0.5em #a3afb7, -2em 2em 0 -0.5em #a3afb7, -3em 0 0 -0.5em #a3afb7, -2em -2em 0 -0.5em #a3afb7;
  }

  25% {
    box-shadow: 0 -3em 0 -0.5em #a3afb7, 2em -2em 0 0 #a3afb7, 3em 0 0 .2em #a3afb7, 2em 2em 0 0 #a3afb7, 0 3em 0 -0.5em #a3afb7, -2em 2em 0 -0.5em #a3afb7, -3em 0 0 -0.5em #a3afb7, -2em -2em 0 -0.5em #a3afb7;
  }

  37.5% {
    box-shadow: 0 -3em 0 -0.5em #a3afb7, 2em -2em 0 -0.5em #a3afb7, 3em 0 0 0 #a3afb7, 2em 2em 0 .2em #a3afb7, 0 3em 0 0 #a3afb7, -2em 2em 0 -0.5em #a3afb7, -3em 0 0 -0.5em #a3afb7, -2em -2em 0 -0.5em #a3afb7;
  }

  50% {
    box-shadow: 0 -3em 0 -0.5em #a3afb7, 2em -2em 0 -0.5em #a3afb7, 3em 0 0 -0.5em #a3afb7, 2em 2em 0 0 #a3afb7, 0 3em 0 .2em #a3afb7, -2em 2em 0 0 #a3afb7, -3em 0 0 -0.5em #a3afb7, -2em -2em 0 -0.5em #a3afb7;
  }

  62.5% {
    box-shadow: 0 -3em 0 -0.5em #a3afb7, 2em -2em 0 -0.5em #a3afb7, 3em 0 0 -0.5em #a3afb7, 2em 2em 0 -0.5em #a3afb7, 0 3em 0 0 #a3afb7, -2em 2em 0 .2em #a3afb7, -3em 0 0 0 #a3afb7, -2em -2em 0 -0.5em #a3afb7;
  }

  75% {
    box-shadow: 0 -3em 0 -0.5em #a3afb7, 2em -2em 0 -0.5em #a3afb7, 3em 0 0 -0.5em #a3afb7, 2em 2em 0 -0.5em #a3afb7, 0 3em 0 -0.5em #a3afb7, -2em 2em 0 0 #a3afb7, -3em 0 0 .2em #a3afb7, -2em -2em 0 0 #a3afb7;
  }

  87.5% {
    box-shadow: 0 -3em 0 0 #a3afb7, 2em -2em 0 -0.5em #a3afb7, 3em 0 0 -0.5em #a3afb7, 2em 2em 0 -0.5em #a3afb7, 0 3em 0 -0.5em #a3afb7, -2em 2em 0 0 #a3afb7, -3em 0 0 0 #a3afb7, -2em -2em 0 .2em #a3afb7;
  }
}

@-webkit-keyframes loader-tadpole {
  0% {
    box-shadow: 0 -0.83em 0 -0.4em #a3afb7, 0 -0.83em 0 -0.42em #a3afb7, 0 -0.83em 0 -0.44em #a3afb7, 0 -0.83em 0 -0.46em #a3afb7, 0 -0.83em 0 -0.477em #a3afb7;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  5%, 95% {
    box-shadow: 0 -0.83em 0 -0.4em #a3afb7, 0 -0.83em 0 -0.42em #a3afb7, 0 -0.83em 0 -0.44em #a3afb7, 0 -0.83em 0 -0.46em #a3afb7, 0 -0.83em 0 -0.477em #a3afb7;
  }

  10%, 59% {
    box-shadow: 0 -0.83em 0 -0.4em #a3afb7, -0.087em -0.825em 0 -0.42em #a3afb7, -0.173em -0.812em 0 -0.44em #a3afb7, -0.256em -0.789em 0 -0.46em #a3afb7, -0.297em -0.775em 0 -0.477em #a3afb7;
  }

  20% {
    box-shadow: 0 -0.83em 0 -0.4em #a3afb7, -0.338em -0.758em 0 -0.42em #a3afb7, -0.555em -0.617em 0 -0.44em #a3afb7, -0.671em -0.488em 0 -0.46em #a3afb7, -0.749em -0.34em 0 -0.477em #a3afb7;
  }

  38% {
    box-shadow: 0 -0.83em 0 -0.4em #a3afb7, -0.377em -0.74em 0 -0.42em #a3afb7, -0.645em -0.522em 0 -0.44em #a3afb7, -0.775em -0.297em 0 -0.46em #a3afb7, -0.82em -0.09em 0 -0.477em #a3afb7;
  }

  100% {
    box-shadow: 0 -0.83em 0 -0.4em #a3afb7, 0 -0.83em 0 -0.42em #a3afb7, 0 -0.83em 0 -0.44em #a3afb7, 0 -0.83em 0 -0.46em #a3afb7, 0 -0.83em 0 -0.477em #a3afb7;
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loader-tadpole {
  0% {
    box-shadow: 0 -0.83em 0 -0.4em #a3afb7, 0 -0.83em 0 -0.42em #a3afb7, 0 -0.83em 0 -0.44em #a3afb7, 0 -0.83em 0 -0.46em #a3afb7, 0 -0.83em 0 -0.477em #a3afb7;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  5%, 95% {
    box-shadow: 0 -0.83em 0 -0.4em #a3afb7, 0 -0.83em 0 -0.42em #a3afb7, 0 -0.83em 0 -0.44em #a3afb7, 0 -0.83em 0 -0.46em #a3afb7, 0 -0.83em 0 -0.477em #a3afb7;
  }

  10%, 59% {
    box-shadow: 0 -0.83em 0 -0.4em #a3afb7, -0.087em -0.825em 0 -0.42em #a3afb7, -0.173em -0.812em 0 -0.44em #a3afb7, -0.256em -0.789em 0 -0.46em #a3afb7, -0.297em -0.775em 0 -0.477em #a3afb7;
  }

  20% {
    box-shadow: 0 -0.83em 0 -0.4em #a3afb7, -0.338em -0.758em 0 -0.42em #a3afb7, -0.555em -0.617em 0 -0.44em #a3afb7, -0.671em -0.488em 0 -0.46em #a3afb7, -0.749em -0.34em 0 -0.477em #a3afb7;
  }

  38% {
    box-shadow: 0 -0.83em 0 -0.4em #a3afb7, -0.377em -0.74em 0 -0.42em #a3afb7, -0.645em -0.522em 0 -0.44em #a3afb7, -0.775em -0.297em 0 -0.46em #a3afb7, -0.82em -0.09em 0 -0.477em #a3afb7;
  }

  100% {
    box-shadow: 0 -0.83em 0 -0.4em #a3afb7, 0 -0.83em 0 -0.42em #a3afb7, 0 -0.83em 0 -0.44em #a3afb7, 0 -0.83em 0 -0.46em #a3afb7, 0 -0.83em 0 -0.477em #a3afb7;
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-webkit-keyframes loader-ellipsis {
  0%, 80%, 100% {
    box-shadow: 0 .625em 0 -0.325em #a3afb7;
  }

  40% {
    box-shadow: 0 .625em 0 0 #a3afb7;
  }
}

@keyframes loader-ellipsis {
  0%, 80%, 100% {
    box-shadow: 0 .625em 0 -0.325em #a3afb7;
  }

  40% {
    box-shadow: 0 .625em 0 0 #a3afb7;
  }
}

.bg-red-100 {
  background-color: #ffdbdc !important;
}

.bg-red-200 {
  background-color: #ffbfc1 !important;
}

.bg-red-300 {
  background-color: #ffa1a4 !important;
}

.bg-red-400 {
  background-color: #ff8589 !important;
}

.bg-red-500 {
  background-color: #ff666b !important;
}

.bg-red-600 {
  background-color: #ff4c52 !important;
}

.bg-red-700 {
  background-color: #f2353c !important;
}

.bg-red-800 {
  background-color: #e62020 !important;
}

.bg-red-900 {
  background-color: #d60b0b !important;
}

.red-100 {
  color: #ffdbdc !important;
}

.red-200 {
  color: #ffbfc1 !important;
}

.red-300 {
  color: #ffa1a4 !important;
}

.red-400 {
  color: #ff8589 !important;
}

.red-500 {
  color: #ff666b !important;
}

.red-600 {
  color: #ff4c52 !important;
}

.red-700 {
  color: #f2353c !important;
}

.red-800 {
  color: #e62020 !important;
}

.red-900 {
  color: #d60b0b !important;
}

.bg-pink-100 {
  background-color: #ffd9e6 !important;
}

.bg-pink-200 {
  background-color: #ffbad2 !important;
}

.bg-pink-300 {
  background-color: #ff9ec0 !important;
}

.bg-pink-400 {
  background-color: #ff7daa !important;
}

.bg-pink-500 {
  background-color: #ff5e97 !important;
}

.bg-pink-600 {
  background-color: #f74584 !important;
}

.bg-pink-700 {
  background-color: #eb2f71 !important;
}

.bg-pink-800 {
  background-color: #e6155e !important;
}

.bg-pink-900 {
  background-color: #d10049 !important;
}

.pink-100 {
  color: #ffd9e6 !important;
}

.pink-200 {
  color: #ffbad2 !important;
}

.pink-300 {
  color: #ff9ec0 !important;
}

.pink-400 {
  color: #ff7daa !important;
}

.pink-500 {
  color: #ff5e97 !important;
}

.pink-600 {
  color: #f74584 !important;
}

.pink-700 {
  color: #eb2f71 !important;
}

.pink-800 {
  color: #e6155e !important;
}

.pink-900 {
  color: #d10049 !important;
}

.bg-purple-100 {
  background-color: #eae1fc !important;
}

.bg-purple-200 {
  background-color: #d9c7fc !important;
}

.bg-purple-300 {
  background-color: #c8aefc !important;
}

.bg-purple-400 {
  background-color: #b693fa !important;
}

.bg-purple-500 {
  background-color: #a57afa !important;
}

.bg-purple-600 {
  background-color: #9463f7 !important;
}

.bg-purple-700 {
  background-color: #8349f5 !important;
}

.bg-purple-800 {
  background-color: #7231f5 !important;
}

.bg-purple-900 {
  background-color: #6118f2 !important;
}

.purple-100 {
  color: #eae1fc !important;
}

.purple-200 {
  color: #d9c7fc !important;
}

.purple-300 {
  color: #c8aefc !important;
}

.purple-400 {
  color: #b693fa !important;
}

.purple-500 {
  color: #a57afa !important;
}

.purple-600 {
  color: #9463f7 !important;
}

.purple-700 {
  color: #8349f5 !important;
}

.purple-800 {
  color: #7231f5 !important;
}

.purple-900 {
  color: #6118f2 !important;
}

.bg-indigo-100 {
  background-color: #e1e4fc !important;
}

.bg-indigo-200 {
  background-color: #c7cffc !important;
}

.bg-indigo-300 {
  background-color: #afb9fa !important;
}

.bg-indigo-400 {
  background-color: #96a3fa !important;
}

.bg-indigo-500 {
  background-color: #7d8efa !important;
}

.bg-indigo-600 {
  background-color: #667afa !important;
}

.bg-indigo-700 {
  background-color: #4d64fa !important;
}

.bg-indigo-800 {
  background-color: #364ff5 !important;
}

.bg-indigo-900 {
  background-color: #1f3aed !important;
}

.indigo-100 {
  color: #e1e4fc !important;
}

.indigo-200 {
  color: #c7cffc !important;
}

.indigo-300 {
  color: #afb9fa !important;
}

.indigo-400 {
  color: #96a3fa !important;
}

.indigo-500 {
  color: #7d8efa !important;
}

.indigo-600 {
  color: #667afa !important;
}

.indigo-700 {
  color: #4d64fa !important;
}

.indigo-800 {
  color: #364ff5 !important;
}

.indigo-900 {
  color: #1f3aed !important;
}

.bg-blue-100 {
  background-color: #d9e9ff !important;
}

.bg-blue-200 {
  background-color: #b8d7ff !important;
}

.bg-blue-300 {
  background-color: #99c5ff !important;
}

.bg-blue-400 {
  background-color: #79b2fc !important;
}

.bg-blue-500 {
  background-color: #589ffc !important;
}

.bg-blue-600 {
  background-color: #3e8ef7 !important;
}

.bg-blue-700 {
  background-color: #247cf0 !important;
}

.bg-blue-800 {
  background-color: #0b69e3 !important;
}

.bg-blue-900 {
  background-color: #0053bf !important;
}

.blue-100 {
  color: #d9e9ff !important;
}

.blue-200 {
  color: #b8d7ff !important;
}

.blue-300 {
  color: #99c5ff !important;
}

.blue-400 {
  color: #79b2fc !important;
}

.blue-500 {
  color: #589ffc !important;
}

.blue-600 {
  color: #3e8ef7 !important;
}

.blue-700 {
  color: #247cf0 !important;
}

.blue-800 {
  color: #0b69e3 !important;
}

.blue-900 {
  color: #0053bf !important;
}

.bg-cyan-100 {
  background-color: #c2f5ff !important;
}

.bg-cyan-200 {
  background-color: #9de6f5 !important;
}

.bg-cyan-300 {
  background-color: #77d9ed !important;
}

.bg-cyan-400 {
  background-color: #54cbe3 !important;
}

.bg-cyan-500 {
  background-color: #28c0de !important;
}

.bg-cyan-600 {
  background-color: #0bb2d4 !important;
}

.bg-cyan-700 {
  background-color: #0099b8 !important;
}

.bg-cyan-800 {
  background-color: #007d96 !important;
}

.bg-cyan-900 {
  background-color: #006275 !important;
}

.cyan-100 {
  color: #c2f5ff !important;
}

.cyan-200 {
  color: #9de6f5 !important;
}

.cyan-300 {
  color: #77d9ed !important;
}

.cyan-400 {
  color: #54cbe3 !important;
}

.cyan-500 {
  color: #28c0de !important;
}

.cyan-600 {
  color: #0bb2d4 !important;
}

.cyan-700 {
  color: #0099b8 !important;
}

.cyan-800 {
  color: #007d96 !important;
}

.cyan-900 {
  color: #006275 !important;
}

.bg-teal-100 {
  background-color: #c3f7f2 !important;
}

.bg-teal-200 {
  background-color: #92f0e6 !important;
}

.bg-teal-300 {
  background-color: #6be3d7 !important;
}

.bg-teal-400 {
  background-color: #45d6c8 !important;
}

.bg-teal-500 {
  background-color: #28c7b7 !important;
}

.bg-teal-600 {
  background-color: #17b3a3 !important;
}

.bg-teal-700 {
  background-color: #089e8f !important;
}

.bg-teal-800 {
  background-color: #008577 !important;
}

.bg-teal-900 {
  background-color: #00665c !important;
}

.teal-100 {
  color: #c3f7f2 !important;
}

.teal-200 {
  color: #92f0e6 !important;
}

.teal-300 {
  color: #6be3d7 !important;
}

.teal-400 {
  color: #45d6c8 !important;
}

.teal-500 {
  color: #28c7b7 !important;
}

.teal-600 {
  color: #17b3a3 !important;
}

.teal-700 {
  color: #089e8f !important;
}

.teal-800 {
  color: #008577 !important;
}

.teal-900 {
  color: #00665c !important;
}

.bg-green-100 {
  background-color: #c2fadc !important;
}

.bg-green-200 {
  background-color: #99f2c2 !important;
}

.bg-green-300 {
  background-color: #72e8ab !important;
}

.bg-green-400 {
  background-color: #49de94 !important;
}

.bg-green-500 {
  background-color: #28d17c !important;
}

.bg-green-600 {
  background-color: #11c26d !important;
}

.bg-green-700 {
  background-color: #05a85c !important;
}

.bg-green-800 {
  background-color: #008c4d !important;
}

.bg-green-900 {
  background-color: #006e3c !important;
}

.green-100 {
  color: #c2fadc !important;
}

.green-200 {
  color: #99f2c2 !important;
}

.green-300 {
  color: #72e8ab !important;
}

.green-400 {
  color: #49de94 !important;
}

.green-500 {
  color: #28d17c !important;
}

.green-600 {
  color: #11c26d !important;
}

.green-700 {
  color: #05a85c !important;
}

.green-800 {
  color: #008c4d !important;
}

.green-900 {
  color: #006e3c !important;
}

.bg-light-green-100 {
  background-color: #dcf7b0 !important;
}

.bg-light-green-200 {
  background-color: #c3e887 !important;
}

.bg-light-green-300 {
  background-color: #add966 !important;
}

.bg-light-green-400 {
  background-color: #94cc39 !important;
}

.bg-light-green-500 {
  background-color: #7eb524 !important;
}

.bg-light-green-600 {
  background-color: #6da611 !important;
}

.bg-light-green-700 {
  background-color: #5a9101 !important;
}

.bg-light-green-800 {
  background-color: #4a7800 !important;
}

.bg-light-green-900 {
  background-color: #3a5e00 !important;
}

.light-green-100 {
  color: #dcf7b0 !important;
}

.light-green-200 {
  color: #c3e887 !important;
}

.light-green-300 {
  color: #add966 !important;
}

.light-green-400 {
  color: #94cc39 !important;
}

.light-green-500 {
  color: #7eb524 !important;
}

.light-green-600 {
  color: #6da611 !important;
}

.light-green-700 {
  color: #5a9101 !important;
}

.light-green-800 {
  color: #4a7800 !important;
}

.light-green-900 {
  color: #3a5e00 !important;
}

.bg-yellow-100 {
  background-color: #fff6b5 !important;
}

.bg-yellow-200 {
  background-color: #fff39c !important;
}

.bg-yellow-300 {
  background-color: #ffed78 !important;
}

.bg-yellow-400 {
  background-color: #ffe54f !important;
}

.bg-yellow-500 {
  background-color: #ffdc2e !important;
}

.bg-yellow-600 {
  background-color: #ffcd17 !important;
}

.bg-yellow-700 {
  background-color: #fcb900 !important;
}

.bg-yellow-800 {
  background-color: #faa700 !important;
}

.bg-yellow-900 {
  background-color: #fa9600 !important;
}

.yellow-100 {
  color: #fff6b5 !important;
}

.yellow-200 {
  color: #fff39c !important;
}

.yellow-300 {
  color: #ffed78 !important;
}

.yellow-400 {
  color: #ffe54f !important;
}

.yellow-500 {
  color: #ffdc2e !important;
}

.yellow-600 {
  color: #ffcd17 !important;
}

.yellow-700 {
  color: #fcb900 !important;
}

.yellow-800 {
  color: #faa700 !important;
}

.yellow-900 {
  color: #fa9600 !important;
}

.bg-orange-100 {
  background-color: #ffe1c4 !important;
}

.bg-orange-200 {
  background-color: #ffc894 !important;
}

.bg-orange-300 {
  background-color: #fab06b !important;
}

.bg-orange-400 {
  background-color: #fa983c !important;
}

.bg-orange-500 {
  background-color: #f57d1b !important;
}

.bg-orange-600 {
  background-color: #eb6709 !important;
}

.bg-orange-700 {
  background-color: #de4e00 !important;
}

.bg-orange-800 {
  background-color: #b53f00 !important;
}

.bg-orange-900 {
  background-color: #962d00 !important;
}

.orange-100 {
  color: #ffe1c4 !important;
}

.orange-200 {
  color: #ffc894 !important;
}

.orange-300 {
  color: #fab06b !important;
}

.orange-400 {
  color: #fa983c !important;
}

.orange-500 {
  color: #f57d1b !important;
}

.orange-600 {
  color: #eb6709 !important;
}

.orange-700 {
  color: #de4e00 !important;
}

.orange-800 {
  color: #b53f00 !important;
}

.orange-900 {
  color: #962d00 !important;
}

.bg-brown-100 {
  background-color: #f5e2da !important;
}

.bg-brown-200 {
  background-color: #e0cdc5 !important;
}

.bg-brown-300 {
  background-color: #cfb8b0 !important;
}

.bg-brown-400 {
  background-color: #bda299 !important;
}

.bg-brown-500 {
  background-color: #ab8c82 !important;
}

.bg-brown-600 {
  background-color: #997b71 !important;
}

.bg-brown-700 {
  background-color: #82675f !important;
}

.bg-brown-800 {
  background-color: #6b534c !important;
}

.bg-brown-900 {
  background-color: #57403a !important;
}

.brown-100 {
  color: #f5e2da !important;
}

.brown-200 {
  color: #e0cdc5 !important;
}

.brown-300 {
  color: #cfb8b0 !important;
}

.brown-400 {
  color: #bda299 !important;
}

.brown-500 {
  color: #ab8c82 !important;
}

.brown-600 {
  color: #997b71 !important;
}

.brown-700 {
  color: #82675f !important;
}

.brown-800 {
  color: #6b534c !important;
}

.brown-900 {
  color: #57403a !important;
}

.bg-grey-100 {
  background-color: #fafafa !important;
}

.bg-grey-200 {
  background-color: #eee !important;
}

.bg-grey-300 {
  background-color: #e0e0e0 !important;
}

.bg-grey-400 {
  background-color: #bdbdbd !important;
}

.bg-grey-500 {
  background-color: #9e9e9e !important;
}

.bg-grey-600 {
  background-color: #757575 !important;
}

.bg-grey-700 {
  background-color: #616161 !important;
}

.bg-grey-800 {
  background-color: #424242 !important;
}

.bg-grey-900 {
  background-color: #474747 !important;
}

.grey-100 {
  color: #fafafa !important;
}

.grey-200 {
  color: #eee !important;
}

.grey-300 {
  color: #e0e0e0 !important;
}

.grey-400 {
  color: #bdbdbd !important;
}

.grey-500 {
  color: #9e9e9e !important;
}

.grey-600 {
  color: #757575 !important;
}

.grey-700 {
  color: #616161 !important;
}

.grey-800 {
  color: #424242 !important;
}

.grey-900 {
  color: #474747 !important;
}

.bg-blue-grey-100 {
  background-color: #f3f7f9 !important;
}

.bg-blue-grey-200 {
  background-color: #e4eaec !important;
}

.bg-blue-grey-300 {
  background-color: #ccd5db !important;
}

.bg-blue-grey-400 {
  background-color: #a3afb7 !important;
}

.bg-blue-grey-500 {
  background-color: #76838f !important;
}

.bg-blue-grey-600 {
  background-color: #526069 !important;
}

.bg-blue-grey-700 {
  background-color: #37474f !important;
}

.bg-blue-grey-800 {
  background-color: #263238 !important;
}

.bg-blue-grey-900 {
  background-color: #3e4854 !important;
}

.blue-grey-100 {
  color: #f3f7f9 !important;
}

.blue-grey-200 {
  color: #e4eaec !important;
}

.blue-grey-300 {
  color: #ccd5db !important;
}

.blue-grey-400 {
  color: #a3afb7 !important;
}

.blue-grey-500 {
  color: #76838f !important;
}

.blue-grey-600 {
  color: #526069 !important;
}

.blue-grey-700 {
  color: #37474f !important;
}

.blue-grey-800 {
  color: #263238 !important;
}

.blue-grey-900 {
  color: #3e4854 !important;
}

.bg-primary-100 {
  background-color: #f5e2da !important;
}

.bg-primary-200 {
  background-color: #e0cdc5 !important;
}

.bg-primary-300 {
  background-color: #cfb8b0 !important;
}

.bg-primary-400 {
  background-color: #bda299 !important;
}

.bg-primary-500 {
  background-color: #ab8c82 !important;
}

.bg-primary-600 {
  background-color: #997b71 !important;
}

.bg-primary-700 {
  background-color: #82675f !important;
}

.bg-primary-800 {
  background-color: #6b534c !important;
}

.primary-100 {
  color: #f5e2da !important;
}

.primary-200 {
  color: #e0cdc5 !important;
}

.primary-300 {
  color: #cfb8b0 !important;
}

.primary-400 {
  color: #bda299 !important;
}

.primary-500 {
  color: #ab8c82 !important;
}

.primary-600 {
  color: #997b71 !important;
}

.primary-700 {
  color: #82675f !important;
}

.primary-800 {
  color: #6b534c !important;
}

.black {
  color: #000 !important;
}

.white {
  color: #fff !important;
}

.bg-white {
  color: #76838f;
  background-color: #fff;
}

.bg-primary {
  color: #fff;
  background-color: #997b71;
}

.bg-primary:hover {
  background-color: #ae978f;
}

.bg-primary a, .bg-primary a.bg-primary {
  color: #fff;
}

.bg-primary a:hover, .bg-primary a.bg-primary:hover {
  color: #fff;
}

.bg-success {
  color: #fff;
  background-color: #11c26d;
}

.bg-success:hover {
  background-color: #1beb87;
}

.bg-success a, .bg-success a.bg-primary {
  color: #fff;
}

.bg-success a:hover, .bg-success a.bg-primary:hover {
  color: #fff;
}

.bg-info {
  color: #fff;
  background-color: #0bb2d4;
}

.bg-info:hover {
  background-color: #1fcff3;
}

.bg-info a, .bg-info a.bg-info {
  color: #fff;
}

.bg-info a:hover, .bg-info a.bg-info:hover {
  color: #fff;
}

.bg-warning {
  color: #fff;
  background-color: #eb6709;
}

.bg-warning:hover {
  background-color: #f78330;
}

.bg-warning a, .bg-warning a.bg-warning {
  color: #fff;
}

.bg-warning a:hover, .bg-warning a.bg-warning:hover {
  color: #fff;
}

.bg-danger {
  color: #fff;
  background-color: #ff4c52;
}

.bg-danger:hover {
  background-color: #ff7f83;
}

.bg-danger a, .bg-danger a.bg-danger {
  color: #fff;
}

.bg-danger a:hover, .bg-danger a.bg-danger:hover {
  color: #fff;
}

.bg-dark {
  color: #fff;
  background-color: #526069;
}

.bg-dark:hover {
  background-color: #687a86;
}

.bg-dark a, .bg-dark a.bg-dark {
  color: #fff;
}

.bg-dark a:hover, .bg-dark a.bg-dark:hover {
  color: #fff;
}

.social-facebook {
  color: #fff;
  background-color: #3b5998 !important;
}

.social-facebook:hover, .social-facebook:focus {
  color: #fff;
  background-color: #4c70ba !important;
}

.social-facebook:active, .social-facebook.active {
  color: #fff;
  background-color: #2d4373 !important;
}

.bg-facebook {
  background-color: #3b5998;
}

.social-twitter {
  color: #fff;
  background-color: #55acee !important;
}

.social-twitter:hover, .social-twitter:focus {
  color: #fff;
  background-color: #83c3f3 !important;
}

.social-twitter:active, .social-twitter.active {
  color: #fff;
  background-color: #2795e9 !important;
}

.bg-twitter {
  background-color: #55acee;
}

.social-google-plus {
  color: #fff;
  background-color: #dd4b39 !important;
}

.social-google-plus:hover, .social-google-plus:focus {
  color: #fff;
  background-color: #e47365 !important;
}

.social-google-plus:active, .social-google-plus.active {
  color: #fff;
  background-color: #c23321 !important;
}

.bg-google-plus {
  background-color: #dd4b39;
}

.social-linkedin {
  color: #fff;
  background-color: #0976b4 !important;
}

.social-linkedin:hover, .social-linkedin:focus {
  color: #fff;
  background-color: #0b96e5 !important;
}

.social-linkedin:active, .social-linkedin.active {
  color: #fff;
  background-color: #075683 !important;
}

.bg-linkedin {
  background-color: #0976b4;
}

.social-flickr {
  color: #fff;
  background-color: #ff0084 !important;
}

.social-flickr:hover, .social-flickr:focus {
  color: #fff;
  background-color: #ff339d !important;
}

.social-flickr:active, .social-flickr.active {
  color: #fff;
  background-color: #cc006a !important;
}

.bg-flickr {
  background-color: #ff0084;
}

.social-tumblr {
  color: #fff;
  background-color: #35465c !important;
}

.social-tumblr:hover, .social-tumblr:focus {
  color: #fff;
  background-color: #485f7c !important;
}

.social-tumblr:active, .social-tumblr.active {
  color: #fff;
  background-color: #222d3c !important;
}

.bg-tumblr {
  background-color: #35465c;
}

.social-xing {
  color: #fff;
  background-color: #024b4d !important;
}

.social-xing:hover, .social-xing:focus {
  color: #fff;
  background-color: #037b7f !important;
}

.social-xing:active, .social-xing.active {
  color: #fff;
  background-color: #011b1b !important;
}

.bg-xing {
  background-color: #024b4d;
}

.social-github {
  color: #fff;
  background-color: #4183c4 !important;
}

.social-github:hover, .social-github:focus {
  color: #fff;
  background-color: #689cd0 !important;
}

.social-github:active, .social-github.active {
  color: #fff;
  background-color: #3269a0 !important;
}

.bg-github {
  background-color: #4183c4;
}

.social-html5 {
  color: #fff;
  background-color: #e44f26 !important;
}

.social-html5:hover, .social-html5:focus {
  color: #fff;
  background-color: #ea7453 !important;
}

.social-html5:active, .social-html5.active {
  color: #fff;
  background-color: #bf3c18 !important;
}

.bg-html5 {
  background-color: #e44f26;
}

.social-openid {
  color: #fff;
  background-color: #f67d28 !important;
}

.social-openid:hover, .social-openid:focus {
  color: #fff;
  background-color: #f89b59 !important;
}

.social-openid:active, .social-openid.active {
  color: #fff;
  background-color: #e26309 !important;
}

.bg-openid {
  background-color: #f67d28;
}

.social-stack-overflow {
  color: #fff;
  background-color: #f86c01 !important;
}

.social-stack-overflow:hover, .social-stack-overflow:focus {
  color: #fff;
  background-color: #fe882e !important;
}

.social-stack-overflow:active, .social-stack-overflow.active {
  color: #fff;
  background-color: #c55601 !important;
}

.bg-stack-overflow {
  background-color: #f86c01;
}

.social-css3 {
  color: #fff;
  background-color: #1572b6 !important;
}

.social-css3:hover, .social-css3:focus {
  color: #fff;
  background-color: #1a8fe4 !important;
}

.social-css3:active, .social-css3.active {
  color: #fff;
  background-color: #105588 !important;
}

.bg-css3 {
  background-color: #1572b6;
}

.social-youtube {
  color: #fff;
  background-color: #b31217 !important;
}

.social-youtube:hover, .social-youtube:focus {
  color: #fff;
  background-color: #e1171d !important;
}

.social-youtube:active, .social-youtube.active {
  color: #fff;
  background-color: #850d11 !important;
}

.bg-youtube {
  background-color: #b31217;
}

.social-dribbble {
  color: #fff;
  background-color: #c32361 !important;
}

.social-dribbble:hover, .social-dribbble:focus {
  color: #fff;
  background-color: #dc3d7b !important;
}

.social-dribbble:active, .social-dribbble.active {
  color: #fff;
  background-color: #981b4b !important;
}

.bg-dribbble {
  background-color: #c32361;
}

.social-instagram {
  color: #fff;
  background-color: #3f729b !important;
}

.social-instagram:hover, .social-instagram:focus {
  color: #fff;
  background-color: #548cb9 !important;
}

.social-instagram:active, .social-instagram.active {
  color: #fff;
  background-color: #305777 !important;
}

.bg-instagram {
  background-color: #3f729b;
}

.social-pinterest {
  color: #fff;
  background-color: #cc2127 !important;
}

.social-pinterest:hover, .social-pinterest:focus {
  color: #fff;
  background-color: #e04046 !important;
}

.social-pinterest:active, .social-pinterest.active {
  color: #fff;
  background-color: #a01a1f !important;
}

.bg-pinterest {
  background-color: #cc2127;
}

.social-vk {
  color: #fff;
  background-color: #3d5a7d !important;
}

.social-vk:hover, .social-vk:focus {
  color: #fff;
  background-color: #4e739f !important;
}

.social-vk:active, .social-vk.active {
  color: #fff;
  background-color: #2c415b !important;
}

.bg-vk {
  background-color: #3d5a7d;
}

.social-yahoo {
  color: #fff;
  background-color: #350178 !important;
}

.social-yahoo:hover, .social-yahoo:focus {
  color: #fff;
  background-color: #4b01ab !important;
}

.social-yahoo:active, .social-yahoo.active {
  color: #fff;
  background-color: #1f0145 !important;
}

.bg-yahoo {
  background-color: #350178;
}

.social-behance {
  color: #fff;
  background-color: #1769ff !important;
}

.social-behance:hover, .social-behance:focus {
  color: #fff;
  background-color: #4a8aff !important;
}

.social-behance:active, .social-behance.active {
  color: #fff;
  background-color: #0050e3 !important;
}

.bg-behance {
  background-color: #024b4d;
}

.social-dropbox {
  color: #fff;
  background-color: #007ee5 !important;
}

.social-dropbox:hover, .social-dropbox:focus {
  color: #fff;
  background-color: #1998ff !important;
}

.social-dropbox:active, .social-dropbox.active {
  color: #fff;
  background-color: #0062b2 !important;
}

.bg-dropbox {
  background-color: #007ee5;
}

.social-reddit {
  color: #fff;
  background-color: #ff4500 !important;
}

.social-reddit:hover, .social-reddit:focus {
  color: #fff;
  background-color: #ff6a33 !important;
}

.social-reddit:active, .social-reddit.active {
  color: #fff;
  background-color: #cc3700 !important;
}

.bg-reddit {
  background-color: #ff4500;
}

.social-spotify {
  color: #fff;
  background-color: #7ab800 !important;
}

.social-spotify:hover, .social-spotify:focus {
  color: #fff;
  background-color: #9ceb00 !important;
}

.social-spotify:active, .social-spotify.active {
  color: #fff;
  background-color: #588500 !important;
}

.bg-spotify {
  background-color: #7ab800;
}

.social-vine {
  color: #fff;
  background-color: #00b488 !important;
}

.social-vine:hover, .social-vine:focus {
  color: #fff;
  background-color: #00e7af !important;
}

.social-vine:active, .social-vine.active {
  color: #fff;
  background-color: #008161 !important;
}

.bg-vine {
  background-color: #00b488;
}

.social-foursquare {
  color: #fff;
  background-color: #0cbadf !important;
}

.social-foursquare:hover, .social-foursquare:focus {
  color: #fff;
  background-color: #2ad0f4 !important;
}

.social-foursquare:active, .social-foursquare.active {
  color: #fff;
  background-color: #0992af !important;
}

.bg-foursquare {
  background-color: #0cbadf;
}

.social-vimeo {
  color: #fff;
  background-color: #1ab7ea !important;
}

.social-vimeo:hover, .social-vimeo:focus {
  color: #fff;
  background-color: #49c6ee !important;
}

.social-vimeo:active, .social-vimeo.active {
  color: #fff;
  background-color: #1295bf !important;
}

.bg-vimeo {
  background-color: #1ab7ea;
}

.social-skype {
  color: #fff;
  background-color: #77bcfd !important;
}

.social-skype:hover, .social-skype:focus {
  color: #fff;
  background-color: #a9d5fe !important;
}

.social-skype:active, .social-skype.active {
  color: #fff;
  background-color: #45a3fc !important;
}

.bg-skype {
  background-color: #77bcfd;
}

.social-evernote {
  color: #fff;
  background-color: #46bf8c !important;
}

.social-evernote:hover, .social-evernote:focus {
  color: #fff;
  background-color: #6ccca4 !important;
}

.social-evernote:active, .social-evernote.active {
  color: #fff;
  background-color: #369c71 !important;
}

.bg-evernote {
  background-color: #46bf8c;
}

.avatar i {
  border: 2px solid #fff;
}

.avatar-online i {
  background-color: #11c26d;
}

.avatar-off i {
  background-color: #526069;
}

.avatar-busy i {
  background-color: #eb6709;
}

.avatar-away i {
  background-color: #ff4c52;
}

.status {
  border: 2px solid #fff;
}

.status-online {
  background-color: #11c26d;
}

.status-off {
  background-color: #526069;
}

.status-busy {
  background-color: #eb6709;
}

.status-away {
  background-color: #ff4c52;
}

.icon-border {
  border: solid .08em #e4eaec;
}

.icon-stack-inverse {
  color: #fff;
}

.icon-color {
  color: rgba(55, 71, 79, .4);
}

.icon-color:hover, .icon-color:focus {
  color: rgba(55, 71, 79, .6);
}

.icon-color.active, .icon-color:active {
  color: #37474f;
}

.icon-color-alt {
  color: rgba(55, 71, 79, .6);
}

.icon-color-alt:hover, .icon-color-alt:focus {
  color: rgba(55, 71, 79, .8);
}

.icon-color-alt.active, .icon-color-alt:active {
  color: #37474f;
}

.hamburger:before, .hamburger .hamburger-bar, .hamburger:after {
  background: #76838f;
}

.navbar-default .hamburger:before ,.navbar-default
  .hamburger .hamburger-bar ,.navbar-default .hamburger:after {
  background: #76838f;
}

.navbar-inverse .hamburger:before ,.navbar-inverse
  .hamburger .hamburger-bar ,.navbar-inverse .hamburger:after {
  background: #fff;
}

.counter > .counter-number, .counter .counter-number-group {
  color: #37474f;
}

.counter-inverse {
  color: #fff;
}

.counter-inverse > .counter-number, .counter-inverse .counter-number-group {
  color: #fff;
}

.counter-inverse .counter-icon {
  color: #fff;
}

.panel {
  background-color: #fff;
}

.panel-heading-tab {
  background-color: #997b71;
}

.panel-heading-tab > .nav-tabs .nav-link.hover, .panel-heading-tab > .nav-tabs .nav-link:hover {
  color: #76838f;
}

.panel-heading-tab > .nav-tabs .nav-link.active, .panel-heading-tab > .nav-tabs .nav-link:active {
  color: #76838f;
  background-color: #fff;
}

.panel-footer {
  background-color: transparent;
}

.table + .panel-footer {
  border-color: #e4eaec;
}

.panel-title {
  color: #37474f;
}

.panel-title small, .panel-title .small {
  color: #76838f;
}

.panel-desc {
  color: #76838f;
}

.panel-actions .panel-action {
  color: #a3afb7;
}

.panel-actions .panel-action:hover {
  color: #526069;
}

.panel-actions .panel-action:active {
  color: #526069;
}

.panel-toolbar {
  background-color: transparent;
  border-top: 1px solid #e4eaec;
  border-bottom: 1px solid #e4eaec;
}

.panel-toolbar .btn {
  color: #a3afb7;
}

.panel-toolbar .btn:hover, .panel-toolbar .btn:active, .panel-toolbar .btn.active {
  color: #76838f;
}

.panel-bordered > .panel-heading {
  border-bottom: 1px solid #e4eaec;
}

.panel-bordered > .panel-footer {
  border-top: 1px solid #e4eaec;
}

.panel > .panel-body + .table, .panel > .panel-body + .table-responsive, .panel > .table + .panel-body, .panel > .table-responsive + .panel-body {
  border-top: 1px solid #e4eaec;
}

.panel > .table > tbody:first-child > tr:first-child th, .panel > .table > tbody:first-child > tr:first-child td {
  border-top: 1px solid #e4eaec;
}

.panel-default .panel-heading {
  color: #76838f;
  background-color: #e4eaec;
}

.panel-default .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #e4eaec;
}

.panel-default .panel-heading .badge-pill {
  color: #e4eaec;
  background-color: #76838f;
}

.panel-default .panel-title {
  color: #76838f;
}

.panel-default .panel-action {
  color: #76838f;
}

.panel-default .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #e4eaec;
}

.panel-default .panel-title {
  color: #37474f;
}

.panel-primary .panel-heading {
  color: #fff;
  background-color: #997b71;
}

.panel-primary .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #997b71;
}

.panel-primary .panel-heading .badge-pill {
  color: #997b71;
  background-color: #fff;
}

.panel-primary .panel-title {
  color: #fff;
}

.panel-primary .panel-action {
  color: #fff;
}

.panel-primary .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #997b71;
}

.panel-success .panel-heading {
  color: #fff;
  background-color: #11c26d;
}

.panel-success .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #0fab46;
}

.panel-success .panel-heading .badge-pill {
  color: #11c26d;
  background-color: #fff;
}

.panel-success .panel-title {
  color: #fff;
}

.panel-success .panel-action {
  color: #fff;
}

.panel-success .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #0fab46;
}

.panel-info .panel-heading {
  color: #fff;
  background-color: #0bb2d4;
}

.panel-info .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #09b2b2;
}

.panel-info .panel-heading .badge-pill {
  color: #0bb2d4;
  background-color: #fff;
}

.panel-info .panel-title {
  color: #fff;
}

.panel-info .panel-action {
  color: #fff;
}

.panel-info .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #09b2b2;
}

.panel-warning .panel-heading {
  color: #fff;
  background-color: #eb6709;
}

.panel-warning .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #dc3d08;
}

.panel-warning .panel-heading .badge-pill {
  color: #eb6709;
  background-color: #fff;
}

.panel-warning .panel-title {
  color: #fff;
}

.panel-warning .panel-action {
  color: #fff;
}

.panel-warning .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #dc3d08;
}

.panel-danger .panel-heading {
  color: #fff;
  background-color: #ff4c52;
}

.panel-danger .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #ff3d64;
}

.panel-danger .panel-heading .badge-pill {
  color: #ff4c52;
  background-color: #fff;
}

.panel-danger .panel-title {
  color: #fff;
}

.panel-danger .panel-action {
  color: #fff;
}

.panel-danger .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #ff3d64;
}

.panel-dark .panel-heading {
  color: #fff;
  background-color: #526069;
}

.panel-dark .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #526069;
}

.panel-dark .panel-heading .badge-pill {
  color: #526069;
  background-color: #fff;
}

.panel-dark .panel-title {
  color: #fff;
}

.panel-dark .panel-action {
  color: #fff;
}

.panel-dark .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #526069;
}

.panel-line.panel-default .panel-heading {
  color: #e4eaec;
  background: transparent;
  border-top-color: #e4eaec;
}

.panel-line.panel-default .panel-title {
  color: #e4eaec;
}

.panel-line.panel-default .panel-action {
  color: #e4eaec;
}

.panel-line.panel-default .panel-title {
  color: #37474f;
}

.panel-line.panel-default .panel-action {
  color: #a3afb7;
}

.panel-line.panel-primary .panel-heading {
  color: #997b71;
  background: transparent;
  border-top-color: #997b71;
}

.panel-line.panel-primary .panel-title {
  color: #997b71;
}

.panel-line.panel-primary .panel-action {
  color: #997b71;
}

.panel-line.panel-success .panel-heading {
  color: #11c26d;
  background: transparent;
  border-top-color: #11c26d;
}

.panel-line.panel-success .panel-title {
  color: #11c26d;
}

.panel-line.panel-success .panel-action {
  color: #11c26d;
}

.panel-line.panel-info .panel-heading {
  color: #0bb2d4;
  background: transparent;
  border-top-color: #0bb2d4;
}

.panel-line.panel-info .panel-title {
  color: #0bb2d4;
}

.panel-line.panel-info .panel-action {
  color: #0bb2d4;
}

.panel-line.panel-warning .panel-heading {
  color: #eb6709;
  background: transparent;
  border-top-color: #eb6709;
}

.panel-line.panel-warning .panel-title {
  color: #eb6709;
}

.panel-line.panel-warning .panel-action {
  color: #eb6709;
}

.panel-line.panel-danger .panel-heading {
  color: #ff4c52;
  background: transparent;
  border-top-color: #ff4c52;
}

.panel-line.panel-danger .panel-title {
  color: #ff4c52;
}

.panel-line.panel-danger .panel-action {
  color: #ff4c52;
}

.panel-line.panel-dark .panel-heading {
  color: #526069;
  background: transparent;
  border-top-color: #526069;
}

.panel-line.panel-dark .panel-title {
  color: #526069;
}

.panel-line.panel-dark .panel-action {
  color: #526069;
}

.panel-group .panel-title:hover, .panel-group .panel-title:focus {
  color: #76838f;
}

.panel-group-continuous .panel + .panel {
  border-top: 1px solid #e4eaec;
}

/* ========================================================================
   Component: Overlay
 ========================================================================== */
/* Sub-object `overlay-panel`
 ========================================================================== */
.overlay-panel {
  color: #fff;
}

.overlay-panel a:not([class]) {
  color: inherit;
}

/* Modifier `overlay-background`
 ========================================================================== */
.overlay-background {
  background: rgba(0, 0, 0, .5);
}

/* Sub-object `overlay-icon`
 ========================================================================== */
.overlay-icon .icon {
  color: #fff;
}

.comments .comment {
  border-bottom: 1px solid #e4eaec;
}

.comments .comment .comment:first-child {
  border-top: 1px solid #e4eaec;
}

.comment-author, .comment-author:hover, .comment-author:focus {
  color: #37474f;
}

.comment-meta {
  color: #a3afb7;
}

.chat-box {
  background-color: #fff;
}

.chat-content {
  color: #fff;
  background-color: #997b71;
}

.chat-content:before {
  border-left-color: #997b71;
}

.chat-time {
  color: rgba(255, 255, 255, .6);
}

.chat-left .chat-content {
  color: #76838f;
  background-color: #dfe9ef;
}

.chat-left .chat-content:before {
  border-right-color: #dfe9ef;
}

.chat-left .chat-time {
  color: #a3afb7;
}

.step {
  color: #a3afb7;
  background-color: #f3f7f9;
}

.step-number {
  color: #fff;
  background: #e4eaec;
}

.step-title {
  color: #526069;
}

.step.current {
  color: #fff;
  background-color: #997b71;
}

.step.current .step-title {
  color: #fff;
}

.step.current .step-number {
  color: #997b71;
  background-color: #fff;
}

.step.disabled {
  color: #ccd5db;
}

.step.disabled .step-title {
  color: #ccd5db;
}

.step.disabled .step-number {
  background-color: #ccd5db;
}

.step.error {
  color: #fff;
  background-color: #ff4c52;
}

.step.error .step-title {
  color: #fff;
}

.step.error .step-number {
  color: #ff4c52;
  background-color: #fff;
}

.step.done {
  color: #fff;
  background-color: #11c26d;
}

.step.done .step-title {
  color: #fff;
}

.step.done .step-number {
  color: #11c26d;
  background-color: #fff;
}

.pearl:before, .pearl:after {
  background-color: #f3f7f9;
}

.pearl-number, .pearl-icon {
  color: #fff;
  background: #ccd5db;
  border: 2px solid #ccd5db;
}

.pearl-title {
  color: #526069;
}

.pearl.current:before, .pearl.current:after {
  background-color: #997b71;
}

.pearl.current .pearl-number, .pearl.current .pearl-icon {
  color: #997b71;
  background-color: #fff;
  border-color: #997b71;
}

.pearl.disabled:before, .pearl.disabled:after {
  background-color: #f3f7f9;
}

.pearl.disabled .pearl-number, .pearl.disabled .pearl-icon {
  color: #fff;
  background-color: #ccd5db;
  border-color: #ccd5db;
}

.pearl.error:before {
  background-color: #997b71;
}

.pearl.error:after {
  background-color: #f3f7f9;
}

.pearl.error .pearl-number, .pearl.error .pearl-icon {
  color: #ff4c52;
  background-color: #fff;
  border-color: #ff4c52;
}

.pearl.done:before, .pearl.done:after {
  background-color: #997b71;
}

.pearl.done .pearl-number, .pearl.done .pearl-icon {
  color: #fff;
  background-color: #997b71;
  border-color: #997b71;
}

.timeline:before {
  background-color: #e4eaec;
}

.timeline-period {
  background: #f1f4f5;
}

.timeline-dot {
  color: #fff;
  background-color: #997b71;
}

.timeline-info {
  background: #e4eaec;
  border: 1px solid #e4eaec;
}

.testimonial-content {
  background-color: #f3f7f9;
}

.testimonial-content:before {
  background-color: #f3f7f9;
}

.testimonial-control a {
  color: #ccd5db;
}

.testimonial-control a:hover {
  color: #ab8c82;
}

.pricing-list {
  border: 1px solid #e4eaec;
}

.pricing-list [class^="bg-"], .pricing-list [class^="bg-"] *, .pricing-list [class*="bg-"], .pricing-list [class*="bg-"] * {
  color: #fff;
}

.pricing-list .pricing-header {
  border-bottom: 1px solid #e4eaec;
}

.pricing-list .pricing-price {
  color: #37474f;
}

.pricing-list .pricing-features li {
  border-top: 1px dashed #e4eaec;
}

.pricing-table::after {
  display: block;
  clear: both;
  content: "";
}

.pricing-table [class*="pricing-column"] {
  background-color: #f3f7f9;
  border: 1px solid #e4eaec;
}

.pricing-table [class*="pricing-column"]:last-child {
  border-right: 1px solid #e4eaec;
}

.pricing-table [class*="pricing-column"].featured {
  background-color: #fff;
  border-right: 1px solid #e4eaec;
}

.pricing-table .pricing-header {
  border-bottom: 1px solid #e4eaec;
}

.rating .icon {
  color: #ccd5db;
}

.rating .icon.active {
  color: #eb6709 !important;
}

.rating .icon.active.hover {
  color: #eb6709 !important;
}

.rating .icon.hover {
  color: #eb6709 !important;
}

.ribbon {
  background-color: transparent;
}

.ribbon-inner {
  color: #fff;
  background-color: #526069;
}

.ribbon-bookmark .ribbon-inner {
  background-color: transparent;
  background-image: linear-gradient(to left, transparent 22px, #526069 0);
}

.ribbon-bookmark .ribbon-inner:before {
  border: 15px solid #526069;
  border-right: 10px solid transparent;
}

.ribbon-bookmark.ribbon-vertical .ribbon-inner {
  background-image: linear-gradient(to top, transparent 22px, #526069 0);
}

.ribbon-bookmark.ribbon-vertical .ribbon-inner:before {
  border-right: 15px solid #526069;
  border-bottom: 10px solid transparent;
}

.ribbon-bookmark.ribbon-reverse .ribbon-inner {
  background-image: linear-gradient(to right, transparent 22px, #526069 0);
}

.ribbon-bookmark.ribbon-reverse .ribbon-inner:before {
  border-right: 15px solid #526069;
  border-left: 10px solid transparent;
}

.ribbon-bookmark.ribbon-reverse.ribbon-vertical .ribbon-inner:before {
  border-right-color: #526069;
  border-bottom-color: transparent;
  border-left: 15px solid #526069;
}

.ribbon-corner:before {
  border-color: transparent;
  border-top-color: #526069;
  border-left-color: #526069;
}

.ribbon-corner .ribbon-inner {
  background-color: transparent;
}

.ribbon-corner.ribbon-reverse:before {
  border-right-color: #526069;
  border-left-color: transparent;
}

.ribbon-corner.ribbon-bottom:before {
  border-top-color: transparent;
  border-bottom-color: #526069;
}

.ribbon-clip:before {
  border-color: transparent;
  border-top-color: #37474f;
  border-right-color: #37474f;
}

.ribbon-clip.ribbon-reverse:before {
  border-right-color: transparent;
  border-left-color: #37474f;
}

.ribbon-clip.ribbon-bottom:before {
  border-top-color: transparent;
  border-bottom-color: #37474f;
}

.ribbon-primary .ribbon-inner {
  background-color: #997b71;
}

.ribbon-primary.ribbon-bookmark .ribbon-inner {
  background-color: transparent;
  background-image: linear-gradient(to left, transparent 22px, #997b71 0);
}

.ribbon-primary.ribbon-bookmark .ribbon-inner:before {
  border-color: #997b71;
  border-right-color: transparent;
}

.ribbon-primary.ribbon-bookmark.ribbon-reverse .ribbon-inner {
  background-image: linear-gradient(to right, transparent 22px, #997b71 0);
}

.ribbon-primary.ribbon-bookmark.ribbon-reverse .ribbon-inner:before {
  border-right-color: #997b71;
  border-left-color: transparent;
}

.ribbon-primary.ribbon-bookmark.ribbon-vertical .ribbon-inner {
  background-image: linear-gradient(to top, transparent 22px, #997b71 0);
}

.ribbon-primary.ribbon-bookmark.ribbon-vertical .ribbon-inner:before {
  border-right-color: #997b71;
  border-bottom-color: transparent;
}

.ribbon-primary.ribbon-bookmark.ribbon-vertical.ribbon-reverse .ribbon-inner:before {
  border-right-color: #997b71;
  border-bottom-color: transparent;
  border-left-color: #997b71;
}

.ribbon-primary.ribbon-corner:before {
  border-top-color: #997b71;
  border-left-color: #997b71;
}

.ribbon-primary.ribbon-corner .ribbon-inner {
  background-color: transparent;
}

.ribbon-primary.ribbon-corner.ribbon-reverse:before {
  border-right-color: #997b71;
  border-left-color: transparent;
}

.ribbon-primary.ribbon-corner.ribbon-bottom:before {
  border-top-color: transparent;
  border-bottom-color: #997b71;
}

.ribbon-primary.ribbon-clip:before {
  border-top-color: #82675f;
  border-right-color: #82675f;
}

.ribbon-primary.ribbon-clip.ribbon-reverse:before {
  border-right-color: transparent;
  border-left-color: #82675f;
}

.ribbon-primary.ribbon-clip.ribbon-bottom:before {
  border-top-color: transparent;
  border-bottom-color: #82675f;
}

.ribbon-success .ribbon-inner {
  background-color: #11c26d;
}

.ribbon-success.ribbon-bookmark .ribbon-inner {
  background-color: transparent;
  background-image: linear-gradient(to left, transparent 22px, #11c26d 0);
}

.ribbon-success.ribbon-bookmark .ribbon-inner:before {
  border-color: #11c26d;
  border-right-color: transparent;
}

.ribbon-success.ribbon-bookmark.ribbon-reverse .ribbon-inner {
  background-image: linear-gradient(to right, transparent 22px, #11c26d 0);
}

.ribbon-success.ribbon-bookmark.ribbon-reverse .ribbon-inner:before {
  border-right-color: #11c26d;
  border-left-color: transparent;
}

.ribbon-success.ribbon-bookmark.ribbon-vertical .ribbon-inner {
  background-image: linear-gradient(to top, transparent 22px, #11c26d 0);
}

.ribbon-success.ribbon-bookmark.ribbon-vertical .ribbon-inner:before {
  border-right-color: #11c26d;
  border-bottom-color: transparent;
}

.ribbon-success.ribbon-bookmark.ribbon-vertical.ribbon-reverse .ribbon-inner:before {
  border-right-color: #11c26d;
  border-bottom-color: transparent;
  border-left-color: #11c26d;
}

.ribbon-success.ribbon-corner:before {
  border-top-color: #11c26d;
  border-left-color: #11c26d;
}

.ribbon-success.ribbon-corner .ribbon-inner {
  background-color: transparent;
}

.ribbon-success.ribbon-corner.ribbon-reverse:before {
  border-right-color: #11c26d;
  border-left-color: transparent;
}

.ribbon-success.ribbon-corner.ribbon-bottom:before {
  border-top-color: transparent;
  border-bottom-color: #11c26d;
}

.ribbon-success.ribbon-clip:before {
  border-top-color: #05a85c;
  border-right-color: #05a85c;
}

.ribbon-success.ribbon-clip.ribbon-reverse:before {
  border-right-color: transparent;
  border-left-color: #05a85c;
}

.ribbon-success.ribbon-clip.ribbon-bottom:before {
  border-top-color: transparent;
  border-bottom-color: #05a85c;
}

.ribbon-info .ribbon-inner {
  background-color: #0bb2d4;
}

.ribbon-info.ribbon-bookmark .ribbon-inner {
  background-color: transparent;
  background-image: linear-gradient(to left, transparent 22px, #0bb2d4 0);
}

.ribbon-info.ribbon-bookmark .ribbon-inner:before {
  border-color: #0bb2d4;
  border-right-color: transparent;
}

.ribbon-info.ribbon-bookmark.ribbon-reverse .ribbon-inner {
  background-image: linear-gradient(to right, transparent 22px, #0bb2d4 0);
}

.ribbon-info.ribbon-bookmark.ribbon-reverse .ribbon-inner:before {
  border-right-color: #0bb2d4;
  border-left-color: transparent;
}

.ribbon-info.ribbon-bookmark.ribbon-vertical .ribbon-inner {
  background-image: linear-gradient(to top, transparent 22px, #0bb2d4 0);
}

.ribbon-info.ribbon-bookmark.ribbon-vertical .ribbon-inner:before {
  border-right-color: #0bb2d4;
  border-bottom-color: transparent;
}

.ribbon-info.ribbon-bookmark.ribbon-vertical.ribbon-reverse .ribbon-inner:before {
  border-right-color: #0bb2d4;
  border-bottom-color: transparent;
  border-left-color: #0bb2d4;
}

.ribbon-info.ribbon-corner:before {
  border-top-color: #0bb2d4;
  border-left-color: #0bb2d4;
}

.ribbon-info.ribbon-corner .ribbon-inner {
  background-color: transparent;
}

.ribbon-info.ribbon-corner.ribbon-reverse:before {
  border-right-color: #0bb2d4;
  border-left-color: transparent;
}

.ribbon-info.ribbon-corner.ribbon-bottom:before {
  border-top-color: transparent;
  border-bottom-color: #0bb2d4;
}

.ribbon-info.ribbon-clip:before {
  border-top-color: #0099b8;
  border-right-color: #0099b8;
}

.ribbon-info.ribbon-clip.ribbon-reverse:before {
  border-right-color: transparent;
  border-left-color: #0099b8;
}

.ribbon-info.ribbon-clip.ribbon-bottom:before {
  border-top-color: transparent;
  border-bottom-color: #0099b8;
}

.ribbon-warning .ribbon-inner {
  background-color: #eb6709;
}

.ribbon-warning.ribbon-bookmark .ribbon-inner {
  background-color: transparent;
  background-image: linear-gradient(to left, transparent 22px, #eb6709 0);
}

.ribbon-warning.ribbon-bookmark .ribbon-inner:before {
  border-color: #eb6709;
  border-right-color: transparent;
}

.ribbon-warning.ribbon-bookmark.ribbon-reverse .ribbon-inner {
  background-image: linear-gradient(to right, transparent 22px, #eb6709 0);
}

.ribbon-warning.ribbon-bookmark.ribbon-reverse .ribbon-inner:before {
  border-right-color: #eb6709;
  border-left-color: transparent;
}

.ribbon-warning.ribbon-bookmark.ribbon-vertical .ribbon-inner {
  background-image: linear-gradient(to top, transparent 22px, #eb6709 0);
}

.ribbon-warning.ribbon-bookmark.ribbon-vertical .ribbon-inner:before {
  border-right-color: #eb6709;
  border-bottom-color: transparent;
}

.ribbon-warning.ribbon-bookmark.ribbon-vertical.ribbon-reverse .ribbon-inner:before {
  border-right-color: #eb6709;
  border-bottom-color: transparent;
  border-left-color: #eb6709;
}

.ribbon-warning.ribbon-corner:before {
  border-top-color: #eb6709;
  border-left-color: #eb6709;
}

.ribbon-warning.ribbon-corner .ribbon-inner {
  background-color: transparent;
}

.ribbon-warning.ribbon-corner.ribbon-reverse:before {
  border-right-color: #eb6709;
  border-left-color: transparent;
}

.ribbon-warning.ribbon-corner.ribbon-bottom:before {
  border-top-color: transparent;
  border-bottom-color: #eb6709;
}

.ribbon-warning.ribbon-clip:before {
  border-top-color: #de4e00;
  border-right-color: #de4e00;
}

.ribbon-warning.ribbon-clip.ribbon-reverse:before {
  border-right-color: transparent;
  border-left-color: #de4e00;
}

.ribbon-warning.ribbon-clip.ribbon-bottom:before {
  border-top-color: transparent;
  border-bottom-color: #de4e00;
}

.ribbon-danger .ribbon-inner {
  background-color: #ff4c52;
}

.ribbon-danger.ribbon-bookmark .ribbon-inner {
  background-color: transparent;
  background-image: linear-gradient(to left, transparent 22px, #ff4c52 0);
}

.ribbon-danger.ribbon-bookmark .ribbon-inner:before {
  border-color: #ff4c52;
  border-right-color: transparent;
}

.ribbon-danger.ribbon-bookmark.ribbon-reverse .ribbon-inner {
  background-image: linear-gradient(to right, transparent 22px, #ff4c52 0);
}

.ribbon-danger.ribbon-bookmark.ribbon-reverse .ribbon-inner:before {
  border-right-color: #ff4c52;
  border-left-color: transparent;
}

.ribbon-danger.ribbon-bookmark.ribbon-vertical .ribbon-inner {
  background-image: linear-gradient(to top, transparent 22px, #ff4c52 0);
}

.ribbon-danger.ribbon-bookmark.ribbon-vertical .ribbon-inner:before {
  border-right-color: #ff4c52;
  border-bottom-color: transparent;
}

.ribbon-danger.ribbon-bookmark.ribbon-vertical.ribbon-reverse .ribbon-inner:before {
  border-right-color: #ff4c52;
  border-bottom-color: transparent;
  border-left-color: #ff4c52;
}

.ribbon-danger.ribbon-corner:before {
  border-top-color: #ff4c52;
  border-left-color: #ff4c52;
}

.ribbon-danger.ribbon-corner .ribbon-inner {
  background-color: transparent;
}

.ribbon-danger.ribbon-corner.ribbon-reverse:before {
  border-right-color: #ff4c52;
  border-left-color: transparent;
}

.ribbon-danger.ribbon-corner.ribbon-bottom:before {
  border-top-color: transparent;
  border-bottom-color: #ff4c52;
}

.ribbon-danger.ribbon-clip:before {
  border-top-color: #f2353c;
  border-right-color: #f2353c;
}

.ribbon-danger.ribbon-clip.ribbon-reverse:before {
  border-right-color: transparent;
  border-left-color: #f2353c;
}

.ribbon-danger.ribbon-clip.ribbon-bottom:before {
  border-top-color: transparent;
  border-bottom-color: #f2353c;
}

.color-selector > li {
  background-color: #997b71;
}

.color-selector > li input[type="radio"]:checked + label:after {
  color: #fff;
}

.color-selector > li.color-selector-disabled {
  background-color: #e4eaec !important;
}

.example-well {
  background-color: #f3f7f9;
}

.example-grid .example-col, .example-blocks .example-col {
  background-color: #f3f7f9;
}

.example-grid .example-col .example-col {
  background-color: #e2ecf1;
}

.example-box {
  border: 1px solid #e4eaec;
}

.example-typography .heading-note, .example-typography .text-note {
  color: #aab2bd;
}

.tox-dialog .tox-dialog__footer .tox-dialog__footer-end button[title="Submit"],
.tox-dialog .tox-dialog__footer .tox-dialog__footer-end button[title="Save"] {
  color: #fff;
  background-color: #997b71 !important;
  border-color: #997b71 !important;
}
.tox-dialog .tox-dialog__footer .tox-dialog__footer-end button[title="Submit"]:hover,
.tox-dialog .tox-dialog__footer .tox-dialog__footer-end button[title="Submit"]:focus,
.tox-dialog .tox-dialog__footer .tox-dialog__footer-end button[title="Save"]:hover,
.tox-dialog .tox-dialog__footer .tox-dialog__footer-end button[title="Save"]:focus {
  background-color: #ab8c82 !important;
  border-color: #ab8c82 !important;
}

.introjs-prevbutton, .introjs-nextbutton,
.introjs-helperNumberLayer,
.introjs-donebutton,
.introjs-bullets a.active {
  background-color: #997b71 !important;
}