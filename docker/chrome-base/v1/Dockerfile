#
# Build App with chrome Base Image
#
FROM gcr.io/wavo-225922/server-base:v2-latest

#
#--------------------------------------------------------------------------
# Additional PHP Extensions
#--------------------------------------------------------------------------
#

RUN apt-get update \
    && apt-get install --no-install-recommends --no-install-suggests -y \
    	wget \
        libzip-dev \
        exif \
        imagemagick \
        libmagickwand-dev \
    && docker-php-ext-install zip exif \
    && pecl install imagick \
    && docker-php-ext-enable imagick

#
#--------------------------------------------------------------------------
# Chrome libraries
#--------------------------------------------------------------------------
#

RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
	&& sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' \
	&& apt-get update \
	&& apt-get install -y google-chrome-stable fonts-ipafont-gothic fonts-wqy-zenhei fonts-thai-tlwg fonts-kacst fonts-freefont-ttf libxss1 --no-install-recommends

#
#--------------------------------------------------------------------------
# Libraries needed for puppeteer
#--------------------------------------------------------------------------
#
RUN apt-get install --no-install-recommends --no-install-suggests -y \
	gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils

#
#--------------------------------------------------------------------------
# Final Touch
#--------------------------------------------------------------------------
#

# Clean up installation artifacts to make image leaner
RUN apt-get remove -y --purge software-properties-common \
    && apt-get -y autoremove \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*


