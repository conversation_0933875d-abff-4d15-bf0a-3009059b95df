FROM php:7.4-fpm-buster
#
#--------------------------------------------------------------------------
# APT Dependencies Installation and Php Extensions
#--------------------------------------------------------------------------
#
RUN apt-get update \
    && apt-get install --no-install-recommends --no-install-suggests -y \
        curl \
        zip \
        unzip \
        software-properties-common \
        supervisor \
        apt-transport-https \
        gnupg2 \
        libxml2-dev \
        nginx \
        # gd libraries
        libfreetype6-dev \
        libpng-dev \
        libjpeg-dev \
        # imap libraries
        libc-client-dev \
        libkrb5-dev \
        # needed for mbstring in php7.4
        libonig-dev \
        # gd php extension
        && docker-php-ext-configure gd --with-freetype --with-jpeg \
        && docker-php-ext-install -j$(nproc) gd \
        # imap php extension
        && docker-php-ext-configure imap \
            --with-kerberos \
            --with-imap-ssl \
        && docker-php-ext-install -j$(nproc) imap \
        && docker-php-ext-install pdo pdo_mysql bcmath ctype fileinfo json mbstring tokenizer sockets xml pcntl posix

#
#--------------------------------------------------------------------------
# Install php redis with igbinary and apcu cache
#--------------------------------------------------------------------------
#
ENV EXT_REDIS_VERSION=5.3.1
ENV EXT_IGBINARY_VERSION=3.1.5
ENV EXT_APCU_VERSION=5.1.18

RUN docker-php-source extract \
    # ext-opache
    && docker-php-ext-enable opcache \
    # ext-igbinary
    && mkdir -p /usr/src/php/ext/igbinary \
    &&  curl -fsSL https://github.com/igbinary/igbinary/archive/$EXT_IGBINARY_VERSION.tar.gz | tar xvz -C /usr/src/php/ext/igbinary --strip 1 \
    && docker-php-ext-install igbinary \
    # ext-redis
    && mkdir -p /usr/src/php/ext/redis \
    && curl -fsSL https://github.com/phpredis/phpredis/archive/$EXT_REDIS_VERSION.tar.gz | tar xvz -C /usr/src/php/ext/redis --strip 1 \
    && docker-php-ext-configure redis --enable-redis-igbinary \
    && docker-php-ext-install redis \
    # ext-apcu
    && mkdir -p /usr/src/php/ext/apcu \
    && curl -fsSL https://github.com/krakjoe/apcu/archive/v$EXT_APCU_VERSION.tar.gz | tar xvz -C /usr/src/php/ext/apcu --strip 1 \
    && docker-php-ext-install apcu \
    # cleanup
    && docker-php-source delete

 #
 #--------------------------------------------------------------------------
 # KUBECTL Installation
 #--------------------------------------------------------------------------
 #
 RUN curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key add - \
 	&& echo "deb https://apt.kubernetes.io/ kubernetes-xenial main" | tee -a /etc/apt/sources.list.d/kubernetes.list \
 	&& apt-get update \
 	&& apt-get install -y kubectl

