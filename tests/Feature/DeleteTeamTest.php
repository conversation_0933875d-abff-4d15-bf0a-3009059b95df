<?php

namespace Tests\Feature;

use App\Team;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DeleteTeamTest extends TestCase
{
    use RefreshDatabase;

    public function test_teams_can_be_deleted_by_agency_admins()
    {
        $this->actingAs($this->agencyAdminUser);

        $this->postJson('/settings/teams', [
            'name'      => 'New Team',
        ]);

        $this->assertDatabaseHas('teams', [
            'name' => 'New Team',
        ]);

        $team = Team::where('name', 'New Team')->first();

        $response = $this->delete('/settings/teams/'.$team->id);

        $response->assertStatus(200);
    }

    public function test_teams_can_be_deleted_by_huron_clients()
    {
        $this->actingAs($this->huronClientUser);

        $this->postJson('/settings/teams', [
            'name'      => 'New Team',
        ]);

        $this->assertDatabaseHas('teams', [
            'name' => 'New Team',
        ]);

        $team = Team::where('name', 'New Team')->first();

        $response = $this->delete('/settings/teams/'.$team->id);

        $response->assertStatus(200);
    }

    public function test_teams_cannot_be_deleted_by_agency_clients()
    {
        $this->actingAs($this->agencyAdminUser);

        $this->postJson('/settings/teams', [
            'name'      => 'New Team',
        ]);

        $this->assertDatabaseHas('teams', [
            'name' => 'New Team',
        ]);

        $team = Team::where('name', 'New Team')->first();

        $this->actingAs($this->agencyClientUser);

        $response = $this->delete('/settings/teams/'.$team->id);

        $response->assertStatus(403);
    }
}
