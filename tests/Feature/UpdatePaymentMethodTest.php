<?php

namespace Tests\Feature;

use Mockery;
use Tests\TestCase;
use <PERSON><PERSON>\Spark\Spark;
use Illuminate\Contracts\Auth\Authenticatable;
use Tests\Traits\InteractsWithPaymentProviders;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UpdatePaymentMethodTest extends TestCase
{
    use RefreshDatabase, InteractsWithPaymentProviders;

    public function test_payment_method_for_stripe_can_be_updated()
    {
        $user = $this->createSubscribedUser('agency-1');

        $this->actingAs($user)
                ->json('PUT', '/settings/payment-method', [
                    'stripe_token' => $this->getStripeToken(),
                    'address'      => '20 Sunny Street',
                    'city'         => 'Athens',
                    'state'        => 'Ohio',
                    'zip'          => '11111',
                    'country'      => 'US',
                ])->assertStatus(200);
    }

    public function test_stripe_token_is_required_to_update_payment_method()
    {
        $user = Mockery::mock(Authenticatable::class);

        $user->shouldR<PERSON>eive('updateCard')->never();

        $response = $this->actingAs($user)
                ->json('PUT', '/settings/payment-method', [
                    'stripe_token' => '',
                    'address'      => '20 Sunny Street',
                    'city'         => 'Athens',
                    'state'        => 'Ohio',
                    'zip'          => '11111',
                    'country'      => 'US',
                ])->assertStatus(500);

        if (Spark::collectsBillingAddress()) {
            // Status 500 due to trying to validate country.
            $response->assertStatus(500);
        } else {
            // If we didn't require billing address, then it should return status 422.
            $response->assertStatus(422);
        }
    }
}
