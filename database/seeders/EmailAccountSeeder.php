<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>aker\Generator as Faker;
use App\EmailAccount;

class EmailAccountSeeder extends Seeder
{
	protected $faker;

	public function __construct(Faker $faker)
    {
        $this->faker = $faker;
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
    	$email = '<EMAIL>';

        $emailAccount = EmailAccount::where('email_address', $email)->first();

        if (is_null($emailAccount)) {
            $emailAccount = EmailAccount::create([
                'name' => '<PERSON> <PERSON>',
                'sender_first_name' => '<PERSON>',
                'sender_last_name' => 'Yohe',
                'email_address' => $email,
                'email_server_type' => 'imap',
                'email_server_password' => 'xxxxxxxxx',
                'email_server_ssl' => 1,
                'email_server_imap_address' => 'outlook.office365.com',
				'email_server_imap_port' => 993,
				'email_server_username' => $email, 
				'email_server_smtp_address' => 'smtp.office365.com',
				'email_server_smtp_port' => 587,
				'email_server_smtp_username' => $email,
				'email_server_smtp_password' => 'xxxxxxxxx',
                'team_id' => 1,
                'agency_id' => 2,
                'owner_id' => 3,
                'added_by' => 'agency-admin',
                'integration_type' => 'ee'
            ]);

            $this->command->info('Created email account '.$email);

			// "imap": {		
			//   "auth": {
			//     "user": "<EMAIL>",
			//     "pass": "xxxxxxxxx"
			//   },
			//   "host": "outlook.office365.com",
			//   "port": 993,
			//   "secure": true
			// },
			// "smtp": {
			//   "auth": {
			//     "user": "<EMAIL>",
			//     "pass": "xxxxxxxxx"
			//   },
			//   "host": "smtp.office365.com",
			//   "port": 587,
			//   "secure": false
			// }
        }

        $emailAccount->activateEmailEngine();

        dd($emailAccount);
    }
}
