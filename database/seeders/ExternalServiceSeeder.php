<?php

namespace Database\Seeders;

use App\Services\ExternalServiceHandler;
use Illuminate\Database\Seeder;

class ExternalServiceSeeder extends Seeder
{
    protected $externalServiceHandler;

    protected $services = [
        'anymail_finder',
        'tomba',
        'serper',
        'reverse_contact',
        'rapid_api',
    ];

    public function __construct(ExternalServiceHandler $externalServiceHandler)
    {
        $this->externalServiceHandler = $externalServiceHandler;
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        foreach ($this->services as $service) {
            $this->externalServiceHandler->activateService($service);
        }
    }
}
