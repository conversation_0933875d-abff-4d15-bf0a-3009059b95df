<?php

namespace Database\Seeders;

use App\LinkedinAccountType;
use Illuminate\Database\Seeder;

class LinkedinAccountTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $free = LinkedinAccountType::firstOrCreate(
			[ 'name' => 'Free' ],
			[
				'profile_views_limit' => 100,
				'invites_limit' => 30,
				'messages_limit' => 50,
				'in_mails_limit' => 0
			]
        );

        $premium = LinkedinAccountType::firstOrCreate(
			[ 'name' => 'Premium' ],
			[
				'profile_views_limit' => 200,
				'invites_limit' => 50,
				'messages_limit' => 100,
				'in_mails_limit' => 5
			]
        );

        $salesNav = LinkedinAccountType::firstOrCreate(
			[ 'name' => 'Sales Navigator' ],
			[
				'profile_views_limit' => 300,
				'invites_limit' => 100,
				'messages_limit' => 200,
				'in_mails_limit' => 30
			]
        );

		$recruiter = LinkedinAccountType::firstOrCreate(
			[ 'name' => 'Recruiter' ],
			[
				'profile_views_limit' => 300,
				'invites_limit' => 100,
				'messages_limit' => 200,
				'in_mails_limit' => 30
			]
        );
    }
}
