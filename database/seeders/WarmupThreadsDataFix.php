<?php

namespace Database\Seeders;

use App\WarmupMessage;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class WarmupThreadsDataFix extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $warmupMessages = WarmupMessage::with('warmupThread')->get();
        foreach ($warmupMessages as $warmupMessage) {
            $warmupThread = $warmupMessage->warmupThread;
            if ($warmupThread) {
                if ($warmupThread->recipient_account_id == null) {
                    $warmupThread->update([
                        'recipient_account_id' => $warmupMessage->recipient_account_id,
                        'last_recipient_account_id' => $warmupMessage->recipient_account_id,
                        'message_count' => $warmupThread->message_count + 1,
                    ]);
                } else {
                    $warmupThread->update([
                        'last_recipient_account_id' => $warmupMessage->recipient_account_id,
                        'message_count' => $warmupThread->message_count + 1,
                    ]);
                }
            }
        }
    }
}
