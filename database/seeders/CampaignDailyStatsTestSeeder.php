<?php

namespace Database\Seeders;

use App\Campaign;
use App\CampaignStage;
use App\EmailAccount;
use App\EmailEngineAccount;
use App\EmailTemplate;
use App\EmailMessage;
use App\Prospect;
use App\Schedule;
use App\ScheduledProspect;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CampaignDailyStatsTestSeeder extends Seeder
{
    private $agencyId = 2;
    private $teamId = 50;
    private $campaign;
    private $emailAccounts = [];
    private $campaignStages = [];
    private $emailTemplates = [];

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::transaction(function () {
            $this->createEmailAccounts();
            $this->createCampaign();
            $this->createCampaignStages();
            $this->createEmailTemplates();
            $this->createProspects();
            $this->createSchedules();
            $this->createTodayEmailMessages();
            $this->createScheduledProspects();
        });

        $this->command->info('Campaign Daily Stats test data created successfully!');
        $this->command->info("Campaign ID: {$this->campaign->id}");
        $this->command->info("Campaign Hash: {$this->campaign->hashid}");
        $this->command->info("Test URL: /campaigns/{$this->campaign->hashid}/daily-stats");
    }

    private function createEmailAccounts()
    {
        $accounts = [
            ['email' => '<EMAIL>', 'first_name' => 'Test1', 'last_name' => 'Account'],
            ['email' => '<EMAIL>', 'first_name' => 'Test2', 'last_name' => 'Account'],
            ['email' => '<EMAIL>', 'first_name' => 'Test3', 'last_name' => 'Account'],
            ['email' => '<EMAIL>', 'first_name' => 'Test4', 'last_name' => 'Account'],
        ];

        foreach ($accounts as $index => $accountData) {
            $account = EmailAccount::create([
                'agency_id' => $this->agencyId,
                'team_id' => $this->teamId,
                'email_address' => $accountData['email'],
                'name' => $accountData['first_name'] . ' ' . $accountData['last_name'],
                'sender_first_name' => $accountData['first_name'],
                'sender_last_name' => $accountData['last_name'],
                'email_server_type' => 'gmail',
                'integration_type' => 'ee', // EmailEngine integration
                'send_limit' => 500,
                'min_interval' => 60,
                'max_interval' => 240,
                'emails_sent' => 0,
                'queued' => 0,
                'signature' => '',
                'email_server_ssl' => 1,
                'email_server_ssl_smtp' => 0,
                'oauth_refresh_token' => '',
                'authenticated_at' => now(),
                'warmup_status' => 'done',
                'alias_enabled' => 0,
                'restart_count' => 0,
                'is_authenticated' => 1,
                'is_cancelled' => 0,
                'enable_warmup' => 0,
                'sends_warmup_messages' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Create corresponding EmailEngineAccount
            $engineAccount = EmailEngineAccount::create([
                'account_id' => Str::random(20), // Generate random account ID like 'krmn3gp65oz8jyo1ld5v'
                'access_token' => 'test_access_token_' . ($index + 1),
                'email_address' => $accountData['email'],
                'name' => $accountData['first_name'] . ' ' . $accountData['last_name'],
                'organization_unit' => '',
                'provider' => 'gmail',
                'sync_state' => 'running',
                'linked_at' => (string) now()->timestamp,
                'email_account_id' => $account->id,
                'billing_state' => 'paid',
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $this->emailAccounts[] = $account;
        }

        $this->command->info('Created 4 email accounts with EmailEngine accounts');
    }

    private function createCampaign()
    {
        $this->campaign = Campaign::create([
            'agency_id' => $this->agencyId,
            'team_id' => $this->teamId,
            'name' => 'Daily Stats Test Campaign',
            'status' => 'RUNNING',
            'timezone' => 'America/New_York',
            'max_emails_per_day' => 100,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Attach email accounts to campaign
        foreach ($this->emailAccounts as $account) {
            $this->campaign->emailAccounts()->attach($account->id, [
                'active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        $this->command->info("Created campaign: {$this->campaign->name}");
    }

    private function createCampaignStages()
    {
        for ($i = 1; $i <= 3; $i++) {
            $stage = CampaignStage::create([
                'campaign_id' => $this->campaign->id,
                'number' => $i,
                'stage_type_id' => 1, // Email Message
                'follow_up' => $i === 1 ? 0 : 3, // First stage immediate, others 3 days
                'delay_type_id' => 1, // days
                'wait_amount' => 1,
                'wait_type_id' => 1, // days
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $this->campaignStages[] = $stage;
        }

        $this->command->info('Created 3 campaign stages');
    }

    private function createEmailTemplates()
    {
        $subjects = [
            // Stage 1 templates
            'Introduction - Let\'s Connect',
            'Quick Question About Your Business',
            'Helping Companies Like Yours',
            'Partnership Opportunity',
            // Stage 2 templates
            'Following Up on My Previous Email',
            'Did You Get a Chance to Review?',
            'Quick Follow-up Question',
            'Still Interested in Learning More?',
            // Stage 3 templates
            'Final Follow-up',
            'Last Chance to Connect',
            'Closing the Loop',
            'One More Try',
        ];

        $templateIndex = 0;
        foreach ($this->campaignStages as $stageIndex => $stage) {
            for ($templateNum = 1; $templateNum <= 4; $templateNum++) {
                $template = EmailTemplate::create([
                    'campaign_id' => $this->campaign->id,
                    'campaign_stage_id' => $stage->id,
                    'campaign_stage_number' => $stage->number,
                    'number' => $templateNum,
                    'subject' => $subjects[$templateIndex],
                    'msg' => "This is template {$templateNum} for stage {$stage->number}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                    'enabled' => true,
                    'track_open' => true,
                    'track_click' => true,
                    // Delivery windows (9 AM to 5 PM)
                    'mon_from' => 540, // 9:00 AM
                    'mon_to' => 1020,  // 5:00 PM
                    'tue_from' => 540,
                    'tue_to' => 1020,
                    'wed_from' => 540,
                    'wed_to' => 1020,
                    'thu_from' => 540,
                    'thu_to' => 1020,
                    'fri_from' => 540,
                    'fri_to' => 1020,
                    'sat_from' => 0,
                    'sat_to' => 0,
                    'sun_from' => 0,
                    'sun_to' => 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                $this->emailTemplates[] = $template;
                $templateIndex++;
            }
        }

        $this->command->info('Created 12 email templates (4 per stage)');
    }

    private function createProspects()
    {
        $prospects = [];
        for ($i = 1; $i <= 50; $i++) {
            $prospect = Prospect::create([
                'agency_id' => $this->agencyId,
                'team_id' => $this->teamId,
                'campaign_id' => $this->campaign->id,
                'email_account_id' => $this->emailAccounts[($i - 1) % 4]->id,
                'first_name' => "Test{$i}",
                'last_name' => "Prospect",
                'email' => "prospect{$i}@example.com",
                'company' => "Company {$i}",
                'title' => "Manager",
                'status' => 'OK',
                'emails_sent' => 0,
                'completed_steps' => 0,
                'next_step' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $prospects[] = $prospect;
        }

        $this->command->info('Created 50 test prospects');
    }

    private function createSchedules()
    {
        $today = Carbon::now($this->campaign->carbon_timezone)->format('Y-m-d');

        foreach ($this->emailAccounts as $account) {
            foreach ($this->emailTemplates as $template) {
                $schedule = Schedule::create([
                    'campaign_id' => $this->campaign->id,
                    'email_account_id' => $account->id,
                    'email_template_id' => $template->id,
                    'template_number' => $template->number,
                    'campaign_stage_id' => $template->campaign_stage_id,
                    'campaign_stage_number' => $template->campaign_stage_number,
                    'campaign_stage_type_id' => 1,
                    'day' => $today,
                    'starts_at' => Carbon::now($this->campaign->carbon_timezone)->setTime(9, 0)->timezone(config('app.timezone')),
                    'ends_at' => Carbon::now($this->campaign->carbon_timezone)->setTime(17, 0)->timezone(config('app.timezone')),
                    'wait_time' => 300, // 5 minutes
                    'amount_to_send' => 10,
                    'amount_sent' => rand(3, 8), // Random sent amount to simulate activity
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        $this->command->info('Created daily schedules for today');
    }

    private function createTodayEmailMessages()
    {
        $today = Carbon::now($this->campaign->carbon_timezone);
        $todayStart = $today->copy()->startOfDay();
        $todayEnd = $today->copy()->endOfDay();

        $messageCount = 0;

        // Create email messages distributed throughout the day
        foreach ($this->emailAccounts as $accountIndex => $account) {
            // Each account sends different amounts
            $messagesForAccount = 15 + ($accountIndex * 5); // 15, 20, 25, 30 messages per account

            for ($i = 0; $i < $messagesForAccount; $i++) {
                // Distribute messages throughout business hours (9 AM - 5 PM)
                $randomHour = rand(9, 16);
                $randomMinute = rand(0, 59);
                $submittedAt = $today->copy()->setTime($randomHour, $randomMinute);

                // Select random template (favor stage 1 for new contacts)
                $templateIndex = $this->getRandomTemplateIndex($i, $messagesForAccount);
                $template = $this->emailTemplates[$templateIndex];

                // Create prospect for this message if needed
                $prospect = Prospect::where('campaign_id', $this->campaign->id)
                    ->where('email_account_id', $account->id)
                    ->skip($i)
                    ->first();

                if (!$prospect) {
                    $prospect = Prospect::create([
                        'agency_id' => $this->agencyId,
                        'team_id' => $this->teamId,
                        'campaign_id' => $this->campaign->id,
                        'email_account_id' => $account->id,
                        'first_name' => "Generated{$messageCount}",
                        'last_name' => "Prospect",
                        'email' => "generated{$messageCount}@example.com",
                        'company' => "Generated Company {$messageCount}",
                        'title' => "Contact",
                        'status' => 'OK',
                        'emails_sent' => 1,
                        'completed_steps' => $template->campaign_stage_number,
                        'next_step' => $template->campaign_stage_number + 1,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }

                EmailMessage::create([
                    'agency_id' => $this->agencyId,
                    'team_id' => $this->teamId,
                    'campaign_id' => $this->campaign->id,
                    'email_account_id' => $account->id,
                    'email_template_id' => $template->id,
                    'prospect_id' => $prospect->id,
                    'origin' => 'self',
                    'status' => 'SENT',
                    'from' => $account->email_address,
                    'from_name' => $account->name,
                    'to' => $prospect->email,
                    'to_name' => $prospect->first_name . ' ' . $prospect->last_name,
                    'subject' => $template->subject,
                    'message' => $template->msg,
                    'submitted_at' => $submittedAt->timezone(config('app.timezone')),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                $messageCount++;
            }
        }

        $this->command->info("Created {$messageCount} email messages for today");
    }

    private function getRandomTemplateIndex($messageIndex, $totalMessages)
    {
        // 60% stage 1 (new contacts), 30% stage 2, 10% stage 3
        $rand = rand(1, 100);

        if ($rand <= 60) {
            // Stage 1 templates (indexes 0-3)
            return rand(0, 3);
        } elseif ($rand <= 90) {
            // Stage 2 templates (indexes 4-7)
            return rand(4, 7);
        } else {
            // Stage 3 templates (indexes 8-11)
            return rand(8, 11);
        }
    }

    private function createScheduledProspects()
    {
        // Create some scheduled prospects for remaining capacity
        $schedules = Schedule::where('campaign_id', $this->campaign->id)
            ->where('day', Carbon::now($this->campaign->carbon_timezone)->format('Y-m-d'))
            ->get();

        $scheduledCount = 0;
        foreach ($schedules as $schedule) {
            $remaining = $schedule->amount_to_send - $schedule->amount_sent;

            if ($remaining > 0) {
                // Create some scheduled prospects for the remaining capacity
                $toSchedule = min($remaining, rand(2, 5));

                for ($i = 0; $i < $toSchedule; $i++) {
                    // Find or create a prospect
                    $prospect = Prospect::where('campaign_id', $this->campaign->id)
                        ->where('email_account_id', $schedule->email_account_id)
                        ->where('status', 'OK')
                        ->whereDoesntHave('scheduledProspects')
                        ->first();

                    if (!$prospect) {
                        $prospect = Prospect::create([
                            'agency_id' => $this->agencyId,
                            'team_id' => $this->teamId,
                            'campaign_id' => $this->campaign->id,
                            'email_account_id' => $schedule->email_account_id,
                            'first_name' => "Scheduled{$scheduledCount}",
                            'last_name' => "Prospect",
                            'email' => "scheduled{$scheduledCount}@example.com",
                            'company' => "Scheduled Company {$scheduledCount}",
                            'title' => "Scheduled Contact",
                            'status' => 'OK',
                            'emails_sent' => 0,
                            'completed_steps' => 0,
                            'next_step' => 1,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }

                    ScheduledProspect::create([
                        'prospect_id' => $prospect->id,
                        'schedule_id' => $schedule->id,
                        'email_template_id' => $schedule->email_template_id,
                        'status' => 'PENDING',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    $scheduledCount++;
                }
            }
        }

        $this->command->info("Created {$scheduledCount} scheduled prospects");
    }
}
