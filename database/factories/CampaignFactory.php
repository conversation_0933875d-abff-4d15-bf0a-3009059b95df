<?php

namespace Database\Factories;

use App\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

class CampaignFactory extends Factory
{
    public function definition(): array
    {
        return [
            'team_id'   => Team::factory(),
            'agency_id' => function (array $attributes) {
                return Team::find($attributes['team_id'])->agency_id;
            },
            'name'      => function (array $attributes) {
                return Team::find($attributes['team_id'])->name . ' ' . $this->faker->numberBetween(1, 20);
            },
            'timezone'  => 'US/Mountain',
            'status'    => 'DRAFT',
            'created_at'=> now(),
        ];
    }
}
