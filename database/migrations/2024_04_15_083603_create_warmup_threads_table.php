<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warmup_threads', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('email_account_id');
            $table->string('ee_thread_id')->nullable();
            $table->timestamps();

            $table->foreign('email_account_id')->references('id')->on('email_accounts');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warmup_threads');
    }
};
