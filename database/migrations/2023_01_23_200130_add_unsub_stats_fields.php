<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUnsubStatsFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('email_templates', function (Blueprint $table) {
            $table->unsignedInteger('unsubscribed')->default(0);
            $table->string('unsubscribe_rate',10)->default('0');
        });

        Schema::table('campaign_stages', function (Blueprint $table) {
            $table->unsignedInteger('unsubscribed')->default(0);
            $table->string('unsubscribe_rate',10)->default('0');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('email_templates', function (Blueprint $table) {
            $table->dropColumn(['unsubscribed', 'unsubscribe_rate']);
        });

        Schema::table('campaign_stages', function (Blueprint $table) {
            $table->dropColumn(['unsubscribed', 'unsubscribe_rate']);
        });
    }
}
