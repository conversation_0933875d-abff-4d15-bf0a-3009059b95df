<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDailyCampaignStatsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('daily_campaign_stats', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('agency_id');
            $table->unsignedInteger('team_id');
            $table->unsignedInteger('campaign_id');
            $table->date('day')->index();
            $table->integer('positive')->default(0);
            $table->integer('neutral')->default(0);
            $table->integer('negative')->default(0);
            $table->unsignedInteger('replied')->default(0);
            $table->unsignedInteger('autoreplied')->default(0);
            $table->unsignedInteger('bounced')->default(0);
            $table->unsignedInteger('contacted')->default(0);
            $table->unsignedInteger('clicked')->default(0);
            $table->unsignedInteger('opened')->default(0);
            $table->unsignedInteger('messages_clicked')->default(0);
            $table->unsignedInteger('messages_opened')->default(0);
            $table->timestamps();

            $table->foreign('agency_id')
                ->references('id')
                ->on('agencies')
                ->onDelete('cascade');

            $table->foreign('team_id')
                ->references('id')
                ->on('teams')
                ->onDelete('cascade');

            $table->foreign('campaign_id')
                ->references('id')
                ->on('campaigns')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('daily_campaign_stats');
    }
}
