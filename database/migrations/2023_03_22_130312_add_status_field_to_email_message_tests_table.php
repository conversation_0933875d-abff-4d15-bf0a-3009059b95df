<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('email_message_tests', function (Blueprint $table) {
            $table->string('status')->nullable(); // 'pending', 'complete', 'failed'
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('email_message_tests', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
