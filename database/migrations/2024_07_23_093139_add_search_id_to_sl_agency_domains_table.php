<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sl_agency_domains', function (Blueprint $table) {
            $table->unsignedBigInteger('agency_search_id')->nullable()->after('domain_id');
            $table->foreign('agency_search_id')->references('id')->on('sl_agency_searches')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sl_agency_domains', function (Blueprint $table) {
            $table->dropForeign(['agency_search_id']);
            $table->dropColumn(['agency_search_id']);
        });
    }
};
