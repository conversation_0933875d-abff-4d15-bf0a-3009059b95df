<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('email_engine_webhooks', function (Blueprint $table) {
            $table->index(['account_id','processed'], 'eew_account_id_processed_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('email_engine_webhooks', function (Blueprint $table) {
            $table->dropIndex('eew_account_id_processed_index');
        });
    }
};
