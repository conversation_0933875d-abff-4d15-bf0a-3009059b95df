<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEeRelatedIndexesToEmailMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('email_messages', function (Blueprint $table) {
            $table->index(['ee_queue_id']);
            $table->index(['ee_id']);
            $table->index(['ee_email_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('email_messages', function (Blueprint $table) {
            $table->dropIndex(['ee_queue_id']);
            $table->dropIndex(['ee_id']);
            $table->dropIndex(['ee_email_id']);
        });
    }
}
