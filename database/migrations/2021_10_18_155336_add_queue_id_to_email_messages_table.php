<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddQueueIdToEmailMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('email_messages', function (Blueprint $table) {
            $table->string('ee_queue_id')->nullable();
        });

        Schema::table('email_message_tests', function (Blueprint $table) {
            $table->string('ee_queue_id')->nullable();
            $table->string('ee_message_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('email_messages', function (Blueprint $table) {
            $table->dropColumn('ee_queue_id');
        });

        Schema::table('email_message_tests', function (Blueprint $table) {
            $table->dropColumn('ee_queue_id');
            $table->dropColumn('ee_message_id');
        });
    }
}
