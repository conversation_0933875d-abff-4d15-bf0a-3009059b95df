<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsForTrackingPositiveStats extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('email_templates', function (Blueprint $table) {
            $table->string('click_rate', 10)->default('0');
            $table->unsignedInteger('positive')->default(0);
            $table->string('positive_rate',10)->default('0');
            $table->string('open_rate', 10)->default('0')->change();
            $table->string('reply_rate', 10)->default('0')->change();
            $table->string('invalid_rate', 10)->default('0')->change();
            $table->string('bounce_rate', 10)->default('0')->change();
        });

        Schema::table('campaign_stages', function (Blueprint $table) {
            $table->string('click_rate', 10)->default('0');
            $table->unsignedInteger('positive')->default(0);
            $table->string('positive_rate',10)->default('0');
            $table->string('open_rate', 10)->default('0')->change();
            $table->string('reply_rate', 10)->default('0')->change();
            $table->string('invalid_rate', 10)->default('0')->change();
            $table->string('bounce_rate', 10)->default('0')->change();
            $table->string('connect_pending_rate', 10)->default('0')->change();
            $table->string('connect_accepted_rate', 10)->default('0')->change();
            $table->string('connect_failed_rate', 10)->default('0')->after('connect_accepted_rate');
            $table->string('linkedin_reply_rate', 10)->default('0')->change();
        });

        Schema::table('linkedin_message_template_stats', function (Blueprint $table) {
            $table->unsignedInteger('positive')->default(0);
            $table->string('positive_rate',10)->default('0');
            $table->string('connect_pending_rate', 10)->default('0')->change();
            $table->string('connect_accepted_rate', 10)->default('0')->change();
            $table->string('connect_failed_rate', 10)->default('0')->change();
            $table->string('linkedin_reply_rate', 10)->default('0')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('email_templates', function (Blueprint $table) {
            $table->dropColumn(['click_rate', 'positive', 'positive_rate']);
            $table->string('open_rate', 10)->default('0.0%')->change();
            $table->string('reply_rate', 10)->default('0.0%')->change();
            $table->string('invalid_rate', 10)->default('0.0%')->change();
            $table->string('bounce_rate', 10)->default('0.0%')->change();
        });

        Schema::table('campaign_stages', function (Blueprint $table) {
            $table->dropColumn(['click_rate', 'positive', 'positive_rate']);
            $table->string('open_rate', 10)->default('0.0%')->change();
            $table->string('reply_rate', 10)->default('0.0%')->change();
            $table->string('invalid_rate', 10)->default('0.0%')->change();
            $table->string('bounce_rate', 10)->default('0.0%')->change();
            $table->string('connect_pending_rate', 10)->default('0.0%')->change();
            $table->string('connect_accepted_rate', 10)->default('0.0%')->change();
            $table->string('connect_failed_rate', 10)->default('0.0%')->change();
            $table->string('linkedin_reply_rate', 10)->default('0.0%')->change();
        });

        Schema::table('linkedin_message_template_stats', function (Blueprint $table) {
            $table->dropColumn(['positive', 'positive_rate']);
            $table->string('connect_pending_rate', 10)->default('0.0%')->change();
            $table->string('connect_accepted_rate', 10)->default('0.0%')->change();
            $table->string('connect_failed_rate', 10)->default('0.0%')->change();
            $table->string('linkedin_reply_rate', 10)->default('0.0%')->change();
        });
    }
}
