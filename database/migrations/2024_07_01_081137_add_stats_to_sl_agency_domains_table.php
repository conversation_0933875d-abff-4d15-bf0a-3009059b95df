<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sl_agency_emails', function (Blueprint $table) {
            $table->boolean('primary')->default(false)->after('email');
            $table->unsignedBigInteger('agency_domain_id')->nullable()->after('contact_id');
            $table->foreign('agency_domain_id')->references('id')->on('sl_agency_domains')->onDelete('cascade');
        });

        Schema::table('sl_agency_domains', function (Blueprint $table) {
            $table->boolean('has_bounced')->default(false)->after('has_replied');
            $table->boolean('is_imported')->default(false)->after('is_contacted');
            $table->unsignedBigInteger('current_email_id')->nullable()->after('domain_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sl_agency_emails', function (Blueprint $table) {
            $table->dropForeign(['agency_domain_id']);
            $table->dropColumn(['agency_domain_id', 'primary']);
        });

        Schema::table('sl_agency_domains', function (Blueprint $table) {
            $table->dropColumn(['has_bounced', 'is_imported', 'current_email_id']);
        });
    }
};
