<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddBillingStateToNylasAccountsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('nylas_accounts', function (Blueprint $table) {
            $table->string('billing_state')->nullable()->default('paid')->after('linked_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('nylas_accounts', function (Blueprint $table) {
            $table->dropColumn(['billing_state']);
        });
    }
}
