<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('linkedin_searches', function (Blueprint $table) {
            $table->json('cookies')->nullable();
            $table->string('li_at')->nullable();
            $table->string('pdl_api_key')->nullable();
            $table->string('type')->nullable()->default('sales_nav')->comment('sales_nav|recruiter');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('linkedin_searches', function (Blueprint $table) {
            $table->dropColumn(['cookies', 'li_at', 'type']);
        });
    }
};
