<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropCancelledEmailAccountsTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('subscription_cancelled_email_accounts');
        Schema::dropIfExists('team_subscription_cancelled_email_accounts');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::create('subscription_cancelled_email_accounts', function (Blueprint $table) {
            $table->integer('email_account_id')->unsigned();
            $table->integer('subscription_id')->unsigned();
            $table->timestamps();

            $table->foreign('email_account_id', 'cancelled_email_account_id')
                ->references('id')
                ->on('email_accounts')
                ->onDelete('cascade');

            $table->foreign('subscription_id', 'cancelled_subscription_id')
                ->references('id')
                ->on('subscriptions')
                ->onDelete('cascade');

            $table->primary(['email_account_id', 'subscription_id'], 'email_account_id_subscription_id');
        });

        Schema::create('team_subscription_cancelled_email_accounts', function (Blueprint $table) {
            $table->integer('email_account_id')->unsigned();
            $table->integer('team_subscription_id')->unsigned();
            $table->timestamps();

            $table->foreign('email_account_id', 'email_account_id')
                ->references('id')
                ->on('email_accounts')
                ->onDelete('cascade');

            $table->foreign('team_subscription_id', 'team_subscription_id')
                ->references('id')
                ->on('team_subscriptions')
                ->onDelete('cascade');

            $table->primary(['email_account_id', 'team_subscription_id'], 'email_account_id_team_subscription_id');
        });
    }
}
