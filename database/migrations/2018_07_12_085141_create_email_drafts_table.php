<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateEmailDraftsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_drafts', function (Blueprint $table) {
            $table->increments('id');

            $table->unsignedInteger('campaign_id')->nullable();
            $table->foreign('campaign_id')->references('id')->on('campaigns');

            $table->unsignedInteger('prospect_id')->nullable();
            $table->foreign('prospect_id')->references('id')->on('prospects');

            $table->unsignedInteger('email_template_id')->nullable();
            $table->foreign('email_template_id')->references('id')->on('email_templates');

            $table->dateTime('send_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_drafts');
    }
}
