<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTotalFieldsToDailyCampaignStatsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('daily_campaign_stats', function (Blueprint $table) {
            $table->integer('positive_total')->default(0);
            $table->integer('neutral_total')->default(0);
            $table->integer('negative_total')->default(0);
            $table->unsignedInteger('replied_total')->default(0);
            $table->unsignedInteger('autoreplied_total')->default(0);
            $table->unsignedInteger('bounced_total')->default(0);
            $table->unsignedInteger('contacted_total')->default(0);
            $table->unsignedInteger('clicked_total')->default(0);
            $table->unsignedInteger('opened_total')->default(0);
            $table->unsignedInteger('messages_clicked_total')->default(0);
            $table->unsignedInteger('messages_opened_total')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('daily_campaign_stats', function (Blueprint $table) {
            $table->dropColumn([
                'positive_total',
                'neutral_total',
                'negative_total',
                'replied_total',
                'autoreplied_total',
                'bounced_total',
                'contacted_total',
                'clicked_total',
                'opened_total',
                'messages_clicked_total',
                'messages_opened_total',
            ]);
        });
    }
}
