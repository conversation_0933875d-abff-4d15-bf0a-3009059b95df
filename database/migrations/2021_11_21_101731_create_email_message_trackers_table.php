<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmailMessageTrackersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_message_trackers', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('email_template_id')->nullable();
            $table->unsignedInteger('email_message_id')->nullable()->unique();
            $table->timestamps();

            $table->foreign('email_message_id')
                ->references('id')
                ->on('email_messages')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_message_trackers');
    }
}
