<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sl_agency_searches', function (Blueprint $table) {
            $table->integer('current_page')->default(1);
            $table->integer('current_contacts')->default(0);
            $table->integer('total_contacts')->default(0);
            $table->string('status')->default('INPROGRESS')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sl_agency_searches', function (Blueprint $table) {
            $table->dropColumn([
                'current_page',
                'current_contacts',
                'total_contacts',
                'status'
            ]);
        });
    }
};
