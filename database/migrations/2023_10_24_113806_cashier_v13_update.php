<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->renameColumn('stripe_plan', 'stripe_price');
        });

        Schema::table('subscription_items', function (Blueprint $table) {
            $table->renameColumn('stripe_plan', 'stripe_price');
            $table->string('stripe_product')->nullable()->after('stripe_id');
        });

        Schema::table('agency_subscriptions', function (Blueprint $table) {
            $table->renameColumn('stripe_plan', 'stripe_price');
        });

        Schema::table('agency_subscription_items', function (Blueprint $table) {
            $table->renameColumn('stripe_plan', 'stripe_price');
            $table->string('stripe_product')->nullable()->after('stripe_id');
        });

        Schema::table('team_subscriptions', function (Blueprint $table) {
            $table->renameColumn('stripe_plan', 'stripe_price');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->renameColumn('card_brand', 'pm_type');
            $table->renameColumn('card_last_four', 'pm_last_four');
            $table->renameColumn('card_country', 'pm_country');
        });

        Schema::table('agencies', function (Blueprint $table) {
            $table->renameColumn('card_brand', 'pm_type');
            $table->renameColumn('card_last_four', 'pm_last_four');
            $table->renameColumn('card_country', 'pm_country');
        });

        Schema::table('teams', function (Blueprint $table) {
            $table->renameColumn('card_brand', 'pm_type');
            $table->renameColumn('card_last_four', 'pm_last_four');
            $table->renameColumn('card_country', 'pm_country');
        });

        Schema::table('invoices', function (Blueprint $table) {
            $table->renameColumn('card_country', 'pm_country');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->renameColumn('stripe_price', 'stripe_plan');
        });

        Schema::table('subscription_items', function (Blueprint $table) {
            $table->renameColumn('stripe_price', 'stripe_plan');
            $table->dropColumn('stripe_product');
        });

        Schema::table('agency_subscriptions', function (Blueprint $table) {
            $table->renameColumn('stripe_price', 'stripe_plan');
        });

        Schema::table('agency_subscription_items', function (Blueprint $table) {
            $table->renameColumn('stripe_price', 'stripe_plan');
            $table->dropColumn('stripe_product');
        });

        Schema::table('team_subscriptions', function (Blueprint $table) {
            $table->renameColumn('stripe_price', 'stripe_plan');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->renameColumn('pm_brand', 'card_type');
            $table->renameColumn('pm_last_four', 'card_last_four');
            $table->renameColumn('pm_country', 'card_country');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->renameColumn('pm_brand', 'card_type');
            $table->renameColumn('pm_last_four', 'card_last_four');
            $table->renameColumn('pm_country', 'card_country');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->renameColumn('pm_brand', 'card_type');
            $table->renameColumn('pm_last_four', 'card_last_four');
            $table->renameColumn('pm_country', 'card_country');
        });

        Schema::table('invoices', function (Blueprint $table) {
            $table->renameColumn('pm_country', 'card_country');
        });
    }
};
