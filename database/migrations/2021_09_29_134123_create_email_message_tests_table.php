<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmailMessageTestsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_message_tests', function (Blueprint $table) {
            $table->id();
            $table->string('nylas_message_id')->nullable();
            $table->string('nylas_thread_id')->nullable();
            $table->string('to_email');
            $table->unsignedInteger('email_account_id');
            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_message_tests');
    }
}
