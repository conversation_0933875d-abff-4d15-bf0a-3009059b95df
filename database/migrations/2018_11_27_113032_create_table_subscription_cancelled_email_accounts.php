<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTableSubscriptionCancelledEmailAccounts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subscription_cancelled_email_accounts', function (Blueprint $table) {
            $table->integer('email_account_id')->unsigned();
            $table->integer('subscription_id')->unsigned();
            $table->timestamps();

            $table->foreign('email_account_id', 'cancelled_email_account_id')
                ->references('id')
                ->on('email_accounts')
                ->onDelete('cascade');

            $table->foreign('subscription_id', 'cancelled_subscription_id')
                ->references('id')
                ->on('subscriptions')
                ->onDelete('cascade');

            $table->primary(['email_account_id', 'subscription_id'], 'email_account_id_subscription_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subscription_cancelled_email_accounts');
    }
}
