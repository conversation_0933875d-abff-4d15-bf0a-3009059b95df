<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLinkedinFieldsToDailyCampaignStatsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('daily_campaign_stats', function (Blueprint $table) {
            $table->unsignedInteger('linkedin_contacted')->default(0);
            $table->unsignedInteger('connect_requests_pending')->default(0);
            $table->unsignedInteger('connect_requests_accepted')->default(0);
            $table->unsignedInteger('connect_requests_failed')->default(0);
            $table->unsignedInteger('linkedin_replied')->default(0);
            $table->unsignedInteger('linkedin_contacted_total')->default(0);
            $table->unsignedInteger('connect_requests_pending_total')->default(0);
            $table->unsignedInteger('connect_requests_accepted_total')->default(0);
            $table->unsignedInteger('connect_requests_failed_total')->default(0);
            $table->unsignedInteger('linkedin_replied_total')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('daily_campaign_stats', function (Blueprint $table) {
            $table->dropColumn([
                'linkedin_contacted',
                'connect_requests_pending',
                'connect_requests_accepted',
                'connect_requests_failed',
                'linkedin_replied',
                'linkedin_contacted_total',
                'connect_requests_pending_total',
                'connect_requests_accepted_total',
                'connect_requests_failed_total',
                'linkedin_replied_total'
            ]);
        });
    }
}
