<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateEmailConfigurationsTableRemoveNylasFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('email_configurations', function (Blueprint $table) {
            $table->string('email_server_type', 20)->nullable()->change();
            $table->integer('campaign_id')->unsigned()->unique()->change();
            $table->dropColumn('email_server_smtp_port');
            $table->dropColumn('email_server_imap_port');
            $table->dropColumn('email_server_ssl');
            $table->dropColumn('email_server_eas_host');
            $table->dropColumn('google_refresh_token');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('email_configurations', function (Blueprint $table) {
//            $table->dropUnique(['campaign_id']);
            $table->integer('email_server_smtp_port')->unsigned()->nullable()->after('email_server_imap_address');
            $table->integer('email_server_imap_port')->unsigned()->nullable()->after('email_server_smtp_port');
            $table->boolean('email_server_ssl')->default(false)->after('email_server_imap_port');
            $table->string('email_server_eas_host')->nullable()->after('email_server_ssl');
            $table->string('google_refresh_token')->nullable()->after('email_server_eas_host');
        });
    }
}
