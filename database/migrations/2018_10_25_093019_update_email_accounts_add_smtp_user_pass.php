<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateEmailAccountsAddSmtpUserPass extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('email_accounts', function (Blueprint $table) {
            $table->string('email_server_smtp_username', 150)->nullable()
                ->after('email_server_password');
            $table->string('email_server_smtp_password', 50)->nullable()
                ->after('email_server_smtp_username');
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('email_accounts', function (Blueprint $table) {
            $table->dropColumn(['email_server_smtp_username', 'email_server_smtp_password']);
        });
    }
}
