<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddInfoFieldToKubernetesLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('kubernetes_logs', function (Blueprint $table) {
            $table->string('info')->nullable()->after('entry');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('kubernetes_logs', function (Blueprint $table) {
            $table->dropColumn('info');
        });
    }
}
