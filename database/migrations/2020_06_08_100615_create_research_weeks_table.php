<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateResearchWeeksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('research_weeks', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('agency_id');
            $table->unsignedSmallInteger('week_number');
            $table->date('week_start');
            $table->date('week_end');
            $table->unsignedSmallInteger('max_quantity')->default(0);
            $table->unsignedSmallInteger('max_hours')->default(0);
            $table->unsignedSmallInteger('hours_worked')->default(0);
            $table->unsignedSmallInteger('hours_remaining')->default(0);
            $table->unsignedSmallInteger('hours_scheduled')->default(0);
            $table->timestamps();

            $table->foreign('agency_id')
                ->references('id')
                ->on('agencies')
                ->onDelete('cascade');
        });

        Schema::table('research_project_weeks', function (Blueprint $table) {
            $table->unsignedBigInteger('research_week_id')->nullable()->after('agency_id');

            $table->foreign('research_week_id')
                ->references('id')
                ->on('research_weeks')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('research_project_weeks', function (Blueprint $table) {
            $table->dropForeign(['research_week_id']);
            $table->dropColumn('research_week_id');
        });

        Schema::dropIfExists('research_weeks');
    }
}
