(function(a){function q(d){return"number"===typeof d&&!isNaN(d)&&isFinite(d)}function s(d,g){return 100*g/(d[1]-d[0])}function p(d,g){for(var e=1;d>=g[e];)e+=1;return e}function k(d,g,e,a){this.xPct=[];this.xVal=[];this.xSteps=[a||!1];this.xNumSteps=[!1];this.snap=g;this.direction=e;for(var f in d)if(d.hasOwnProperty(f)){g=f;e=d[f];a=void 0;"number"===typeof e&&(e=[e]);if("[object Array]"!==Object.prototype.toString.call(e))throw Error("noUiSlider: 'range' contains invalid value.");a="min"===g?0:
"max"===g?100:parseFloat(g);if(!q(a)||!q(e[0]))throw Error("noUiSlider: 'range' value isn't numeric.");this.xPct.push(a);this.xVal.push(e[0]);a?this.xSteps.push(isNaN(e[1])?!1:e[1]):isNaN(e[1])||(this.xSteps[0]=e[1])}this.xNumSteps=this.xSteps.slice(0);for(f in this.xNumSteps)this.xNumSteps.hasOwnProperty(f)&&(d=Number(f),(g=this.xNumSteps[f])&&(this.xSteps[d]=s([this.xVal[d],this.xVal[d+1]],g)/(100/(this.xPct[d+1]-this.xPct[d]))))}k.prototype.getMargin=function(d){return 2===this.xPct.length?s(this.xVal,
d):!1};k.prototype.toStepping=function(d){var g=this.xVal,a=this.xPct;if(d>=g.slice(-1)[0])d=100;else{var m=p(d,g),f,k;f=g[m-1];k=g[m];g=a[m-1];a=a[m];f=[f,k];d=s(f,0>f[0]?d+Math.abs(f[0]):d-f[0]);d=g+d/(100/(a-g))}this.direction&&(d=100-d);return d};k.prototype.fromStepping=function(d){this.direction&&(d=100-d);var a;var e=this.xVal;a=this.xPct;if(100<=d)a=e.slice(-1)[0];else{var m=p(d,a),f,k;f=e[m-1];k=e[m];e=a[m-1];f=[f,k];a=100/(a[m]-e)*(d-e)*(f[1]-f[0])/100+f[0]}d=Math.pow(10,7);return Number((Math.round(a*
d)/d).toFixed(7))};k.prototype.getStep=function(d){this.direction&&(d=100-d);var a=this.xPct,e=this.xSteps,k=this.snap;if(100!==d){var f=p(d,a);k?(e=a[f-1],a=a[f],d=d-e>(a-e)/2?a:e):(e[f-1]?(k=a[f-1],e=e[f-1],a=Math.round((d-a[f-1])/e)*e,a=k+a):a=d,d=a)}this.direction&&(d=100-d);return d};k.prototype.getApplicableStep=function(a){var g=p(a,this.xPct);a=100===a?2:1;return[this.xNumSteps[g-2],this.xVal[g-a],this.xNumSteps[g-a]]};k.prototype.convert=function(a){return this.getStep(this.toStepping(a))};
a.noUiSlider={Spectrum:k}})(window.jQuery||window.Zepto);(function(a){function q(c){return"number"===typeof c&&!isNaN(c)&&isFinite(c)}function s(c,b){if(!q(b))throw Error("noUiSlider: 'step' is not numeric.");c.singleStep=b}function p(c,b){if("object"!==typeof b||a.isArray(b))throw Error("noUiSlider: 'range' is not an object.");if(void 0===b.min||void 0===b.max)throw Error("noUiSlider: Missing 'min' or 'max' in 'range'.");c.spectrum=new a.noUiSlider.Spectrum(b,c.snap,c.dir,c.singleStep)}function k(c,b){var d=b;b=a.isArray(d)?d:[d];if(!a.isArray(b)||!b.length||
2<b.length)throw Error("noUiSlider: 'start' option is incorrect.");c.handles=b.length;c.start=b}function d(c,b){c.snap=b;if("boolean"!==typeof b)throw Error("noUiSlider: 'snap' option must be a boolean.");}function g(c,b){c.animate=b;if("boolean"!==typeof b)throw Error("noUiSlider: 'animate' option must be a boolean.");}function e(c,b){if("lower"===b&&1===c.handles)c.connect=1;else if("upper"===b&&1===c.handles)c.connect=2;else if(!0===b&&2===c.handles)c.connect=3;else if(!1===b)c.connect=0;else throw Error("noUiSlider: 'connect' option doesn't match handle count.");
}function m(c,b){switch(b){case "horizontal":c.ort=0;break;case "vertical":c.ort=1;break;default:throw Error("noUiSlider: 'orientation' option is invalid.");}}function f(c,b){if(!q(b))throw Error("noUiSlider: 'margin' option must be numeric.");c.margin=c.spectrum.getMargin(b);if(!c.margin)throw Error("noUiSlider: 'margin' option is only supported on linear sliders.");}function x(c,b){if(!q(b))throw Error("noUiSlider: 'limit' option must be numeric.");c.limit=c.spectrum.getMargin(b);if(!c.limit)throw Error("noUiSlider: 'limit' option is only supported on linear sliders.");
}function y(c,b){switch(b){case "ltr":c.dir=0;break;case "rtl":c.dir=1;c.connect=[0,2,1,3][c.connect];break;default:throw Error("noUiSlider: 'direction' option was not recognized.");}}function t(c,b){if("string"!==typeof b)throw Error("noUiSlider: 'behaviour' must be a string containing options.");var a=0<=b.indexOf("tap"),d=0<=b.indexOf("drag"),e=0<=b.indexOf("fixed"),f=0<=b.indexOf("snap");c.events={tap:a||f,drag:d,fixed:e,snap:f}}function u(c,b){c.format=b;if("function"===typeof b.to&&"function"===
typeof b.from)return!0;throw Error("noUiSlider: 'format' requires 'to' and 'from' methods.");}var h={to:function(c){return c.toFixed(2)},from:Number};a.noUiSlider.testOptions=function(c){var b={margin:0,limit:0,animate:!0,format:h},G;G={step:{r:!1,t:s},start:{r:!0,t:k},connect:{r:!0,t:e},direction:{r:!0,t:y},snap:{r:!1,t:d},animate:{r:!1,t:g},range:{r:!0,t:p},orientation:{r:!1,t:m},margin:{r:!1,t:f},limit:{r:!1,t:x},behaviour:{r:!0,t:t},format:{r:!1,t:u}};c=a.extend({connect:!1,direction:"ltr",behaviour:"tap",
orientation:"horizontal"},c);a.each(G,function(a,d){if(void 0===c[a]){if(d.r)throw Error("noUiSlider: '"+a+"' is required.");return!0}d.t(b,c[a])});b.style=b.ort?"top":"left";return b}})(window.jQuery||window.Zepto);(function(a){function q(c){return Math.max(Math.min(c,100),0)}function s(c,b,a){c.addClass(b);setTimeout(function(){c.removeClass(b)},a)}function p(c,b){var d=a("<div><div/></div>").addClass(h[2]),e=["-lower","-upper"];c&&e.reverse();d.children().addClass(h[3]+" "+h[3]+e[b]);return d}function k(a,b,d){switch(a){case 1:b.addClass(h[7]);d[0].addClass(h[6]);break;case 3:d[1].addClass(h[6]);case 2:d[0].addClass(h[7]);case 0:b.addClass(h[6])}}function d(a,b,d){var e,f=[];for(e=0;e<a;e+=1)f.push(p(b,e).appendTo(d));
return f}function g(c,b,d){d.addClass([h[0],h[8+c],h[4+b]].join(" "));return a("<div/>").appendTo(d).addClass(h[1])}function e(c,b,e){function f(){return v[["width","height"][b.ort]]()}function m(b){var a,c=[n.val()];for(a=0;a<b.length;a+=1)n.trigger(b[a],c)}function p(a){return 1===a.length?a[0]:b.dir?a.reverse():a}function x(b){return function(a,c){n.val([b?null:c,b?c:null],!0)}}function t(b){var c=a.inArray(b,w);n[0].linkAPI&&n[0].linkAPI[b]&&n[0].linkAPI[b].change(B[c],l[c].children(),n)}function C(a,
c,d){var e=a[0]!==l[0][0]?1:0,H=r[0]+b.margin,f=r[1]-b.margin,g=r[0]+b.limit,k=r[1]-b.limit;1<l.length&&(c=e?Math.max(c,H):Math.min(c,f));!1!==d&&b.limit&&1<l.length&&(c=e?Math.min(c,g):Math.max(c,k));c=z.getStep(c);c=q(parseFloat(c.toFixed(7)));if(c===r[e])return!1;a.css(b.style,c+"%");a.is(":first-child")&&a.toggleClass(h[17],50<c);r[e]=c;B[e]=z.fromStepping(c);t(w[e]);return!0}function A(a,c,d,e){a=a.replace(/\s/g,".nui ")+".nui";return c.on(a,function(a){if(n.attr("disabled")||n.hasClass(h[14]))return!1;
a.preventDefault();var c=0===a.type.indexOf("touch"),f=0===a.type.indexOf("mouse"),E=0===a.type.indexOf("pointer"),D,g,k=a;0===a.type.indexOf("MSPointer")&&(E=!0);a.originalEvent&&(a=a.originalEvent);c&&(D=a.changedTouches[0].pageX,g=a.changedTouches[0].pageY);if(f||E)E||void 0!==window.pageXOffset||(window.pageXOffset=document.documentElement.scrollLeft,window.pageYOffset=document.documentElement.scrollTop),D=a.clientX+window.pageXOffset,g=a.clientY+window.pageYOffset;k.points=[D,g];k.cursor=f;a=
k;a.calcPoint=a.points[b.ort];d(a,e)})}function I(a,b){var c=b.handles||l,d,e=!1,e=100*(a.calcPoint-b.start)/f(),g=c[0][0]!==l[0][0]?1:0;var h=b.positions;d=e+h[0];e+=h[1];1<c.length?(0>d&&(e+=Math.abs(d)),100<e&&(d-=e-100),d=[q(d),q(e)]):d=[d,e];e=C(c[0],d[g],1===c.length);1<c.length&&(e=C(c[1],d[g?0:1],!1)||e);e&&m(["slide"])}function J(b){a("."+h[15]).removeClass(h[15]);b.cursor&&a("body").css("cursor","").off(".nui");y.off(".nui");n.removeClass(h[12]);m(["set","change"])}function F(b,c){1===c.handles.length&&
c.handles[0].children().addClass(h[15]);b.stopPropagation();A(u.move,y,I,{start:b.calcPoint,handles:c.handles,positions:[r[0],r[l.length-1]]});A(u.end,y,J,null);b.cursor&&(a("body").css("cursor",a(b.target).css("cursor")),1<l.length&&n.addClass(h[12]),a("body").on("selectstart.nui",!1))}function K(c){var d=c.calcPoint,e=0;c.stopPropagation();a.each(l,function(){e+=this.offset()[b.style]});e=d<e/2||1===l.length?0:1;d-=v.offset()[b.style];d=100*d/f();b.events.snap||s(n,h[14],300);C(l[e],d);m(["slide",
"set","change"]);b.events.snap&&F(c,{handles:[l[e]]})}var n=a(c),r=[-1,-1],v,l,z=b.spectrum,B=[],w=["lower","upper"].slice(0,b.handles);b.dir&&w.reverse();c.LinkUpdate=t;c.LinkConfirm=function(c,d){var e=a.inArray(c,w);d&&d.appendTo(l[e].children());b.dir&&(e=1===e?0:1);return x(e)};c.LinkDefaultFormatter=b.format;c.LinkDefaultFlag="lower";c.reappend=function(){var a,b;for(a=0;a<w.length;a+=1)this.linkAPI&&this.linkAPI[b=w[a]]&&this.linkAPI[b].reconfirm(b)};if(n.hasClass(h[0]))throw Error("Slider was already initialized.");
v=g(b.dir,b.ort,n);l=d(b.handles,b.dir,v);k(b.connect,n,l);(function(a){var b;if(!a.fixed)for(b=0;b<l.length;b+=1)A(u.start,l[b].children(),F,{handles:[l[b]]});a.tap&&A(u.start,v,K,{handles:l});a.drag&&(b=v.find("."+h[7]).addClass(h[10]),a.fixed&&(b=b.add(v.children().not(b).children())),A(u.start,b,F,{handles:l}))})(b.events);c.vSet=function(c){if(n[0].LinkIsEmitting)return this;var d;c=a.isArray(c)?c:[c];b.dir&&1<b.handles&&c.reverse();b.animate&&-1!==r[0]&&s(n,h[14],300);d=1<l.length?3:1;1===c.length&&
(d=1);var e,f,g;b.limit&&(d+=1);for(e=0;e<d;e+=1)f=e%2,g=c[f],null!==g&&!1!==g&&("number"===typeof g&&(g=String(g)),g=b.format.from(g),(!1===g||isNaN(g)||!1===C(l[f],z.toStepping(g),e===3-b.dir))&&t(w[f]));m(["set"]);return this};c.vGet=function(){var a,c=[];for(a=0;a<b.handles;a+=1)c[a]=b.format.to(B[a]);return p(c)};c.destroy=function(){a(this).off(".nui").removeClass(h.join(" ")).empty();delete this.LinkUpdate;delete this.LinkConfirm;delete this.LinkDefaultFormatter;delete this.LinkDefaultFlag;
delete this.reappend;delete this.vGet;delete this.vSet;delete this.getCurrentStep;delete this.getInfo;delete this.destroy;return e};c.getCurrentStep=function(){var b=a.map(r,function(a,b){var c=z.getApplicableStep(a);return[[B[b]-c[2]>=c[1]?c[2]:c[0],c[2]]]});return p(b)};c.getInfo=function(){return[z,b.style,b.ort]};n.val(b.start)}function m(c){if(!this.length)throw Error("noUiSlider: Can't initialize slider on empty selection.");var b=a.noUiSlider.testOptions(c,this);return this.each(function(){e(this,
b,c)})}function f(c){return this.each(function(){if(this.destroy){var b=a(this).val(),d=this.destroy(),e=a.extend({},d,c);a(this).noUiSlider(e);this.reappend();d.start===e.start&&a(this).val(b)}else a(this).noUiSlider(c)})}function x(){return this[0][arguments.length?"vSet":"vGet"].apply(this[0],arguments)}var y=a(document),t=a.fn.val,u=window.navigator.pointerEnabled?{start:"pointerdown",move:"pointermove",end:"pointerup"}:window.navigator.msPointerEnabled?{start:"MSPointerDown",move:"MSPointerMove",
end:"MSPointerUp"}:{start:"mousedown touchstart",move:"mousemove touchmove",end:"mouseup touchend"},h="noUi-target noUi-base noUi-origin noUi-handle noUi-horizontal noUi-vertical noUi-background noUi-connect noUi-ltr noUi-rtl noUi-dragable  noUi-state-drag  noUi-state-tap noUi-active  noUi-stacking".split(" ");a.fn.val=function(){var c=arguments,b=a(this[0]);return arguments.length?this.each(function(){(a(this).hasClass(h[0])?x:t).apply(a(this),c)}):(b.hasClass(h[0])?x:t).call(b)};a.fn.noUiSlider=
function(a,b){return(b?f:m).call(this,a)}})(window.jQuery||window.Zepto);
