.alertify-logs > * {
  padding: 12px 24px;
  color: #fff;
  border-radius: .286rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, .2);
}

.alertify-logs > *, .alertify-logs > *.default {
  background: rgba(0, 0, 0, .8);
}

.alertify-logs > *.error {
  background: #ff4c52;
}

.alertify-logs > *.success {
  background: #11c26d;
}

.alertify {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1700;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .3);
}

.alertify.hide {
  pointer-events: none;
  opacity: 0;
}

.alertify, .alertify.show, .alertify.hide {
  box-sizing: border-box;
  transition: all .33s cubic-bezier(.25, .8, .25, 1);
}

.alertify, .alertify * {
  box-sizing: border-box;
}

.alertify .dialog {
  padding: 12px;
}

.alertify .dialog, .alertify .alert {
  position: relative;
  top: 50%;
  width: 100%;
  margin: 0 auto;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.alertify .dialog > *, .alertify .alert > * {
  width: 400px;
  max-width: 95%;
  padding: 12px;
  margin: 0 auto;
  text-align: center;
  background: #fff;
  border-radius: .286rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, .2);
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, .14), 0 4px 5px 0 rgba(0, 0, 0, .098), 0 1px 10px 0 rgba(0, 0, 0, .084);
}

.alertify .dialog .msg, .alertify .alert .msg {
  padding: 12px;
  margin: 0;
  margin-bottom: 12px;
  text-align: left;
}

.alertify .dialog input, .alertify .alert input {
  width: 100%;
  height: 2.573rem;
  padding: .429rem 1rem;
  margin-bottom: 15px;
  font-size: 1rem;
  line-height: 1.57142857;
  color: #76838f;
  background-color: #fff;
  background-image: none;
  border: 1px solid #e4eaec;
  border-radius: .215rem;
  box-shadow: none;
  transition: box-shadow .25s linear, border .25s linear, color .25s linear, background-color .25s linear;
}

.alertify .dialog input.focus, .alertify .dialog input:focus, .alertify .alert input.focus, .alertify .alert input:focus {
  border-color: #3e8ef7;
  box-shadow: none;
}

.alertify .dialog nav, .alertify .alert nav {
  text-align: right;
}

.alertify .dialog nav .btn, .alertify .alert nav .btn {
  margin: 4px 5px;
}

.alertify-logs {
  position: fixed;
  right: 16px;
  bottom: 16px;
  z-index: 1699;
}

.alertify-logs > * {
  position: relative;
  box-sizing: border-box;
  float: right;
  margin-top: 10px;
  clear: both;
  transition: all .3s cubic-bezier(.25, .8, .25, 1);
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000;
  perspective: 1000;
}

.alertify-logs > *.show {
  right: 0;
  opacity: 1;
}

.alertify-logs > *, .alertify-logs > *.hide {
  right: -100%;
  opacity: 0;
}
