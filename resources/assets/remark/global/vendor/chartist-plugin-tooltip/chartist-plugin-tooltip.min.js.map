{"version": 3, "sources": ["chartist-plugin-tooltip.js"], "names": ["root", "factory", "define", "amd", "Chartist", "returnExportsGlobal", "exports", "module", "require", "this", "window", "document", "show", "element", "hasClass", "className", "hide", "regex", "RegExp", "replace", "trim", "getAttribute", "indexOf", "next", "nextS<PERSON>ling", "text", "innerText", "textContent", "defaultOptions", "currency", "undefined", "currencyFormatCallback", "tooltipOffset", "x", "y", "anchorToPoint", "appendToBody", "class", "pointClass", "plugins", "tooltip", "options", "extend", "chart", "on", "event", "selector", "callback", "$chart", "addEventListener", "e", "target", "setPosition", "height", "$toolTip", "offsetHeight", "width", "offsetWidth", "anchorX", "anchorY", "offsetX", "offsetY", "style", "top", "pageY", "left", "pageX", "box", "getBoundingClientRect", "pageXOffset", "pageYOffset", "x2", "y2", "parseInt", "baseVal", "value", "tooltipSelector", "constructor", "name", "Bar", "prototype", "Pie", "donut", "container", "querySelector", "createElement", "body", "append<PERSON><PERSON><PERSON>", "$point", "tooltipText", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parentNode", "seriesName", "meta", "hasMeta", "transformTooltipTextFnc", "tooltipFnc", "metaIsHTML", "txt", "innerHTML", "label"], "mappings": ";;;;;;CAAC,SAAUA,EAAMC,GACO,kBAAXC,SAAyBA,OAAOC,IAEzCD,QAAQ,YAAa,SAAUE,GAC7B,MAAQJ,GAAKK,oBAAsBJ,EAAQG,KAEjB,gBAAZE,SAIhBC,OAAOD,QAAUL,EAAQO,QAAQ,aAEjCR,EAAK,4BAA8BC,EAAQG,WAE7CK,KAAM,SAAUL,GA6LhB,MAtLC,UAAUM,EAAQC,EAAUP,GAC3B,YAyJA,SAASQ,GAAKC,GACRC,EAASD,EAAS,kBACpBA,EAAQE,UAAYF,EAAQE,UAAY,iBAI5C,QAASC,GAAKH,GACZ,GAAII,GAAQ,GAAIC,QAAO,mBAAyB,KAChDL,GAAQE,UAAYF,EAAQE,UAAUI,QAAQF,EAAO,IAAIG,OAG3D,QAASN,GAASD,EAASE,GACzB,OAAQ,IAAMF,EAAQQ,aAAa,SAAW,KAAKC,QAAQ,IAAMP,EAAY,MAAQ,EAGvF,QAASQ,GAAKV,EAASE,GACrB,GACEF,EAAUA,EAAQW,kBACXX,IAAYC,EAASD,EAASE,GACvC,OAAOF,GAGT,QAASY,GAAKZ,GACZ,MAAOA,GAAQa,WAAab,EAAQc,YA9KtC,GAAIC,IACFC,aAAUC,GACVC,2BAAwBD,GACxBE,eACEC,EAAG,EACHC,GAAI,IAENC,eAAe,EACfC,cAAc,EACdC,UAAOP,GACPQ,WAAY,WAGdlC,GAASmC,QAAUnC,EAASmC,YAC5BnC,EAASmC,QAAQC,QAAU,SAAUC,GAGnC,MAFAA,GAAUrC,EAASsC,UAAWd,EAAgBa,GAEvC,SAAiBE,GA6BtB,QAASC,GAAGC,EAAOC,EAAUC,GAC3BC,EAAOC,iBAAiBJ,EAAO,SAAUK,GAClCJ,IAAYhC,EAASoC,EAAEC,OAAQL,IACpCC,EAASG,KA2Eb,QAASE,GAAYP,GACnBQ,EAASA,GAAUC,EAASC,aAC5BC,EAAQA,GAASF,EAASG,WAC1B,IAEIC,GAASC,EAFTC,GAAYJ,EAAQ,EAAIf,EAAQT,cAAcC,EAC9C4B,GAAYR,EAASZ,EAAQT,cAAcE,CAG/C,IAAKO,EAAQL,aAaXkB,EAASQ,MAAMC,IAAMlB,EAAMmB,MAAQH,EAAU,KAC7CP,EAASQ,MAAMG,KAAOpB,EAAMqB,MAAQN,EAAU,SAdrB,CACzB,GAAIO,GAAMnB,EAAOoB,wBACbH,EAAOpB,EAAMqB,MAAQC,EAAIF,KAAOvD,EAAO2D,YACvCN,EAAMlB,EAAMmB,MAAQG,EAAIJ,IAAMrD,EAAO4D,aAErC,IAAS7B,EAAQN,eAAiBU,EAAMM,OAAOoB,IAAM1B,EAAMM,OAAOqB,KACpEd,EAAUe,SAAS5B,EAAMM,OAAOoB,GAAGG,QAAQC,OAC3ChB,EAAUc,SAAS5B,EAAMM,OAAOqB,GAAGE,QAAQC,QAG7CrB,EAASQ,MAAMC,KAAOJ,GAAWI,GAAOF,EAAU,KAClDP,EAASQ,MAAMG,MAAQP,GAAWO,GAAQL,EAAU,MA5HxD,GAAIgB,GAAkBnC,EAAQH,UAC1BK,GAAMkC,YAAYC,MAAQ1E,EAAS2E,IAAIC,UAAUH,YAAYC,KAC/DF,EAAkB,SACTjC,EAAMkC,YAAYC,MAAS1E,EAAS6E,IAAID,UAAUH,YAAYC,OAGrEF,EADEjC,EAAMF,QAAQyC,MACE,iBAEA,eAItB,IAAIlC,GAASL,EAAMwC,UACf7B,EAAWN,EAAOoC,cAAc,oBAC/B9B,KACHA,EAAW3C,EAAS0E,cAAc,OAClC/B,EAASvC,UAAc0B,EAAQJ,MAA8B,oBAAsBI,EAAQJ,MAAnD,mBACnCI,EAAQL,aAGXzB,EAAS2E,KAAKC,YAAYjC,GAF1BN,EAAOuC,YAAYjC,GAKvB,IAAID,GAASC,EAASC,aAClBC,EAAQF,EAASG,WAErBzC,GAAKsC,GASLV,EAAG,YAAagC,EAAiB,SAAU/B,GACzC,GAAI2C,GAAS3C,EAAMM,OACfsC,EAAc,GAEdC,EAAc/C,YAAiBvC,GAAS6E,IAAOO,EAASA,EAAOG,WAC/DC,EAAa,EAAeJ,EAAOG,WAAWtE,aAAa,YAAcmE,EAAOG,WAAWtE,aAAa,kBAAoB,GAC5HwE,EAAOL,EAAOnE,aAAa,YAAcuE,GAAc,GACvDE,IAAYD,EACZlB,EAAQa,EAAOnE,aAAa,WAMhC,IAJIoB,EAAQsD,yBAAsE,kBAApCtD,GAAQsD,0BACpDpB,EAAQlC,EAAQsD,wBAAwBpB,IAGtClC,EAAQuD,YAA4C,kBAAvBvD,GAAQuD,WACvCP,EAAchD,EAAQuD,WAAWH,EAAMlB,OAClC,CACL,GAAIlC,EAAQwD,WAAY,CACtB,GAAIC,GAAMvF,EAAS0E,cAAc,WACjCa,GAAIC,UAAYN,EAChBA,EAAOK,EAAIvB,MAKb,GAFAkB,EAAO,uCAAyCA,EAAO,UAEnDC,EACFL,GAAeI,EAAO,WAItB,IAAIlD,YAAiBvC,GAAS6E,IAAK,CACjC,GAAImB,GAAQ7E,EAAKiE,EAAQ,WACrBY,KACFX,GAAehE,EAAK2E,GAAS,QAK/BzB,IACElC,EAAQZ,WAER8C,MADoC7C,IAAlCW,EAAQV,uBACFU,EAAQV,uBAAuB4C,EAAOlC,GAEtCA,EAAQZ,SAAW8C,EAAMxD,QAAQ,+BAAgC,QAG7EwD,EAAQ,wCAA0CA,EAAQ,UAC1Dc,GAAed,GAIhBc,IACDnC,EAAS6C,UAAYV,EACrBrC,EAAYP,GACZjC,EAAK0C,GAGLD,EAASC,EAASC,aAClBC,EAAQF,EAASG,eAIrBb,EAAG,WAAYgC,EAAiB,WAC9B5D,EAAKsC,KAGPV,EAAG,YAAa,KAAM,SAAUC,IAC1B,IAAUJ,EAAQN,eACtBiB,EAAYP,QAwDjBnC,OAAQC,SAAUP,GAEdA,EAASmC,QAAQC", "file": "chartist-plugin-tooltip.min.js", "sourcesContent": ["(function (root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define([\"chartist\"], function (Chartist) {\n      return (root.returnExportsGlobal = factory(Chartist));\n    });\n  } else if (typeof exports === 'object') {\n    // Node. Does not work with strict CommonJS, but\n    // only CommonJS-like enviroments that support module.exports,\n    // like Node.\n    module.exports = factory(require(\"chartist\"));\n  } else {\n    root['Chartist.plugins.tooltip'] = factory(Chartist);\n  }\n}(this, function (Chartist) {\n\n  /**\n  * Chartist.js plugin to display a data label on top of the points in a line chart.\n  *\n  */\n  /* global Chartist */\n  (function (window, document, Chartist) {\n    'use strict';\n\n    var defaultOptions = {\n      currency: undefined,\n      currencyFormatCallback: undefined,\n      tooltipOffset: {\n        x: 0,\n        y: -20\n      },\n      anchorToPoint: false,\n      appendToBody: false,\n      class: undefined,\n      pointClass: 'ct-point'\n    };\n\n    Chartist.plugins = Chartist.plugins || {};\n    Chartist.plugins.tooltip = function (options) {\n      options = Chartist.extend({}, defaultOptions, options);\n\n      return function tooltip(chart) {\n        var tooltipSelector = options.pointClass;\n        if (chart.constructor.name == Chartist.Bar.prototype.constructor.name) {\n          tooltipSelector = 'ct-bar';\n        } else if (chart.constructor.name ==  Chartist.Pie.prototype.constructor.name) {\n          // Added support for donut graph\n          if (chart.options.donut) {\n            tooltipSelector = 'ct-slice-donut';\n          } else {\n            tooltipSelector = 'ct-slice-pie';\n          }\n        }\n\n        var $chart = chart.container;\n        var $toolTip = $chart.querySelector('.chartist-tooltip');\n        if (!$toolTip) {\n          $toolTip = document.createElement('div');\n          $toolTip.className = (!options.class) ? 'chartist-tooltip' : 'chartist-tooltip ' + options.class;\n          if (!options.appendToBody) {\n            $chart.appendChild($toolTip);\n          } else {\n            document.body.appendChild($toolTip);\n          }\n        }\n        var height = $toolTip.offsetHeight;\n        var width = $toolTip.offsetWidth;\n\n        hide($toolTip);\n\n        function on(event, selector, callback) {\n          $chart.addEventListener(event, function (e) {\n            if (!selector || hasClass(e.target, selector))\n            callback(e);\n          });\n        }\n\n        on('mouseover', tooltipSelector, function (event) {\n          var $point = event.target;\n          var tooltipText = '';\n\n          var isPieChart = (chart instanceof Chartist.Pie) ? $point : $point.parentNode;\n          var seriesName = (isPieChart) ? $point.parentNode.getAttribute('ct:meta') || $point.parentNode.getAttribute('ct:series-name') : '';\n          var meta = $point.getAttribute('ct:meta') || seriesName || '';\n          var hasMeta = !!meta;\n          var value = $point.getAttribute('ct:value');\n\n          if (options.transformTooltipTextFnc && typeof options.transformTooltipTextFnc === 'function') {\n            value = options.transformTooltipTextFnc(value);\n          }\n\n          if (options.tooltipFnc && typeof options.tooltipFnc === 'function') {\n            tooltipText = options.tooltipFnc(meta, value);\n          } else {\n            if (options.metaIsHTML) {\n              var txt = document.createElement('textarea');\n              txt.innerHTML = meta;\n              meta = txt.value;\n            }\n\n            meta = '<span class=\"chartist-tooltip-meta\">' + meta + '</span>';\n\n            if (hasMeta) {\n              tooltipText += meta + '<br>';\n            } else {\n              // For Pie Charts also take the labels into account\n              // Could add support for more charts here as well!\n              if (chart instanceof Chartist.Pie) {\n                var label = next($point, 'ct-label');\n                if (label) {\n                  tooltipText += text(label) + '<br>';\n                }\n              }\n            }\n\n            if (value) {\n              if (options.currency) {\n                if (options.currencyFormatCallback != undefined) {\n                  value = options.currencyFormatCallback(value, options);\n                } else {\n                  value = options.currency + value.replace(/(\\d)(?=(\\d{3})+(?:\\.\\d+)?$)/g, '$1,');\n                }\n              }\n              value = '<span class=\"chartist-tooltip-value\">' + value + '</span>';\n              tooltipText += value;\n            }\n          }\n\n          if(tooltipText) {\n            $toolTip.innerHTML = tooltipText;\n            setPosition(event);\n            show($toolTip);\n\n            // Remember height and width to avoid wrong position in IE\n            height = $toolTip.offsetHeight;\n            width = $toolTip.offsetWidth;\n          }\n        });\n\n        on('mouseout', tooltipSelector, function () {\n          hide($toolTip);\n        });\n\n        on('mousemove', null, function (event) {\n          if (false === options.anchorToPoint)\n          setPosition(event);\n        });\n\n        function setPosition(event) {\n          height = height || $toolTip.offsetHeight;\n          width = width || $toolTip.offsetWidth;\n          var offsetX = - width / 2 + options.tooltipOffset.x\n          var offsetY = - height + options.tooltipOffset.y;\n          var anchorX, anchorY;\n\n          if (!options.appendToBody) {\n            var box = $chart.getBoundingClientRect();\n            var left = event.pageX - box.left - window.pageXOffset ;\n            var top = event.pageY - box.top - window.pageYOffset ;\n\n            if (true === options.anchorToPoint && event.target.x2 && event.target.y2) {\n              anchorX = parseInt(event.target.x2.baseVal.value);\n              anchorY = parseInt(event.target.y2.baseVal.value);\n            }\n\n            $toolTip.style.top = (anchorY || top) + offsetY + 'px';\n            $toolTip.style.left = (anchorX || left) + offsetX + 'px';\n          } else {\n            $toolTip.style.top = event.pageY + offsetY + 'px';\n            $toolTip.style.left = event.pageX + offsetX + 'px';\n          }\n        }\n      }\n    };\n\n    function show(element) {\n      if(!hasClass(element, 'tooltip-show')) {\n        element.className = element.className + ' tooltip-show';\n      }\n    }\n\n    function hide(element) {\n      var regex = new RegExp('tooltip-show' + '\\\\s*', 'gi');\n      element.className = element.className.replace(regex, '').trim();\n    }\n\n    function hasClass(element, className) {\n      return (' ' + element.getAttribute('class') + ' ').indexOf(' ' + className + ' ') > -1;\n    }\n\n    function next(element, className) {\n      do {\n        element = element.nextSibling;\n      } while (element && !hasClass(element, className));\n      return element;\n    }\n\n    function text(element) {\n      return element.innerText || element.textContent;\n    }\n\n  } (window, document, Chartist));\n\n  return Chartist.plugins.tooltip;\n\n}));\n"]}