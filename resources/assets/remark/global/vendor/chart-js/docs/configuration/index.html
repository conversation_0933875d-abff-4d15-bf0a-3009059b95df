
<!DOCTYPE HTML>
<html lang="" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>Configuration · GitBook</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.2">
        <meta name="author" content="chartjs">
        
        
    
    <link rel="stylesheet" href="../gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-highlight/website.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
        <link rel="stylesheet" href="../style.css">
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="../gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="../gitbook/images/favicon.ico" type="image/x-icon">

    
    <link rel="next" href="animations.html" />
    
    
    <link rel="prev" href="../general/fonts.html" />
    

    <link rel="stylesheet" href="../gitbook/gitbook-plugin-chartjs/style.css">
    <script src="../gitbook/gitbook-plugin-chartjs/Chart.bundle.js"></script>
    <script src="../gitbook/gitbook-plugin-chartjs/chartjs-plugin-deferred.js"></script>
    

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="Type to search" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="../">
            
                <a href="../">
            
                    
                    Chart.js
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" data-path="../getting-started/">
            
                <a href="../getting-started/">
            
                    
                    Getting Started
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.2.1" data-path="../getting-started/installation.html">
            
                <a href="../getting-started/installation.html">
            
                    
                    Installation
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.2" data-path="../getting-started/integration.html">
            
                <a href="../getting-started/integration.html">
            
                    
                    Integration
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.3" data-path="../getting-started/usage.html">
            
                <a href="../getting-started/usage.html">
            
                    
                    Usage
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3" data-path="../general/">
            
                <a href="../general/">
            
                    
                    General
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1" data-path="../general/responsive.html">
            
                <a href="../general/responsive.html">
            
                    
                    Responsive
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2" data-path="../general/interactions/">
            
                <a href="../general/interactions/">
            
                    
                    Interactions
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.2.1" data-path="../general/interactions/events.html">
            
                <a href="../general/interactions/events.html">
            
                    
                    Events
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.2" data-path="../general/interactions/modes.html">
            
                <a href="../general/interactions/modes.html">
            
                    
                    Modes
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3.3" data-path="../general/options.html">
            
                <a href="../general/options.html">
            
                    
                    Options
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.4" data-path="../general/colors.html">
            
                <a href="../general/colors.html">
            
                    
                    Colors
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.5" data-path="../general/fonts.html">
            
                <a href="../general/fonts.html">
            
                    
                    Fonts
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter active" data-level="1.4" data-path="./">
            
                <a href="./">
            
                    
                    Configuration
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1" data-path="animations.html">
            
                <a href="animations.html">
            
                    
                    Animations
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.2" data-path="layout.html">
            
                <a href="layout.html">
            
                    
                    Layout
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.3" data-path="legend.html">
            
                <a href="legend.html">
            
                    
                    Legend
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.4" data-path="title.html">
            
                <a href="title.html">
            
                    
                    Title
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.5" data-path="tooltip.html">
            
                <a href="tooltip.html">
            
                    
                    Tooltip
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.6" data-path="elements.html">
            
                <a href="elements.html">
            
                    
                    Elements
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.5" data-path="../charts/">
            
                <a href="../charts/">
            
                    
                    Charts
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.5.1" data-path="../charts/line.html">
            
                <a href="../charts/line.html">
            
                    
                    Line
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.2" data-path="../charts/bar.html">
            
                <a href="../charts/bar.html">
            
                    
                    Bar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.3" data-path="../charts/radar.html">
            
                <a href="../charts/radar.html">
            
                    
                    Radar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.4" data-path="../charts/doughnut.html">
            
                <a href="../charts/doughnut.html">
            
                    
                    Doughnut & Pie
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.5" data-path="../charts/polar.html">
            
                <a href="../charts/polar.html">
            
                    
                    Polar Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.6" data-path="../charts/bubble.html">
            
                <a href="../charts/bubble.html">
            
                    
                    Bubble
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.7" data-path="../charts/scatter.html">
            
                <a href="../charts/scatter.html">
            
                    
                    Scatter
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.8" data-path="../charts/area.html">
            
                <a href="../charts/area.html">
            
                    
                    Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.9" data-path="../charts/mixed.html">
            
                <a href="../charts/mixed.html">
            
                    
                    Mixed
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6" data-path="../axes/">
            
                <a href="../axes/">
            
                    
                    Axes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1" data-path="../axes/cartesian/">
            
                <a href="../axes/cartesian/">
            
                    
                    Cartesian
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1.1" data-path="../axes/cartesian/category.html">
            
                <a href="../axes/cartesian/category.html">
            
                    
                    Category
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.2" data-path="../axes/cartesian/linear.html">
            
                <a href="../axes/cartesian/linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.3" data-path="../axes/cartesian/logarithmic.html">
            
                <a href="../axes/cartesian/logarithmic.html">
            
                    
                    Logarithmic
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.4" data-path="../axes/cartesian/time.html">
            
                <a href="../axes/cartesian/time.html">
            
                    
                    Time
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.2" data-path="../axes/radial/">
            
                <a href="../axes/radial/">
            
                    
                    Radial
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.2.1" data-path="../axes/radial/linear.html">
            
                <a href="../axes/radial/linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.3" data-path="../axes/labelling.html">
            
                <a href="../axes/labelling.html">
            
                    
                    Labelling
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.4" data-path="../axes/styling.html">
            
                <a href="../axes/styling.html">
            
                    
                    Styling
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.7" data-path="../developers/">
            
                <a href="../developers/">
            
                    
                    Developers
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.7.1" data-path="../developers/api.html">
            
                <a href="../developers/api.html">
            
                    
                    Chart.js API
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.2" data-path="../developers/updates.html">
            
                <a href="../developers/updates.html">
            
                    
                    Updating Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.3" data-path="../developers/plugins.html">
            
                <a href="../developers/plugins.html">
            
                    
                    Plugins
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.4" data-path="../developers/charts.html">
            
                <a href="../developers/charts.html">
            
                    
                    New Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.5" data-path="../developers/axes.html">
            
                <a href="../developers/axes.html">
            
                    
                    New Axes
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.6" data-path="../developers/contributing.html">
            
                <a href="../developers/contributing.html">
            
                    
                    Contributing
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.8" data-path="../notes/">
            
                <a href="../notes/">
            
                    
                    Additional Notes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.8.1" data-path="../notes/comparison.html">
            
                <a href="../notes/comparison.html">
            
                    
                    Comparison Table
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.2" data-path="../notes/extensions.html">
            
                <a href="../notes/extensions.html">
            
                    
                    Popular Extensions
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.3" data-path="../notes/license.html">
            
                <a href="../notes/license.html">
            
                    
                    License
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            Published with GitBook
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href=".." >Configuration</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <h1 id="configuration">Configuration</h1>
<p>The configuration is used to change how the chart behaves. There are properties to control styling, fonts, the legend, etc.</p>
<h2 id="global-configuration">Global Configuration</h2>
<p>This concept was introduced in Chart.js 1.0 to keep configuration <a href="https://en.wikipedia.org/wiki/Don%27t_repeat_yourself" target="_blank">DRY</a>, and allow for changing options globally across chart types, avoiding the need to specify options for each instance, or the default for a particular chart type.</p>
<p>Chart.js merges the options object passed to the chart with the global configuration using chart type defaults and scales defaults appropriately. This way you can be as specific as you would like in your individual chart configuration, while still changing the defaults for all chart types where applicable. The global general options are defined in <code>Chart.defaults.global</code>. The defaults for each chart type are discussed in the documentation for that chart type.</p>
<p>The following example would set the hover mode to &apos;nearest&apos; for all charts where this was not overridden by the chart type defaults or the options passed to the constructor on creation.</p>
<pre><code class="lang-javascript">Chart.defaults.global.hover.mode = <span class="hljs-string">&apos;nearest&apos;</span>;

<span class="hljs-comment">// Hover mode is set to nearest because it was not overridden here</span>
<span class="hljs-keyword">var</span> chartHoverModeNearest  = <span class="hljs-keyword">new</span> Chart(ctx, {
    type: <span class="hljs-string">&apos;line&apos;</span>,
    data: data,
});

<span class="hljs-comment">// This chart would have the hover mode that was passed in</span>
<span class="hljs-keyword">var</span> chartDifferentHoverMode = <span class="hljs-keyword">new</span> Chart(ctx, {
    type: <span class="hljs-string">&apos;line&apos;</span>,
    data: data,
    options: {
        hover: {
            <span class="hljs-comment">// Overrides the global setting</span>
            mode: <span class="hljs-string">&apos;index&apos;</span>
        }
    }
})
</code></pre>

                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="../general/fonts.html" class="navigation navigation-prev " aria-label="Previous page: Fonts">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
                <a href="animations.html" class="navigation navigation-next " aria-label="Next page: Animations">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"Configuration","level":"1.4","depth":1,"next":{"title":"Animations","level":"1.4.1","depth":2,"path":"configuration/animations.md","ref":"configuration/animations.md","articles":[]},"previous":{"title":"Fonts","level":"1.3.5","depth":2,"path":"general/fonts.md","ref":"general/fonts.md","articles":[]},"dir":"ltr"},"config":{"plugins":["-lunr","-search","search-plus","anchorjs","chartjs","ga"],"root":"./docs","styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"anchorjs":{"icon":"#","placement":"left","visible":"always"},"ga":{"configuration":"auto","token":"UA-28909194-3"},"theme-default":{"styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"search-plus":{},"chartjs":{"defaults":null},"highlight":{},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"fontsettings":{"theme":"white","family":"sans","size":2}},"theme":"default","author":"chartjs","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{},"gitbook":"3.2.2"},"file":{"path":"configuration/README.md","mtime":"2017-10-28T15:03:49.266Z","type":"markdown"},"gitbook":{"version":"3.2.2","time":"2017-10-28T15:09:53.587Z"},"basePath":"..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="../gitbook/gitbook.js"></script>
    <script src="../gitbook/theme.js"></script>
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="https://cdnjs.cloudflare.com/ajax/libs/anchor-js/3.1.1/anchor.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-anchorjs/anchor-style.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-ga/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-sharing/buttons.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>

