
<!DOCTYPE HTML>
<html lang="" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>Linear · GitBook</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.2">
        <meta name="author" content="chartjs">
        
        
    
    <link rel="stylesheet" href="../../gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="../../gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="../../gitbook/gitbook-plugin-highlight/website.css">
                
            
                
                <link rel="stylesheet" href="../../gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
        <link rel="stylesheet" href="../../style.css">
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="../../gitbook/images/favicon.ico" type="image/x-icon">

    
    <link rel="next" href="logarithmic.html" />
    
    
    <link rel="prev" href="category.html" />
    

    <link rel="stylesheet" href="../../gitbook/gitbook-plugin-chartjs/style.css">
    <script src="../../gitbook/gitbook-plugin-chartjs/Chart.bundle.js"></script>
    <script src="../../gitbook/gitbook-plugin-chartjs/chartjs-plugin-deferred.js"></script>
    

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="Type to search" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="../../">
            
                <a href="../../">
            
                    
                    Chart.js
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" data-path="../../getting-started/">
            
                <a href="../../getting-started/">
            
                    
                    Getting Started
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.2.1" data-path="../../getting-started/installation.html">
            
                <a href="../../getting-started/installation.html">
            
                    
                    Installation
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.2" data-path="../../getting-started/integration.html">
            
                <a href="../../getting-started/integration.html">
            
                    
                    Integration
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.3" data-path="../../getting-started/usage.html">
            
                <a href="../../getting-started/usage.html">
            
                    
                    Usage
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3" data-path="../../general/">
            
                <a href="../../general/">
            
                    
                    General
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1" data-path="../../general/responsive.html">
            
                <a href="../../general/responsive.html">
            
                    
                    Responsive
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2" data-path="../../general/interactions/">
            
                <a href="../../general/interactions/">
            
                    
                    Interactions
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.2.1" data-path="../../general/interactions/events.html">
            
                <a href="../../general/interactions/events.html">
            
                    
                    Events
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.2" data-path="../../general/interactions/modes.html">
            
                <a href="../../general/interactions/modes.html">
            
                    
                    Modes
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3.3" data-path="../../general/options.html">
            
                <a href="../../general/options.html">
            
                    
                    Options
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.4" data-path="../../general/colors.html">
            
                <a href="../../general/colors.html">
            
                    
                    Colors
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.5" data-path="../../general/fonts.html">
            
                <a href="../../general/fonts.html">
            
                    
                    Fonts
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4" data-path="../../configuration/">
            
                <a href="../../configuration/">
            
                    
                    Configuration
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1" data-path="../../configuration/animations.html">
            
                <a href="../../configuration/animations.html">
            
                    
                    Animations
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.2" data-path="../../configuration/layout.html">
            
                <a href="../../configuration/layout.html">
            
                    
                    Layout
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.3" data-path="../../configuration/legend.html">
            
                <a href="../../configuration/legend.html">
            
                    
                    Legend
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.4" data-path="../../configuration/title.html">
            
                <a href="../../configuration/title.html">
            
                    
                    Title
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.5" data-path="../../configuration/tooltip.html">
            
                <a href="../../configuration/tooltip.html">
            
                    
                    Tooltip
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.6" data-path="../../configuration/elements.html">
            
                <a href="../../configuration/elements.html">
            
                    
                    Elements
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.5" data-path="../../charts/">
            
                <a href="../../charts/">
            
                    
                    Charts
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.5.1" data-path="../../charts/line.html">
            
                <a href="../../charts/line.html">
            
                    
                    Line
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.2" data-path="../../charts/bar.html">
            
                <a href="../../charts/bar.html">
            
                    
                    Bar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.3" data-path="../../charts/radar.html">
            
                <a href="../../charts/radar.html">
            
                    
                    Radar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.4" data-path="../../charts/doughnut.html">
            
                <a href="../../charts/doughnut.html">
            
                    
                    Doughnut & Pie
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.5" data-path="../../charts/polar.html">
            
                <a href="../../charts/polar.html">
            
                    
                    Polar Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.6" data-path="../../charts/bubble.html">
            
                <a href="../../charts/bubble.html">
            
                    
                    Bubble
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.7" data-path="../../charts/scatter.html">
            
                <a href="../../charts/scatter.html">
            
                    
                    Scatter
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.8" data-path="../../charts/area.html">
            
                <a href="../../charts/area.html">
            
                    
                    Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.9" data-path="../../charts/mixed.html">
            
                <a href="../../charts/mixed.html">
            
                    
                    Mixed
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6" data-path="../">
            
                <a href="../">
            
                    
                    Axes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1" data-path="./">
            
                <a href="./">
            
                    
                    Cartesian
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="*******" data-path="category.html">
            
                <a href="category.html">
            
                    
                    Category
            
                </a>
            

            
        </li>
    
        <li class="chapter active" data-level="*******" data-path="linear.html">
            
                <a href="linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="*******" data-path="logarithmic.html">
            
                <a href="logarithmic.html">
            
                    
                    Logarithmic
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.4" data-path="time.html">
            
                <a href="time.html">
            
                    
                    Time
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.2" data-path="../radial/">
            
                <a href="../radial/">
            
                    
                    Radial
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.2.1" data-path="../radial/linear.html">
            
                <a href="../radial/linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.3" data-path="../labelling.html">
            
                <a href="../labelling.html">
            
                    
                    Labelling
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.4" data-path="../styling.html">
            
                <a href="../styling.html">
            
                    
                    Styling
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.7" data-path="../../developers/">
            
                <a href="../../developers/">
            
                    
                    Developers
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.7.1" data-path="../../developers/api.html">
            
                <a href="../../developers/api.html">
            
                    
                    Chart.js API
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.2" data-path="../../developers/updates.html">
            
                <a href="../../developers/updates.html">
            
                    
                    Updating Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.3" data-path="../../developers/plugins.html">
            
                <a href="../../developers/plugins.html">
            
                    
                    Plugins
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.4" data-path="../../developers/charts.html">
            
                <a href="../../developers/charts.html">
            
                    
                    New Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.5" data-path="../../developers/axes.html">
            
                <a href="../../developers/axes.html">
            
                    
                    New Axes
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.6" data-path="../../developers/contributing.html">
            
                <a href="../../developers/contributing.html">
            
                    
                    Contributing
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.8" data-path="../../notes/">
            
                <a href="../../notes/">
            
                    
                    Additional Notes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.8.1" data-path="../../notes/comparison.html">
            
                <a href="../../notes/comparison.html">
            
                    
                    Comparison Table
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.2" data-path="../../notes/extensions.html">
            
                <a href="../../notes/extensions.html">
            
                    
                    Popular Extensions
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.3" data-path="../../notes/license.html">
            
                <a href="../../notes/license.html">
            
                    
                    License
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            Published with GitBook
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href="../.." >Linear</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <h1 id="linear-cartesian-axis">Linear Cartesian Axis</h1>
<p>The linear scale is use to chart numerical data. It can be placed on either the x or y axis. The scatter chart type automatically configures a line chart to use one of these scales for the x axis. As the name suggests, linear interpolation is used to determine where a value lies on the axis.</p>
<h2 id="tick-configuration-options">Tick Configuration Options</h2>
<p>The following options are provided by the linear scale. They are all located in the <code>ticks</code> sub options. These options extend the <a href="./#tick-configuration">common tick configuration</a>.</p>
<table>
<thead>
<tr>
<th>Name</th>
<th>Type</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>beginAtZero</code></td>
<td><code>Boolean</code></td>
<td></td>
<td>if true, scale will include 0 if it is not already included.</td>
</tr>
<tr>
<td><code>min</code></td>
<td><code>Number</code></td>
<td></td>
<td>User defined minimum number for the scale, overrides minimum value from data. <a href="#axis-range-settings">more...</a></td>
</tr>
<tr>
<td><code>max</code></td>
<td><code>Number</code></td>
<td></td>
<td>User defined maximum number for the scale, overrides maximum value from data. <a href="#axis-range-settings">more...</a></td>
</tr>
<tr>
<td><code>maxTicksLimit</code></td>
<td><code>Number</code></td>
<td><code>11</code></td>
<td>Maximum number of ticks and gridlines to show.</td>
</tr>
<tr>
<td><code>stepSize</code></td>
<td><code>Number</code></td>
<td></td>
<td>User defined fixed step size for the scale. <a href="#step-size">more...</a></td>
</tr>
<tr>
<td><code>suggestedMax</code></td>
<td><code>Number</code></td>
<td></td>
<td>Adjustment used when calculating the maximum data value. <a href="#axis-range-settings">more...</a></td>
</tr>
<tr>
<td><code>suggestedMin</code></td>
<td><code>Number</code></td>
<td></td>
<td>Adjustment used when calculating the minimum data value. <a href="#axis-range-settings">more...</a></td>
</tr>
</tbody>
</table>
<h2 id="axis-range-settings">Axis Range Settings</h2>
<p>Given the number of axis range settings, it is important to understand how they all interact with each other.</p>
<p>The <code>suggestedMax</code> and <code>suggestedMin</code> settings only change the data values that are used to scale the axis. These are useful for extending the range of the axis while maintaing the auto fit behaviour.</p>
<pre><code class="lang-javascript"><span class="hljs-keyword">let</span> minDataValue = <span class="hljs-built_in">Math</span>.min(mostNegativeValue, options.ticks.suggestedMin);
<span class="hljs-keyword">let</span> maxDataValue = <span class="hljs-built_in">Math</span>.max(mostPositiveValue, options.ticks.suggestedMax);
</code></pre>
<p>In this example, the largest positive value is 50, but the data maximum is expanded out to 100. However, because the lowest data value is below the <code>suggestedMin</code> setting, it is ignored.</p>
<pre><code class="lang-javascript"><span class="hljs-keyword">let</span> chart = <span class="hljs-keyword">new</span> Chart(ctx, {
    type: <span class="hljs-string">&apos;line&apos;</span>,
    data: {
        datasets: [{
            label: <span class="hljs-string">&apos;First dataset&apos;</span>,
            data: [<span class="hljs-number">0</span>, <span class="hljs-number">20</span>, <span class="hljs-number">40</span>, <span class="hljs-number">50</span>]
        }],
        labels: [<span class="hljs-string">&apos;January&apos;</span>, <span class="hljs-string">&apos;February&apos;</span>, <span class="hljs-string">&apos;March&apos;</span>, <span class="hljs-string">&apos;April&apos;</span>]
    },
    options: {
        scales: {
            yAxes: [{
                ticks: {
                    suggestedMin: <span class="hljs-number">50</span>
                    suggestedMax: <span class="hljs-number">100</span>
                }
            }]
        }
    }
});
</code></pre>
<p>In contrast to the <code>suggested*</code> settings, the <code>min</code> and <code>max</code> settings set explicit ends to the axes. When these are set, some data points may not be visible.</p>
<h2 id="step-size">Step Size</h2>
<p> If set, the scale ticks will be enumerated by multiple of stepSize, having one tick per increment. If not set, the ticks are labeled automatically using the nice numbers algorithm.</p>
<p>This example sets up a chart with a y axis that creates ticks at <code>0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5</code>.</p>
<pre><code class="lang-javascript"><span class="hljs-keyword">let</span> options = {
    scales: {
        yAxes: [{
            ticks: {
                max: <span class="hljs-number">5</span>,
                min: <span class="hljs-number">0</span>,
                stepSize: <span class="hljs-number">0.5</span>
            }
        }]
    }
};
</code></pre>

                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="category.html" class="navigation navigation-prev " aria-label="Previous page: Category">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
                <a href="logarithmic.html" class="navigation navigation-next " aria-label="Next page: Logarithmic">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"Linear","level":"*******","depth":3,"next":{"title":"Logarithmic","level":"*******","depth":3,"path":"axes/cartesian/logarithmic.md","ref":"axes/cartesian/logarithmic.md","articles":[]},"previous":{"title":"Category","level":"*******","depth":3,"path":"axes/cartesian/category.md","ref":"axes/cartesian/category.md","articles":[]},"dir":"ltr"},"config":{"plugins":["-lunr","-search","search-plus","anchorjs","chartjs","ga"],"root":"./docs","styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"anchorjs":{"icon":"#","placement":"left","visible":"always"},"ga":{"configuration":"auto","token":"UA-28909194-3"},"theme-default":{"styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"search-plus":{},"chartjs":{"defaults":null},"highlight":{},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"fontsettings":{"theme":"white","family":"sans","size":2}},"theme":"default","author":"chartjs","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{},"gitbook":"3.2.2"},"file":{"path":"axes/cartesian/linear.md","mtime":"2017-10-28T15:03:49.266Z","type":"markdown"},"gitbook":{"version":"3.2.2","time":"2017-10-28T15:09:53.587Z"},"basePath":"../..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="../../gitbook/gitbook.js"></script>
    <script src="../../gitbook/theme.js"></script>
    
        
        <script src="../../gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="../../gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="https://cdnjs.cloudflare.com/ajax/libs/anchor-js/3.1.1/anchor.min.js"></script>
        
    
        
        <script src="../../gitbook/gitbook-plugin-anchorjs/anchor-style.js"></script>
        
    
        
        <script src="../../gitbook/gitbook-plugin-ga/plugin.js"></script>
        
    
        
        <script src="../../gitbook/gitbook-plugin-sharing/buttons.js"></script>
        
    
        
        <script src="../../gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>

