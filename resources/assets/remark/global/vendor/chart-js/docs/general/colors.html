
<!DOCTYPE HTML>
<html lang="" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>Colors · GitBook</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.2">
        <meta name="author" content="chartjs">
        
        
    
    <link rel="stylesheet" href="../gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-highlight/website.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
        <link rel="stylesheet" href="../style.css">
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="../gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="../gitbook/images/favicon.ico" type="image/x-icon">

    
    <link rel="next" href="fonts.html" />
    
    
    <link rel="prev" href="options.html" />
    

    <link rel="stylesheet" href="../gitbook/gitbook-plugin-chartjs/style.css">
    <script src="../gitbook/gitbook-plugin-chartjs/Chart.bundle.js"></script>
    <script src="../gitbook/gitbook-plugin-chartjs/chartjs-plugin-deferred.js"></script>
    

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="Type to search" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="../">
            
                <a href="../">
            
                    
                    Chart.js
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" data-path="../getting-started/">
            
                <a href="../getting-started/">
            
                    
                    Getting Started
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.2.1" data-path="../getting-started/installation.html">
            
                <a href="../getting-started/installation.html">
            
                    
                    Installation
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.2" data-path="../getting-started/integration.html">
            
                <a href="../getting-started/integration.html">
            
                    
                    Integration
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.3" data-path="../getting-started/usage.html">
            
                <a href="../getting-started/usage.html">
            
                    
                    Usage
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3" data-path="./">
            
                <a href="./">
            
                    
                    General
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1" data-path="responsive.html">
            
                <a href="responsive.html">
            
                    
                    Responsive
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2" data-path="interactions/">
            
                <a href="interactions/">
            
                    
                    Interactions
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.2.1" data-path="interactions/events.html">
            
                <a href="interactions/events.html">
            
                    
                    Events
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.2" data-path="interactions/modes.html">
            
                <a href="interactions/modes.html">
            
                    
                    Modes
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3.3" data-path="options.html">
            
                <a href="options.html">
            
                    
                    Options
            
                </a>
            

            
        </li>
    
        <li class="chapter active" data-level="1.3.4" data-path="colors.html">
            
                <a href="colors.html">
            
                    
                    Colors
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.5" data-path="fonts.html">
            
                <a href="fonts.html">
            
                    
                    Fonts
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4" data-path="../configuration/">
            
                <a href="../configuration/">
            
                    
                    Configuration
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1" data-path="../configuration/animations.html">
            
                <a href="../configuration/animations.html">
            
                    
                    Animations
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.2" data-path="../configuration/layout.html">
            
                <a href="../configuration/layout.html">
            
                    
                    Layout
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.3" data-path="../configuration/legend.html">
            
                <a href="../configuration/legend.html">
            
                    
                    Legend
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.4" data-path="../configuration/title.html">
            
                <a href="../configuration/title.html">
            
                    
                    Title
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.5" data-path="../configuration/tooltip.html">
            
                <a href="../configuration/tooltip.html">
            
                    
                    Tooltip
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.6" data-path="../configuration/elements.html">
            
                <a href="../configuration/elements.html">
            
                    
                    Elements
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.5" data-path="../charts/">
            
                <a href="../charts/">
            
                    
                    Charts
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.5.1" data-path="../charts/line.html">
            
                <a href="../charts/line.html">
            
                    
                    Line
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.2" data-path="../charts/bar.html">
            
                <a href="../charts/bar.html">
            
                    
                    Bar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.3" data-path="../charts/radar.html">
            
                <a href="../charts/radar.html">
            
                    
                    Radar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.4" data-path="../charts/doughnut.html">
            
                <a href="../charts/doughnut.html">
            
                    
                    Doughnut & Pie
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.5" data-path="../charts/polar.html">
            
                <a href="../charts/polar.html">
            
                    
                    Polar Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.6" data-path="../charts/bubble.html">
            
                <a href="../charts/bubble.html">
            
                    
                    Bubble
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.7" data-path="../charts/scatter.html">
            
                <a href="../charts/scatter.html">
            
                    
                    Scatter
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.8" data-path="../charts/area.html">
            
                <a href="../charts/area.html">
            
                    
                    Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.9" data-path="../charts/mixed.html">
            
                <a href="../charts/mixed.html">
            
                    
                    Mixed
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6" data-path="../axes/">
            
                <a href="../axes/">
            
                    
                    Axes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1" data-path="../axes/cartesian/">
            
                <a href="../axes/cartesian/">
            
                    
                    Cartesian
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1.1" data-path="../axes/cartesian/category.html">
            
                <a href="../axes/cartesian/category.html">
            
                    
                    Category
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.2" data-path="../axes/cartesian/linear.html">
            
                <a href="../axes/cartesian/linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.3" data-path="../axes/cartesian/logarithmic.html">
            
                <a href="../axes/cartesian/logarithmic.html">
            
                    
                    Logarithmic
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.4" data-path="../axes/cartesian/time.html">
            
                <a href="../axes/cartesian/time.html">
            
                    
                    Time
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.2" data-path="../axes/radial/">
            
                <a href="../axes/radial/">
            
                    
                    Radial
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.2.1" data-path="../axes/radial/linear.html">
            
                <a href="../axes/radial/linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.3" data-path="../axes/labelling.html">
            
                <a href="../axes/labelling.html">
            
                    
                    Labelling
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.4" data-path="../axes/styling.html">
            
                <a href="../axes/styling.html">
            
                    
                    Styling
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.7" data-path="../developers/">
            
                <a href="../developers/">
            
                    
                    Developers
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.7.1" data-path="../developers/api.html">
            
                <a href="../developers/api.html">
            
                    
                    Chart.js API
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.2" data-path="../developers/updates.html">
            
                <a href="../developers/updates.html">
            
                    
                    Updating Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.3" data-path="../developers/plugins.html">
            
                <a href="../developers/plugins.html">
            
                    
                    Plugins
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.4" data-path="../developers/charts.html">
            
                <a href="../developers/charts.html">
            
                    
                    New Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.5" data-path="../developers/axes.html">
            
                <a href="../developers/axes.html">
            
                    
                    New Axes
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.6" data-path="../developers/contributing.html">
            
                <a href="../developers/contributing.html">
            
                    
                    Contributing
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.8" data-path="../notes/">
            
                <a href="../notes/">
            
                    
                    Additional Notes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.8.1" data-path="../notes/comparison.html">
            
                <a href="../notes/comparison.html">
            
                    
                    Comparison Table
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.2" data-path="../notes/extensions.html">
            
                <a href="../notes/extensions.html">
            
                    
                    Popular Extensions
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.3" data-path="../notes/license.html">
            
                <a href="../notes/license.html">
            
                    
                    License
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            Published with GitBook
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href=".." >Colors</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <h1 id="colors">Colors</h1>
<p>When supplying colors to Chart options, you can use a number of formats. You can specify the color as a string in hexadecimal, RGB, or HSL notations. If a color is needed, but not specified, Chart.js will use the global default color. This color is stored at <code>Chart.defaults.global.defaultColor</code>. It is initially set to <code>&apos;rgba(0, 0, 0, 0.1)&apos;</code></p>
<p>You can also pass a <a href="https://developer.mozilla.org/en-US/docs/Web/API/CanvasGradient" target="_blank">CanvasGradient</a> object. You will need to create this before passing to the chart, but using it you can achieve some interesting effects.</p>
<h2 id="patterns-and-gradients">Patterns and Gradients</h2>
<p>An alternative option is to pass a <a href="https://developer.mozilla.org/en-US/docs/Web/API/CanvasPattern" target="_blank">CanvasPattern</a> or <a href="https://developer.mozilla.org/en/docs/Web/API/CanvasGradient" target="_blank">CanvasGradient</a> object instead of a string colour. </p>
<p>For example, if you wanted to fill a dataset with a pattern from an image you could do the following.</p>
<pre><code class="lang-javascript"><span class="hljs-keyword">var</span> img = <span class="hljs-keyword">new</span> Image();
img.src = <span class="hljs-string">&apos;https://example.com/my_image.png&apos;</span>;
img.onload = <span class="hljs-function"><span class="hljs-keyword">function</span>(<span class="hljs-params"></span>) </span>{
    <span class="hljs-keyword">var</span> ctx = <span class="hljs-built_in">document</span>.getElementById(<span class="hljs-string">&apos;canvas&apos;</span>).getContext(<span class="hljs-string">&apos;2d&apos;</span>);
    <span class="hljs-keyword">var</span> fillPattern = ctx.createPattern(img, <span class="hljs-string">&apos;repeat&apos;</span>);

    <span class="hljs-keyword">var</span> chart = <span class="hljs-keyword">new</span> Chart(ctx, {
        data: {
            labels: [<span class="hljs-string">&apos;Item 1&apos;</span>, <span class="hljs-string">&apos;Item 2&apos;</span>, <span class="hljs-string">&apos;Item 3&apos;</span>],
            datasets: [{
                data: [<span class="hljs-number">10</span>, <span class="hljs-number">20</span>, <span class="hljs-number">30</span>],
                backgroundColor: fillPattern
            }]
        }
    })
}
</code></pre>
<p>Using pattern fills for data graphics can help viewers with vision deficiencies (e.g. color-blindness or partial sight) to <a href="http://betweentwobrackets.com/data-graphics-and-colour-vision/" target="_blank">more easily understand your data</a>.</p>
<p>Using the <a href="https://github.com/ashiguruma/patternomaly" target="_blank">Patternomaly</a> library you can generate patterns to fill datasets.</p>
<pre><code class="lang-javascript"><span class="hljs-keyword">var</span> chartData = {
    datasets: [{
        data: [<span class="hljs-number">45</span>, <span class="hljs-number">25</span>, <span class="hljs-number">20</span>, <span class="hljs-number">10</span>],
        backgroundColor: [
            pattern.draw(<span class="hljs-string">&apos;square&apos;</span>, <span class="hljs-string">&apos;#ff6384&apos;</span>),
            pattern.draw(<span class="hljs-string">&apos;circle&apos;</span>, <span class="hljs-string">&apos;#36a2eb&apos;</span>),
            pattern.draw(<span class="hljs-string">&apos;diamond&apos;</span>, <span class="hljs-string">&apos;#cc65fe&apos;</span>),
            pattern.draw(<span class="hljs-string">&apos;triangle&apos;</span>, <span class="hljs-string">&apos;#ffce56&apos;</span>),
        ]
    }],
    labels: [<span class="hljs-string">&apos;Red&apos;</span>, <span class="hljs-string">&apos;Blue&apos;</span>, <span class="hljs-string">&apos;Purple&apos;</span>, <span class="hljs-string">&apos;Yellow&apos;</span>]
};
</code></pre>

                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="options.html" class="navigation navigation-prev " aria-label="Previous page: Options">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
                <a href="fonts.html" class="navigation navigation-next " aria-label="Next page: Fonts">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"Colors","level":"1.3.4","depth":2,"next":{"title":"Fonts","level":"1.3.5","depth":2,"path":"general/fonts.md","ref":"general/fonts.md","articles":[]},"previous":{"title":"Options","level":"1.3.3","depth":2,"path":"general/options.md","ref":"general/options.md","articles":[]},"dir":"ltr"},"config":{"plugins":["-lunr","-search","search-plus","anchorjs","chartjs","ga"],"root":"./docs","styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"anchorjs":{"icon":"#","placement":"left","visible":"always"},"ga":{"configuration":"auto","token":"UA-28909194-3"},"theme-default":{"styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"search-plus":{},"chartjs":{"defaults":null},"highlight":{},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"fontsettings":{"theme":"white","family":"sans","size":2}},"theme":"default","author":"chartjs","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{},"gitbook":"3.2.2"},"file":{"path":"general/colors.md","mtime":"2017-10-28T15:03:49.266Z","type":"markdown"},"gitbook":{"version":"3.2.2","time":"2017-10-28T15:09:53.587Z"},"basePath":"..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="../gitbook/gitbook.js"></script>
    <script src="../gitbook/theme.js"></script>
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="https://cdnjs.cloudflare.com/ajax/libs/anchor-js/3.1.1/anchor.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-anchorjs/anchor-style.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-ga/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-sharing/buttons.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>

