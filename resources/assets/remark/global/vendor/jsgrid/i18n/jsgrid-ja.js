(function(jsGrid) {

    jsGrid.locales.ja = {
        grid: {
            noDataContent: "データが見つかりません。",
            deleteConfirm: "削除しますよろしですか。",
            pagerFormat: "頁: {first} {prev} {pages} {next} {last} &nbsp;&nbsp; 【{pageIndex}／{pageCount}】",
            pagePrevText: "前",
            pageNextText: "次",
            pageFirstText: "最初",
            pageLastText: "最後",
            loadMessage: "しばらくお待ちください…",
            invalidMessage: "入力されたデータが不正です。"
        },

        loadIndicator: {
            message: "処理中…"
        },

        fields: {
            control: {
                searchModeButtonTooltip: "検索モードへ",
                insertModeButtonTooltip: "登録モードへ",
                editButtonTooltip: "編集",
                deleteButtonTooltip: "削除",
                searchButtonTooltip: "フィルター",
                clearFilterButtonTooltip: "クリア",
                insertButtonTooltip: "登録",
                updateButtonTooltip: "更新",
                cancelEditButtonTooltip: "編集戻す"
            }
        },

        validators: {
            required: { message: "項目が必要です。" },
            rangeLength: { message: "項目の桁数が範囲外です。" },
            minLength: { message: "項目の桁数が超過しています。" },
            maxLength: { message: "項目の桁数が不足しています。" },
            pattern: { message: "項目の値がパターンに一致しません。" },
            range: { message: "項目の値が範囲外です。" },
            min: { message: "項目の値が超過しています。" },
            max: { message: "項目の値が不足しています。" }
        }
    };

}(jsGrid, jQuery));