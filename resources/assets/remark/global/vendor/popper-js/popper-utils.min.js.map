{"version": 3, "file": "popper-utils.min.js", "sources": ["../src/utils/getStyleComputedProperty.js", "../src/utils/getParentNode.js", "../src/utils/getScrollParent.js", "../src/utils/getOffsetParent.js", "../src/utils/isOffsetContainer.js", "../src/utils/getRoot.js", "../src/utils/findCommonOffsetParent.js", "../src/utils/getScroll.js", "../src/utils/includeScroll.js", "../src/utils/getBordersSize.js", "../src/utils/isIE10.js", "../src/utils/getWindowSizes.js", "../src/utils/getClientRect.js", "../src/utils/getBoundingClientRect.js", "../src/utils/getOffsetRectRelativeToArbitraryNode.js", "../src/utils/getViewportOffsetRectRelativeToArtbitraryNode.js", "../src/utils/isFixed.js", "../src/utils/getBoundaries.js", "../src/utils/computeAutoPlacement.js", "../src/utils/debounce.js", "../src/utils/find.js", "../src/utils/findIndex.js", "../src/utils/getOffsetRect.js", "../src/utils/getOuterSizes.js", "../src/utils/getOppositePlacement.js", "../src/utils/getPopperOffsets.js", "../src/utils/getReferenceOffsets.js", "../src/utils/getSupportedPropertyName.js", "../src/utils/isFunction.js", "../src/utils/isModifierEnabled.js", "../src/utils/isModifierRequired.js", "../src/utils/isNumeric.js", "../src/utils/getWindow.js", "../src/utils/removeEventListeners.js", "../src/utils/runModifiers.js", "../src/utils/setAttributes.js", "../src/utils/setStyles.js", "../src/utils/setupEventListeners.js", "../src/utils/index.js"], "sourcesContent": ["/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return window.document.body\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body\n    case '#document':\n      return element.body\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  // NOTE: 1 DOM access here\n  const offsetParent = element && element.offsetParent;\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    if (element) {\n      return element.ownerDocument.documentElement\n    }\n\n    return window.document.documentElement;\n  }\n\n  // .offsetParent will return the closest TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (\n    ['TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 &&\n    getStyleComputedProperty(offsetParent, 'position') === 'static'\n  ) {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n", "import getOffsetParent from './getOffsetParent';\n\nexport default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return window.document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    (element1 !== commonAncestorContainer &&\n      element2 !== commonAncestorContainer) ||\n    start.contains(end)\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    +styles[`border${sideA}Width`].split('px')[0] +\n    +styles[`border${sideB}Width`].split('px')[0]\n  );\n}\n", "/**\n * Tells if you are running Internet Explorer 10\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean} isIE10\n */\nlet isIE10 = undefined;\n\nexport default function() {\n  if (isIE10 === undefined) {\n    isIE10 = navigator.appVersion.indexOf('MSIE 10') !== -1;\n  }\n  return isIE10;\n}\n", "import isIE10 from './isIE10';\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(\n    body[`offset${axis}`],\n    body[`scroll${axis}`],\n    html[`client${axis}`],\n    html[`offset${axis}`],\n    html[`scroll${axis}`],\n    isIE10()\n      ? html[`offset${axis}`] +\n        computedStyle[`margin${axis === 'Height' ? 'Top' : 'Left'}`] +\n        computedStyle[`margin${axis === 'Height' ? 'Bottom' : 'Right'}`]\n      : 0\n  );\n}\n\nexport default function getWindowSizes() {\n  const body = window.document.body;\n  const html = window.document.documentElement;\n  const computedStyle = isIE10() && window.getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport isIE10 from './isIE10';\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  if (isIE10()) {\n    try {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } catch (err) {}\n  } else {\n    rect = element.getBoundingClientRect();\n  }\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes() : {};\n  const width =\n    sizes.width || element.clientWidth || result.right - result.left;\n  const height =\n    sizes.height || element.clientHeight || result.bottom - result.top;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE10 from './isIE10';\nimport getClientRect from './getClientRect';\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent) {\n  const isIE10 = runIsIE10();\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = +styles.borderTopWidth.split('px')[0];\n  const borderLeftWidth = +styles.borderLeftWidth.split('px')[0];\n\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    const marginTop = +styles.marginTop.split('px')[0];\n    const marginLeft = +styles.marginLeft.split('px')[0];\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    isIE10\n      ? parent.contains(scrollParent)\n      : parent === scrollParent && scrollParent.nodeName !== 'BODY'\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = getScroll(html);\n  const scrollLeft = getScroll(html, 'left');\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement\n) {\n  // NOTE: 1 DOM access here\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = findCommonOffsetParent(popper, reference);\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(popper));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes();\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  boundaries.left += padding;\n  boundaries.top += padding;\n  boundaries.right -= padding;\n  boundaries.bottom -= padding;\n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\nfunction getArea({ width, height }) {\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference,\n  boundariesElement,\n  padding = 0\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(\n    popper,\n    reference,\n    padding,\n    boundariesElement\n  );\n\n  const rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top,\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height,\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom,\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height,\n    },\n  };\n\n  const sortedAreas = Object.keys(rects)\n    .map(key => ({\n      key,\n      ...rects[key],\n      area: getArea(rects[key]),\n    }))\n    .sort((a, b) => b.area - a.area);\n\n  const filteredAreas = sortedAreas.filter(\n    ({ width, height }) =>\n      width >= popper.clientWidth && height >= popper.clientHeight\n  );\n\n  const computedPlacement = filteredAreas.length > 0\n    ? filteredAreas[0].key\n    : sortedAreas[0].key;\n\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "const isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nconst longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\nlet timeoutDuration = 0;\nfor (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n  if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n    timeoutDuration = 1;\n    break;\n  }\n}\n\nexport function microtaskDebounce(fn) {\n  let called = false\n  return () => {\n    if (called) {\n      return\n    }\n    called = true\n    Promise.resolve().then(() => {\n      called = false\n      fn()\n    })\n  }\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nconst supportsMicroTasks = isBrowser && window.Promise\n\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsMicroTasks\n  ? microtaskDebounce\n  : taskDebounce);\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import getWindowSizes from './getWindowSizes';\nimport getClientRect from './getClientRect';\n\n/**\n * Get the position of the given element, relative to its offset parent\n * @method\n * @memberof Popper.Utils\n * @param {Element} element\n * @return {Object} position - Coordinates of the element and its `scrollTop`\n */\nexport default function getOffsetRect(element) {\n  let elementRect;\n  if (element.nodeName === 'HTML') {\n    const { width, height } = getWindowSizes();\n    elementRect = {\n      width,\n      height,\n      left: 0,\n      top: 0,\n    };\n  } else {\n    elementRect = {\n      width: element.offsetWidth,\n      height: element.offsetHeight,\n      left: element.offsetLeft,\n      top: element.offsetTop,\n    };\n  }\n\n  // position\n  return getClientRect(elementRect);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const styles = window.getComputedStyle(element);\n  const x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n  const y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference) {\n  const commonOffsetParent = findCommonOffsetParent(popper, reference);\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent);\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length - 1; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof window.document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  const isRequired =\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    });\n\n  if (!isRequired) {\n    const requesting = `\\`${requestingName}\\``;\n    const requested = `\\`${requestedName}\\``;\n    console.warn(\n      `${requested} modifier is required by ${requesting} modifier in order to work, be sure to include it before ${requesting}!`\n    );\n  }\n  return isRequired;\n}\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nexport default function getWindow(element) {\n  const ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n", "import getWindow from './getWindow';\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier['function']) { // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    const fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 &&\n      isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "import getScrollParent from './getScrollParent';\nimport getWindow from './getWindow';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "import computeAutoPlacement from './computeAutoPlacement';\nimport debounce from './debounce';\nimport findIndex from './findIndex';\nimport getBordersSize from './getBordersSize';\nimport getBoundaries from './getBoundaries';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getClientRect from './getClientRect';\nimport getOffsetParent from './getOffsetParent';\nimport getOffsetRect from './getOffsetRect';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getOuterSizes from './getOuterSizes';\nimport getParentNode from './getParentNode';\nimport getPopperOffsets from './getPopperOffsets';\nimport getReferenceOffsets from './getReferenceOffsets';\nimport getScroll from './getScroll';\nimport getScrollParent from './getScrollParent';\nimport getStyleComputedProperty from './getStyleComputedProperty';\nimport getSupportedPropertyName from './getSupportedPropertyName';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport isFunction from './isFunction';\nimport isModifierEnabled from './isModifierEnabled';\nimport isModifierRequired from './isModifierRequired';\nimport isNumeric from './isNumeric';\nimport removeEventListeners from './removeEventListeners';\nimport runModifiers from './runModifiers';\nimport setAttributes from './setAttributes';\nimport setStyles from './setStyles';\nimport setupEventListeners from './setupEventListeners';\n\n/** @namespace Popper.Utils */\nexport {\n  computeAutoPlacement,\n  debounce,\n  findIndex,\n  getBordersSize,\n  getBoundaries,\n  getBoundingClientRect,\n  getClientRect,\n  getOffsetParent,\n  getOffsetRect,\n  getOffsetRectRelativeToArbitraryNode,\n  getOuterSizes,\n  getParentNode,\n  getPopperOffsets,\n  getReferenceOffsets,\n  getScroll,\n  getScrollParent,\n  getStyleComputedProperty,\n  getSupportedPropertyName,\n  getWindowSizes,\n  isFixed,\n  isFunction,\n  isModifierEnabled,\n  isModifierRequired,\n  isNumeric,\n  removeEventListeners,\n  runModifiers,\n  setAttributes,\n  setStyles,\n  setupEventListeners,\n};\n\n// This is here just for backward compatibility with versions lower than v1.10.3\n// you should import the utilities using named exports, if you want them all use:\n// ```\n// import * as PopperUtils from 'popper-utils';\n// ```\n// The default export will be removed in the next major version.\nexport default {\n  computeAutoPlacement,\n  debounce,\n  findIndex,\n  getBordersSize,\n  getBoundaries,\n  getBoundingClientRect,\n  getClientRect,\n  getOffsetParent,\n  getOffsetRect,\n  getOffsetRectRelativeToArbitraryNode,\n  getOuterSizes,\n  getParentNode,\n  getPopperOffsets,\n  getReferenceOffsets,\n  getScroll,\n  getScrollParent,\n  getStyleComputedProperty,\n  getSupportedPropertyName,\n  getWindowSizes,\n  isFixed,\n  isFunction,\n  isModifierEnabled,\n  isModifierRequired,\n  isNumeric,\n  removeEventListeners,\n  runModifiers,\n  setAttributes,\n  setStyles,\n  setupEventListeners,\n};\n"], "names": ["getStyleComputedProperty", "element", "nodeType", "css", "window", "getComputedStyle", "property", "getParentNode", "nodeName", "parentNode", "host", "getScrollParent", "document", "body", "ownerDocument", "overflow", "overflowX", "overflowY", "test", "getOffsetParent", "offsetParent", "indexOf", "documentElement", "isOffsetContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "element1root", "getScroll", "side", "upperSide", "html", "scrollingElement", "includeScroll", "subtract", "scrollTop", "scrollLeft", "modifier", "top", "bottom", "left", "right", "getBordersSize", "sideA", "axis", "sideB", "styles", "split", "isIE10", "navigator", "appVersion", "getSize", "Math", "max", "computedStyle", "getWindowSizes", "getClientRect", "offsets", "width", "height", "getBoundingClientRect", "rect", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getOffsetRectRelativeToArbitraryNode", "runIsIE10", "isHTML", "parent", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "relativeOffset", "innerWidth", "innerHeight", "offset", "isFixed", "getBoundaries", "boundaries", "boundariesElement", "boundariesNode", "popper", "getArea", "computeAutoPlacement", "padding", "placement", "rects", "refRect", "sorted<PERSON>reas", "Object", "keys", "map", "key", "sort", "b", "area", "a", "filtered<PERSON><PERSON>s", "filter", "computedPlacement", "length", "variation", "<PERSON><PERSON><PERSON><PERSON>", "longerTimeoutBrowsers", "timeoutDuration", "i", "userAgent", "microtaskDebounce", "called", "resolve", "then", "taskDebounce", "scheduled", "supportsMicroTasks", "Promise", "find", "Array", "prototype", "arr", "findIndex", "cur", "match", "obj", "getOffsetRect", "elementRect", "offsetLeft", "offsetTop", "getOuterSizes", "x", "parseFloat", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "replace", "matched", "getPopperOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "referenceOffsets", "getReferenceOffsets", "commonOffsetParent", "getSupportedPropertyName", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "slice", "prefix", "to<PERSON><PERSON><PERSON>", "style", "isFunction", "functionToCheck", "getType", "toString", "call", "isModifierEnabled", "modifiers", "some", "name", "enabled", "isModifierRequired", "requesting", "isRequired", "requested", "warn", "isNumeric", "n", "isNaN", "isFinite", "getWindow", "defaultView", "removeEventListeners", "removeEventListener", "state", "updateBound", "scrollParents", "for<PERSON>ach", "target", "scrollElement", "eventsEnabled", "runModifiers", "modifiersToRun", "ends", "fn", "data", "reference", "setAttributes", "value", "attributes", "removeAttribute", "setAttribute", "setStyles", "prop", "unit", "attachToScrollParents", "isBody", "addEventListener", "passive", "push", "setupEventListeners"], "mappings": ";;;GAOA,QAAwBA,yBAAxB,KAAoE,IACzC,CAArBC,KAAQC,uBAINC,GAAMC,OAAOC,gBAAPD,GAAiC,IAAjCA,QACLE,GAAWH,IAAXG,GCNT,QAAwBC,cAAxB,GAA+C,OACpB,MAArBN,KAAQO,QADiC,GAItCP,EAAQQ,UAARR,EAAsBA,EAAQS,KCDvC,QAAwBC,gBAAxB,GAAiD,IAE3C,SACKP,QAAOQ,QAAPR,CAAgBS,YAGjBZ,EAAQO,cACT,WACA,aACIP,GAAQa,aAARb,CAAsBY,SAC1B,kBACIZ,GAAQY,WAIb,CAAEE,UAAF,CAAYC,WAAZ,CAAuBC,WAAvB,EAAqCjB,4BAfI,MAgB3C,iBAAgBkB,IAAhB,CAAqBH,KAArB,CAhB2C,GAoBxCJ,gBAAgBJ,gBAAhBI,ECtBT,QAAwBQ,gBAAxB,GAAiD,MAEzCC,GAAenB,GAAWA,EAAQmB,aAClCZ,EAAWY,GAAgBA,EAAaZ,SAHC,MAK3C,IAA0B,MAAbA,IAAb,EAAiD,MAAbA,IALO,CAgBM,CAAC,CAApD,kBAAgBa,OAAhB,CAAwBD,EAAaZ,QAArC,GACuD,QAAvDR,8BAAuC,UAAvCA,CAjB6C,CAmBtCmB,kBAnBsC,KAOpClB,EAAQa,aAARb,CAAsBqB,eAPc,CAUtClB,OAAOQ,QAAPR,CAAgBkB,wBChBHC,qBAA2B,MAC3C,CAAEf,UAAF,IAD2C,MAEhC,MAAbA,IAF6C,GAMlC,MAAbA,MAAuBW,gBAAgBlB,EAAQuB,iBAAxBL,KANwB,ECKnD,QAAwBM,QAAxB,GAAsC,OACZ,KAApBC,KAAKjB,UAD2B,GAE3BgB,QAAQC,EAAKjB,UAAbgB,ECGX,QAAwBE,uBAAxB,KAAmE,IAE7D,IAAa,CAACC,EAAS1B,QAAvB,EAAmC,EAAnC,EAAgD,CAAC2B,EAAS3B,eACrDE,QAAOQ,QAAPR,CAAgBkB,qBAInBQ,GACJF,EAASG,uBAATH,IACAI,KAAKC,4BACDC,EAAQJ,MACRK,EAAML,MAGNM,EAAQxB,SAASyB,WAATzB,KACR0B,WAAgB,EAf2C,GAgB3DC,SAAY,EAhB+C,MAiB3D,CAAEC,yBAAF,OAIHZ,OACCC,KADDD,EAEDM,EAAMO,QAANP,UAEIX,wBAIGJ,wBAIHuB,GAAejB,WAjC4C,MAkC7DiB,GAAahC,IAlCgD,CAmCxDiB,uBAAuBe,EAAahC,IAApCiB,GAnCwD,CAqCxDA,yBAAiCF,WAAkBf,IAAnDiB,ECzCX,QAAwBgB,UAAxB,GAA2CC,EAAO,KAAlD,CAAyD,MACjDC,GAAqB,KAATD,KAAiB,WAAjBA,CAA+B,aAC3CpC,EAAWP,EAAQO,YAER,MAAbA,MAAoC,MAAbA,KAAqB,MACxCsC,GAAO7C,EAAQa,aAARb,CAAsBqB,gBAC7ByB,EAAmB9C,EAAQa,aAARb,CAAsB8C,gBAAtB9C,UAClB8C,YAGF9C,MCPT,QAAwB+C,cAAxB,KAAqDC,IAArD,CAAuE,MAC/DC,GAAYP,YAAmB,KAAnBA,EACZQ,EAAaR,YAAmB,MAAnBA,EACbS,EAAWH,EAAW,CAAC,CAAZA,CAAgB,WAC5BI,KAAOH,MACPI,QAAUJ,MACVK,MAAQJ,MACRK,OAASL,MCRhB,QAAwBM,eAAxB,KAAqD,MAC7CC,GAAiB,GAATC,KAAe,MAAfA,CAAwB,MAChCC,EAAkB,MAAVF,IAAmB,OAAnBA,CAA6B,eAGzC,CAACG,WAAQ,QAARA,EAA8BC,KAA9BD,CAAoC,IAApCA,EAA0C,CAA1CA,CAAD,CACA,EAACA,WAAQ,QAARA,EAA8BC,KAA9BD,CAAoC,IAApCA,EAA0C,CAA1CA,ECVL,GAAIE,OAAJ,CAEA,aAAe,UAAW,OACpBA,yBACmD,CAAC,CAA7CC,aAAUC,UAAVD,CAAqB3C,OAArB2C,CAA6B,SAA7BA,GAEJD,OAJT,SCNSG,iBAAyC,OACzCC,MAAKC,GAALD,CACLtD,WAAM,GAANA,CADKsD,CAELtD,WAAM,GAANA,CAFKsD,CAGLrB,WAAM,GAANA,CAHKqB,CAILrB,WAAM,GAANA,CAJKqB,CAKLrB,WAAM,GAANA,CALKqB,CAMLJ,WACIjB,WAAM,GAANA,EACAuB,WAAgC,QAATV,KAAoB,KAApBA,CAA4B,QAAnDU,CADAvB,CAEAuB,WAAgC,QAATV,KAAoB,QAApBA,CAA+B,SAAtDU,CAHJN,CAII,CAVCI,EAcT,QAAwBG,eAAxB,EAAyC,MACjCzD,GAAOT,OAAOQ,QAAPR,CAAgBS,KACvBiC,EAAO1C,OAAOQ,QAAPR,CAAgBkB,gBACvB+C,EAAgBN,YAAY3D,OAAOC,gBAAPD,UAE3B,QACG8D,QAAQ,QAARA,OADH,OAEEA,QAAQ,OAARA,OAFF,8KCfT,QAAwBK,cAAxB,GAA+C,6BAGpCC,EAAQjB,IAARiB,CAAeA,EAAQC,aACtBD,EAAQnB,GAARmB,CAAcA,EAAQE,SCGlC,QAAwBC,sBAAxB,GAAuD,IACjDC,SAKAb,cACE,GACK9D,EAAQ0E,qBAAR1E,EADL,MAEIiD,GAAYP,YAAmB,KAAnBA,EACZQ,EAAaR,YAAmB,MAAnBA,IACdU,MAJH,GAKGE,OALH,GAMGD,SANH,GAOGE,QAPP,CAQE,QAAY,SAEPvD,EAAQ0E,qBAAR1E,QAGH4E,GAAS,MACPD,EAAKrB,IADE,KAERqB,EAAKvB,GAFG,OAGNuB,EAAKpB,KAALoB,CAAaA,EAAKrB,IAHZ,QAILqB,EAAKtB,MAALsB,CAAcA,EAAKvB,GAJd,EAQTyB,EAA6B,MAArB7E,KAAQO,QAARP,CAA8BqE,gBAA9BrE,IACRwE,EACJK,EAAML,KAANK,EAAe7E,EAAQ8E,WAAvBD,EAAsCD,EAAOrB,KAAPqB,CAAeA,EAAOtB,KACxDmB,EACJI,EAAMJ,MAANI,EAAgB7E,EAAQ+E,YAAxBF,EAAwCD,EAAOvB,MAAPuB,CAAgBA,EAAOxB,OAE7D4B,GAAiBhF,EAAQiF,WAARjF,GACjBkF,EAAgBlF,EAAQmF,YAARnF,MAIhBgF,KAAiC,MAC7BpB,GAAS7D,+BACGyD,iBAAuB,GAAvBA,CAFiB,IAGlBA,iBAAuB,GAAvBA,CAHkB,GAK5BgB,QAL4B,GAM5BC,gBAGFH,0BCvDec,0CAAuD,MACvEtB,GAASuB,WACTC,EAA6B,MAApBC,KAAOhF,SAChBiF,EAAed,yBACfe,EAAaf,yBACbgB,EAAehF,mBAEfkD,EAAS7D,4BACT4F,EAAiB,CAAC/B,EAAO+B,cAAP/B,CAAsBC,KAAtBD,CAA4B,IAA5BA,EAAkC,CAAlCA,EAClBgC,EAAkB,CAAChC,EAAOgC,eAAPhC,CAAuBC,KAAvBD,CAA6B,IAA7BA,EAAmC,CAAnCA,KAErBW,GAAUD,cAAc,KACrBkB,EAAapC,GAAboC,CAAmBC,EAAWrC,GAA9BoC,EADqB,MAEpBA,EAAalC,IAAbkC,CAAoBC,EAAWnC,IAA/BkC,EAFoB,OAGnBA,EAAahB,KAHM,QAIlBgB,EAAaf,MAJK,CAAdH,OAMNuB,UAAY,IACZC,WAAa,EAMjB,MAAmB,MACfD,GAAY,CAACjC,EAAOiC,SAAPjC,CAAiBC,KAAjBD,CAAuB,IAAvBA,EAA6B,CAA7BA,EACbkC,EAAa,CAAClC,EAAOkC,UAAPlC,CAAkBC,KAAlBD,CAAwB,IAAxBA,EAA8B,CAA9BA,IAEZR,KAAOuC,GAJM,GAKbtC,QAAUsC,GALG,GAMbrC,MAAQsC,GANK,GAObrC,OAASqC,GAPI,GAUbC,WAVa,GAWbC,oBAIRhC,EACIyB,EAAO/C,QAAP+C,GADJzB,CAEIyB,OAAqD,MAA1BG,KAAanF,cAElCwC,8BC9CUgD,iDAAuD,OAG/D7B,KAAKC,GAH0D,MACvEtB,GAAO7C,EAAQa,aAARb,CAAsBqB,gBAC7B2E,EAAiBZ,0CACjBZ,EAAQN,EAASrB,EAAKiC,WAAdZ,CAA2B/D,OAAO8F,UAAP9F,EAAqB,CAAhD+D,EACRO,EAASP,EAASrB,EAAKkC,YAAdb,CAA4B/D,OAAO+F,WAAP/F,EAAsB,CAAlD+D,EAETjB,EAAYP,aACZQ,EAAaR,YAAgB,MAAhBA,EAEbyD,EAAS,KACRlD,EAAY+C,EAAe5C,GAA3BH,CAAiC+C,EAAeH,SADxC,MAEP3C,EAAa8C,EAAe1C,IAA5BJ,CAAmC8C,EAAeF,UAF3C,QAAA,SAAA,QAORxB,kBCTT,QAAwB8B,QAAxB,GAAyC,MACjC7F,GAAWP,EAAQO,SADc,MAEtB,MAAbA,MAAoC,MAAbA,IAFY,MAKe,OAAlDR,8BAAkC,UAAlCA,CALmC,GAQhCqG,QAAQ9F,gBAAR8F,ECDT,QAAwBC,cAAxB,SAKE,IAEIC,GAAa,CAAElD,IAAK,CAAP,CAAUE,KAAM,CAAhB,OACXnC,GAAeO,+BAGK,UAAtB6E,OACWR,qDACR,IAEDS,GACsB,cAAtBD,IAHC,IAIc7F,gBAAgBJ,gBAAhBI,CAJd,CAK6B,MAA5B8F,KAAejG,QALhB,KAMgBkG,EAAO5F,aAAP4F,CAAqBpF,eANrC,GAQ4B,QAAtBkF,IARN,GAScE,EAAO5F,aAAP4F,CAAqBpF,eATnC,IAAA,MAcCkD,GAAUa,6CAMgB,MAA5BoB,KAAejG,QAAfiG,EAAsC,CAACJ,WAAuB,MAC1D,CAAE3B,QAAF,CAAUD,OAAV,EAAoBH,mBACfjB,KAAOmB,EAAQnB,GAARmB,CAAcA,EAAQsB,SAFwB,GAGrDxC,OAASoB,EAASF,EAAQnB,GAH2B,GAIrDE,MAAQiB,EAAQjB,IAARiB,CAAeA,EAAQuB,UAJsB,GAKrDvC,MAAQiB,EAAQD,EAAQjB,IALrC,mBAaSA,UACAF,SACAG,WACAF,oBCjEJqD,SAAQ,CAAElC,OAAF,CAASC,QAAT,EAAmB,OAC3BD,KAYT,QAAwBmC,qBAAxB,WAMEC,EAAU,CANZ,CAOE,IACkC,CAAC,CAA/BC,KAAUzF,OAAVyF,CAAkB,MAAlBA,gBAIEP,GAAaD,uBAObS,EAAQ,KACP,OACIR,EAAW9B,KADf,QAEKuC,EAAQ3D,GAAR2D,CAAcT,EAAWlD,GAF9B,CADO,OAKL,OACEkD,EAAW/C,KAAX+C,CAAmBS,EAAQxD,KAD7B,QAEG+C,EAAW7B,MAFd,CALK,QASJ,OACC6B,EAAW9B,KADZ,QAEE8B,EAAWjD,MAAXiD,CAAoBS,EAAQ1D,MAF9B,CATI,MAaN,OACG0D,EAAQzD,IAARyD,CAAeT,EAAWhD,IAD7B,QAEIgD,EAAW7B,MAFf,CAbM,EAmBRuC,EAAcC,OAAOC,IAAPD,IACjBE,GADiBF,CACbG,sBAEAN,WACGJ,QAAQI,IAARJ,GAJUO,EAMjBI,IANiBJ,CAMZ,OAAUK,EAAEC,IAAFD,CAASE,EAAED,IANTN,EAQdQ,EAAgBT,EAAYU,MAAZV,CACpB,CAAC,CAAExC,OAAF,CAASC,QAAT,CAAD,GACED,GAASiC,EAAO3B,WAAhBN,EAA+BC,GAAUgC,EAAO1B,YAF9BiC,EAKhBW,EAA2C,CAAvBF,GAAcG,MAAdH,CACtBA,EAAc,CAAdA,EAAiBL,GADKK,CAEtBT,EAAY,CAAZA,EAAeI,IAEbS,EAAYhB,EAAUhD,KAAVgD,CAAgB,GAAhBA,EAAqB,CAArBA,QAEXc,IAAqBE,MAAa,GAAbA,CAA8B,EAAnDF,ECxET,KAAMG,WAA8B,WAAlB,QAAO3H,OAAP,EAA4D,WAA3B,QAAOA,QAAOQ,QAAjE,CACMoH,kDADN,CAEA,GAAIC,iBAAkB,CAAtB,CACA,IAAK,GAAIC,GAAI,CAAb,CAAgBA,EAAIF,sBAAsBH,MAA1C,CAAkDK,GAAK,CAAvD,IACMH,WAAsE,CAAzD/D,YAAUmE,SAAVnE,CAAoB3C,OAApB2C,CAA4BgE,wBAA5BhE,EAA4D,iBACzD,CADyD,OAM/E,QAAgBoE,kBAAhB,GAAsC,IAChCC,YACG,IAAM,SAAA,SAKHC,UAAUC,KAAK,IAAM,KAAA,IAA7B,EALW,CAAb,EAYF,QAAgBC,aAAhB,GAAiC,IAC3BC,YACG,IAAM,SAAA,YAGE,IAAM,KAAA,IAAjB,EAGGR,gBANM,CAAb,EAWF,KAAMS,oBAAqBX,WAAa3H,OAAOuI,OAA/C,CAYA,aAAgBD,mBACZN,iBADYM,CAEZF,YAFJ,CCxCA,QAAwBI,KAAxB,KAAyC,OAEnCC,OAAMC,SAAND,CAAgBD,IAFmB,CAG9BG,EAAIH,IAAJG,GAH8B,CAOhCA,EAAIpB,MAAJoB,IAAkB,CAAlBA,ECLT,QAAwBC,UAAxB,OAAoD,IAE9CH,MAAMC,SAAND,CAAgBG,gBACXD,GAAIC,SAAJD,CAAcE,KAAOA,QAArBF,OAIHG,GAAQN,OAAUO,KAAOA,QAAjBP,QACPG,GAAI1H,OAAJ0H,ICTT,QAAwBK,cAAxB,GAA+C,IACzCC,MACqB,MAArBpJ,KAAQO,SAAqB,MACzB,CAAEiE,OAAF,CAASC,QAAT,EAAoBJ,mBACZ,QAAA,SAAA,MAGN,CAHM,KAIP,CAJO,CAFhB,QASgB,OACLrE,EAAQiF,WADH,QAEJjF,EAAQmF,YAFJ,MAGNnF,EAAQqJ,UAHF,KAIPrJ,EAAQsJ,SAJD,QASThF,kBCvBT,QAAwBiF,cAAxB,GAA+C,MACvC3F,GAASzD,OAAOC,gBAAPD,IACTqJ,EAAIC,WAAW7F,EAAOiC,SAAlB4D,EAA+BA,WAAW7F,EAAO8F,YAAlBD,EACnCE,EAAIF,WAAW7F,EAAOkC,UAAlB2D,EAAgCA,WAAW7F,EAAOgG,WAAlBH,EACpC7E,EAAS,OACN5E,EAAQiF,WAARjF,EADM,QAELA,EAAQmF,YAARnF,EAFK,WCJjB,QAAwB6J,qBAAxB,GAAwD,MAChDC,GAAO,CAAExG,KAAM,OAAR,CAAiBC,MAAO,MAAxB,CAAgCF,OAAQ,KAAxC,CAA+CD,IAAK,QAApD,QACNyD,GAAUkD,OAAVlD,CAAkB,wBAAlBA,CAA4CmD,KAAWF,IAAvDjD,ECIT,QAAwBoD,iBAAxB,OAA8E,GAChEpD,EAAUhD,KAAVgD,CAAgB,GAAhBA,EAAqB,CAArBA,CADgE,MAItEqD,GAAaX,iBAGbY,EAAgB,OACbD,EAAW1F,KADE,QAEZ0F,EAAWzF,MAFC,EAMhB2F,EAAmD,CAAC,CAA1C,oBAAkBhJ,OAAlB,IACViJ,EAAWD,EAAU,KAAVA,CAAkB,OAC7BE,EAAgBF,EAAU,MAAVA,CAAmB,MACnCG,EAAcH,EAAU,QAAVA,CAAqB,QACnCI,EAAuB,EAAsB,OAAtB,CAAW,qBAGtCC,KACAA,KAAgC,CADhCA,CAEAP,KAA0B,OACxBrD,MAEA4D,KAAkCP,KAGlCO,EAAiBZ,uBAAjBY,IC7BN,QAAwBC,oBAAxB,OAAsE,MAC9DC,GAAqBjJ,kCACpB0D,2CCPT,QAAwBwF,yBAAxB,GAA2D,MACnDC,gCACAC,EAAYzK,EAAS0K,MAAT1K,CAAgB,CAAhBA,EAAmB2K,WAAnB3K,GAAmCA,EAAS4K,KAAT5K,CAAe,CAAfA,MAEhD,GAAI4H,GAAI,EAAGA,EAAI4C,EAASjD,MAATiD,CAAkB,EAAG5C,IAAK,MACtCiD,GAASL,KACTM,EAAUD,KAAU,IAAA,GAAVA,MACmC,WAA/C,QAAO/K,QAAOQ,QAAPR,CAAgBS,IAAhBT,CAAqBiL,KAArBjL,mBAIN,MCXT,QAAwBkL,WAAxB,GAAoD,OAGhDC,IAC2C,mBAA3CC,MAAQC,QAARD,CAAiBE,IAAjBF,ICLJ,QAAwBG,kBAAxB,KAAmE,OAC1DC,GAAUC,IAAVD,CACL,CAAC,CAAEE,MAAF,CAAQC,SAAR,CAAD,GAAuBA,GAAWD,KAD7BF,ECKT,QAAwBI,mBAAxB,OAIE,MACMC,GAAarD,OAAgB,CAAC,CAAEkD,MAAF,CAAD,GAAcA,KAA9BlD,EAEbsD,EACJ,CAAC,EAAD,EACAN,EAAUC,IAAVD,CAAexI,KAEXA,EAAS0I,IAAT1I,MACAA,EAAS2I,OADT3I,EAEAA,EAAStB,KAATsB,CAAiB6I,EAAWnK,KAJhC8J,KAQE,GAAa,MACTK,QAAc,MACdE,OAAa,cACXC,QACL,6BAAA,6DAAA,eC1BP,QAAwBC,UAAxB,GAAqC,OACtB,EAANC,MAAY,CAACC,MAAM7C,aAAN6C,CAAbD,EAAqCE,YCH9C,QAAwBC,UAAxB,GAA2C,MACnC3L,GAAgBb,EAAQa,oBACvBA,GAAgBA,EAAc4L,WAA9B5L,CAA4CV,OCCrD,QAAwBuM,qBAAxB,KAA+D,qBAExCC,oBAAoB,SAAUC,EAAMC,eAGnDC,cAAcC,QAAQC,KAAU,GAC7BL,oBAAoB,SAAUC,EAAMC,YAD7C,KAKMA,YAAc,OACdC,mBACAG,cAAgB,OAChBC,mBCPR,QAAwBC,aAAxB,OAA4D,MACpDC,GAAiBC,aAEnB1B,EAAUV,KAAVU,CAAgB,CAAhBA,CAAmB5C,YAAqB,MAArBA,GAAnB4C,WAEWoB,QAAQ5J,KAAY,CAC7BA,EAAS,UAATA,CAD6B,UAEvBgJ,KAAK,wDAFkB,MAI3BmB,GAAKnK,EAAS,UAATA,GAAwBA,EAASmK,GACxCnK,EAAS2I,OAAT3I,EAAoBkI,aALS,KAS1B9G,QAAQkC,OAASnC,cAAciJ,EAAKhJ,OAALgJ,CAAa9G,MAA3BnC,CATS,GAU1BC,QAAQiJ,UAAYlJ,cAAciJ,EAAKhJ,OAALgJ,CAAaC,SAA3BlJ,CAVM,GAYxBgJ,MAZwB,CAAnC,KCXF,QAAwBG,cAAxB,KAA2D,QAClDvG,QAAiB6F,QAAQ,WAAe,MACvCW,GAAQC,KACVD,MAFyC,GAKnCE,kBALmC,GAGnCC,eAAmBF,KAH/B,GCCF,QAAwBG,UAAxB,KAAmD,QAC1C5G,QAAa6F,QAAQgB,KAAQ,IAC9BC,GAAO,GAIP,CAAC,CADH,oDAAsD5M,OAAtD,KAEAgL,UAAUxI,IAAVwI,CANgC,KAQzB,IARyB,IAU1BhB,SAAcxH,MAVxB,WCROqK,+BAAoE,MACrEC,GAAmC,MAA1BxI,KAAanF,SACtByM,EAASkB,EAASxI,EAAa7E,aAAb6E,CAA2B+G,WAApCyB,KACRC,qBAAkC,CAAEC,UAAF,EAHkC,0BAOvE1N,gBAAgBsM,EAAOxM,UAAvBE,QAPuE,GAa7D2N,QAShB,QAAwBC,oBAAxB,SAKE,GAEMzB,aAFN,cAGqBsB,iBAAiB,SAAUvB,EAAMC,YAAa,CAAEuB,UAAF,EAHnE,MAMMnB,GAAgBvM,kDAGpB,SACAkM,EAAMC,YACND,EAAME,iBAEFG,kBACAC,mBCyBR,UAAe,qBAAA,SAAA,UAAA,eAAA,cAAA,sBAAA,cAAA,gBAAA,cAAA,qCAAA,cAAA,cAAA,iBAAA,oBAAA,UAAA,gBAAA,yBAAA,yBAAA,eAAA,QAAA,WAAA,kBAAA,mBAAA,UAAA,qBAAA,aAAA,cAAA,UAAA,oBAAA,CAAf"}