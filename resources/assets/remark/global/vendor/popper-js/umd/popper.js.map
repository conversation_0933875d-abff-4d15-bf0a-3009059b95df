{"version": 3, "file": "popper.js", "sources": ["../../src/utils/debounce.js", "../../src/utils/isFunction.js", "../../src/utils/getStyleComputedProperty.js", "../../src/utils/getParentNode.js", "../../src/utils/getScrollParent.js", "../../src/utils/getOffsetParent.js", "../../src/utils/isOffsetContainer.js", "../../src/utils/getRoot.js", "../../src/utils/findCommonOffsetParent.js", "../../src/utils/getScroll.js", "../../src/utils/includeScroll.js", "../../src/utils/getBordersSize.js", "../../src/utils/isIE10.js", "../../src/utils/getWindowSizes.js", "../../src/utils/getClientRect.js", "../../src/utils/getBoundingClientRect.js", "../../src/utils/getOffsetRectRelativeToArbitraryNode.js", "../../src/utils/getViewportOffsetRectRelativeToArtbitraryNode.js", "../../src/utils/isFixed.js", "../../src/utils/getBoundaries.js", "../../src/utils/computeAutoPlacement.js", "../../src/utils/getReferenceOffsets.js", "../../src/utils/getOuterSizes.js", "../../src/utils/getOppositePlacement.js", "../../src/utils/getPopperOffsets.js", "../../src/utils/find.js", "../../src/utils/findIndex.js", "../../src/utils/runModifiers.js", "../../src/methods/update.js", "../../src/utils/isModifierEnabled.js", "../../src/utils/getSupportedPropertyName.js", "../../src/methods/destroy.js", "../../src/utils/getWindow.js", "../../src/utils/setupEventListeners.js", "../../src/methods/enableEventListeners.js", "../../src/utils/removeEventListeners.js", "../../src/methods/disableEventListeners.js", "../../src/utils/isNumeric.js", "../../src/utils/setStyles.js", "../../src/utils/setAttributes.js", "../../src/modifiers/applyStyle.js", "../../src/modifiers/computeStyle.js", "../../src/utils/isModifierRequired.js", "../../src/modifiers/arrow.js", "../../src/utils/getOppositeVariation.js", "../../src/methods/placements.js", "../../src/utils/clockwise.js", "../../src/modifiers/flip.js", "../../src/modifiers/keepTogether.js", "../../src/modifiers/offset.js", "../../src/modifiers/preventOverflow.js", "../../src/modifiers/shift.js", "../../src/modifiers/hide.js", "../../src/modifiers/inner.js", "../../src/modifiers/index.js", "../../src/methods/defaults.js", "../../src/index.js"], "sourcesContent": ["const isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nconst longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\nlet timeoutDuration = 0;\nfor (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n  if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n    timeoutDuration = 1;\n    break;\n  }\n}\n\nexport function microtaskDebounce(fn) {\n  let called = false\n  return () => {\n    if (called) {\n      return\n    }\n    called = true\n    Promise.resolve().then(() => {\n      called = false\n      fn()\n    })\n  }\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nconst supportsMicroTasks = isBrowser && window.Promise\n\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsMicroTasks\n  ? microtaskDebounce\n  : taskDebounce);\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return window.document.body\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body\n    case '#document':\n      return element.body\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  // NOTE: 1 DOM access here\n  const offsetParent = element && element.offsetParent;\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    if (element) {\n      return element.ownerDocument.documentElement\n    }\n\n    return window.document.documentElement;\n  }\n\n  // .offsetParent will return the closest TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (\n    ['TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 &&\n    getStyleComputedProperty(offsetParent, 'position') === 'static'\n  ) {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n", "import getOffsetParent from './getOffsetParent';\n\nexport default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return window.document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    (element1 !== commonAncestorContainer &&\n      element2 !== commonAncestorContainer) ||\n    start.contains(end)\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    +styles[`border${sideA}Width`].split('px')[0] +\n    +styles[`border${sideB}Width`].split('px')[0]\n  );\n}\n", "/**\n * Tells if you are running Internet Explorer 10\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean} isIE10\n */\nlet isIE10 = undefined;\n\nexport default function() {\n  if (isIE10 === undefined) {\n    isIE10 = navigator.appVersion.indexOf('MSIE 10') !== -1;\n  }\n  return isIE10;\n}\n", "import isIE10 from './isIE10';\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(\n    body[`offset${axis}`],\n    body[`scroll${axis}`],\n    html[`client${axis}`],\n    html[`offset${axis}`],\n    html[`scroll${axis}`],\n    isIE10()\n      ? html[`offset${axis}`] +\n        computedStyle[`margin${axis === 'Height' ? 'Top' : 'Left'}`] +\n        computedStyle[`margin${axis === 'Height' ? 'Bottom' : 'Right'}`]\n      : 0\n  );\n}\n\nexport default function getWindowSizes() {\n  const body = window.document.body;\n  const html = window.document.documentElement;\n  const computedStyle = isIE10() && window.getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport isIE10 from './isIE10';\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  if (isIE10()) {\n    try {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } catch (err) {}\n  } else {\n    rect = element.getBoundingClientRect();\n  }\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes() : {};\n  const width =\n    sizes.width || element.clientWidth || result.right - result.left;\n  const height =\n    sizes.height || element.clientHeight || result.bottom - result.top;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE10 from './isIE10';\nimport getClientRect from './getClientRect';\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent) {\n  const isIE10 = runIsIE10();\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = +styles.borderTopWidth.split('px')[0];\n  const borderLeftWidth = +styles.borderLeftWidth.split('px')[0];\n\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    const marginTop = +styles.marginTop.split('px')[0];\n    const marginLeft = +styles.marginLeft.split('px')[0];\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    isIE10\n      ? parent.contains(scrollParent)\n      : parent === scrollParent && scrollParent.nodeName !== 'BODY'\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = getScroll(html);\n  const scrollLeft = getScroll(html, 'left');\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement\n) {\n  // NOTE: 1 DOM access here\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = findCommonOffsetParent(popper, reference);\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(popper));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes();\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  boundaries.left += padding;\n  boundaries.top += padding;\n  boundaries.right -= padding;\n  boundaries.bottom -= padding;\n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\nfunction getArea({ width, height }) {\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference,\n  boundariesElement,\n  padding = 0\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(\n    popper,\n    reference,\n    padding,\n    boundariesElement\n  );\n\n  const rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top,\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height,\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom,\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height,\n    },\n  };\n\n  const sortedAreas = Object.keys(rects)\n    .map(key => ({\n      key,\n      ...rects[key],\n      area: getArea(rects[key]),\n    }))\n    .sort((a, b) => b.area - a.area);\n\n  const filteredAreas = sortedAreas.filter(\n    ({ width, height }) =>\n      width >= popper.clientWidth && height >= popper.clientHeight\n  );\n\n  const computedPlacement = filteredAreas.length > 0\n    ? filteredAreas[0].key\n    : sortedAreas[0].key;\n\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference) {\n  const commonOffsetParent = findCommonOffsetParent(popper, reference);\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const styles = window.getComputedStyle(element);\n  const x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n  const y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier['function']) { // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    const fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "import computeAutoPlacement from '../utils/computeAutoPlacement';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nexport default function update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  let data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {},\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(\n    this.state,\n    this.popper,\n    this.reference\n  );\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(\n    this.options.placement,\n    data.offsets.reference,\n    this.popper,\n    this.reference,\n    this.options.modifiers.flip.boundariesElement,\n    this.options.modifiers.flip.padding\n  );\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(\n    this.popper,\n    data.offsets.reference,\n    data.placement\n  );\n  data.offsets.popper.position = 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length - 1; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof window.document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "import isModifierEnabled from '../utils/isModifierEnabled';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * Destroy the popper\n * @method\n * @memberof Popper\n */\nexport default function destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.left = '';\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicity asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n", "/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nexport default function getWindow(element) {\n  const ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n", "import getScrollParent from './getScrollParent';\nimport getWindow from './getWindow';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "import setupEventListeners from '../utils/setupEventListeners';\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nexport default function enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(\n      this.reference,\n      this.options,\n      this.state,\n      this.scheduleUpdate\n    );\n  }\n}\n", "import getWindow from './getWindow';\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "import removeEventListeners from '../utils/removeEventListeners';\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger onUpdate callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nexport default function disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    window.cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 &&\n      isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import setStyles from '../utils/setStyles';\nimport setAttributes from '../utils/setAttributes';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nexport default function applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper.\n * @param {Object} options - Popper.js options\n */\nexport function applyStyleOnLoad(\n  reference,\n  popper,\n  options,\n  modifierOptions,\n  state\n) {\n  // compute reference element offsets\n  const referenceOffsets = getReferenceOffsets(state, popper, reference);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  const placement = computeAutoPlacement(\n    options.placement,\n    referenceOffsets,\n    popper,\n    reference,\n    options.modifiers.flip.boundariesElement,\n    options.modifiers.flip.padding\n  );\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: 'absolute' });\n\n  return options;\n}\n", "import getSupportedPropertyName from '../utils/getSupportedPropertyName';\nimport find from '../utils/find';\nimport getOffsetParent from '../utils/getOffsetParent';\nimport getBoundingClientRect from '../utils/getBoundingClientRect';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeStyle(data, options) {\n  const { x, y } = options;\n  const { popper } = data.offsets;\n\n  // Remove this legacy support in Popper.js v2\n  const legacyGpuAccelerationOption = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'applyStyle'\n  ).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn(\n      'WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!'\n    );\n  }\n  const gpuAcceleration =\n    legacyGpuAccelerationOption !== undefined\n      ? legacyGpuAccelerationOption\n      : options.gpuAcceleration;\n\n  const offsetParent = getOffsetParent(data.instance.popper);\n  const offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  const styles = {\n    position: popper.position,\n  };\n\n  // floor sides to avoid blurry text\n  const offsets = {\n    left: Math.floor(popper.left),\n    top: Math.floor(popper.top),\n    bottom: Math.floor(popper.bottom),\n    right: Math.floor(popper.right),\n  };\n\n  const sideA = x === 'bottom' ? 'top' : 'bottom';\n  const sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  const prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  let left, top;\n  if (sideA === 'bottom') {\n    top = -offsetParentRect.height + offsets.bottom;\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    left = -offsetParentRect.width + offsets.right;\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = `translate3d(${left}px, ${top}px, 0)`;\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    const invertTop = sideA === 'bottom' ? -1 : 1;\n    const invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = `${sideA}, ${sideB}`;\n  }\n\n  // Attributes\n  const attributes = {\n    'x-placement': data.placement,\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = { ...attributes, ...data.attributes };\n  data.styles = { ...styles, ...data.styles };\n  data.arrowStyles = { ...data.offsets.arrow, ...data.arrowStyles };\n\n  return data;\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  const isRequired =\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    });\n\n  if (!isRequired) {\n    const requesting = `\\`${requestingName}\\``;\n    const requested = `\\`${requestedName}\\``;\n    console.warn(\n      `${requested} modifier is required by ${requesting} modifier in order to work, be sure to include it before ${requesting}!`\n    );\n  }\n  return isRequired;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOuterSizes from '../utils/getOuterSizes';\nimport isModifierRequired from '../utils/isModifierRequired';\nimport getStyleComputedProperty from '../utils/getStyleComputedProperty';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function arrow(data, options) {\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  let arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn(\n        'WARNING: `arrow.element` must be child of its popper element!'\n      );\n      return data;\n    }\n  }\n\n  const placement = data.placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  const len = isVertical ? 'height' : 'width';\n  const sideCapitalized = isVertical ? 'Top' : 'Left';\n  const side = sideCapitalized.toLowerCase();\n  const altSide = isVertical ? 'left' : 'top';\n  const opSide = isVertical ? 'bottom' : 'right';\n  const arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjuction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -=\n      popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] +=\n      reference[side] + arrowElementSize - popper[opSide];\n  }\n\n  // compute center of the popper\n  const center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  const popperMarginSide = getStyleComputedProperty(\n    data.instance.popper,\n    `margin${sideCapitalized}`\n  ).replace('px', '');\n  let sideValue =\n    center - getClientRect(data.offsets.popper)[side] - popperMarginSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = {};\n  data.offsets.arrow[side] = Math.round(sideValue);\n  data.offsets.arrow[altSide] = ''; // make sure to unset any eventual altSide value from the DOM node\n\n  return data;\n}\n", "/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nexport default function getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n", "/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-right` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nexport default [\n  'auto-start',\n  'auto',\n  'auto-end',\n  'top-start',\n  'top',\n  'top-end',\n  'right-start',\n  'right',\n  'right-end',\n  'bottom-end',\n  'bottom',\n  'bottom-start',\n  'left-end',\n  'left',\n  'left-start',\n];\n", "import placements from '../methods/placements';\n\n// Get rid of `auto` `auto-start` and `auto-end`\nconst validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nexport default function clockwise(placement, counter = false) {\n  const index = validPlacements.indexOf(placement);\n  const arr = validPlacements\n    .slice(index + 1)\n    .concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n", "import getOppositePlacement from '../utils/getOppositePlacement';\nimport getOppositeVariation from '../utils/getOppositeVariation';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\nimport getBoundaries from '../utils/getBoundaries';\nimport isModifierEnabled from '../utils/isModifierEnabled';\nimport clockwise from '../utils/clockwise';\n\nconst BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise',\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    options.boundariesElement\n  );\n\n  let placement = data.placement.split('-')[0];\n  let placementOpposite = getOppositePlacement(placement);\n  let variation = data.placement.split('-')[1] || '';\n\n  let flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach((step, index) => {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    const popperOffsets = data.offsets.popper;\n    const refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    const floor = Math.floor;\n    const overlapsRef =\n      (placement === 'left' &&\n        floor(popperOffsets.right) > floor(refOffsets.left)) ||\n      (placement === 'right' &&\n        floor(popperOffsets.left) < floor(refOffsets.right)) ||\n      (placement === 'top' &&\n        floor(popperOffsets.bottom) > floor(refOffsets.top)) ||\n      (placement === 'bottom' &&\n        floor(popperOffsets.top) < floor(refOffsets.bottom));\n\n    const overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    const overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    const overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    const overflowsBottom =\n      floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    const overflowsBoundaries =\n      (placement === 'left' && overflowsLeft) ||\n      (placement === 'right' && overflowsRight) ||\n      (placement === 'top' && overflowsTop) ||\n      (placement === 'bottom' && overflowsBottom);\n\n    // flip the variation if required\n    const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n    const flippedVariation =\n      !!options.flipVariations &&\n      ((isVertical && variation === 'start' && overflowsLeft) ||\n        (isVertical && variation === 'end' && overflowsRight) ||\n        (!isVertical && variation === 'start' && overflowsTop) ||\n        (!isVertical && variation === 'end' && overflowsBottom));\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = {\n        ...data.offsets.popper,\n        ...getPopperOffsets(\n          data.instance.popper,\n          data.offsets.reference,\n          data.placement\n        ),\n      };\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function keepTogether(data) {\n  const { popper, reference } = data.offsets;\n  const placement = data.placement.split('-')[0];\n  const floor = Math.floor;\n  const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  const side = isVertical ? 'right' : 'bottom';\n  const opSide = isVertical ? 'left' : 'top';\n  const measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] =\n      floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n", "import isNumeric from '../utils/isNumeric';\nimport getClientRect from '../utils/getClientRect';\nimport find from '../utils/find';\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nexport function toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  const split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  const value = +split[1];\n  const unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    let element;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    const rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    let size;\n    if (unit === 'vh') {\n      size = Math.max(\n        document.documentElement.clientHeight,\n        window.innerHeight || 0\n      );\n    } else {\n      size = Math.max(\n        document.documentElement.clientWidth,\n        window.innerWidth || 0\n      );\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nexport function parseOffset(\n  offset,\n  popperOffsets,\n  referenceOffsets,\n  basePlacement\n) {\n  const offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  const useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  const fragments = offset.split(/(\\+|\\-)/).map(frag => frag.trim());\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  const divider = fragments.indexOf(\n    find(fragments, frag => frag.search(/,|\\s/) !== -1)\n  );\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn(\n      'Offsets separated by white space(s) are deprecated, use a comma (,) instead.'\n    );\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  const splitRegex = /\\s*,\\s*|\\s+/;\n  let ops = divider !== -1\n    ? [\n        fragments\n          .slice(0, divider)\n          .concat([fragments[divider].split(splitRegex)[0]]),\n        [fragments[divider].split(splitRegex)[1]].concat(\n          fragments.slice(divider + 1)\n        ),\n      ]\n    : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map((op, index) => {\n    // Most of the units rely on the orientation of the popper\n    const measurement = (index === 1 ? !useHeight : useHeight)\n      ? 'height'\n      : 'width';\n    let mergeWithPrevious = false;\n    return (\n      op\n        // This aggregates any `+` or `-` sign that aren't considered operators\n        // e.g.: 10 + +5 => [10, +, +5]\n        .reduce((a, b) => {\n          if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n            a[a.length - 1] = b;\n            mergeWithPrevious = true;\n            return a;\n          } else if (mergeWithPrevious) {\n            a[a.length - 1] += b;\n            mergeWithPrevious = false;\n            return a;\n          } else {\n            return a.concat(b);\n          }\n        }, [])\n        // Here we convert the string values into number values (in px)\n        .map(str => toValue(str, measurement, popperOffsets, referenceOffsets))\n    );\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach((op, index) => {\n    op.forEach((frag, index2) => {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nexport default function offset(data, { offset }) {\n  const { placement, offsets: { popper, reference } } = data;\n  const basePlacement = placement.split('-')[0];\n\n  let offsets;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n", "import getOffsetParent from '../utils/getOffsetParent';\nimport getBoundaries from '../utils/getBoundaries';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function preventOverflow(data, options) {\n  let boundariesElement =\n    options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    boundariesElement\n  );\n  options.boundaries = boundaries;\n\n  const order = options.priority;\n  let popper = data.offsets.popper;\n\n  const check = {\n    primary(placement) {\n      let value = popper[placement];\n      if (\n        popper[placement] < boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return { [placement]: value };\n    },\n    secondary(placement) {\n      const mainSide = placement === 'right' ? 'left' : 'top';\n      let value = popper[mainSide];\n      if (\n        popper[placement] > boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.min(\n          popper[mainSide],\n          boundaries[placement] -\n            (placement === 'right' ? popper.width : popper.height)\n        );\n      }\n      return { [mainSide]: value };\n    },\n  };\n\n  order.forEach(placement => {\n    const side = ['left', 'top'].indexOf(placement) !== -1\n      ? 'primary'\n      : 'secondary';\n    popper = { ...popper, ...check[side](placement) };\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function shift(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    const { reference, popper } = data.offsets;\n    const isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    const side = isVertical ? 'left' : 'top';\n    const measurement = isVertical ? 'width' : 'height';\n\n    const shiftOffsets = {\n      start: { [side]: reference[side] },\n      end: {\n        [side]: reference[side] + reference[measurement] - popper[measurement],\n      },\n    };\n\n    data.offsets.popper = { ...popper, ...shiftOffsets[shiftvariation] };\n  }\n\n  return data;\n}\n", "import isModifierRequired from '../utils/isModifierRequired';\nimport find from '../utils/find';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  const refRect = data.offsets.reference;\n  const bound = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'preventOverflow'\n  ).boundaries;\n\n  if (\n    refRect.bottom < bound.top ||\n    refRect.left > bound.right ||\n    refRect.top > bound.bottom ||\n    refRect.right < bound.left\n  ) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOppositePlacement from '../utils/getOppositePlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function inner(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  const subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] =\n    reference[basePlacement] -\n    (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n", "import applyStyle, { applyStyleOnLoad } from './applyStyle';\nimport computeStyle from './computeStyle';\nimport arrow from './arrow';\nimport flip from './flip';\nimport keepTogether from './keepTogether';\nimport offset from './offset';\nimport preventOverflow from './preventOverflow';\nimport shift from './shift';\nimport hide from './hide';\nimport inner from './inner';\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nexport default {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift,\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unitless, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the height.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > More on this [reading this issue](https://github.com/FezVrasta/popper.js/issues/373)\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0,\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * An scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper this makes sure the popper has always a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier, can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent',\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near eachothers\n   * without leaving any gap between the two. Expecially useful when the arrow is\n   * enabled and you want to assure it to point to its reference element.\n   * It cares only about the first axis, you can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether,\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjuction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]',\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations).\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position,\n     * the popper will never be placed outside of the defined boundaries\n     * (except if keepTogether is enabled)\n     */\n    boundariesElement: 'viewport',\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner,\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide,\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right',\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define you own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: undefined,\n  },\n};\n\n/**\n * The `dataObject` is an object containing all the informations used by Popper.js\n * this object get passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper.\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements.\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n", "import modifiers from '../modifiers/index';\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overriden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass as 3rd argument an object with the same\n * structure of this object, example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nexport default {\n  /**\n   * Popper's placement\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Whether events (resize, scroll) are initially enabled\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: () => {},\n\n  /**\n   * Callback called when the popper is updated, this callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: () => {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js\n   * @prop {modifiers}\n   */\n  modifiers,\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n", "// Utils\nimport debounce from './utils/debounce';\nimport isFunction from './utils/isFunction';\n\n// Methods\nimport update from './methods/update';\nimport destroy from './methods/destroy';\nimport enableEventListeners from './methods/enableEventListeners';\nimport disableEventListeners from './methods/disableEventListeners';\nimport Defaults from './methods/defaults';\nimport placements from './methods/placements';\n\nexport default class Popper {\n  /**\n   * Create a new Popper.js instance\n   * @class Popper\n   * @param {HTMLElement|referenceObject} reference - The reference element used to position the popper\n   * @param {HTMLElement} popper - The HTML element used as popper.\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  constructor(reference, popper, options = {}) {\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = { ...Popper.Defaults, ...options };\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: [],\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys({\n      ...Popper.Defaults.modifiers,\n      ...options.modifiers,\n    }).forEach(name => {\n      this.options.modifiers[name] = {\n        // If it's a built-in modifier, use it as base\n        ...(Popper.Defaults.modifiers[name] || {}),\n        // If there are custom options, override and merge with default ones\n        ...(options.modifiers ? options.modifiers[name] : {}),\n      };\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers)\n      .map(name => ({\n        name,\n        ...this.options.modifiers[name],\n      }))\n      // sort the modifiers by order\n      .sort((a, b) => a.order - b.order);\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(modifierOptions => {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(\n          this.reference,\n          this.popper,\n          this.options,\n          modifierOptions,\n          this.state\n        );\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    const eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n  update() {\n    return update.call(this);\n  }\n  destroy() {\n    return destroy.call(this);\n  }\n  enableEventListeners() {\n    return enableEventListeners.call(this);\n  }\n  disableEventListeners() {\n    return disableEventListeners.call(this);\n  }\n\n  /**\n   * Schedule an update, it will run on the next UI update available\n   * @method scheduleUpdate\n   * @memberof Popper\n   */\n  scheduleUpdate = () => requestAnimationFrame(this.update);\n\n  /**\n   * Collection of utilities useful when writing custom modifiers.\n   * Starting from version 1.7, this method is available only if you\n   * include `popper-utils.js` before `popper.js`.\n   *\n   * **DEPRECATION**: This way to access PopperUtils is deprecated\n   * and will be removed in v2! Use the PopperUtils module directly instead.\n   * Due to the high instability of the methods contained in Utils, we can't\n   * guarantee them to follow semver. Use them at your own risk!\n   * @static\n   * @private\n   * @type {Object}\n   * @deprecated since version 1.8\n   * @member Utils\n   * @memberof Popper\n   */\n  static Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\n\n  static placements = placements;\n\n  static Defaults = Defaults;\n}\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "window", "document", "longerTimeoutBrowsers", "timeoutDuration", "i", "length", "navigator", "userAgent", "indexOf", "microtaskDebounce", "fn", "called", "resolve", "then", "taskDebounce", "scheduled", "supportsMicroTasks", "Promise", "isFunction", "functionToCheck", "getType", "toString", "call", "getStyleComputedProperty", "element", "property", "nodeType", "css", "getComputedStyle", "getParentNode", "nodeName", "parentNode", "host", "getScrollParent", "body", "ownerDocument", "overflow", "overflowX", "overflowY", "test", "getOffsetParent", "offsetParent", "documentElement", "isOffsetContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "element1root", "getScroll", "side", "upperSide", "html", "scrollingElement", "includeScroll", "rect", "subtract", "scrollTop", "scrollLeft", "modifier", "top", "bottom", "left", "right", "getBordersSize", "styles", "axis", "sideA", "sideB", "split", "isIE10", "undefined", "appVersion", "getSize", "computedStyle", "Math", "max", "getWindowSizes", "getClientRect", "offsets", "width", "height", "getBoundingClientRect", "err", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getOffsetRectRelativeToArbitraryNode", "children", "parent", "runIsIE10", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "relativeOffset", "innerWidth", "innerHeight", "offset", "isFixed", "getBoundaries", "popper", "reference", "padding", "boundariesElement", "boundaries", "boundariesNode", "getArea", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "Object", "keys", "map", "key", "sort", "a", "b", "area", "filtered<PERSON><PERSON>s", "filter", "computedPlacement", "variation", "getReferenceOffsets", "state", "commonOffsetParent", "getOuterSizes", "x", "parseFloat", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "replace", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "find", "arr", "check", "Array", "prototype", "findIndex", "prop", "value", "cur", "match", "obj", "runModifiers", "modifiers", "data", "ends", "modifiersToRun", "slice", "for<PERSON>ach", "warn", "enabled", "update", "isDestroyed", "options", "flip", "originalPlacement", "position", "isCreated", "onCreate", "onUpdate", "isModifierEnabled", "modifierName", "some", "name", "getSupportedPropertyName", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "prefix", "to<PERSON><PERSON><PERSON>", "style", "destroy", "removeAttribute", "disableEventListeners", "removeOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "getWindow", "defaultView", "attachToScrollParents", "event", "callback", "scrollParents", "isBody", "target", "addEventListener", "passive", "push", "setupEventListeners", "updateBound", "scrollElement", "eventsEnabled", "enableEventListeners", "scheduleUpdate", "removeEventListeners", "removeEventListener", "cancelAnimationFrame", "isNumeric", "n", "isNaN", "isFinite", "setStyles", "unit", "setAttributes", "attributes", "setAttribute", "applyStyle", "instance", "arrowElement", "arrowStyles", "applyStyleOnLoad", "modifierOptions", "computeStyle", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "floor", "prefixedProperty", "<PERSON><PERSON><PERSON><PERSON>", "invertTop", "invertLeft", "arrow", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "requested", "querySelector", "isVertical", "len", "sideCapitalized", "toLowerCase", "altSide", "opSide", "arrowElementSize", "center", "popperMarginSide", "sideValue", "min", "round", "getOppositeVariation", "validPlacements", "placements", "clockwise", "counter", "index", "concat", "reverse", "BEHAVIORS", "flipped", "placementOpposite", "flipOrder", "behavior", "FLIP", "CLOCKWISE", "COUNTERCLOCKWISE", "step", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariation", "flipVariations", "keepTogether", "toValue", "str", "size", "parseOffset", "basePlacement", "useHeight", "fragments", "frag", "trim", "divider", "search", "splitRegex", "ops", "op", "mergeWithPrevious", "reduce", "index2", "preventOverflow", "priority", "escapeWithReference", "shift", "shiftvariation", "shiftOffsets", "hide", "bound", "inner", "subtractLength", "<PERSON><PERSON>", "requestAnimationFrame", "debounce", "bind", "De<PERSON>ults", "j<PERSON>y", "onLoad", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAMA,YAAY,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,OAAOA,OAAOC,QAAd,KAA2B,WAA9E;AACA,IAAMC,wBAAwB,CAAC,MAAD,EAAS,SAAT,EAAoB,SAApB,CAA9B;AACA,IAAIC,kBAAkB,CAAtB;AACA,KAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIF,sBAAsBG,MAA1C,EAAkDD,KAAK,CAAvD,EAA0D;MACpDL,aAAaO,UAAUC,SAAV,CAAoBC,OAApB,CAA4BN,sBAAsBE,CAAtB,CAA5B,KAAyD,CAA1E,EAA6E;sBACzD,CAAlB;;;;;AAKJ,AAAO,SAASK,iBAAT,CAA2BC,EAA3B,EAA+B;MAChCC,SAAS,KAAb;SACO,YAAM;QACPA,MAAJ,EAAY;;;aAGH,IAAT;YACQC,OAAR,GAAkBC,IAAlB,CAAuB,YAAM;eAClB,KAAT;;KADF;GALF;;;AAYF,AAAO,SAASC,YAAT,CAAsBJ,EAAtB,EAA0B;MAC3BK,YAAY,KAAhB;SACO,YAAM;QACP,CAACA,SAAL,EAAgB;kBACF,IAAZ;iBACW,YAAM;oBACH,KAAZ;;OADF,EAGGZ,eAHH;;GAHJ;;;AAWF,IAAMa,qBAAqBjB,aAAaC,OAAOiB,OAA/C;;;;;;;;;;;AAYA,eAAgBD,qBACZP,iBADY,GAEZK,YAFJ;;ACjDA;;;;;;;AAOA,AAAe,SAASI,UAAT,CAAoBC,eAApB,EAAqC;MAC5CC,UAAU,EAAhB;SAEED,mBACAC,QAAQC,QAAR,CAAiBC,IAAjB,CAAsBH,eAAtB,MAA2C,mBAF7C;;;ACTF;;;;;;;AAOA,AAAe,SAASI,wBAAT,CAAkCC,OAAlC,EAA2CC,QAA3C,EAAqD;MAC9DD,QAAQE,QAAR,KAAqB,CAAzB,EAA4B;WACnB,EAAP;;;MAGIC,MAAM3B,OAAO4B,gBAAP,CAAwBJ,OAAxB,EAAiC,IAAjC,CAAZ;SACOC,WAAWE,IAAIF,QAAJ,CAAX,GAA2BE,GAAlC;;;ACbF;;;;;;;AAOA,AAAe,SAASE,aAAT,CAAuBL,OAAvB,EAAgC;MACzCA,QAAQM,QAAR,KAAqB,MAAzB,EAAiC;WACxBN,OAAP;;SAEKA,QAAQO,UAAR,IAAsBP,QAAQQ,IAArC;;;ACRF;;;;;;;AAOA,AAAe,SAASC,eAAT,CAAyBT,OAAzB,EAAkC;;MAE3C,CAACA,OAAL,EAAc;WACLxB,OAAOC,QAAP,CAAgBiC,IAAvB;;;UAGMV,QAAQM,QAAhB;SACO,MAAL;SACK,MAAL;aACSN,QAAQW,aAAR,CAAsBD,IAA7B;SACG,WAAL;aACSV,QAAQU,IAAf;;;;;8BAIuCX,yBAAyBC,OAAzB,CAfI;MAevCY,QAfuC,yBAevCA,QAfuC;MAe7BC,SAf6B,yBAe7BA,SAf6B;MAelBC,SAfkB,yBAelBA,SAfkB;;MAgB3C,gBAAgBC,IAAhB,CAAqBH,WAAWE,SAAX,GAAuBD,SAA5C,CAAJ,EAA4D;WACnDb,OAAP;;;SAGKS,gBAAgBJ,cAAcL,OAAd,CAAhB,CAAP;;;AC7BF;;;;;;;AAOA,AAAe,SAASgB,eAAT,CAAyBhB,OAAzB,EAAkC;;MAEzCiB,eAAejB,WAAWA,QAAQiB,YAAxC;MACMX,WAAWW,gBAAgBA,aAAaX,QAA9C;;MAEI,CAACA,QAAD,IAAaA,aAAa,MAA1B,IAAoCA,aAAa,MAArD,EAA6D;QACvDN,OAAJ,EAAa;aACJA,QAAQW,aAAR,CAAsBO,eAA7B;;;WAGK1C,OAAOC,QAAP,CAAgByC,eAAvB;;;;;MAMA,CAAC,IAAD,EAAO,OAAP,EAAgBlC,OAAhB,CAAwBiC,aAAaX,QAArC,MAAmD,CAAC,CAApD,IACAP,yBAAyBkB,YAAzB,EAAuC,UAAvC,MAAuD,QAFzD,EAGE;WACOD,gBAAgBC,YAAhB,CAAP;;;SAGKA,YAAP;;;AC5Ba,SAASE,iBAAT,CAA2BnB,OAA3B,EAAoC;MACzCM,QADyC,GAC5BN,OAD4B,CACzCM,QADyC;;MAE7CA,aAAa,MAAjB,EAAyB;WAChB,KAAP;;SAGAA,aAAa,MAAb,IAAuBU,gBAAgBhB,QAAQoB,iBAAxB,MAA+CpB,OADxE;;;ACPF;;;;;;;AAOA,AAAe,SAASqB,OAAT,CAAiBC,IAAjB,EAAuB;MAChCA,KAAKf,UAAL,KAAoB,IAAxB,EAA8B;WACrBc,QAAQC,KAAKf,UAAb,CAAP;;;SAGKe,IAAP;;;ACRF;;;;;;;;AAQA,AAAe,SAASC,sBAAT,CAAgCC,QAAhC,EAA0CC,QAA1C,EAAoD;;MAE7D,CAACD,QAAD,IAAa,CAACA,SAAStB,QAAvB,IAAmC,CAACuB,QAApC,IAAgD,CAACA,SAASvB,QAA9D,EAAwE;WAC/D1B,OAAOC,QAAP,CAAgByC,eAAvB;;;;MAIIQ,QACJF,SAASG,uBAAT,CAAiCF,QAAjC,IACAG,KAAKC,2BAFP;MAGMC,QAAQJ,QAAQF,QAAR,GAAmBC,QAAjC;MACMM,MAAML,QAAQD,QAAR,GAAmBD,QAA/B;;;MAGMQ,QAAQvD,SAASwD,WAAT,EAAd;QACMC,QAAN,CAAeJ,KAAf,EAAsB,CAAtB;QACMK,MAAN,CAAaJ,GAAb,EAAkB,CAAlB;MACQK,uBAjByD,GAiB7BJ,KAjB6B,CAiBzDI,uBAjByD;;;;MAqB9DZ,aAAaY,uBAAb,IACCX,aAAaW,uBADf,IAEAN,MAAMO,QAAN,CAAeN,GAAf,CAHF,EAIE;QACIZ,kBAAkBiB,uBAAlB,CAAJ,EAAgD;aACvCA,uBAAP;;;WAGKpB,gBAAgBoB,uBAAhB,CAAP;;;;MAIIE,eAAejB,QAAQG,QAAR,CAArB;MACIc,aAAa9B,IAAjB,EAAuB;WACde,uBAAuBe,aAAa9B,IAApC,EAA0CiB,QAA1C,CAAP;GADF,MAEO;WACEF,uBAAuBC,QAAvB,EAAiCH,QAAQI,QAAR,EAAkBjB,IAAnD,CAAP;;;;ACjDJ;;;;;;;;AAQA,AAAe,SAAS+B,SAAT,CAAmBvC,OAAnB,EAA0C;MAAdwC,IAAc,uEAAP,KAAO;;MACjDC,YAAYD,SAAS,KAAT,GAAiB,WAAjB,GAA+B,YAAjD;MACMlC,WAAWN,QAAQM,QAAzB;;MAEIA,aAAa,MAAb,IAAuBA,aAAa,MAAxC,EAAgD;QACxCoC,OAAO1C,QAAQW,aAAR,CAAsBO,eAAnC;QACMyB,mBAAmB3C,QAAQW,aAAR,CAAsBgC,gBAAtB,IAA0CD,IAAnE;WACOC,iBAAiBF,SAAjB,CAAP;;;SAGKzC,QAAQyC,SAAR,CAAP;;;AChBF;;;;;;;;;AASA,AAAe,SAASG,aAAT,CAAuBC,IAAvB,EAA6B7C,OAA7B,EAAwD;MAAlB8C,QAAkB,uEAAP,KAAO;;MAC/DC,YAAYR,UAAUvC,OAAV,EAAmB,KAAnB,CAAlB;MACMgD,aAAaT,UAAUvC,OAAV,EAAmB,MAAnB,CAAnB;MACMiD,WAAWH,WAAW,CAAC,CAAZ,GAAgB,CAAjC;OACKI,GAAL,IAAYH,YAAYE,QAAxB;OACKE,MAAL,IAAeJ,YAAYE,QAA3B;OACKG,IAAL,IAAaJ,aAAaC,QAA1B;OACKI,KAAL,IAAcL,aAAaC,QAA3B;SACOJ,IAAP;;;ACnBF;;;;;;;;;;AAUA,AAAe,SAASS,cAAT,CAAwBC,MAAxB,EAAgCC,IAAhC,EAAsC;MAC7CC,QAAQD,SAAS,GAAT,GAAe,MAAf,GAAwB,KAAtC;MACME,QAAQD,UAAU,MAAV,GAAmB,OAAnB,GAA6B,QAA3C;;SAGE,CAACF,kBAAgBE,KAAhB,YAA8BE,KAA9B,CAAoC,IAApC,EAA0C,CAA1C,CAAD,GACA,CAACJ,kBAAgBG,KAAhB,YAA8BC,KAA9B,CAAoC,IAApC,EAA0C,CAA1C,CAFH;;;ACdF;;;;;;AAMA,IAAIC,SAASC,SAAb;;AAEA,eAAe,YAAW;MACpBD,WAAWC,SAAf,EAA0B;aACf/E,UAAUgF,UAAV,CAAqB9E,OAArB,CAA6B,SAA7B,MAA4C,CAAC,CAAtD;;SAEK4E,MAAP;;;ACVF,SAASG,OAAT,CAAiBP,IAAjB,EAAuB9C,IAAvB,EAA6BgC,IAA7B,EAAmCsB,aAAnC,EAAkD;SACzCC,KAAKC,GAAL,CACLxD,gBAAc8C,IAAd,CADK,EAEL9C,gBAAc8C,IAAd,CAFK,EAGLd,gBAAcc,IAAd,CAHK,EAILd,gBAAcc,IAAd,CAJK,EAKLd,gBAAcc,IAAd,CALK,EAMLI,aACIlB,gBAAcc,IAAd,IACAQ,0BAAuBR,SAAS,QAAT,GAAoB,KAApB,GAA4B,MAAnD,EADA,GAEAQ,0BAAuBR,SAAS,QAAT,GAAoB,QAApB,GAA+B,OAAtD,EAHJ,GAII,CAVC,CAAP;;;AAcF,AAAe,SAASW,cAAT,GAA0B;MACjCzD,OAAOlC,OAAOC,QAAP,CAAgBiC,IAA7B;MACMgC,OAAOlE,OAAOC,QAAP,CAAgByC,eAA7B;MACM8C,gBAAgBJ,cAAYpF,OAAO4B,gBAAP,CAAwBsC,IAAxB,CAAlC;;SAEO;YACGqB,QAAQ,QAAR,EAAkBrD,IAAlB,EAAwBgC,IAAxB,EAA8BsB,aAA9B,CADH;WAEED,QAAQ,OAAR,EAAiBrD,IAAjB,EAAuBgC,IAAvB,EAA6BsB,aAA7B;GAFT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtBF;;;;;;;AAOA,AAAe,SAASI,aAAT,CAAuBC,OAAvB,EAAgC;sBAExCA,OADL;WAESA,QAAQjB,IAAR,GAAeiB,QAAQC,KAFhC;YAGUD,QAAQnB,GAAR,GAAcmB,QAAQE;;;;ACJlC;;;;;;;AAOA,AAAe,SAASC,qBAAT,CAA+BxE,OAA/B,EAAwC;MACjD6C,OAAO,EAAX;;;;;MAKIe,UAAJ,EAAc;QACR;aACK5D,QAAQwE,qBAAR,EAAP;UACMzB,YAAYR,UAAUvC,OAAV,EAAmB,KAAnB,CAAlB;UACMgD,aAAaT,UAAUvC,OAAV,EAAmB,MAAnB,CAAnB;WACKkD,GAAL,IAAYH,SAAZ;WACKK,IAAL,IAAaJ,UAAb;WACKG,MAAL,IAAeJ,SAAf;WACKM,KAAL,IAAcL,UAAd;KAPF,CAQE,OAAOyB,GAAP,EAAY;GAThB,MAUO;WACEzE,QAAQwE,qBAAR,EAAP;;;MAGIE,SAAS;UACP7B,KAAKO,IADE;SAERP,KAAKK,GAFG;WAGNL,KAAKQ,KAAL,GAAaR,KAAKO,IAHZ;YAILP,KAAKM,MAAL,GAAcN,KAAKK;GAJ7B;;;MAQMyB,QAAQ3E,QAAQM,QAAR,KAAqB,MAArB,GAA8B6D,gBAA9B,GAAiD,EAA/D;MACMG,QACJK,MAAML,KAAN,IAAetE,QAAQ4E,WAAvB,IAAsCF,OAAOrB,KAAP,GAAeqB,OAAOtB,IAD9D;MAEMmB,SACJI,MAAMJ,MAAN,IAAgBvE,QAAQ6E,YAAxB,IAAwCH,OAAOvB,MAAP,GAAgBuB,OAAOxB,GADjE;;MAGI4B,iBAAiB9E,QAAQ+E,WAAR,GAAsBT,KAA3C;MACIU,gBAAgBhF,QAAQiF,YAAR,GAAuBV,MAA3C;;;;MAIIO,kBAAkBE,aAAtB,EAAqC;QAC7BzB,SAASxD,yBAAyBC,OAAzB,CAAf;sBACkBsD,eAAeC,MAAf,EAAuB,GAAvB,CAAlB;qBACiBD,eAAeC,MAAf,EAAuB,GAAvB,CAAjB;;WAEOe,KAAP,IAAgBQ,cAAhB;WACOP,MAAP,IAAiBS,aAAjB;;;SAGKZ,cAAcM,MAAd,CAAP;;;ACvDa,SAASQ,oCAAT,CAA8CC,QAA9C,EAAwDC,MAAxD,EAAgE;MACvExB,SAASyB,UAAf;MACMC,SAASF,OAAO9E,QAAP,KAAoB,MAAnC;MACMiF,eAAef,sBAAsBW,QAAtB,CAArB;MACMK,aAAahB,sBAAsBY,MAAtB,CAAnB;MACMK,eAAehF,gBAAgB0E,QAAhB,CAArB;;MAEM5B,SAASxD,yBAAyBqF,MAAzB,CAAf;MACMM,iBAAiB,CAACnC,OAAOmC,cAAP,CAAsB/B,KAAtB,CAA4B,IAA5B,EAAkC,CAAlC,CAAxB;MACMgC,kBAAkB,CAACpC,OAAOoC,eAAP,CAAuBhC,KAAvB,CAA6B,IAA7B,EAAmC,CAAnC,CAAzB;;MAEIU,UAAUD,cAAc;SACrBmB,aAAarC,GAAb,GAAmBsC,WAAWtC,GAA9B,GAAoCwC,cADf;UAEpBH,aAAanC,IAAb,GAAoBoC,WAAWpC,IAA/B,GAAsCuC,eAFlB;WAGnBJ,aAAajB,KAHM;YAIlBiB,aAAahB;GAJT,CAAd;UAMQqB,SAAR,GAAoB,CAApB;UACQC,UAAR,GAAqB,CAArB;;;;;;MAMI,CAACjC,MAAD,IAAW0B,MAAf,EAAuB;QACfM,YAAY,CAACrC,OAAOqC,SAAP,CAAiBjC,KAAjB,CAAuB,IAAvB,EAA6B,CAA7B,CAAnB;QACMkC,aAAa,CAACtC,OAAOsC,UAAP,CAAkBlC,KAAlB,CAAwB,IAAxB,EAA8B,CAA9B,CAApB;;YAEQT,GAAR,IAAewC,iBAAiBE,SAAhC;YACQzC,MAAR,IAAkBuC,iBAAiBE,SAAnC;YACQxC,IAAR,IAAgBuC,kBAAkBE,UAAlC;YACQxC,KAAR,IAAiBsC,kBAAkBE,UAAnC;;;YAGQD,SAAR,GAAoBA,SAApB;YACQC,UAAR,GAAqBA,UAArB;;;MAIAjC,SACIwB,OAAO/C,QAAP,CAAgBoD,YAAhB,CADJ,GAEIL,WAAWK,YAAX,IAA2BA,aAAanF,QAAb,KAA0B,MAH3D,EAIE;cACUsC,cAAcyB,OAAd,EAAuBe,MAAvB,CAAV;;;SAGKf,OAAP;;;ACjDa,SAASyB,6CAAT,CAAuD9F,OAAvD,EAAgE;MACvE0C,OAAO1C,QAAQW,aAAR,CAAsBO,eAAnC;MACM6E,iBAAiBb,qCAAqClF,OAArC,EAA8C0C,IAA9C,CAAvB;MACM4B,QAAQL,KAAKC,GAAL,CAASxB,KAAKkC,WAAd,EAA2BpG,OAAOwH,UAAP,IAAqB,CAAhD,CAAd;MACMzB,SAASN,KAAKC,GAAL,CAASxB,KAAKmC,YAAd,EAA4BrG,OAAOyH,WAAP,IAAsB,CAAlD,CAAf;;MAEMlD,YAAYR,UAAUG,IAAV,CAAlB;MACMM,aAAaT,UAAUG,IAAV,EAAgB,MAAhB,CAAnB;;MAEMwD,SAAS;SACRnD,YAAYgD,eAAe7C,GAA3B,GAAiC6C,eAAeH,SADxC;UAEP5C,aAAa+C,eAAe3C,IAA5B,GAAmC2C,eAAeF,UAF3C;gBAAA;;GAAf;;SAOOzB,cAAc8B,MAAd,CAAP;;;ACjBF;;;;;;;;AAQA,AAAe,SAASC,OAAT,CAAiBnG,OAAjB,EAA0B;MACjCM,WAAWN,QAAQM,QAAzB;MACIA,aAAa,MAAb,IAAuBA,aAAa,MAAxC,EAAgD;WACvC,KAAP;;MAEEP,yBAAyBC,OAAzB,EAAkC,UAAlC,MAAkD,OAAtD,EAA+D;WACtD,IAAP;;SAEKmG,QAAQ9F,cAAcL,OAAd,CAAR,CAAP;;;ACXF;;;;;;;;;;AAUA,AAAe,SAASoG,aAAT,CACbC,MADa,EAEbC,SAFa,EAGbC,OAHa,EAIbC,iBAJa,EAKb;;MAEIC,aAAa,EAAEvD,KAAK,CAAP,EAAUE,MAAM,CAAhB,EAAjB;MACMnC,eAAeM,uBAAuB8E,MAAvB,EAA+BC,SAA/B,CAArB;;;MAGIE,sBAAsB,UAA1B,EAAsC;iBACvBV,8CAA8C7E,YAA9C,CAAb;GADF,MAEO;;QAEDyF,uBAAJ;QACIF,sBAAsB,cAA1B,EAA0C;uBACvB/F,gBAAgBJ,cAAcgG,MAAd,CAAhB,CAAjB;UACIK,eAAepG,QAAf,KAA4B,MAAhC,EAAwC;yBACrB+F,OAAO1F,aAAP,CAAqBO,eAAtC;;KAHJ,MAKO,IAAIsF,sBAAsB,QAA1B,EAAoC;uBACxBH,OAAO1F,aAAP,CAAqBO,eAAtC;KADK,MAEA;uBACYsF,iBAAjB;;;QAGInC,UAAUa,qCACdwB,cADc,EAEdzF,YAFc,CAAhB;;;QAMIyF,eAAepG,QAAf,KAA4B,MAA5B,IAAsC,CAAC6F,QAAQlF,YAAR,CAA3C,EAAkE;4BACtCkD,gBADsC;UACxDI,MADwD,mBACxDA,MADwD;UAChDD,KADgD,mBAChDA,KADgD;;iBAErDpB,GAAX,IAAkBmB,QAAQnB,GAAR,GAAcmB,QAAQuB,SAAxC;iBACWzC,MAAX,GAAoBoB,SAASF,QAAQnB,GAArC;iBACWE,IAAX,IAAmBiB,QAAQjB,IAAR,GAAeiB,QAAQwB,UAA1C;iBACWxC,KAAX,GAAmBiB,QAAQD,QAAQjB,IAAnC;KALF,MAMO;;mBAEQiB,OAAb;;;;;aAKOjB,IAAX,IAAmBmD,OAAnB;aACWrD,GAAX,IAAkBqD,OAAlB;aACWlD,KAAX,IAAoBkD,OAApB;aACWpD,MAAX,IAAqBoD,OAArB;;SAEOE,UAAP;;;ACnEF,SAASE,OAAT,OAAoC;MAAjBrC,KAAiB,QAAjBA,KAAiB;MAAVC,MAAU,QAAVA,MAAU;;SAC3BD,QAAQC,MAAf;;;;;;;;;;;;AAYF,AAAe,SAASqC,oBAAT,CACbC,SADa,EAEbC,OAFa,EAGbT,MAHa,EAIbC,SAJa,EAKbE,iBALa,EAOb;MADAD,OACA,uEADU,CACV;;MACIM,UAAU7H,OAAV,CAAkB,MAAlB,MAA8B,CAAC,CAAnC,EAAsC;WAC7B6H,SAAP;;;MAGIJ,aAAaL,cACjBC,MADiB,EAEjBC,SAFiB,EAGjBC,OAHiB,EAIjBC,iBAJiB,CAAnB;;MAOMO,QAAQ;SACP;aACIN,WAAWnC,KADf;cAEKwC,QAAQ5D,GAAR,GAAcuD,WAAWvD;KAHvB;WAKL;aACEuD,WAAWpD,KAAX,GAAmByD,QAAQzD,KAD7B;cAEGoD,WAAWlC;KAPT;YASJ;aACCkC,WAAWnC,KADZ;cAEEmC,WAAWtD,MAAX,GAAoB2D,QAAQ3D;KAX1B;UAaN;aACG2D,QAAQ1D,IAAR,GAAeqD,WAAWrD,IAD7B;cAEIqD,WAAWlC;;GAfvB;;MAmBMyC,cAAcC,OAAOC,IAAP,CAAYH,KAAZ,EACjBI,GADiB,CACb;;;OAEAJ,MAAMK,GAAN,CAFA;YAGGT,QAAQI,MAAMK,GAAN,CAAR;;GAJU,EAMjBC,IANiB,CAMZ,UAACC,CAAD,EAAIC,CAAJ;WAAUA,EAAEC,IAAF,GAASF,EAAEE,IAArB;GANY,CAApB;;MAQMC,gBAAgBT,YAAYU,MAAZ,CACpB;QAAGpD,KAAH,SAAGA,KAAH;QAAUC,MAAV,SAAUA,MAAV;WACED,SAAS+B,OAAOzB,WAAhB,IAA+BL,UAAU8B,OAAOxB,YADlD;GADoB,CAAtB;;MAKM8C,oBAAoBF,cAAc5I,MAAd,GAAuB,CAAvB,GACtB4I,cAAc,CAAd,EAAiBL,GADK,GAEtBJ,YAAY,CAAZ,EAAeI,GAFnB;;MAIMQ,YAAYf,UAAUlD,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAlB;;SAEOgE,qBAAqBC,kBAAgBA,SAAhB,GAA8B,EAAnD,CAAP;;;ACrEF;;;;;;;;;AASA,AAAe,SAASC,mBAAT,CAA6BC,KAA7B,EAAoCzB,MAApC,EAA4CC,SAA5C,EAAuD;MAC9DyB,qBAAqBxG,uBAAuB8E,MAAvB,EAA+BC,SAA/B,CAA3B;SACOpB,qCAAqCoB,SAArC,EAAgDyB,kBAAhD,CAAP;;;ACdF;;;;;;;AAOA,AAAe,SAASC,aAAT,CAAuBhI,OAAvB,EAAgC;MACvCuD,SAAS/E,OAAO4B,gBAAP,CAAwBJ,OAAxB,CAAf;MACMiI,IAAIC,WAAW3E,OAAOqC,SAAlB,IAA+BsC,WAAW3E,OAAO4E,YAAlB,CAAzC;MACMC,IAAIF,WAAW3E,OAAOsC,UAAlB,IAAgCqC,WAAW3E,OAAO8E,WAAlB,CAA1C;MACM3D,SAAS;WACN1E,QAAQ+E,WAAR,GAAsBqD,CADhB;YAELpI,QAAQiF,YAAR,GAAuBgD;GAFjC;SAIOvD,MAAP;;;ACfF;;;;;;;AAOA,AAAe,SAAS4D,oBAAT,CAA8BzB,SAA9B,EAAyC;MAChD0B,OAAO,EAAEnF,MAAM,OAAR,EAAiBC,OAAO,MAAxB,EAAgCF,QAAQ,KAAxC,EAA+CD,KAAK,QAApD,EAAb;SACO2D,UAAU2B,OAAV,CAAkB,wBAAlB,EAA4C;WAAWD,KAAKE,OAAL,CAAX;GAA5C,CAAP;;;ACNF;;;;;;;;;;AAUA,AAAe,SAASC,gBAAT,CAA0BrC,MAA1B,EAAkCsC,gBAAlC,EAAoD9B,SAApD,EAA+D;cAChEA,UAAUlD,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAZ;;;MAGMiF,aAAaZ,cAAc3B,MAAd,CAAnB;;;MAGMwC,gBAAgB;WACbD,WAAWtE,KADE;YAEZsE,WAAWrE;GAFrB;;;MAMMuE,UAAU,CAAC,OAAD,EAAU,MAAV,EAAkB9J,OAAlB,CAA0B6H,SAA1B,MAAyC,CAAC,CAA1D;MACMkC,WAAWD,UAAU,KAAV,GAAkB,MAAnC;MACME,gBAAgBF,UAAU,MAAV,GAAmB,KAAzC;MACMG,cAAcH,UAAU,QAAV,GAAqB,OAAzC;MACMI,uBAAuB,CAACJ,OAAD,GAAW,QAAX,GAAsB,OAAnD;;gBAEcC,QAAd,IACEJ,iBAAiBI,QAAjB,IACAJ,iBAAiBM,WAAjB,IAAgC,CADhC,GAEAL,WAAWK,WAAX,IAA0B,CAH5B;MAIIpC,cAAcmC,aAAlB,EAAiC;kBACjBA,aAAd,IACEL,iBAAiBK,aAAjB,IAAkCJ,WAAWM,oBAAX,CADpC;GADF,MAGO;kBACSF,aAAd,IACEL,iBAAiBL,qBAAqBU,aAArB,CAAjB,CADF;;;SAIKH,aAAP;;;AC5CF;;;;;;;;;AASA,AAAe,SAASM,IAAT,CAAcC,GAAd,EAAmBC,KAAnB,EAA0B;;MAEnCC,MAAMC,SAAN,CAAgBJ,IAApB,EAA0B;WACjBC,IAAID,IAAJ,CAASE,KAAT,CAAP;;;;SAIKD,IAAI1B,MAAJ,CAAW2B,KAAX,EAAkB,CAAlB,CAAP;;;ACdF;;;;;;;;;AASA,AAAe,SAASG,SAAT,CAAmBJ,GAAnB,EAAwBK,IAAxB,EAA8BC,KAA9B,EAAqC;;MAE9CJ,MAAMC,SAAN,CAAgBC,SAApB,EAA+B;WACtBJ,IAAII,SAAJ,CAAc;aAAOG,IAAIF,IAAJ,MAAcC,KAArB;KAAd,CAAP;;;;MAIIE,QAAQT,KAAKC,GAAL,EAAU;WAAOS,IAAIJ,IAAJ,MAAcC,KAArB;GAAV,CAAd;SACON,IAAIpK,OAAJ,CAAY4K,KAAZ,CAAP;;;ACfF;;;;;;;;;;AAUA,AAAe,SAASE,YAAT,CAAsBC,SAAtB,EAAiCC,IAAjC,EAAuCC,IAAvC,EAA6C;MACpDC,iBAAiBD,SAASpG,SAAT,GACnBkG,SADmB,GAEnBA,UAAUI,KAAV,CAAgB,CAAhB,EAAmBX,UAAUO,SAAV,EAAqB,MAArB,EAA6BE,IAA7B,CAAnB,CAFJ;;iBAIeG,OAAf,CAAuB,oBAAY;QAC7BnH,SAAS,UAAT,CAAJ,EAA0B;;cAChBoH,IAAR,CAAa,uDAAb;;QAEInL,KAAK+D,SAAS,UAAT,KAAwBA,SAAS/D,EAA5C,CAJiC;QAK7B+D,SAASqH,OAAT,IAAoB5K,WAAWR,EAAX,CAAxB,EAAwC;;;;WAIjCmF,OAAL,CAAagC,MAAb,GAAsBjC,cAAc4F,KAAK3F,OAAL,CAAagC,MAA3B,CAAtB;WACKhC,OAAL,CAAaiC,SAAb,GAAyBlC,cAAc4F,KAAK3F,OAAL,CAAaiC,SAA3B,CAAzB;;aAEOpH,GAAG8K,IAAH,EAAS/G,QAAT,CAAP;;GAZJ;;SAgBO+G,IAAP;;;AC9BF;;;;;;;AAOA,AAAe,SAASO,MAAT,GAAkB;;MAE3B,KAAKzC,KAAL,CAAW0C,WAAf,EAA4B;;;;MAIxBR,OAAO;cACC,IADD;YAED,EAFC;iBAGI,EAHJ;gBAIG,EAJH;aAKA,KALA;aAMA;GANX;;;OAUK3F,OAAL,CAAaiC,SAAb,GAAyBuB,oBACvB,KAAKC,KADkB,EAEvB,KAAKzB,MAFkB,EAGvB,KAAKC,SAHkB,CAAzB;;;;;OASKO,SAAL,GAAiBD,qBACf,KAAK6D,OAAL,CAAa5D,SADE,EAEfmD,KAAK3F,OAAL,CAAaiC,SAFE,EAGf,KAAKD,MAHU,EAIf,KAAKC,SAJU,EAKf,KAAKmE,OAAL,CAAaV,SAAb,CAAuBW,IAAvB,CAA4BlE,iBALb,EAMf,KAAKiE,OAAL,CAAaV,SAAb,CAAuBW,IAAvB,CAA4BnE,OANb,CAAjB;;;OAUKoE,iBAAL,GAAyBX,KAAKnD,SAA9B;;;OAGKxC,OAAL,CAAagC,MAAb,GAAsBqC,iBACpB,KAAKrC,MADe,EAEpB2D,KAAK3F,OAAL,CAAaiC,SAFO,EAGpB0D,KAAKnD,SAHe,CAAtB;OAKKxC,OAAL,CAAagC,MAAb,CAAoBuE,QAApB,GAA+B,UAA/B;;;SAGOd,aAAa,KAAKC,SAAlB,EAA6BC,IAA7B,CAAP;;;;MAII,CAAC,KAAKlC,KAAL,CAAW+C,SAAhB,EAA2B;SACpB/C,KAAL,CAAW+C,SAAX,GAAuB,IAAvB;SACKJ,OAAL,CAAaK,QAAb,CAAsBd,IAAtB;GAFF,MAGO;SACAS,OAAL,CAAaM,QAAb,CAAsBf,IAAtB;;;;AClEJ;;;;;;AAMA,AAAe,SAASgB,iBAAT,CAA2BjB,SAA3B,EAAsCkB,YAAtC,EAAoD;SAC1DlB,UAAUmB,IAAV,CACL;QAAGC,IAAH,QAAGA,IAAH;QAASb,OAAT,QAASA,OAAT;WAAuBA,WAAWa,SAASF,YAA3C;GADK,CAAP;;;ACPF;;;;;;;AAOA,AAAe,SAASG,wBAAT,CAAkCnL,QAAlC,EAA4C;MACnDoL,WAAW,CAAC,KAAD,EAAQ,IAAR,EAAc,QAAd,EAAwB,KAAxB,EAA+B,GAA/B,CAAjB;MACMC,YAAYrL,SAASsL,MAAT,CAAgB,CAAhB,EAAmBC,WAAnB,KAAmCvL,SAASkK,KAAT,CAAe,CAAf,CAArD;;OAEK,IAAIvL,IAAI,CAAb,EAAgBA,IAAIyM,SAASxM,MAAT,GAAkB,CAAtC,EAAyCD,GAAzC,EAA8C;QACtC6M,SAASJ,SAASzM,CAAT,CAAf;QACM8M,UAAUD,cAAYA,MAAZ,GAAqBH,SAArB,GAAmCrL,QAAnD;QACI,OAAOzB,OAAOC,QAAP,CAAgBiC,IAAhB,CAAqBiL,KAArB,CAA2BD,OAA3B,CAAP,KAA+C,WAAnD,EAAgE;aACvDA,OAAP;;;SAGG,IAAP;;;ACfF;;;;;AAKA,AAAe,SAASE,OAAT,GAAmB;OAC3B9D,KAAL,CAAW0C,WAAX,GAAyB,IAAzB;;;MAGIQ,kBAAkB,KAAKjB,SAAvB,EAAkC,YAAlC,CAAJ,EAAqD;SAC9C1D,MAAL,CAAYwF,eAAZ,CAA4B,aAA5B;SACKxF,MAAL,CAAYsF,KAAZ,CAAkBvI,IAAlB,GAAyB,EAAzB;SACKiD,MAAL,CAAYsF,KAAZ,CAAkBf,QAAlB,GAA6B,EAA7B;SACKvE,MAAL,CAAYsF,KAAZ,CAAkBzI,GAAlB,GAAwB,EAAxB;SACKmD,MAAL,CAAYsF,KAAZ,CAAkBP,yBAAyB,WAAzB,CAAlB,IAA2D,EAA3D;;;OAGGU,qBAAL;;;;MAII,KAAKrB,OAAL,CAAasB,eAAjB,EAAkC;SAC3B1F,MAAL,CAAY9F,UAAZ,CAAuByL,WAAvB,CAAmC,KAAK3F,MAAxC;;SAEK,IAAP;;;AC3BF;;;;;AAKA,AAAe,SAAS4F,SAAT,CAAmBjM,OAAnB,EAA4B;MACnCW,gBAAgBX,QAAQW,aAA9B;SACOA,gBAAgBA,cAAcuL,WAA9B,GAA4C1N,MAAnD;;;ACJF,SAAS2N,qBAAT,CAA+B1G,YAA/B,EAA6C2G,KAA7C,EAAoDC,QAApD,EAA8DC,aAA9D,EAA6E;MACrEC,SAAS9G,aAAanF,QAAb,KAA0B,MAAzC;MACMkM,SAASD,SAAS9G,aAAa9E,aAAb,CAA2BuL,WAApC,GAAkDzG,YAAjE;SACOgH,gBAAP,CAAwBL,KAAxB,EAA+BC,QAA/B,EAAyC,EAAEK,SAAS,IAAX,EAAzC;;MAEI,CAACH,MAAL,EAAa;0BAET9L,gBAAgB+L,OAAOjM,UAAvB,CADF,EAEE6L,KAFF,EAGEC,QAHF,EAIEC,aAJF;;gBAOYK,IAAd,CAAmBH,MAAnB;;;;;;;;;AASF,AAAe,SAASI,mBAAT,CACbtG,SADa,EAEbmE,OAFa,EAGb3C,KAHa,EAIb+E,WAJa,EAKb;;QAEMA,WAAN,GAAoBA,WAApB;YACUvG,SAAV,EAAqBmG,gBAArB,CAAsC,QAAtC,EAAgD3E,MAAM+E,WAAtD,EAAmE,EAAEH,SAAS,IAAX,EAAnE;;;MAGMI,gBAAgBrM,gBAAgB6F,SAAhB,CAAtB;wBAEEwG,aADF,EAEE,QAFF,EAGEhF,MAAM+E,WAHR,EAIE/E,MAAMwE,aAJR;QAMMQ,aAAN,GAAsBA,aAAtB;QACMC,aAAN,GAAsB,IAAtB;;SAEOjF,KAAP;;;AC5CF;;;;;;AAMA,AAAe,SAASkF,oBAAT,GAAgC;MACzC,CAAC,KAAKlF,KAAL,CAAWiF,aAAhB,EAA+B;SACxBjF,KAAL,GAAa8E,oBACX,KAAKtG,SADM,EAEX,KAAKmE,OAFM,EAGX,KAAK3C,KAHM,EAIX,KAAKmF,cAJM,CAAb;;;;ACRJ;;;;;;AAMA,AAAe,SAASC,oBAAT,CAA8B5G,SAA9B,EAAyCwB,KAAzC,EAAgD;;YAEnDxB,SAAV,EAAqB6G,mBAArB,CAAyC,QAAzC,EAAmDrF,MAAM+E,WAAzD;;;QAGMP,aAAN,CAAoBlC,OAApB,CAA4B,kBAAU;WAC7B+C,mBAAP,CAA2B,QAA3B,EAAqCrF,MAAM+E,WAA3C;GADF;;;QAKMA,WAAN,GAAoB,IAApB;QACMP,aAAN,GAAsB,EAAtB;QACMQ,aAAN,GAAsB,IAAtB;QACMC,aAAN,GAAsB,KAAtB;SACOjF,KAAP;;;ACpBF;;;;;;;AAOA,AAAe,SAASgE,qBAAT,GAAiC;MAC1C,KAAKhE,KAAL,CAAWiF,aAAf,EAA8B;WACrBK,oBAAP,CAA4B,KAAKH,cAAjC;SACKnF,KAAL,GAAaoF,qBAAqB,KAAK5G,SAA1B,EAAqC,KAAKwB,KAA1C,CAAb;;;;ACZJ;;;;;;;AAOA,AAAe,SAASuF,SAAT,CAAmBC,CAAnB,EAAsB;SAC5BA,MAAM,EAAN,IAAY,CAACC,MAAMrF,WAAWoF,CAAX,CAAN,CAAb,IAAqCE,SAASF,CAAT,CAA5C;;;ACNF;;;;;;;;AAQA,AAAe,SAASG,SAAT,CAAmBzN,OAAnB,EAA4BuD,MAA5B,EAAoC;SAC1C2D,IAAP,CAAY3D,MAAZ,EAAoB6G,OAApB,CAA4B,gBAAQ;QAC9BsD,OAAO,EAAX;;QAGE,CAAC,OAAD,EAAU,QAAV,EAAoB,KAApB,EAA2B,OAA3B,EAAoC,QAApC,EAA8C,MAA9C,EAAsD1O,OAAtD,CAA8DyK,IAA9D,MACE,CAAC,CADH,IAEA4D,UAAU9J,OAAOkG,IAAP,CAAV,CAHF,EAIE;aACO,IAAP;;YAEMkC,KAAR,CAAclC,IAAd,IAAsBlG,OAAOkG,IAAP,IAAeiE,IAArC;GAVF;;;ACXF;;;;;;;;AAQA,AAAe,SAASC,aAAT,CAAuB3N,OAAvB,EAAgC4N,UAAhC,EAA4C;SAClD1G,IAAP,CAAY0G,UAAZ,EAAwBxD,OAAxB,CAAgC,UAASX,IAAT,EAAe;QACvCC,QAAQkE,WAAWnE,IAAX,CAAd;QACIC,UAAU,KAAd,EAAqB;cACXmE,YAAR,CAAqBpE,IAArB,EAA2BmE,WAAWnE,IAAX,CAA3B;KADF,MAEO;cACGoC,eAAR,CAAwBpC,IAAxB;;GALJ;;;ACJF;;;;;;;;;AASA,AAAe,SAASqE,UAAT,CAAoB9D,IAApB,EAA0B;;;;;YAK7BA,KAAK+D,QAAL,CAAc1H,MAAxB,EAAgC2D,KAAKzG,MAArC;;;;gBAIcyG,KAAK+D,QAAL,CAAc1H,MAA5B,EAAoC2D,KAAK4D,UAAzC;;;MAGI5D,KAAKgE,YAAL,IAAqB/G,OAAOC,IAAP,CAAY8C,KAAKiE,WAAjB,EAA8BpP,MAAvD,EAA+D;cACnDmL,KAAKgE,YAAf,EAA6BhE,KAAKiE,WAAlC;;;SAGKjE,IAAP;;;;;;;;;;;;;AAaF,AAAO,SAASkE,gBAAT,CACL5H,SADK,EAELD,MAFK,EAGLoE,OAHK,EAIL0D,eAJK,EAKLrG,KALK,EAML;;MAEMa,mBAAmBd,oBAAoBC,KAApB,EAA2BzB,MAA3B,EAAmCC,SAAnC,CAAzB;;;;;MAKMO,YAAYD,qBAChB6D,QAAQ5D,SADQ,EAEhB8B,gBAFgB,EAGhBtC,MAHgB,EAIhBC,SAJgB,EAKhBmE,QAAQV,SAAR,CAAkBW,IAAlB,CAAuBlE,iBALP,EAMhBiE,QAAQV,SAAR,CAAkBW,IAAlB,CAAuBnE,OANP,CAAlB;;SASOsH,YAAP,CAAoB,aAApB,EAAmChH,SAAnC;;;;YAIUR,MAAV,EAAkB,EAAEuE,UAAU,UAAZ,EAAlB;;SAEOH,OAAP;;;AClEF;;;;;;;AAOA,AAAe,SAAS2D,YAAT,CAAsBpE,IAAtB,EAA4BS,OAA5B,EAAqC;MAC1CxC,CAD0C,GACjCwC,OADiC,CAC1CxC,CAD0C;MACvCG,CADuC,GACjCqC,OADiC,CACvCrC,CADuC;MAE1C/B,MAF0C,GAE/B2D,KAAK3F,OAF0B,CAE1CgC,MAF0C;;;;MAK5CgI,8BAA8BlF,KAClCa,KAAK+D,QAAL,CAAchE,SADoB,EAElC;WAAY9G,SAASkI,IAAT,KAAkB,YAA9B;GAFkC,EAGlCmD,eAHF;MAIID,gCAAgCxK,SAApC,EAA+C;YACrCwG,IAAR,CACE,+HADF;;MAIIiE,kBACJD,gCAAgCxK,SAAhC,GACIwK,2BADJ,GAEI5D,QAAQ6D,eAHd;;MAKMrN,eAAeD,gBAAgBgJ,KAAK+D,QAAL,CAAc1H,MAA9B,CAArB;MACMkI,mBAAmB/J,sBAAsBvD,YAAtB,CAAzB;;;MAGMsC,SAAS;cACH8C,OAAOuE;GADnB;;;MAKMvG,UAAU;UACRJ,KAAKuK,KAAL,CAAWnI,OAAOjD,IAAlB,CADQ;SAETa,KAAKuK,KAAL,CAAWnI,OAAOnD,GAAlB,CAFS;YAGNe,KAAKuK,KAAL,CAAWnI,OAAOlD,MAAlB,CAHM;WAIPc,KAAKuK,KAAL,CAAWnI,OAAOhD,KAAlB;GAJT;;MAOMI,QAAQwE,MAAM,QAAN,GAAiB,KAAjB,GAAyB,QAAvC;MACMvE,QAAQ0E,MAAM,OAAN,GAAgB,MAAhB,GAAyB,OAAvC;;;;;MAKMqG,mBAAmBrD,yBAAyB,WAAzB,CAAzB;;;;;;;;;;;MAWIhI,aAAJ;MAAUF,YAAV;MACIO,UAAU,QAAd,EAAwB;UAChB,CAAC8K,iBAAiBhK,MAAlB,GAA2BF,QAAQlB,MAAzC;GADF,MAEO;UACCkB,QAAQnB,GAAd;;MAEEQ,UAAU,OAAd,EAAuB;WACd,CAAC6K,iBAAiBjK,KAAlB,GAA0BD,QAAQhB,KAAzC;GADF,MAEO;WACEgB,QAAQjB,IAAf;;MAEEkL,mBAAmBG,gBAAvB,EAAyC;WAChCA,gBAAP,qBAA0CrL,IAA1C,YAAqDF,GAArD;WACOO,KAAP,IAAgB,CAAhB;WACOC,KAAP,IAAgB,CAAhB;WACOgL,UAAP,GAAoB,WAApB;GAJF,MAKO;;QAECC,YAAYlL,UAAU,QAAV,GAAqB,CAAC,CAAtB,GAA0B,CAA5C;QACMmL,aAAalL,UAAU,OAAV,GAAoB,CAAC,CAArB,GAAyB,CAA5C;WACOD,KAAP,IAAgBP,MAAMyL,SAAtB;WACOjL,KAAP,IAAgBN,OAAOwL,UAAvB;WACOF,UAAP,GAAuBjL,KAAvB,UAAiCC,KAAjC;;;;MAIIkK,aAAa;mBACF5D,KAAKnD;GADtB;;;OAKK+G,UAAL,gBAAuBA,UAAvB,EAAsC5D,KAAK4D,UAA3C;OACKrK,MAAL,gBAAmBA,MAAnB,EAA8ByG,KAAKzG,MAAnC;OACK0K,WAAL,gBAAwBjE,KAAK3F,OAAL,CAAawK,KAArC,EAA+C7E,KAAKiE,WAApD;;SAEOjE,IAAP;;;ACjGF;;;;;;;;;;AAUA,AAAe,SAAS8E,kBAAT,CACb/E,SADa,EAEbgF,cAFa,EAGbC,aAHa,EAIb;MACMC,aAAa9F,KAAKY,SAAL,EAAgB;QAAGoB,IAAH,QAAGA,IAAH;WAAcA,SAAS4D,cAAvB;GAAhB,CAAnB;;MAEMG,aACJ,CAAC,CAACD,UAAF,IACAlF,UAAUmB,IAAV,CAAe,oBAAY;WAEvBjI,SAASkI,IAAT,KAAkB6D,aAAlB,IACA/L,SAASqH,OADT,IAEArH,SAASvB,KAAT,GAAiBuN,WAAWvN,KAH9B;GADF,CAFF;;MAUI,CAACwN,UAAL,EAAiB;QACTD,oBAAkBF,cAAlB,MAAN;QACMI,kBAAiBH,aAAjB,MAAN;YACQ3E,IAAR,CACK8E,SADL,iCAC0CF,WAD1C,iEACgHA,WADhH;;SAIKC,UAAP;;;AC/BF;;;;;;;AAOA,AAAe,SAASL,KAAT,CAAe7E,IAAf,EAAqBS,OAArB,EAA8B;;MAEvC,CAACqE,mBAAmB9E,KAAK+D,QAAL,CAAchE,SAAjC,EAA4C,OAA5C,EAAqD,cAArD,CAAL,EAA2E;WAClEC,IAAP;;;MAGEgE,eAAevD,QAAQzK,OAA3B;;;MAGI,OAAOgO,YAAP,KAAwB,QAA5B,EAAsC;mBACrBhE,KAAK+D,QAAL,CAAc1H,MAAd,CAAqB+I,aAArB,CAAmCpB,YAAnC,CAAf;;;QAGI,CAACA,YAAL,EAAmB;aACVhE,IAAP;;GALJ,MAOO;;;QAGD,CAACA,KAAK+D,QAAL,CAAc1H,MAAd,CAAqBhE,QAArB,CAA8B2L,YAA9B,CAAL,EAAkD;cACxC3D,IAAR,CACE,+DADF;aAGOL,IAAP;;;;MAIEnD,YAAYmD,KAAKnD,SAAL,CAAelD,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAlB;sBAC8BqG,KAAK3F,OA5BQ;MA4BnCgC,MA5BmC,iBA4BnCA,MA5BmC;MA4B3BC,SA5B2B,iBA4B3BA,SA5B2B;;MA6BrC+I,aAAa,CAAC,MAAD,EAAS,OAAT,EAAkBrQ,OAAlB,CAA0B6H,SAA1B,MAAyC,CAAC,CAA7D;;MAEMyI,MAAMD,aAAa,QAAb,GAAwB,OAApC;MACME,kBAAkBF,aAAa,KAAb,GAAqB,MAA7C;MACM7M,OAAO+M,gBAAgBC,WAAhB,EAAb;MACMC,UAAUJ,aAAa,MAAb,GAAsB,KAAtC;MACMK,SAASL,aAAa,QAAb,GAAwB,OAAvC;MACMM,mBAAmB3H,cAAcgG,YAAd,EAA4BsB,GAA5B,CAAzB;;;;;;;;MAQIhJ,UAAUoJ,MAAV,IAAoBC,gBAApB,GAAuCtJ,OAAO7D,IAAP,CAA3C,EAAyD;SAClD6B,OAAL,CAAagC,MAAb,CAAoB7D,IAApB,KACE6D,OAAO7D,IAAP,KAAgB8D,UAAUoJ,MAAV,IAAoBC,gBAApC,CADF;;;MAIErJ,UAAU9D,IAAV,IAAkBmN,gBAAlB,GAAqCtJ,OAAOqJ,MAAP,CAAzC,EAAyD;SAClDrL,OAAL,CAAagC,MAAb,CAAoB7D,IAApB,KACE8D,UAAU9D,IAAV,IAAkBmN,gBAAlB,GAAqCtJ,OAAOqJ,MAAP,CADvC;;;;MAKIE,SAAStJ,UAAU9D,IAAV,IAAkB8D,UAAUgJ,GAAV,IAAiB,CAAnC,GAAuCK,mBAAmB,CAAzE;;;;MAIME,mBAAmB9P,yBACvBiK,KAAK+D,QAAL,CAAc1H,MADS,aAEdkJ,eAFc,EAGvB/G,OAHuB,CAGf,IAHe,EAGT,EAHS,CAAzB;MAIIsH,YACFF,SAASxL,cAAc4F,KAAK3F,OAAL,CAAagC,MAA3B,EAAmC7D,IAAnC,CAAT,GAAoDqN,gBADtD;;;cAIY5L,KAAKC,GAAL,CAASD,KAAK8L,GAAL,CAAS1J,OAAOiJ,GAAP,IAAcK,gBAAvB,EAAyCG,SAAzC,CAAT,EAA8D,CAA9D,CAAZ;;OAEK9B,YAAL,GAAoBA,YAApB;OACK3J,OAAL,CAAawK,KAAb,GAAqB,EAArB;OACKxK,OAAL,CAAawK,KAAb,CAAmBrM,IAAnB,IAA2ByB,KAAK+L,KAAL,CAAWF,SAAX,CAA3B;OACKzL,OAAL,CAAawK,KAAb,CAAmBY,OAAnB,IAA8B,EAA9B,CAxE2C;;SA0EpCzF,IAAP;;;ACtFF;;;;;;;AAOA,AAAe,SAASiG,oBAAT,CAA8BrI,SAA9B,EAAyC;MAClDA,cAAc,KAAlB,EAAyB;WAChB,OAAP;GADF,MAEO,IAAIA,cAAc,OAAlB,EAA2B;WACzB,KAAP;;SAEKA,SAAP;;;ACbF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,iBAAe,CACb,YADa,EAEb,MAFa,EAGb,UAHa,EAIb,WAJa,EAKb,KALa,EAMb,SANa,EAOb,aAPa,EAQb,OARa,EASb,WATa,EAUb,YAVa,EAWb,QAXa,EAYb,cAZa,EAab,UAba,EAcb,MAda,EAeb,YAfa,CAAf;;AC7BA;AACA,IAAMsI,kBAAkBC,WAAWhG,KAAX,CAAiB,CAAjB,CAAxB;;;;;;;;;;;;AAYA,AAAe,SAASiG,SAAT,CAAmBvJ,SAAnB,EAA+C;MAAjBwJ,OAAiB,uEAAP,KAAO;;MACtDC,QAAQJ,gBAAgBlR,OAAhB,CAAwB6H,SAAxB,CAAd;MACMuC,MAAM8G,gBACT/F,KADS,CACHmG,QAAQ,CADL,EAETC,MAFS,CAEFL,gBAAgB/F,KAAhB,CAAsB,CAAtB,EAAyBmG,KAAzB,CAFE,CAAZ;SAGOD,UAAUjH,IAAIoH,OAAJ,EAAV,GAA0BpH,GAAjC;;;ACZF,IAAMqH,YAAY;QACV,MADU;aAEL,WAFK;oBAGE;CAHpB;;;;;;;;;AAaA,AAAe,SAAS/F,IAAT,CAAcV,IAAd,EAAoBS,OAApB,EAA6B;;MAEtCO,kBAAkBhB,KAAK+D,QAAL,CAAchE,SAAhC,EAA2C,OAA3C,CAAJ,EAAyD;WAChDC,IAAP;;;MAGEA,KAAK0G,OAAL,IAAgB1G,KAAKnD,SAAL,KAAmBmD,KAAKW,iBAA5C,EAA+D;;WAEtDX,IAAP;;;MAGIvD,aAAaL,cACjB4D,KAAK+D,QAAL,CAAc1H,MADG,EAEjB2D,KAAK+D,QAAL,CAAczH,SAFG,EAGjBmE,QAAQlE,OAHS,EAIjBkE,QAAQjE,iBAJS,CAAnB;;MAOIK,YAAYmD,KAAKnD,SAAL,CAAelD,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAhB;MACIgN,oBAAoBrI,qBAAqBzB,SAArB,CAAxB;MACIe,YAAYoC,KAAKnD,SAAL,CAAelD,KAAf,CAAqB,GAArB,EAA0B,CAA1B,KAAgC,EAAhD;;MAEIiN,YAAY,EAAhB;;UAEQnG,QAAQoG,QAAhB;SACOJ,UAAUK,IAAf;kBACc,CAACjK,SAAD,EAAY8J,iBAAZ,CAAZ;;SAEGF,UAAUM,SAAf;kBACcX,UAAUvJ,SAAV,CAAZ;;SAEG4J,UAAUO,gBAAf;kBACcZ,UAAUvJ,SAAV,EAAqB,IAArB,CAAZ;;;kBAGY4D,QAAQoG,QAApB;;;YAGMzG,OAAV,CAAkB,UAAC6G,IAAD,EAAOX,KAAP,EAAiB;QAC7BzJ,cAAcoK,IAAd,IAAsBL,UAAU/R,MAAV,KAAqByR,QAAQ,CAAvD,EAA0D;aACjDtG,IAAP;;;gBAGUA,KAAKnD,SAAL,CAAelD,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAZ;wBACoB2E,qBAAqBzB,SAArB,CAApB;;QAEMgC,gBAAgBmB,KAAK3F,OAAL,CAAagC,MAAnC;QACM6K,aAAalH,KAAK3F,OAAL,CAAaiC,SAAhC;;;QAGMkI,QAAQvK,KAAKuK,KAAnB;QACM2C,cACHtK,cAAc,MAAd,IACC2H,MAAM3F,cAAcxF,KAApB,IAA6BmL,MAAM0C,WAAW9N,IAAjB,CAD/B,IAECyD,cAAc,OAAd,IACC2H,MAAM3F,cAAczF,IAApB,IAA4BoL,MAAM0C,WAAW7N,KAAjB,CAH9B,IAICwD,cAAc,KAAd,IACC2H,MAAM3F,cAAc1F,MAApB,IAA8BqL,MAAM0C,WAAWhO,GAAjB,CALhC,IAMC2D,cAAc,QAAd,IACC2H,MAAM3F,cAAc3F,GAApB,IAA2BsL,MAAM0C,WAAW/N,MAAjB,CAR/B;;QAUMiO,gBAAgB5C,MAAM3F,cAAczF,IAApB,IAA4BoL,MAAM/H,WAAWrD,IAAjB,CAAlD;QACMiO,iBAAiB7C,MAAM3F,cAAcxF,KAApB,IAA6BmL,MAAM/H,WAAWpD,KAAjB,CAApD;QACMiO,eAAe9C,MAAM3F,cAAc3F,GAApB,IAA2BsL,MAAM/H,WAAWvD,GAAjB,CAAhD;QACMqO,kBACJ/C,MAAM3F,cAAc1F,MAApB,IAA8BqL,MAAM/H,WAAWtD,MAAjB,CADhC;;QAGMqO,sBACH3K,cAAc,MAAd,IAAwBuK,aAAzB,IACCvK,cAAc,OAAd,IAAyBwK,cAD1B,IAECxK,cAAc,KAAd,IAAuByK,YAFxB,IAGCzK,cAAc,QAAd,IAA0B0K,eAJ7B;;;QAOMlC,aAAa,CAAC,KAAD,EAAQ,QAAR,EAAkBrQ,OAAlB,CAA0B6H,SAA1B,MAAyC,CAAC,CAA7D;QACM4K,mBACJ,CAAC,CAAChH,QAAQiH,cAAV,KACErC,cAAczH,cAAc,OAA5B,IAAuCwJ,aAAxC,IACE/B,cAAczH,cAAc,KAA5B,IAAqCyJ,cADvC,IAEE,CAAChC,UAAD,IAAezH,cAAc,OAA7B,IAAwC0J,YAF1C,IAGE,CAACjC,UAAD,IAAezH,cAAc,KAA7B,IAAsC2J,eAJzC,CADF;;QAOIJ,eAAeK,mBAAf,IAAsCC,gBAA1C,EAA4D;;WAErDf,OAAL,GAAe,IAAf;;UAEIS,eAAeK,mBAAnB,EAAwC;oBAC1BZ,UAAUN,QAAQ,CAAlB,CAAZ;;;UAGEmB,gBAAJ,EAAsB;oBACRxB,qBAAqBrI,SAArB,CAAZ;;;WAGGf,SAAL,GAAiBA,aAAae,YAAY,MAAMA,SAAlB,GAA8B,EAA3C,CAAjB;;;;WAIKvD,OAAL,CAAagC,MAAb,gBACK2D,KAAK3F,OAAL,CAAagC,MADlB,EAEKqC,iBACDsB,KAAK+D,QAAL,CAAc1H,MADb,EAED2D,KAAK3F,OAAL,CAAaiC,SAFZ,EAGD0D,KAAKnD,SAHJ,CAFL;;aASOiD,aAAaE,KAAK+D,QAAL,CAAchE,SAA3B,EAAsCC,IAAtC,EAA4C,MAA5C,CAAP;;GArEJ;SAwEOA,IAAP;;;ACnIF;;;;;;;AAOA,AAAe,SAAS2H,YAAT,CAAsB3H,IAAtB,EAA4B;sBACXA,KAAK3F,OADM;MACjCgC,MADiC,iBACjCA,MADiC;MACzBC,SADyB,iBACzBA,SADyB;;MAEnCO,YAAYmD,KAAKnD,SAAL,CAAelD,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAlB;MACM6K,QAAQvK,KAAKuK,KAAnB;MACMa,aAAa,CAAC,KAAD,EAAQ,QAAR,EAAkBrQ,OAAlB,CAA0B6H,SAA1B,MAAyC,CAAC,CAA7D;MACMrE,OAAO6M,aAAa,OAAb,GAAuB,QAApC;MACMK,SAASL,aAAa,MAAb,GAAsB,KAArC;MACMpG,cAAcoG,aAAa,OAAb,GAAuB,QAA3C;;MAEIhJ,OAAO7D,IAAP,IAAegM,MAAMlI,UAAUoJ,MAAV,CAAN,CAAnB,EAA6C;SACtCrL,OAAL,CAAagC,MAAb,CAAoBqJ,MAApB,IACElB,MAAMlI,UAAUoJ,MAAV,CAAN,IAA2BrJ,OAAO4C,WAAP,CAD7B;;MAGE5C,OAAOqJ,MAAP,IAAiBlB,MAAMlI,UAAU9D,IAAV,CAAN,CAArB,EAA6C;SACtC6B,OAAL,CAAagC,MAAb,CAAoBqJ,MAApB,IAA8BlB,MAAMlI,UAAU9D,IAAV,CAAN,CAA9B;;;SAGKwH,IAAP;;;ACpBF;;;;;;;;;;;;AAYA,AAAO,SAAS4H,OAAT,CAAiBC,GAAjB,EAAsB5I,WAAtB,EAAmCJ,aAAnC,EAAkDF,gBAAlD,EAAoE;;MAEnEhF,QAAQkO,IAAIjI,KAAJ,CAAU,2BAAV,CAAd;MACMF,QAAQ,CAAC/F,MAAM,CAAN,CAAf;MACM+J,OAAO/J,MAAM,CAAN,CAAb;;;MAGI,CAAC+F,KAAL,EAAY;WACHmI,GAAP;;;MAGEnE,KAAK1O,OAAL,CAAa,GAAb,MAAsB,CAA1B,EAA6B;QACvBgB,gBAAJ;YACQ0N,IAAR;WACO,IAAL;kBACY7E,aAAV;;WAEG,GAAL;WACK,IAAL;;kBAEYF,gBAAV;;;QAGE9F,OAAOuB,cAAcpE,OAAd,CAAb;WACO6C,KAAKoG,WAAL,IAAoB,GAApB,GAA0BS,KAAjC;GAbF,MAcO,IAAIgE,SAAS,IAAT,IAAiBA,SAAS,IAA9B,EAAoC;;QAErCoE,aAAJ;QACIpE,SAAS,IAAb,EAAmB;aACVzJ,KAAKC,GAAL,CACLzF,SAASyC,eAAT,CAAyB2D,YADpB,EAELrG,OAAOyH,WAAP,IAAsB,CAFjB,CAAP;KADF,MAKO;aACEhC,KAAKC,GAAL,CACLzF,SAASyC,eAAT,CAAyB0D,WADpB,EAELpG,OAAOwH,UAAP,IAAqB,CAFhB,CAAP;;WAKK8L,OAAO,GAAP,GAAapI,KAApB;GAdK,MAeA;;;WAGEA,KAAP;;;;;;;;;;;;;;;AAeJ,AAAO,SAASqI,WAAT,CACL7L,MADK,EAEL2C,aAFK,EAGLF,gBAHK,EAILqJ,aAJK,EAKL;MACM3N,UAAU,CAAC,CAAD,EAAI,CAAJ,CAAhB;;;;;MAKM4N,YAAY,CAAC,OAAD,EAAU,MAAV,EAAkBjT,OAAlB,CAA0BgT,aAA1B,MAA6C,CAAC,CAAhE;;;;MAIME,YAAYhM,OAAOvC,KAAP,CAAa,SAAb,EAAwBwD,GAAxB,CAA4B;WAAQgL,KAAKC,IAAL,EAAR;GAA5B,CAAlB;;;;MAIMC,UAAUH,UAAUlT,OAAV,CACdmK,KAAK+I,SAAL,EAAgB;WAAQC,KAAKG,MAAL,CAAY,MAAZ,MAAwB,CAAC,CAAjC;GAAhB,CADc,CAAhB;;MAIIJ,UAAUG,OAAV,KAAsBH,UAAUG,OAAV,EAAmBrT,OAAnB,CAA2B,GAA3B,MAAoC,CAAC,CAA/D,EAAkE;YACxDqL,IAAR,CACE,8EADF;;;;;MAOIkI,aAAa,aAAnB;MACIC,MAAMH,YAAY,CAAC,CAAb,GACN,CACEH,UACG/H,KADH,CACS,CADT,EACYkI,OADZ,EAEG9B,MAFH,CAEU,CAAC2B,UAAUG,OAAV,EAAmB1O,KAAnB,CAAyB4O,UAAzB,EAAqC,CAArC,CAAD,CAFV,CADF,EAIE,CAACL,UAAUG,OAAV,EAAmB1O,KAAnB,CAAyB4O,UAAzB,EAAqC,CAArC,CAAD,EAA0ChC,MAA1C,CACE2B,UAAU/H,KAAV,CAAgBkI,UAAU,CAA1B,CADF,CAJF,CADM,GASN,CAACH,SAAD,CATJ;;;QAYMM,IAAIrL,GAAJ,CAAQ,UAACsL,EAAD,EAAKnC,KAAL,EAAe;;QAErBrH,cAAc,CAACqH,UAAU,CAAV,GAAc,CAAC2B,SAAf,GAA2BA,SAA5B,IAChB,QADgB,GAEhB,OAFJ;QAGIS,oBAAoB,KAAxB;WAEED;;;KAGGE,MAHH,CAGU,UAACrL,CAAD,EAAIC,CAAJ,EAAU;UACZD,EAAEA,EAAEzI,MAAF,GAAW,CAAb,MAAoB,EAApB,IAA0B,CAAC,GAAD,EAAM,GAAN,EAAWG,OAAX,CAAmBuI,CAAnB,MAA0B,CAAC,CAAzD,EAA4D;UACxDD,EAAEzI,MAAF,GAAW,CAAb,IAAkB0I,CAAlB;4BACoB,IAApB;eACOD,CAAP;OAHF,MAIO,IAAIoL,iBAAJ,EAAuB;UAC1BpL,EAAEzI,MAAF,GAAW,CAAb,KAAmB0I,CAAnB;4BACoB,KAApB;eACOD,CAAP;OAHK,MAIA;eACEA,EAAEiJ,MAAF,CAAShJ,CAAT,CAAP;;KAbN,EAeK,EAfL;;KAiBGJ,GAjBH,CAiBO;aAAOyK,QAAQC,GAAR,EAAa5I,WAAb,EAA0BJ,aAA1B,EAAyCF,gBAAzC,CAAP;KAjBP,CADF;GANI,CAAN;;;MA6BIyB,OAAJ,CAAY,UAACqI,EAAD,EAAKnC,KAAL,EAAe;OACtBlG,OAAH,CAAW,UAAC+H,IAAD,EAAOS,MAAP,EAAkB;UACvBvF,UAAU8E,IAAV,CAAJ,EAAqB;gBACX7B,KAAR,KAAkB6B,QAAQM,GAAGG,SAAS,CAAZ,MAAmB,GAAnB,GAAyB,CAAC,CAA1B,GAA8B,CAAtC,CAAlB;;KAFJ;GADF;SAOOvO,OAAP;;;;;;;;;;;;AAYF,AAAe,SAAS6B,MAAT,CAAgB8D,IAAhB,QAAkC;MAAV9D,MAAU,QAAVA,MAAU;MACvCW,SADuC,GACOmD,IADP,CACvCnD,SADuC;sBACOmD,IADP,CAC5B3F,OAD4B;MACjBgC,MADiB,iBACjBA,MADiB;MACTC,SADS,iBACTA,SADS;;MAEzC0L,gBAAgBnL,UAAUlD,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAtB;;MAEIU,gBAAJ;MACIgJ,UAAU,CAACnH,MAAX,CAAJ,EAAwB;cACZ,CAAC,CAACA,MAAF,EAAU,CAAV,CAAV;GADF,MAEO;cACK6L,YAAY7L,MAAZ,EAAoBG,MAApB,EAA4BC,SAA5B,EAAuC0L,aAAvC,CAAV;;;MAGEA,kBAAkB,MAAtB,EAA8B;WACrB9O,GAAP,IAAcmB,QAAQ,CAAR,CAAd;WACOjB,IAAP,IAAeiB,QAAQ,CAAR,CAAf;GAFF,MAGO,IAAI2N,kBAAkB,OAAtB,EAA+B;WAC7B9O,GAAP,IAAcmB,QAAQ,CAAR,CAAd;WACOjB,IAAP,IAAeiB,QAAQ,CAAR,CAAf;GAFK,MAGA,IAAI2N,kBAAkB,KAAtB,EAA6B;WAC3B5O,IAAP,IAAeiB,QAAQ,CAAR,CAAf;WACOnB,GAAP,IAAcmB,QAAQ,CAAR,CAAd;GAFK,MAGA,IAAI2N,kBAAkB,QAAtB,EAAgC;WAC9B5O,IAAP,IAAeiB,QAAQ,CAAR,CAAf;WACOnB,GAAP,IAAcmB,QAAQ,CAAR,CAAd;;;OAGGgC,MAAL,GAAcA,MAAd;SACO2D,IAAP;;;AC7LF;;;;;;;AAOA,AAAe,SAAS6I,eAAT,CAAyB7I,IAAzB,EAA+BS,OAA/B,EAAwC;MACjDjE,oBACFiE,QAAQjE,iBAAR,IAA6BxF,gBAAgBgJ,KAAK+D,QAAL,CAAc1H,MAA9B,CAD/B;;;;;MAMI2D,KAAK+D,QAAL,CAAczH,SAAd,KAA4BE,iBAAhC,EAAmD;wBAC7BxF,gBAAgBwF,iBAAhB,CAApB;;;MAGIC,aAAaL,cACjB4D,KAAK+D,QAAL,CAAc1H,MADG,EAEjB2D,KAAK+D,QAAL,CAAczH,SAFG,EAGjBmE,QAAQlE,OAHS,EAIjBC,iBAJiB,CAAnB;UAMQC,UAAR,GAAqBA,UAArB;;MAEM/E,QAAQ+I,QAAQqI,QAAtB;MACIzM,SAAS2D,KAAK3F,OAAL,CAAagC,MAA1B;;MAEMgD,QAAQ;WAAA,mBACJxC,SADI,EACO;UACb6C,QAAQrD,OAAOQ,SAAP,CAAZ;UAEER,OAAOQ,SAAP,IAAoBJ,WAAWI,SAAX,CAApB,IACA,CAAC4D,QAAQsI,mBAFX,EAGE;gBACQ9O,KAAKC,GAAL,CAASmC,OAAOQ,SAAP,CAAT,EAA4BJ,WAAWI,SAAX,CAA5B,CAAR;;gCAEQA,SAAV,EAAsB6C,KAAtB;KATU;aAAA,qBAWF7C,SAXE,EAWS;UACbkC,WAAWlC,cAAc,OAAd,GAAwB,MAAxB,GAAiC,KAAlD;UACI6C,QAAQrD,OAAO0C,QAAP,CAAZ;UAEE1C,OAAOQ,SAAP,IAAoBJ,WAAWI,SAAX,CAApB,IACA,CAAC4D,QAAQsI,mBAFX,EAGE;gBACQ9O,KAAK8L,GAAL,CACN1J,OAAO0C,QAAP,CADM,EAENtC,WAAWI,SAAX,KACGA,cAAc,OAAd,GAAwBR,OAAO/B,KAA/B,GAAuC+B,OAAO9B,MADjD,CAFM,CAAR;;gCAMQwE,QAAV,EAAqBW,KAArB;;GAxBJ;;QA4BMU,OAAN,CAAc,qBAAa;QACnB5H,OAAO,CAAC,MAAD,EAAS,KAAT,EAAgBxD,OAAhB,CAAwB6H,SAAxB,MAAuC,CAAC,CAAxC,GACT,SADS,GAET,WAFJ;0BAGcR,MAAd,EAAyBgD,MAAM7G,IAAN,EAAYqE,SAAZ,CAAzB;GAJF;;OAOKxC,OAAL,CAAagC,MAAb,GAAsBA,MAAtB;;SAEO2D,IAAP;;;ACrEF;;;;;;;AAOA,AAAe,SAASgJ,KAAT,CAAehJ,IAAf,EAAqB;MAC5BnD,YAAYmD,KAAKnD,SAAvB;MACMmL,gBAAgBnL,UAAUlD,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAtB;MACMsP,iBAAiBpM,UAAUlD,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAvB;;;MAGIsP,cAAJ,EAAoB;wBACYjJ,KAAK3F,OADjB;QACViC,SADU,iBACVA,SADU;QACCD,MADD,iBACCA,MADD;;QAEZgJ,aAAa,CAAC,QAAD,EAAW,KAAX,EAAkBrQ,OAAlB,CAA0BgT,aAA1B,MAA6C,CAAC,CAAjE;QACMxP,OAAO6M,aAAa,MAAb,GAAsB,KAAnC;QACMpG,cAAcoG,aAAa,OAAb,GAAuB,QAA3C;;QAEM6D,eAAe;gCACT1Q,IAAV,EAAiB8D,UAAU9D,IAAV,CAAjB,CADmB;8BAGhBA,IADH,EACU8D,UAAU9D,IAAV,IAAkB8D,UAAU2C,WAAV,CAAlB,GAA2C5C,OAAO4C,WAAP,CADrD;KAFF;;SAOK5E,OAAL,CAAagC,MAAb,gBAA2BA,MAA3B,EAAsC6M,aAAaD,cAAb,CAAtC;;;SAGKjJ,IAAP;;;AC1BF;;;;;;;AAOA,AAAe,SAASmJ,IAAT,CAAcnJ,IAAd,EAAoB;MAC7B,CAAC8E,mBAAmB9E,KAAK+D,QAAL,CAAchE,SAAjC,EAA4C,MAA5C,EAAoD,iBAApD,CAAL,EAA6E;WACpEC,IAAP;;;MAGIlD,UAAUkD,KAAK3F,OAAL,CAAaiC,SAA7B;MACM8M,QAAQjK,KACZa,KAAK+D,QAAL,CAAchE,SADF,EAEZ;WAAY9G,SAASkI,IAAT,KAAkB,iBAA9B;GAFY,EAGZ1E,UAHF;;MAMEK,QAAQ3D,MAAR,GAAiBiQ,MAAMlQ,GAAvB,IACA4D,QAAQ1D,IAAR,GAAegQ,MAAM/P,KADrB,IAEAyD,QAAQ5D,GAAR,GAAckQ,MAAMjQ,MAFpB,IAGA2D,QAAQzD,KAAR,GAAgB+P,MAAMhQ,IAJxB,EAKE;;QAEI4G,KAAKmJ,IAAL,KAAc,IAAlB,EAAwB;aACfnJ,IAAP;;;SAGGmJ,IAAL,GAAY,IAAZ;SACKvF,UAAL,CAAgB,qBAAhB,IAAyC,EAAzC;GAZF,MAaO;;QAED5D,KAAKmJ,IAAL,KAAc,KAAlB,EAAyB;aAChBnJ,IAAP;;;SAGGmJ,IAAL,GAAY,KAAZ;SACKvF,UAAL,CAAgB,qBAAhB,IAAyC,KAAzC;;;SAGK5D,IAAP;;;ACzCF;;;;;;;AAOA,AAAe,SAASqJ,KAAT,CAAerJ,IAAf,EAAqB;MAC5BnD,YAAYmD,KAAKnD,SAAvB;MACMmL,gBAAgBnL,UAAUlD,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAtB;sBAC8BqG,KAAK3F,OAHD;MAG1BgC,MAH0B,iBAG1BA,MAH0B;MAGlBC,SAHkB,iBAGlBA,SAHkB;;MAI5BwC,UAAU,CAAC,MAAD,EAAS,OAAT,EAAkB9J,OAAlB,CAA0BgT,aAA1B,MAA6C,CAAC,CAA9D;;MAEMsB,iBAAiB,CAAC,KAAD,EAAQ,MAAR,EAAgBtU,OAAhB,CAAwBgT,aAAxB,MAA2C,CAAC,CAAnE;;SAEOlJ,UAAU,MAAV,GAAmB,KAA1B,IACExC,UAAU0L,aAAV,KACCsB,iBAAiBjN,OAAOyC,UAAU,OAAV,GAAoB,QAA3B,CAAjB,GAAwD,CADzD,CADF;;OAIKjC,SAAL,GAAiByB,qBAAqBzB,SAArB,CAAjB;OACKxC,OAAL,CAAagC,MAAb,GAAsBjC,cAAciC,MAAd,CAAtB;;SAEO2D,IAAP;;;ACdF;;;;;;;;;;;;;;;;;;;;;AAqBA,gBAAe;;;;;;;;;SASN;;WAEE,GAFF;;aAII,IAJJ;;QAMDgJ;GAfO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAwDL;;WAEC,GAFD;;aAIG,IAJH;;QAMF9M,MANE;;;;YAUE;GAlEG;;;;;;;;;;;;;;;;;;;mBAsFI;;WAER,GAFQ;;aAIN,IAJM;;QAMX2M,eANW;;;;;;cAYL,CAAC,MAAD,EAAS,OAAT,EAAkB,KAAlB,EAAyB,QAAzB,CAZK;;;;;;;aAmBN,CAnBM;;;;;;uBAyBI;GA/GR;;;;;;;;;;;gBA2HC;;WAEL,GAFK;;aAIH,IAJG;;QAMRlB;GAjIO;;;;;;;;;;;;SA8IN;;WAEE,GAFF;;aAII,IAJJ;;QAMD9C,KANC;;aAQI;GAtJE;;;;;;;;;;;;;QAoKP;;WAEG,GAFH;;aAIK,IAJL;;QAMAnE,IANA;;;;;;;cAaM,MAbN;;;;;aAkBK,CAlBL;;;;;;;uBAyBe;GA7LR;;;;;;;;;SAuMN;;WAEE,GAFF;;aAII,KAJJ;;QAMD2I;GA7MO;;;;;;;;;;;;QA0NP;;WAEG,GAFH;;aAIK,IAJL;;QAMAF;GAhOO;;;;;;;;;;;;;;;;;gBAkPC;;WAEL,GAFK;;aAIH,IAJG;;QAMR/E,YANQ;;;;;;qBAYK,IAZL;;;;;;OAkBT,QAlBS;;;;;;OAwBT;GA1QQ;;;;;;;;;;;;;;;;;cA4RD;;WAEH,GAFG;;aAID,IAJC;;QAMNN,UANM;;YAQFI,gBARE;;;;;;;qBAeOrK;;CA3SrB;;;;;;;;;;;;;;;;;;;;;AC9BA;;;;;;;;;;;;;;;;AAgBA,eAAe;;;;;aAKF,QALE;;;;;;iBAWE,IAXF;;;;;;;mBAkBI,KAlBJ;;;;;;;;YA0BH,oBAAM,EA1BH;;;;;;;;;;YAoCH,oBAAM,EApCH;;;;;;;;CAAf;;;;;;;;;;;;AClBA;AACA,AAGA;AACA,IAOqB0P;;;;;;;;;kBASPjN,SAAZ,EAAuBD,MAAvB,EAA6C;;;QAAdoE,OAAc,uEAAJ,EAAI;;;SAyF7CwC,cAzF6C,GAyF5B;aAAMuG,sBAAsB,MAAKjJ,MAA3B,CAAN;KAzF4B;;;SAEtCA,MAAL,GAAckJ,SAAS,KAAKlJ,MAAL,CAAYmJ,IAAZ,CAAiB,IAAjB,CAAT,CAAd;;;SAGKjJ,OAAL,gBAAoB8I,OAAOI,QAA3B,EAAwClJ,OAAxC;;;SAGK3C,KAAL,GAAa;mBACE,KADF;iBAEA,KAFA;qBAGI;KAHjB;;;SAOKxB,SAAL,GAAiBA,aAAaA,UAAUsN,MAAvB,GAAgCtN,UAAU,CAAV,CAAhC,GAA+CA,SAAhE;SACKD,MAAL,GAAcA,UAAUA,OAAOuN,MAAjB,GAA0BvN,OAAO,CAAP,CAA1B,GAAsCA,MAApD;;;SAGKoE,OAAL,CAAaV,SAAb,GAAyB,EAAzB;WACO7C,IAAP,cACKqM,OAAOI,QAAP,CAAgB5J,SADrB,EAEKU,QAAQV,SAFb,GAGGK,OAHH,CAGW,gBAAQ;YACZK,OAAL,CAAaV,SAAb,CAAuBoB,IAAvB,iBAEMoI,OAAOI,QAAP,CAAgB5J,SAAhB,CAA0BoB,IAA1B,KAAmC,EAFzC,EAIMV,QAAQV,SAAR,GAAoBU,QAAQV,SAAR,CAAkBoB,IAAlB,CAApB,GAA8C,EAJpD;KAJF;;;SAaKpB,SAAL,GAAiB9C,OAAOC,IAAP,CAAY,KAAKuD,OAAL,CAAaV,SAAzB,EACd5C,GADc,CACV;;;SAEA,MAAKsD,OAAL,CAAaV,SAAb,CAAuBoB,IAAvB,CAFA;KADU;;KAMd9D,IANc,CAMT,UAACC,CAAD,EAAIC,CAAJ;aAAUD,EAAE5F,KAAF,GAAU6F,EAAE7F,KAAtB;KANS,CAAjB;;;;;;SAYKqI,SAAL,CAAeK,OAAf,CAAuB,2BAAmB;UACpC+D,gBAAgB7D,OAAhB,IAA2B5K,WAAWyO,gBAAgB0F,MAA3B,CAA/B,EAAmE;wBACjDA,MAAhB,CACE,MAAKvN,SADP,EAEE,MAAKD,MAFP,EAGE,MAAKoE,OAHP,EAIE0D,eAJF,EAKE,MAAKrG,KALP;;KAFJ;;;SAaKyC,MAAL;;QAEMwC,gBAAgB,KAAKtC,OAAL,CAAasC,aAAnC;QACIA,aAAJ,EAAmB;;WAEZC,oBAAL;;;SAGGlF,KAAL,CAAWiF,aAAX,GAA2BA,aAA3B;;;;;;;;;gCAKO;aACAxC,OAAOzK,IAAP,CAAY,IAAZ,CAAP;;;;iCAEQ;aACD8L,QAAQ9L,IAAR,CAAa,IAAb,CAAP;;;;8CAEqB;aACdkN,qBAAqBlN,IAArB,CAA0B,IAA1B,CAAP;;;;+CAEsB;aACfgM,sBAAsBhM,IAAtB,CAA2B,IAA3B,CAAP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1FiByT,OAoHZO,QAAQ,CAAC,OAAOtV,MAAP,KAAkB,WAAlB,GAAgCA,MAAhC,GAAyCuV,MAA1C,EAAkDC;AApH9CT,OAsHZpD,aAAaA;AAtHDoD,OAwHZI,WAAWA;;;;;;;;"}