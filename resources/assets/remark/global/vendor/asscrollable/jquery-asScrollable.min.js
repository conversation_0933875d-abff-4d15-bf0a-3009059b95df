/**
* jQuery asScrollable v0.4.10
* https://github.com/amazingSurge/jquery-asScrollable
*
* Copyright (c) amazingSurge
* Released under the LGPL-3.0 license
*/
!function(t,e){if("function"==typeof define&&define.amd)define(["jquery"],e);else if("undefined"!=typeof exports)e(require("jquery"));else{var i={exports:{}};e(t.jQuery),t.jqueryAsScrollableEs=i.exports}}(this,function(t){"use strict";function e(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(){return void 0!==window.performance&&window.performance.now?window.performance.now():Date.now()}function s(t){return"string"==typeof t&&-1!==t.indexOf("%")}function n(t){return t<0?t=0:t>1&&(t=1),100*parseFloat(t).toFixed(4)+"%"}function a(t){return parseFloat(t.slice(0,-1)/100,10)}var o=function(t){return t&&t.__esModule?t:{default:t}}(t),r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l=function(){function t(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}return function(e,i,s){return i&&t(e.prototype,i),s&&t(e,s),e}}(),h={namespace:"asScrollable",skin:null,contentSelector:null,containerSelector:null,enabledClass:"is-enabled",disabledClass:"is-disabled",draggingClass:"is-dragging",hoveringClass:"is-hovering",scrollingClass:"is-scrolling",direction:"vertical",showOnHover:!0,showOnBarHover:!1,duration:500,easing:"ease-in",responsive:!0,throttle:20,scrollbar:{}},c=function(){var t=void 0,e=void 0,i=void 0;return e=window.navigator.userAgent,!!(t=/(?=.+Mac OS X)(?=.+Firefox)/.test(e))&&((i=/Firefox\/\d{2}\./.exec(e))&&(i=i[0].replace(/\D+/g,"")),t&&+i>23)}(),f=0,u=function(){function t(i,s){e(this,t),this.$element=(0,o.default)(i),s=this.options=o.default.extend({},h,s||{},this.$element.data("options")||{}),this.classes={wrap:s.namespace,content:s.namespace+"-content",container:s.namespace+"-container",bar:s.namespace+"-bar",barHide:s.namespace+"-bar-hide",skin:s.skin},this.attributes={vertical:{axis:"Y",overflow:"overflow-y",scroll:"scrollTop",scrollLength:"scrollHeight",pageOffset:"pageYOffset",ffPadding:"padding-right",length:"height",clientLength:"clientHeight",offset:"offsetHeight",crossLength:"width",crossClientLength:"clientWidth",crossOffset:"offsetWidth"},horizontal:{axis:"X",overflow:"overflow-x",scroll:"scrollLeft",scrollLength:"scrollWidth",pageOffset:"pageXOffset",ffPadding:"padding-bottom",length:"width",clientLength:"clientWidth",offset:"offsetWidth",crossLength:"height",crossClientLength:"clientHeight",crossOffset:"offsetHeight"}},this._states={},this.horizontal=null,this.vertical=null,this.$bar=null,this._frameId=null,this._timeoutId=null,this.instanceId=++f,this.easing=o.default.asScrollbar.getEasing(this.options.easing)||o.default.asScrollbar.getEasing("ease"),this.init()}return l(t,[{key:"init",value:function(){var t=this.$element.css("position");switch(this.options.containerSelector?(this.$container=this.$element.find(this.options.containerSelector),this.$wrap=this.$element,"static"===t&&this.$wrap.css("position","relative")):(this.$container=this.$element.wrap("<div>"),this.$wrap=this.$container.parent(),this.$wrap.height(this.$element.height()),"static"!==t?this.$wrap.css("position",t):this.$wrap.css("position","relative")),this.options.contentSelector?this.$content=this.$container.find(this.options.contentSelector):(this.$content=this.$container.wrap("<div>"),this.$container=this.$content.parent()),this.options.direction){case"vertical":this.vertical=!0;break;case"horizontal":this.horizontal=!0;break;case"both":this.horizontal=!0,this.vertical=!0;break;case"auto":var e=this.$element.css("overflow-x"),i=this.$element.css("overflow-y");"scroll"!==e&&"auto"!==e||(this.horizontal=!0),"scroll"!==i&&"auto"!==i||(this.vertical=!0)}(this.vertical||this.horizontal)&&(this.$wrap.addClass(this.classes.wrap),this.$container.addClass(this.classes.container),this.$content.addClass(this.classes.content),this.options.skin&&this.$wrap.addClass(this.classes.skin),this.$wrap.addClass(this.options.enabledClass),this.vertical&&(this.$wrap.addClass(this.classes.wrap+"-vertical"),this.initLayout("vertical"),this.createBar("vertical")),this.horizontal&&(this.$wrap.addClass(this.classes.wrap+"-horizontal"),this.initLayout("horizontal"),this.createBar("horizontal")),this.bindEvents(),this.trigger("ready"))}},{key:"bindEvents",value:function(){var t=this;if(this.options.responsive&&((0,o.default)(window).on(this.eventNameWithId("orientationchange"),function(){t.update()}),(0,o.default)(window).on(this.eventNameWithId("resize"),this.throttle(function(){t.update()},this.options.throttle))),this.horizontal||this.vertical){var e=this;this.$wrap.on(this.eventName("mouseenter"),function(){e.$wrap.addClass(t.options.hoveringClass),e.enter("hovering"),e.trigger("hover")}),this.$wrap.on(this.eventName("mouseleave"),function(){e.$wrap.removeClass(t.options.hoveringClass),e.is("hovering")&&(e.leave("hovering"),e.trigger("hovered"))}),this.options.showOnHover&&(this.options.showOnBarHover?this.$bar.on("asScrollbar::hover",function(){e.horizontal&&e.showBar("horizontal"),e.vertical&&e.showBar("vertical")}).on("asScrollbar::hovered",function(){e.horizontal&&e.hideBar("horizontal"),e.vertical&&e.hideBar("vertical")}):(this.$element.on("asScrollable::hover",o.default.proxy(this.showBar,this)),this.$element.on("asScrollable::hovered",o.default.proxy(this.hideBar,this)))),this.$container.on(this.eventName("scroll"),function(){if(e.horizontal){var t=e.offsetLeft;e.offsetLeft=e.getOffset("horizontal"),t!==e.offsetLeft&&(e.trigger("scroll",e.getPercentOffset("horizontal"),"horizontal"),0===e.offsetLeft&&e.trigger("scrolltop","horizontal"),e.offsetLeft===e.getScrollLength("horizontal")&&e.trigger("scrollend","horizontal"))}if(e.vertical){var i=e.offsetTop;e.offsetTop=e.getOffset("vertical"),i!==e.offsetTop&&(e.trigger("scroll",e.getPercentOffset("vertical"),"vertical"),0===e.offsetTop&&e.trigger("scrolltop","vertical"),e.offsetTop===e.getScrollLength("vertical")&&e.trigger("scrollend","vertical"))}}),this.$element.on("asScrollable::scroll",function(t,i,s,a){e.is("scrolling")||(e.enter("scrolling"),e.$wrap.addClass(e.options.scrollingClass)),i.getBarApi(a).moveTo(n(s),!1,!0),clearTimeout(e._timeoutId),e._timeoutId=setTimeout(function(){e.$wrap.removeClass(e.options.scrollingClass),e.leave("scrolling")},200)}),this.$bar.on("asScrollbar::change",function(t,i,s){"string"==typeof t.target.direction&&e.scrollTo(t.target.direction,n(s),!1,!0)}),this.$bar.on("asScrollbar::drag",function(){e.$wrap.addClass(e.options.draggingClass)}).on("asScrollbar::dragged",function(){e.$wrap.removeClass(e.options.draggingClass)})}}},{key:"unbindEvents",value:function(){this.$wrap.off(this.eventName()),this.$element.off("asScrollable::scroll").off("asScrollable::hover").off("asScrollable::hovered"),this.$container.off(this.eventName()),(0,o.default)(window).off(this.eventNameWithId())}},{key:"initLayout",value:function(t){"vertical"===t&&this.$container.css("height",this.$wrap.height());var e=this.attributes[t],i=this.$container[0].parentNode[e.crossClientLength],s=this.getBrowserScrollbarWidth(t);this.$content.css(e.crossLength,i+"px"),this.$container.css(e.crossLength,s+i+"px"),0===s&&c&&this.$container.css(e.ffPadding,16)}},{key:"createBar",value:function(t){var e=o.default.extend(this.options.scrollbar,{namespace:this.classes.bar,direction:t,useCssTransitions:!1,keyboard:!1}),i=(0,o.default)("<div>");i.asScrollbar(e),this.options.showOnHover&&i.addClass(this.classes.barHide),i.appendTo(this.$wrap),this["$"+t]=i,null===this.$bar?this.$bar=i:this.$bar=this.$bar.add(i),this.updateBarHandle(t)}},{key:"trigger",value:function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];var n=[this].concat(i);this.$element.trigger("asScrollable::"+t,n);var a="on"+(t=t.replace(/\b\w+\b/g,function(t){return t.substring(0,1).toUpperCase()+t.substring(1)}));"function"==typeof this.options[a]&&this.options[a].apply(this,i)}},{key:"is",value:function(t){return this._states[t]&&this._states[t]>0}},{key:"enter",value:function(t){void 0===this._states[t]&&(this._states[t]=0),this._states[t]=1}},{key:"leave",value:function(t){this._states[t]=-1}},{key:"eventName",value:function(t){if("string"!=typeof t||""===t)return"."+this.options.namespace;for(var e=(t=t.split(" ")).length,i=0;i<e;i++)t[i]=t[i]+"."+this.options.namespace;return t.join(" ")}},{key:"eventNameWithId",value:function(t){if("string"!=typeof t||""===t)return"."+this.options.namespace+"-"+this.instanceId;for(var e=(t=t.split(" ")).length,i=0;i<e;i++)t[i]=t[i]+"."+this.options.namespace+"-"+this.instanceId;return t.join(" ")}},{key:"throttle",value:function(t,e){var i=this,s=Date.now||function(){return(new Date).getTime()},n=void 0,a=void 0,o=void 0,r=void 0,l=0,h=function(){l=s(),n=null,r=t.apply(a,o),n||(a=o=null)};return function(){for(var c=arguments.length,f=Array(c),u=0;u<c;u++)f[u]=arguments[u];var d=s(),v=e-(d-l);return a=i,o=f,v<=0||v>e?(n&&(clearTimeout(n),n=null),l=d,r=t.apply(a,o),n||(a=o=null)):n||(n=setTimeout(h,v)),r}}},{key:"getBrowserScrollbarWidth",value:function(t){var e=this.attributes[t],i=void 0,s=void 0;return e.scrollbarWidth?e.scrollbarWidth:(i=document.createElement("div"),s=i.style,s.position="absolute",s.width="100px",s.height="100px",s.overflow="scroll",s.top="-9999px",document.body.appendChild(i),e.scrollbarWidth=i[e.offset]-i[e.clientLength],document.body.removeChild(i),e.scrollbarWidth)}},{key:"getOffset",value:function(t){var e=this.attributes[t],i=this.$container[0];return i[e.pageOffset]||i[e.scroll]}},{key:"getPercentOffset",value:function(t){return this.getOffset(t)/this.getScrollLength(t)}},{key:"getContainerLength",value:function(t){return this.$container[0][this.attributes[t].clientLength]}},{key:"getScrollLength",value:function(t){return this.$content[0][this.attributes[t].scrollLength]-this.getContainerLength(t)}},{key:"scrollTo",value:function(t,e,i,n){var o=void 0===e?"undefined":r(e);"string"===o&&(s(e)&&(e=a(e)*this.getScrollLength(t)),e=parseFloat(e),o="number"),"number"===o&&this.move(t,e,i,n)}},{key:"scrollBy",value:function(t,e,i,n){var o=void 0===e?"undefined":r(e);"string"===o&&(s(e)&&(e=a(e)*this.getScrollLength(t)),e=parseFloat(e),o="number"),"number"===o&&this.move(t,this.getOffset(t)+e,i,n)}},{key:"move",value:function(t,e,s,n){if(!0===this[t]&&"number"==typeof e){this.enter("moving"),e<0?e=0:e>this.getScrollLength(t)&&(e=this.getScrollLength(t));var a=this.attributes[t],o=this,r=function(){o.leave("moving")};if(n)this.$container[0][a.scroll]=e,!1!==s&&this.trigger("change",e/this.getScrollLength(t),t),r();else{this.enter("animating");var l=i(),h=this.getOffset(t),c=e;this._frameId=window.requestAnimationFrame(function i(n){var f=(n-l)/o.options.duration;f>1&&(f=1),f=o.easing.fn(f);var u=parseFloat(h+f*(c-h),10);o.$container[0][a.scroll]=u,!1!==s&&o.trigger("change",e/o.getScrollLength(t),t),1===f?(window.cancelAnimationFrame(o._frameId),o._frameId=null,o.leave("animating"),r()):o._frameId=window.requestAnimationFrame(i)})}}}},{key:"scrollXto",value:function(t,e,i){return this.scrollTo("horizontal",t,e,i)}},{key:"scrollYto",value:function(t,e,i){return this.scrollTo("vertical",t,e,i)}},{key:"scrollXby",value:function(t,e,i){return this.scrollBy("horizontal",t,e,i)}},{key:"scrollYby",value:function(t,e,i){return this.scrollBy("vertical",t,e,i)}},{key:"getBar",value:function(t){return t&&this["$"+t]?this["$"+t]:this.$bar}},{key:"getBarApi",value:function(t){return this.getBar(t).data("asScrollbar")}},{key:"getBarX",value:function(){return this.getBar("horizontal")}},{key:"getBarY",value:function(){return this.getBar("vertical")}},{key:"showBar",value:function(t){this.getBar(t).removeClass(this.classes.barHide)}},{key:"hideBar",value:function(t){this.getBar(t).addClass(this.classes.barHide)}},{key:"updateBarHandle",value:function(t){var e=this.getBarApi(t);if(e){var i=this.getContainerLength(t),s=this.getScrollLength(t);s>0?(e.is("disabled")&&e.enable(),e.setHandleLength(e.getBarLength()*i/(s+i),!0)):e.disable()}}},{key:"disable",value:function(){this.is("disabled")||(this.enter("disabled"),this.$wrap.addClass(this.options.disabledClass).removeClass(this.options.enabledClass),this.unbindEvents(),this.unStyle()),this.trigger("disable")}},{key:"enable",value:function(){this.is("disabled")&&(this.leave("disabled"),this.$wrap.addClass(this.options.enabledClass).removeClass(this.options.disabledClass),this.bindEvents(),this.update()),this.trigger("enable")}},{key:"update",value:function(){this.is("disabled")||this.$element.is(":visible")&&(this.vertical&&(this.initLayout("vertical"),this.updateBarHandle("vertical")),this.horizontal&&(this.initLayout("horizontal"),this.updateBarHandle("horizontal")))}},{key:"unStyle",value:function(){this.horizontal&&(this.$container.css({height:"","padding-bottom":""}),this.$content.css({height:""})),this.vertical&&(this.$container.css({width:"",height:"","padding-right":""}),this.$content.css({width:""})),this.options.containerSelector||this.$wrap.css({height:""})}},{key:"destroy",value:function(){this.$wrap.removeClass(this.classes.wrap+"-vertical").removeClass(this.classes.wrap+"-horizontal").removeClass(this.classes.wrap).removeClass(this.options.enabledClass).removeClass(this.classes.disabledClass),this.unStyle(),this.$bar&&this.$bar.remove(),this.unbindEvents(),this.options.containerSelector?this.$container.removeClass(this.classes.container):this.$container.unwrap(),this.options.contentSelector||this.$content.unwrap(),this.$content.removeClass(this.classes.content),this.$element.data("asScrollable",null),this.trigger("destroy")}}],[{key:"setDefaults",value:function(t){o.default.extend(h,o.default.isPlainObject(t)&&t)}}]),t}(),d={version:"0.4.10"},v=o.default.fn.asScrollable,g=function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];if("string"==typeof t){var n=t;if(/^_/.test(n))return!1;if(!/^(get)/.test(n))return this.each(function(){var t=o.default.data(this,"asScrollable");t&&"function"==typeof t[n]&&t[n].apply(t,i)});var a=this.first().data("asScrollable");if(a&&"function"==typeof a[n])return a[n].apply(a,i)}return this.each(function(){(0,o.default)(this).data("asScrollable")||(0,o.default)(this).data("asScrollable",new u(this,t))})};o.default.fn.asScrollable=g,o.default.asScrollable=o.default.extend({setDefaults:u.setDefaults,noConflict:function(){return o.default.fn.asScrollable=v,g}},d)});
//# sourceMappingURL=jquery-asScrollable.min.js.map
