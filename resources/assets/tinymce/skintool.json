{"backgroundColor": "#ffffff", "skinName": "wavo", "contentCss": "body {\n  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n\tfont-size: 14px;\n\tline-height: 1.4;\n  margin: 1rem;\n}\n\ntable {\n  border-collapse: collapse;\n}\n\ntable th,\ntable td {\n  border: 1px solid #ccc;\n  padding: .4rem;\n}\n\nfigure {\n  display: table;\n  margin: 1rem auto;\n}\n\nfigure figcaption {\n  color: #999;\n  display: block;\n  margin-top: .25rem;\n  text-align: center;\n}\n\nhr {\n  border-color: #ccc;\n  border-style: solid;\n  border-width: 1px 0 0 0;\n}\n\ncode {\n  background-color: #e8e8e8;\n  border-radius: 3px;\n  padding: .1rem .2rem;\n}\n\n.mce-content-body:not([dir=rtl]) {\n  blockquote {\n    border-left: 2px solid #ccc;\n    margin-left: 1.5rem;\n    padding-left: 1rem;\n  }\n}\n\n// RTL\n.mce-content-body[dir=rtl] {\n  blockquote {\n    border-right: 2px solid #ccc;\n    margin-right: 1.5rem;\n    padding-right: 1rem;\n  }\n}\n", "initConfig": "tinymce.init({\n  plugins: [\n    \"a11ychecker advcode casechange formatpainter\",\n    \"linkchecker autolink lists checklist\",\n    \"media mediaembed pageembed permanentpen\",\n    \"powerpaste table advtable tinymcespellchecker\"\n  ],\n  toolbar: \"formatselect | fontselect | bold italic strikethrough forecolor backcolor formatpainter | alignleft aligncenter alignright alignjustify | numlist bullist outdent indent | link insertfile image | removeformat | code | addcomment | checklist | casechange\",\n  height: 360\n});", "lessVariablesMap": {"@background-color": "#fff", "@toolbar-background-color": "rgba(236, 240, 241, 1)", "@menubar-background-color": "rgba(236, 240, 241, 1)", "@border-color": "rgba(215, 224, 226, 1)", "@color-black": "rgba(51, 51, 51, 1)", "@color-white": "#fff", "@color-tint": "rgba(61, 61, 61, 1)", "@color-error": "rgba(255, 76, 82, 1)", "@color-success": "rgba(40, 167, 69, 1)", "@base-value": "14px", "@control-border-radius": "3px", "@panel-border-radius": "3px", "@tinymce-border-radius": "0px", "@font-stack": "-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,Oxygen-Sans,Ubuntu,Cantarell,\"Helvetica Neue\",sans-serif", "@font-weight-normal": "400", "@font-weight-bold": "bold", "@toolbar-button-icon-color": "contrast(@background-color, @color-white, @color-black)", "@toolbar-button-text-color": "contrast(@background-color, @color-white, @color-black)", "@toolbar-button-hover-background-color": "rgba(255, 255, 255, 1)", "@toolbar-button-hover-icon-color": "rgba(51, 51, 51, 1)", "@toolbar-button-hover-text-color": "rgba(51, 51, 51, 1)", "@toolbar-button-focus-background-color": "@toolbar-button-hover-background-color", "@toolbar-button-focus-icon-color": "@toolbar-button-hover-icon-color", "@toolbar-button-focus-text-color": "@toolbar-button-hover-icon-color", "@toolbar-button-active-background-color": "@toolbar-button-hover-background-color", "@toolbar-button-enabled-background-color": "@toolbar-button-hover-background-color", "@toolbar-button-active-icon-color": "@toolbar-button-hover-icon-color", "@toolbar-button-active-text-color": "@toolbar-button-hover-icon-color", "@toolbar-button-enabled-icon-color": "@toolbar-button-hover-icon-color"}, "lessVariables": "@background-color: #fff;\n@toolbar-background-color: rgba(236, 240, 241, 1);\n@menubar-background-color: rgba(236, 240, 241, 1);\n@border-color: rgba(215, 224, 226, 1);\n@color-black: rgba(51, 51, 51, 1);\n@color-white: #fff;\n@color-tint: rgba(61, 61, 61, 1);\n@color-error: rgba(255, 76, 82, 1);\n@color-success: rgba(40, 167, 69, 1);\n@base-value: 14px;\n@control-border-radius: 3px;\n@panel-border-radius: 3px;\n@tinymce-border-radius: 0px;\n@font-stack: -apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,Oxygen-Sans,Ubuntu,Cantarell,\"Helvetica Neue\",sans-serif;\n@font-weight-normal: 400;\n@font-weight-bold: bold;\n@toolbar-button-icon-color: contrast(@background-color, @color-white, @color-black);\n@toolbar-button-text-color: contrast(@background-color, @color-white, @color-black);\n@toolbar-button-hover-background-color: rgba(255, 255, 255, 1);\n@toolbar-button-hover-icon-color: rgba(51, 51, 51, 1);\n@toolbar-button-hover-text-color: rgba(51, 51, 51, 1);\n@toolbar-button-focus-background-color: @toolbar-button-hover-background-color;\n@toolbar-button-focus-icon-color: @toolbar-button-hover-icon-color;\n@toolbar-button-focus-text-color: @toolbar-button-hover-icon-color;\n@toolbar-button-active-background-color: @toolbar-button-hover-background-color;\n@toolbar-button-enabled-background-color: @toolbar-button-hover-background-color;\n@toolbar-button-active-icon-color: @toolbar-button-hover-icon-color;\n@toolbar-button-active-text-color: @toolbar-button-hover-icon-color;\n@toolbar-button-enabled-icon-color: @toolbar-button-hover-icon-color;\n"}