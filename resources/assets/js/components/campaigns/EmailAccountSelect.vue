<template>
    <div class="panel-container loader-wrapper" :class="{'loader-wrapper-loading': isFetching}">
        <header class="slidePanel-header">
            <div class="overlay-top overlay-panel" :class="headerBg">
                <div class="slidePanel-actions btn-group" aria-label="actions" role="group">
                    <button type="button"
                            class="btn btn-pure slidePanel-close icon wb-close"
                            :disabled="isFetching"
                            aria-hidden="false"
                            @click="closePanel"
                            v-tooltip.auto="{ content: 'Close', classes: 'v-tooltip' }"
                    >
                    </button>
                </div>
                <h4>
                    Select Email Accounts for this Campaign
                </h4>
            </div>
        </header>
        <div class="slidePanel-inner">
        	<section id="editor" class="slidePanel-inner-section">
                <div class="row justify-content-center">
                    <div class="col-md-12">
                        <div class="section-description mb-10">
                            Select at least one Email Account to send campaign emails from.
                            If multiple Email Accounts are selected, the prospects will be evenly split between them.
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="mb-40" v-for="(team, indexTeam) in arrTeams">
                            <h4 v-if="agencyPlan == 'agency-dashboard' || agencyPlan == 'agency-dashboard-wavo3'">
                                <span class="inline-block">
                                    Accounts available for:
                                </span>
                                <span class="inline-block">
                                    {{team.name}}
                                </span>
                            </h4>
                            <div v-if="team.email_accounts && team.email_accounts.length">
                                <div class="alert alert-warning" v-if="hasVolumeWarning">
                                    The total daily sending limit of selected working email accounts is not enough to cover this campaign's daily email volume
                                </div>
                                <div class="alert alert-info" v-if="isCurrentlySending">
                                    As the campaign has already started sending today's messages, any new email accounts added will start sending from tomorrow.
                                </div>

                                <div class="list-group list-group-gap">
                                    <div v-for="(account, indexAccnt) in team.email_accounts">
                                        <!-- skip if account has error and not selected -->
                                        <div v-if="account.error && !account.is_selected"></div>
                                        <div class="list-group-item border blue-grey-800 bg-grey-100 " v-else>
                                            <div class="emailAccountCheckbox checkbox-custom checkbox-primary">
                                                <input type="checkbox"
                                                    :id="'emailSelect'+account.hashid"
                                                    :value="account.hashid"
                                                    v-model="arrSelectedEmailAccounts"
                                                >
                                                <label :for="'emailSelect'+account.hashid" class="d-block clearfix">
                                                    <span v-if="account.error" class="text-danger"
                                                        v-tooltip.auto="{ content: 'Email account is stopped/has invalid credentials. Please view account settings.', classes: 'v-tooltip' }"
                                                    >
                                                        <i class="fa fa-exclamation"
                                                            v-if="account.error"
                                                        ></i>
                                                        {{account.email_address}}
                                                    </span>
                                                    <span v-else>
                                                        {{account.email_address}}
                                                    </span>
                                                </label>
                                            </div>
                                            <div class="pl-30 blue-grey-500">
                                                <label :for="'emailSelect'+account.hashid" class="emailAccountCheckDetail">
                                                    <span v-if="account.email_server_type == 'imap'">
                                                        <span class="d-inline-block"
                                                            v-tooltip.auto="{ content: 'IMAP: '+account.email_server_imap_address+' <br/> SMTP: '+account.email_server_smtp_address, classes: 'v-tooltip servertype-tooltip' }"
                                                        >
                                                            IMAP/SMTP
                                                            <i class="blue-grey-400 wb-info-circle"></i>
                                                        </span>
                                                    </span>
                                                    <span v-else>
                                                        {{ account.email_server_type_nice_name }}
                                                    </span>

                                                    <span class="mx-10">&bull;</span>

                                                    <span class="text-capitalize">
                                                        <span v-if="account.warmup_status == 'on' && account.emailWarmup">
                                                            {{account.emailWarmup.send_limit}}
                                                        </span>
                                                        <span v-else>
                                                            {{account.send_limit}}
                                                        </span>
                                                        Daily Limit
                                                    </span>

                                                    <span class="mx-10">&bull;</span>

                                                    <span class="text-capitalize">
                                                        {{account.active_campaigns}} Active
                                                        <span v-if="account.active_campaigns == 1">Campaign</span>
                                                        <span v-else>Campaigns</span>
                                                    </span>

                                                    <span class="mx-10">&bull;</span>

                                                    <span class="text-capitalize">{{account.name}}</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group d-none" v-for="(account, indexAccnt) in team.email_accounts">
                                    <div class="checkbox-custom checkbox-default">
                                        <input type="checkbox"
                                            :id="'emailSelect'+account.hashid"
                                            :value="account.hashid"
                                            v-model="arrSelectedEmailAccounts"
                                        >
                                        <label :for="'emailSelect'+account.hashid">
                                            {{account.email_address}}
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                No email accounts found for this client
                            </div>
                        </div>

                        <button class="btn btn-primary btn-block"
                            @click="submitSelectedEmailAccount"
                            :disabled="isSubmitting || !hasEmailAccounts"
                        >
                            <span v-if="isSubmitting">
                                <i class="fa fa-cog fa-spin"></i>
                                Selecting...
                            </span>
                            <span v-else>
                                <i class="icon wb-chevron-right"></i>
                                Select
                            </span>
                        </button>
                    </div>
                </div>
            </section>
        </div>
        <div class="loader-box vertical-align text-center">
            <div class="loader vertical-align-middle loader-circle"></div>
        </div>
    </div>
</template>

<script>

export default {
	name: 'EmailAccountSelect',
	components: {},
	props: ['currEmailAccounts', 'campaignHash', 'agencyPlan'],
	data: function() {
        return {
            isFetching: false,
            isSubmitting: false,
            templateUpdated: false,
            arrCurrentEmailAccounts: [],
            arrSelectedEmailAccounts: [],
            arrTeams: [],
            hasEmailAccounts: false,
            hasVolumeWarning: false,
            isCurrentlySending: false,
        }
    },
    watch: {

    },
    computed: {
        headerBg() {
            return 'bg-' + this.spark.agency.color + '-600';
        },
    },
    mounted() {
        this.arrCurrentEmailAccounts = this.currEmailAccounts;
        this.fetchClientEmailAccounts();
    },
    created() {

    },
    methods: {
        closePanel() {
            this.$emit('closePanel', {
                templateUpdated: this.templateUpdated
            });
        },

        /*
         * fetch list of available emails for this campaign
         */
        fetchClientEmailAccounts() {
            this.isFetching = true;

            axios.get('/campaigns/'+this.campaignHash+'/email-accounts' )
                .then((response)=>{
                    if(response && response.data && response.data.status == 'success'){
                        this.arrTeams = response.data.teams;
                        this.arrSelectedEmailAccounts = response.data.selected_accounts;
                        this.hasVolumeWarning = response.data.has_volume_warning;
                        this.isCurrentlySending = response.data.is_currently_sending;

                        this.arrTeams.map(team => {
                            if(team.email_accounts.length > 0) {
                                this.hasEmailAccounts = true;
                            }
                        });
                    }

                    this.isFetching = false;
                }).catch((error)=>{
                    this.isFetching = false;
                });
        },

        /*
         * Submit the selected email accounts to API
         */
        submitSelectedEmailAccount() {
            if(this.isSubmitting){
                return false;
            }

            this.isSubmitting = true;

            axios.post('/campaigns/'+this.campaignHash+'/email-accounts', {
                'email_accounts': this.arrSelectedEmailAccounts
            }).then((response)=>{
                if(response && response.data && response.data.status == 'success'){
                    this.hasVolumeWarning = response.data.has_volume_warning;

                    // notify campaign and update the email account list
                    this.$root.$emit('emailAccountsUpdated', {
                        accounts: response.data.new_email_accounts,
                        histories: response.data.email_histories
                    });

                    swal('Success', {
                        icon: 'success',
                    }).then(() => {
                        // do not close panel if has warning
                        // so user can see the prompted volume message
                        if(!this.hasVolumeWarning){
                            this.closePanel();
                        }
                    });
                } else {
                    // display error and reload page
                    swal({
                        title: 'Warning',
                        text: 'An error happened while saving your changes',
                        icon: "warning",
                    }).then(() => {
                        this.closePanel();
                        location.reload();
                    });
                }

                this.isSubmitting = false;
            }).catch((error)=>{
                swal({
                    title: 'Unable to save changes',
                    // text: 'error',
                    icon: "error",
                }).then(() => {
                    this.closePanel();
                    location.reload();
                });

                this.isSubmitting = false;
            });
        },
    }
}

</script>

<style>
	.emailAccountCheckbox.checkbox-custom {
        padding-bottom: 0 !important;
        padding-top: 0 !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
    }
    .emailAccountCheckbox.checkbox-custom input {
        top: 10px !important;
    }
    .emailAccountCheckbox.checkbox-custom label {
        font-weight: 400;
    }
    .emailAccountCheckbox.checkbox-custom label::before {
        top: 10px !important;
    }
    .emailAccountCheckbox.checkbox-custom label::after {
        top: 10px !important;
    }
    .emailAccountCheckDetail {
        font-size: 12px;
        line-height: 12px;
        margin: 0px;
        display: block;
        cursor: pointer;
        padding-top: 5px !important;
    }
    .servertype-tooltip .tooltip-inner {
        max-width: 450px;
    }
</style>
