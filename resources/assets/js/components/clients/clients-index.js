var base = require('kiosk/users');
var tabState = require('./../../spark-components/mixins/tab-state.js');

Vue.component('clients-index', {
    mixins: [base, tabState],

    props: ['user', 'clients'],

    /**
     * The component's data.
     */
    data() {
        return {
            clientData: [],
        };
    },

    /**
     * Bootstrap the component.
     */
    mounted() {
        // Initialize client data based on the initial data passed by the Controller.
        this.searchResults = this.clientData = this.clients;

        // Check if we need to show a specific client.
        if (window.location.hash) {
            let hash = window.location.hash.substring(2);
            let parameters = hash.split('/');

            hash = parameters.shift();

            let showingClient = this.clientData.find(function (client) {
                return client.id == parameters[0];
            });

            if (hash == 'show' && showingClient) {
                this.loadProfile(showingClient);
            }
        }
    },

    /**
     * The component has been created by Vue.
     */
    created() {
        let self = this;

        this.getPlans();

        this.$on('showSearch', function(){
            self.navigateToSearch();
        });

        Bus.$on('sparkHashChanged', function (hash, parameters) {
            if (hash != 'show') {
                return true;
            }

            if (parameters && parameters.length > 0) {
                self.loadProfile({ id: parameters[0] });
            } else {
                self.showSearch();
            }

            return true;
        });

        Bus.$on('updateUser', function (user) {
            self.clientData = self.clientData.map(
                client => client.id == user.id ? user : client
            );
            self.searchResults = self.clientData;
            self.searchForm.query = '';

            if (self.showingUserProfile) {
                self.loadProfile(user);
            }
        });
    },

    watch: {
        // Watch for changes in the search fields.
        'searchForm.query' (val) {
            this.search();
        },
    },

    methods: {
        /**
         * Perform a search for the given query.
         */
        search() {
            let self = this;

            this.searchResults = this.clientData.filter(function (client) {
                return client.name.toLowerCase().indexOf(self.searchForm.query.toLowerCase()) != -1 ||
                    client.email.toLowerCase().indexOf(self.searchForm.query.toLowerCase()) != -1;
            });
        },

        /**
         * Show the user profile for the given user.
         */
        showUserProfile(user) {
            history.pushState(null, null, '#/show/' + user.id);

            this.loadProfile(user);
        },

        /**
         * Load the user profile for the given user.
         */
        loadProfile(user) {
            this.$emit('showUserProfile', user);

            this.showingUserProfile = true;
        },

        /**
         * Show the search results and update the browser history.
         */
        navigateToSearch() {
            history.pushState(null, null, '#/');

            this.showSearch();
        },
    }

});