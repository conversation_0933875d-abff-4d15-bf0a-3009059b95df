Vue.component('client-edit', {
    props: ['team', 'old_webhook_replied', 'old_webhook_positive'],

    data() {
        return {
            isLoading: false,
            isTesting: false,
            errorWebhooks: [],
            testedWebhooks: [],
            webhook_replied: '',
            webhook_positive: ''
        }
    },

    mounted() {
        if(this.old_webhook_replied) {
            this.webhook_replied = this.old_webhook_replied
        } else {
            this.webhook_replied = this.team.webhooks['replied']
        }

        if(this.old_webhook_positive) {
            this.webhook_positive = this.old_webhook_positive
        } else {
            this.webhook_positive = this.team.webhooks['positive']
        }
    },

    computed: {
        
    },

    methods: {
        webhookTest() {
            this.isTesting = true;
            this.errors = [];
            this.testedWebhooks = [];

            axios.post('/clients/webhook-test', {
                'webhook_positive': this.webhook_positive,
                'webhook_replied': this.webhook_replied
            }).then(response => {
                    if(response.data && response.data.status == 'success'){
                        this.isTesting = false;
                        this.testedWebhooks = response.data.tested_webhooks;
                        this.errorWebhooks = response.data.errors;
                    }
                })
                .catch((error)=>{
                    this.errorWebhooks = error.response.data.errors;
                    this.isTesting = false;
                });
        },
    }
});