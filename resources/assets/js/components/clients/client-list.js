Vue.component('client-list', {
    props: ['agencies'],

    data() {
        return {
            arrClients: {
                data:[],
                per_page: 10,
                total: 0
            }, 
            filters: { 
                page: 1,
                per_page: 10,
                keywords: '',
                agency_id: null
            },
            isLoading: false,
            arrAgencies: [],
            typingTimer: null,
            typingWait: 500,
            searchParams: null
        }
    },

    mounted() {
        this.arrAgencies = this.agencies;

        if(this.arrAgencies.length == 1){
            this.filters.agency_id = this.arrAgencies[0].hashid
        }

        let intPerPage = localStorage.getItem('client_per_page');

        if(intPerPage){
            this.filters.per_page = intPerPage;
        }

        // check URL if filter exists
        this.setupQueryParamFilter()
        this.getClients(this.filters.page);  
    },

    computed: {
        
    },

    methods: {
         

        /*
        * when per_page changed, update save the value to localstorage for future 
        */ 
        filterPerPage() {
            localStorage.setItem('campaign_per_page', this.filters.per_page);
            this.getClients(1);
        }, 

        /*
         * Filter the contacts campaigns by name
         */
        filterByKeywords() {
            clearTimeout(this.typingTimer);

            this.typingTimer = setTimeout(()=>{
                this.filters.keywords = $.trim(this.filters.keywords);
                this.filters.page = 1;  

                this.getClients(1)
            }, this.typingWait); 
        },

        /*
        * Fetch a list of inboxes
        */ 
        getClients(page) {
            this.isLoading = true;
            this.filters.page = page;
            this.updateFilterParams()
             
            axios.get('/clients/list', { params: this.filters } )
                .then((response)=>{ 
                    if(response && response.data && response.data.status == 'success'){
                        this.arrClients = response.data.teams; 
                        setTimeout(()=>{
                            $('[data-toggle="tooltip"]').tooltip();
                        }, 500) 
                    }

                    this.isLoading = false; 
                }).catch((error)=>{
                    this.isLoading = false;
                });
        }, 

        /**
         * Assign query params to filter
         */
        setupQueryParamFilter() {
            let queryParams = new URLSearchParams(window.location.search);

            if(queryParams.get('keywords')) {
                this.filters.keywords = queryParams.get('keywords')
            }

            if(queryParams.get('page')) {
                this.filters.page = queryParams.get('page')
            }
        },

        /**
         * Push filters as query parameters to retain current filters 
         * when user navigate back here
         */
        updateFilterParams() {
            if (!history?.pushState) {
                return;
            }

            this.searchParams = new URLSearchParams(window.location.search)

            if(this.filters.page && this.filters.page != 1) {
                this.searchParams.set('page', this.filters.page)
            } else {
                this.searchParams.delete('page')
            }

            if(this.filters.keywords.trim() != '') {
                this.searchParams.set('keywords', this.filters.keywords)
            } else {
                this.searchParams.delete('keywords')
            }

            let newurl = window.location.protocol + "//" + window.location.host + window.location.pathname + '?' + this.searchParams.toString();
            window.history.pushState({path: newurl}, '', newurl);
        }
    }
});