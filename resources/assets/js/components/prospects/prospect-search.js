Vue.component('prospect-search', {
    props: ['user', 'agency', 'campaigns', 'uri',
        'defaultfields', 'statuses', 'interests',
        'clients', 'is_client', 'can_export', 'is_userdash'],

    data() {
       return {
           form: new SparkForm({
               campaign: '',
               interested: '',
               status: '',
               completed_steps: '',
               email: '',
               company: '',
               client: ''
           }),
           filters: [],
           all_prospects: [],
           prospects: [],
           steps: ['0', '1', '2', '3', '4', '5', '6'],
           detailingProspect: null,
           showFilters: true,
           loading: false,
           tableHeaders: [
               {title: 'id', field: 'hashid', class: 'never', select_checked: false},
               {title: 'Email', field: 'email', class: 'all', select_checked: true },
               {title: 'First Name', field: 'first_name', class: 'all', select_checked: true},
               {title: 'Last Name', field: 'last_name', class: 'all', select_checked: true},
               {title: 'Company', field: 'company', class: 'desktop', select_checked: false},
               {title: 'Industry', field: 'industry', class: 'never', select_checked: false},
               {title: 'Website', field: 'website', class: 'never', select_checked: false},
               {title: 'Tags', field: 'tags', class: 'never', select_checked: false},
               {title: 'Title', field: 'title', class: 'never', select_checked: false},
               {title: 'Phone', field: 'phone', class: 'never', select_checked: false},
               {title: 'Address', field: 'address', class: 'never', select_checked: false},
               {title: 'City', field: 'city', class: 'never', select_checked: false},
               {title: 'State', field: 'state', class: 'never', select_checked: false},
               {title: 'Country', field: 'country', class: 'never', select_checked: false},
               {title: 'Interested', field: 'interested', class: 'desktop', select_checked: true},
               {title: 'Status', field: 'status', class: 'desktop', select_checked: true},
               {title: 'Steps Completed', field: 'completed_steps', class: 'desktop', select_checked: true},
               {title: 'Last Contacted', field: 'last_contacted', class: 'desktop', select_checked: false},
               {title: 'Last Replied', field: 'last_replied', class: 'desktop', select_checked: false},
               {title: 'Last Opened', field: 'last_opened', class: 'desktop', select_checked: false},
           ],
           objProspect: {},
           isUploaded: false,
           strUploadErrorMsg: '',
           uploadData: {
               headers:[],
               prospects:[]
           },
           fieldBindings: {},
           duplicate: 0,
           db_duplicates: 0,
           selectCampaigns: [],
       }
    },



    created() {
        // Bus.$on('prospect-search-complete', this.fillTable);
        Bus.$on('prospect-search-start', this.loadingOn);
        Bus.$on('prospect-search-fail', this.loadingOff);
    },

    mounted() {
        // this.form.campaign = this.campaigns[0].id;
        // this.dt = $('#prospects-table').DataTable();
        this.selectCampaigns = this.campaigns;

        if(this.is_client || this.is_userdash){
            this.form.client = this.user.team_id;
            this.filterByClient();
        }

        // this.search();
    },

    // Clean up event listeners before component is destroyed.
    beforeDestroy: function () {
        // Bus.$off('prospect-search-complete', this.fillTable);
        Bus.$off('prospect-search-start', this.loadingOn);
        Bus.$off('prospect-search-fail', this.loadingOff);
    },

    watch: {
        // Watch for changes in the search fields.
        'form.client' (val) {
            this.$root.$emit('prospectFilterUpdate', this.form);
        },
        'form.campaign' (val) {
            // this.search();
            this.$root.$emit('prospectFilterUpdate', this.form);
        },
        'form.interested' (val) {
            // this.searchLocal();
            this.$root.$emit('prospectFilterUpdate', this.form);
        },
        'form.status' (val) {
            // this.searchLocal();
            this.$root.$emit('prospectFilterUpdate', this.form);
        },
        'form.completed_steps' (val) {
            // this.searchLocal();
            this.$root.$emit('prospectFilterUpdate', this.form);
        },
    },

    methods: {
        /**
         * send a search request when client filter was changed
         */
        filterByClient(){
            console.log('client: '+this.form.client);
            // displa only campaigns that belongs to the selected client
            if(this.form.client) {
                this.selectCampaigns = this.campaigns.filter((campaign) => {
                    return campaign.team_id == this.form.client;
                });
            } else {
                // if no client id (all clients), display all campaigns
                this.selectCampaigns = this.campaigns;
            }

            // reset campaign filter everytime client filter is changed
            this.form.campaign = '';

            // refresh the selectpicker plugin to render the new items
            setTimeout(()=>{
                $(".select-filter").selectpicker('refresh');
            }, 500)

            // and finaly send the search
            // this.search();
        },

        /**
         * Build options suitable select2 options object from array field.
         *
         * @param arrayField
         * @param valueAttribute
         */
        select2Options(arrayField, valueAttribute = 'name') {
            return arrayField.map((value) => {
                return {'id': value.id, 'text': value[valueAttribute]};
            });
        },

        toggleFilters() {
            this.showFilters = !this.showFilters;
        },

        loadingOn() {
            this.loading =  true;
        },

        loadingOff() {
            this.loading = false;
        },

        /**
         * Show the plan details for the given plan.
         */
        showProspectDetails(hashid) {
            // this.detailingProspect = this.prospects.filter(function (el) {
            //     return el.id === id;
            // })[0];

            axios.get('/prospects/'+hashid)
            .then((response)=>{
                if(response && response.data && response.data.status == 'success'){
                    this.detailingProspect = response.data.prospect
                }
            }).catch((error)=>{
                // display error feedback
            });

            $('#modal-prospect-details').modal('show');
        },

        /*
         * Convert MySQL datetime format, to presentation format.
         */
        convertDateTime(value, format='MMMM Do YYYY, h:mm:ss A') {
            if (value) {
                return moment(value, moment.ISO_8601).format('MMMM Do YYYY, h:mm:ss A');
            }

            return '';
        },

        getUniqueValuesOfKey(array, key){
            let tmp = array.reduce(function(carry, item){
                if(!~carry.indexOf(item[key])) carry.push(item[key]);
                return carry;
            }, []);
            return tmp.sort();
        },

        clearFilters() {
            this.form.interested = '';
            this.form.status = '';
            this.form.completed_steps = '';
            this.form.email = '';
            this.form.company = '';
        },

        searchLocal() {
            let self = this;
            let newProspects = this.all_prospects;

            this.loadingOn();

            if (this.form.completed_steps !== '') {
                newProspects = newProspects.filter(function (prospect) {
                    return prospect.completed_steps == self.form.completed_steps;
                });
            }

            if (this.form.interested !== '') {
                newProspects = newProspects.filter(function (prospect) {
                    return prospect.interested == self.form.interested;
                });
            }

            if (this.form.status !== '') {
                newProspects = newProspects.filter(function (prospect) {
                    return prospect.status == self.form.status;
                });
            }

            this.prospects = newProspects;
        },


        search() {
            let self = this;
            Bus.$emit('prospect-search-start');

            // let thisForm = new SparkForm(this.form);

            Spark.post( `${this.uri}/search`, this.form)
                .then(response => {
                    self.prospects = self.all_prospects = response.prospects;
                    self.searchLocal();

                    if (response.prospects.length == 0) {
                        swal({
                            title: 'Prospects found: 0',
                            text: 'No prospects met the search criteria.',
                            icon: 'info',
                            button: true,
                            timer: 3000
                        });
                    }
                    self.showFilters = false;
                })
                .catch(error => {

                    Bus.$emit('prospect-search-fail');
                });
        },

        /*
         * Saves a single prospect to the database.
         */
        saveManualForm(campaign_id){
            this.objProspect.campaign_id = campaign_id;

            axios.post('/campaigns/'+campaign_id+'/prospects', this.objProspect)
                .then((response)=>{
                    if(response && response.data && response.data.status == 'success'){
                        this.prospects.unshift(response.data.prospect);
                        $('#prospectManualModal').modal('hide');

                        // reset form
                        this.objProspect = {}
                    }
                }).catch((error)=>{
                    // display error feedback
                });
        },

        /*
         * Close the modal for uploading CSV prospect
         */
        cancelCsvProspects(){
            $('#prospectUploadModal').modal('hide');
            this.duplicate = 0;
            this.db_duplicates = 0;
            this.strUploadErrorMsg = '';
            this.isUploaded = false;
            this.uploadData.headers = [];
            this.uploadData.prospects = [];
            this.fieldBindings = {};

            // reload to fully reset the file input
            window.location.reload()
        },

        /*
         * Upload the CSV to the API and will receive the listed prospects in the CSV
         */
        uploadCsvProspects(campaign_id){
            this.isUploaded = false;

            let prospectCsvData = new FormData();
            let inputEl = document.getElementById('prospectCsvInput');

            if (inputEl.files.length > 0) {
                prospectCsvData.append('csv_file', inputEl.files.item(0));
            } else {
                return false;
            }

            axios.post('/campaigns/'+campaign_id+'/prospects/upload', prospectCsvData)
                .then((response)=>{
                    if(response && response.data && response.data.status == 'success'){
                        this.uploadData.headers = response.data.headers;
                        this.uploadData.prospects = response.data.prospects;
                        this.duplicate = response.data.duplicates;
                        this.db_duplicates = response.data.db_duplicates;
                        this.isUploaded = true;
                    }
                }).catch((error)=>{
                    // display error feedback
                });
        },

        /*
         * Once the CSV headers were re-mapped to the "Database headers"
         * Save all the uploaded prospects to the database
         */
        processCsvProspects(campaign_id){
            let processData = {
                "prospects": this.uploadData.prospects,
                "campaign_id": campaign_id,
                "fields": this.fieldBindings
            };

            axios.post('/campaigns/'+campaign_id+'/prospects/process', processData)
                .then((response)=>{
                    if(response && response.data && response.data.status == 'success'){
                        // this.prospects = response.data.prospects;
                        response.data.prospects.forEach((prospect) => {
                            this.prospects.push(prospect);
                        });
                        this.duplicate = 0;
                        this.db_duplicates = 0;
                        this.isUploaded = false;
                        this.uploadData.headers = [];
                        this.uploadData.prospects = [];
                        this.fieldBindings = {};
                        this.strUploadErrorMsg = '';

                        $('#prospectUploadModal').modal('hide');
                        swal("Prospects has been added successfully!", {
                            icon: "success",
                        }).then(() => {})
                    }
                }).catch((error)=>{
                    // display error feedback
                    this.strUploadErrorMsg = error.response.data.message;
                });
        },

        /*
         * Delete a prospect.
         */
        deleteProspect(prospect){
            swal({
                title: "Delete " + prospect.full_name + "?",
                icon: "warning",
                text: "Are you sure you want to delete this contact?",
                buttons: ['No, Go Back', 'Yes, Delete'],
                dangerMode: true,
            }).then((res) => {
                if (res) {
                    let selectedId = [prospect.hashid];
                    axios.delete('/campaigns/'+prospect.campaign.hashid+'/prospects', {data:{"ids": selectedId}})
                        .then((response)=>{
                            if(response && response.data && response.data.status == 'success'){
                                // loop thru selectedIds and remove the prospect
                                selectedId.forEach(selectedId => {
                                    let prospectIndex = this.prospects.map(prospect => prospect.hashid).indexOf(selectedId);
                                    this.prospects.splice(prospectIndex, 1)
                                });

                                $('#modal-prospect-details').modal('hide');

                                swal("Contact has been deleted!", {
                                    icon: "success",
                                }).then(() => {});

                                // after prospect is deleted, notify the datatables so it reload
                                this.$root.$emit('prospectStateUpdate', this.form);
                            }
                        }).catch((error)=>{
                            // display error feedback
                        });
                }
            });
        },

        /*
         * Send the updated prospect object to the API.
         */
        saveProspectUpdate(prospect){
            // let selectedId = prospect.id
            axios.put('/prospects/'+prospect.hashid+'/state/', {
                'interested':prospect.interested,
                'status':prospect.status,
            }).then((response)=>{
                    if(response && response.data && response.data.status == 'success'){
                        /*
                        // get the index of the updated prospect from the "prospects" array
                        let prospectIndex = this.prospects.map(prospect => prospect.id).indexOf(selectedId);

                        // delete the previous prospect and push the new prospect
                        // this is to reupdate the list on datatables
                        this.prospects.splice(prospectIndex, 1)
                        this.prospects.push(response.data.prospect);
                        */

                        // after prospect is updated, notify the datatables so it reload
                        this.$root.$emit('prospectStateUpdate', this.form);
                    }
                }).catch((error)=>{

                });
        },

        /*
         * Get the activity label from activity string DEPRECATED
         */
        // getActivityLabel(activity){
        //     let activityLabel = '';
        //
        //     if(activity == 'CampaignEmailSent'){
        //         activityLabel = 'EMAIL SENT'
        //     } else if(activity == 'ProspectOpenedEmail'){
        //         activityLabel = 'EMAIL OPENED'
        //     } else if(activity == 'ProspectClickedLink'){
        //         activityLabel = 'LINK CLICKED'
        //     } else {
        //         let lastWord = activity.split(" ").slice(-1);
        //         activityLabel = lastWord[0];
        //     }
        //
        //     return activityLabel;
        // },

        /*
         * check if we need to skip displaying the activity because of double logging
         * ex: "updated prospect status to REPLIED" then followed by "ProspectReplied"
         */
        displayActivity(activity){
            let display = true;

            if(['ProspectReplied', 'EmailBounced'].includes(activity)){
                display = false;
            }

            return display;
        }
    }
});
