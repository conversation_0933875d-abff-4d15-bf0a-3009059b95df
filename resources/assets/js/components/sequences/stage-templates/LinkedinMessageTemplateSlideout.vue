<template>
    <div class="panel-container loader-wrapper" :class="{'loader-wrapper-loading': isUpdating}">
        <header class="slidePanel-header">
            <div class="overlay-top overlay-panel" :class="headerBg">
                <div class="slidePanel-actions btn-group" aria-label="actions" role="group">
                    <button type="button"
                            class="btn btn-pure icon wb-trash"
                            :disabled="isUpdating"
                            aria-hidden="false"
                            @click="deleteMessageTemplate"
                            v-tooltip.auto="{ content: 'Delete Message Template', classes: 'v-tooltip' }"
                    >
                    </button>
                    <button type="button"
                            class="btn btn-pure slidePanel-close icon wb-close"
                            :disabled="isUpdating"
                            aria-hidden="false"
                            @click="closePanel"
                            v-tooltip.auto="{ content: 'Save and Close', classes: 'v-tooltip' }"
                    >
                    </button>
                </div>
                <h4>
                    Step #{{ stage.number }}: {{ emailLabel }}
                </h4>
            </div>
        </header>
        <div class="slidePanel-inner">
            <section id="editor" class="slidePanel-inner-section">
                <div class="row">
                    <div class="col-md-3">
                        <h5 class="section-title">Message Template</h5>
                        <div class="section-description">Edit the message that will be sent using this message template.</div>
                    </div>
                    <div class="col-md-9">
                        <label>
                            Message
                        </label>
                        <div id="template-main-editor" :class="this.spark.agency.color">
                            <tiny-editor
                                v-model="msgBody"
                                type="linkedin"
                                :fields="editorFields"
                                :custom-fields="editorCustomFields"
                                :readonly="isReadOnly"
                                @update-field="updateCustomFields"
                                @onInit="editorReady"
                                @onBlur="toggleEditorFocus(false)"
                                @onFocus="toggleEditorFocus(true)"
                            >
                            </tiny-editor>
                        </div>
                        <div class="error mb-20" v-if="messageTemplate.has_warning_msg || messageTemplate.has_unmatched_msg">
                            <span v-if="messageTemplate.has_warning_msg" class="mr-3">
                                Fix broken merge fields.
                            </span>
                            <span v-if="messageTemplate.has_unmatched_msg">
                                {{ errSnippetMsgs.sub_unmatched }}
                            </span>
                        </div>
                        <div class="error mb-20" v-if="messageTemplate.has_warning_format">
                            Fix broken merge fields due to inserted styles
                        </div>
                        <div class="error mb-20" v-if="messageTemplate?.errors?.has('msgBody')">
                            {{ messageTemplate.errors.get('msgBody') }}
                        </div>
                    </div>
                </div>
            </section>
            <section id="delivery-windows" class="slidePanel-inner-section">
                <div class="row">
                    <div class="col-md-3">
                        <h5 class="section-title">Delivery Windows</h5>
                        <div class="section-description">Set the the daily send schedules for this template. The timezone is set on the Campaign details screen.</div>
                    </div>
                    <div class="col-md-9">
                        <div v-for="day in scheduleDays">
                            <template-schedule
                                :email="messageTemplate"
                                :from-time="messageTemplate[day + '_from']"
                                :to-time="messageTemplate[day + '_to']"
                                :label="day"
                                :campaign="campaign"
                                @copy="copySchedule"
                                @input="updateDay"
                            >
                            </template-schedule>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <div class="loader-box vertical-align text-center">
            <div class="loader vertical-align-middle loader-circle"></div>
        </div>
    </div>
</template>

<script>
import TinyEditor from "../../ui/tiny-editor";
import TemplateSchedule from "./TemplateSchedule";

export default {
    name: 'LinkedinMessageTemplateSlideout',

    components: {
        TinyEditor: TinyEditor,
        TemplateSchedule: TemplateSchedule
    },

    props: {
        template: Object,
        stage: Object,
        campaign: Object,
        defaultFields: Array,
        customFields: Array,
        linkedinAccountsCount: Number // needed to enable sending test message
    },

    data: function() {
        return {
            messageTemplate: {},
            editorInit: false,
            templateUpdated: false,
            isUpdating: false,
            isSendingEmail: false,
            isUpdatingLimit: false,
            isUpdatingStage: false,
            showSchedules: false,
            showSettings: false,
            showSendTestEmail: false,
            msgBody: '',
            newSnippetErrMsg: '',
            selectedEmailAddress: '',
            selectedEmailError: '',
            errSnippetMsgs: {
                sub_unmatched: "There's a {{field}} that doesn't exist in your Merge Fields."
            },
            templateSubTags: [],
            templateMsgTags: [],
            scheduleDays: [
                'sun',
                'mon',
                'tue',
                'wed',
                'thu',
                'fri',
                'sat',
            ],
            calculatedFields: [
                'sender_firstname',
                'sender_lastname',
                'time_of_day'
            ],
            variableFieldSeparator: '||',
            isSubjectToggled: false,
            isEditorFocused: false
        }
    },

    watch: {
        // customFields: {
        //     handler: function(newValue) {
        //         this.checkMergeFieldWarning();
        //     },
        //     deep: true
        // },
        customFields() {
            if (!this.isReadOnly && this.editorInit) {
                this.checkMergeFieldUnmatched();
                this.checkMergeFieldWarning();
                this.checkMergeFieldBrokenFormat();
            }
        },
        msgBody(val) {
            if (!this.isReadOnly && this.editorInit) {
                this.saveEmail();
            }
        },
    },

    computed: {
        headerBg() {
            return 'bg-' + this.spark.agency.color + '-600';
        },
        emailLabel() {
            let labels = [
                'A','B','C','D','E','F','G',
                'H','I','J','K','L','M','N','O','P',
                'Q','R','S','T','U','V','W','X','X','Z'
            ];

            return 'Linkedin Message Version ' + labels[this.messageTemplate.number - 1];
        },
        allFields() {
            return this.calculatedFields.concat(this.defaultFields, this.customFields);
        },
        editorFields() {
            return this.defaultFields.join(',');
        },
        editorCustomFields() {
            return this.customFields.join(',');
            // return this.customFields.join(',');
        },
        isReadOnly() {
            return this.campaign.status === 'RUNNING'
        }
    },

    mounted() {
        this.messageTemplate = new SparkForm(this.template);
        this.msgBody = this.messageTemplate.msg;

        if (this.messageTemplate.number === 1) {
            this.messageTemplate.max_emails_per_day = this.campaign.max_emails_per_day;
        }

        // if email is deleted, compare email number and reduce by 1
        // this.$root.$on('emailDeleted', deletedEmail => {
        //     if(this.messageTemplate.number > deletedEmail.number){
        //         this.messageTemplate.number = this.messageTemplate.number - 1;
        //     }
        // });

        // when snippet was fetched and default fields are constructed,
        // check for unmatched fields from the email template
        this.$root.$on('snippetFetched', () => {
            this.checkMergeFieldUnmatched();
        });
    },

    created() {
        //
    },

    methods: {
        editorReady() {
            this.editorInit = true;
        },

        closePanel() {
            this.$emit('closePanel', {
                templateUpdated: this.templateUpdated
            });
        },

        copySchedule(event) {
            // loop thru days
            this.scheduleDays.forEach((day) => {
                // check if this "day" of this "email" is active
                if (this.messageTemplate[day+'_from'] > -1) {
                    this.messageTemplate[day+'_from'] = event.fromTime;
                    this.messageTemplate[day+'_to'] = event.toTime;
                }
            });

            this.saveEmail();
        },

        updateDay(event) {
            this.messageTemplate[event.label+'_from'] = event.fromTime;
            this.messageTemplate[event.label+'_to'] = event.toTime;

            this.saveEmail();
        },

        // check if template contains broken merge fields due to bad style wrapping in the editor
        checkMergeFieldBrokenFormat(){
            var patternSnip = new RegExp('[^{\}]+(?=})', 'g');
            var patterTag = new RegExp('[^<\}]+(?=>)', 'g');
            var snippets = this.messageTemplate.msg ? this.messageTemplate.msg.match(patternSnip) : null;

            if (snippets && snippets.length) {
                snippets.forEach((snippet)=>{
                    if (patterTag.test(snippet)) {
                        this.messageTemplate.has_warning_format = true;
                    }
                })
            }
        },

        // check if template contains merge fields that doesnt exist in default and custom snippets
        checkMergeFieldUnmatched(){
            let patternTags = new RegExp('{{([^}}]*)}}', 'g');
            let templateMsgTags = this.messageTemplate.msg ? this.messageTemplate.msg.match(patternTags) : [];

            // msg tags cleanup - remove {{}}
            if(templateMsgTags && templateMsgTags.length){
                templateMsgTags.forEach((val, key)=>{
                    templateMsgTags[key] = val.replace(/{{|}}/g, '');
                });
            }

            // remove default fields to check if there are unmatched fields
            let defMsgUnmatched = $(templateMsgTags).not(this.allFields).get()
                .filter(e => (!e.startsWith('today') && e.indexOf(this.variableFieldSeparator) < 0));

            if (defMsgUnmatched && defMsgUnmatched.length) {
                this.messageTemplate.has_unmatched_msg = true;
            }
        },

        // check if template contains broken merge fields
        checkMergeFieldWarning(){
            this.defaultFields.forEach((field)=>{
                let lookahead = '{'+field+'}(?!})|'+field+'}(?!})|{'+field+'(?!})';
                let lookbehind = '(?<!{){'+field+'}|(?<!{)'+field+'}|(?<!{){'+field;
                var pattern = new RegExp(lookbehind+'|'+lookahead, 'g');

                if(pattern.test(this.messageTemplate.msg)){
                    this.messageTemplate.has_warning_msg = true;
                }
            })
        },

        // update the custom merge fields based on editor events
        updateCustomFields(event) {
            if (event.type === 'create-field') {
                this.createCustomField(event.field);
            } else if (event.type === 'delete-field') {
                this.deleteCustomField(event.field);
            }
        },

        /**
         * helper function to set isEditorFocused value
         */
        toggleEditorFocus(isFocused) {
            this.isEditorFocused = isFocused

            // resave if click outside the form
            if(!this.isEditorFocused) {
                this.saveEmail()
            }
        },

        /**
         * saving changes on editor doesn't happen on every key event
         * it has to wait for 3 secs after the user stopped typing then call the "saveEmail" function
         */
        updateEmail() {
            clearTimeout(this.typingTimer);
            this.typingTimer = setTimeout(this.saveEmail, this.typingWait);
        },

        /**
         * Send the "objEmail" - this is a single email-item on the email-list - to save any changes
         */
        saveEmail() {
            // block action if campaign is running
            if(this.campaign.status === 'RUNNING'){
                swal({
                    title: "Changes Not Saved",
                    text: 'Pause this campaign to edit.',
                    icon: 'error',
                }).then(() => {})

                return false;
            }

            // let elEmailTab = $('#messageTemplateItem'+this.objEmail.id);
            // elEmailTab.removeClass('email-saved email-err').addClass('email-saving');

            // this.messageTemplate.follow_up = parseInt(this.messageTemplate.follow_up);

            // get the template "msg" from the editor because 'trumbowyg' doesn't have a way to refresh thhe content of
            // the textarea or the model when updating its content with 'insertHTML'.
            // send a 'msg_raw' and use that as a value of 'msg' in the backend
            // this.objEmail.msg_raw = $('#campaignEmailEditor_'+this.objEmail.id+' .trumbowyg-editor').html();
            this.messageTemplate.msg = this.msgBody;
            this.messageTemplate.msg_raw = this.msgBody;

            return Spark.put('/campaigns/'+this.campaign.hashid+'/linkedin-message-templates/'+this.messageTemplate.hashid, this.messageTemplate).then(
                (response) => {
                    this.$root.$emit('templateUpdated', response.stages);
                    this.messageTemplate.setErrors([]);

                    // display popup error only if not focused on the editor
                    // so user will not be annoyed while they are still typing/editing template
                    if ((response.has_warning_msg
                        || response.has_warning_format
                        || response.has_unmatched_msg
                    ) && !this.isEditorFocused){
                        swal({
                            title: "Merge Field Warning",
                            text: 'Message template has been saved but has merge field errors. Please check your merge fields format.',
                            icon: 'warning',
                        }).then(() => {});
                    }

                    this.messageTemplate.has_warning_msg = response.has_warning_msg;
                    this.messageTemplate.has_warning_format = response.has_warning_format;
                    this.messageTemplate.has_unmatched_msg = response.has_unmatched_msg;

                    // add loading/success classes and remove them after 3seconds
                    // elEmailTab.removeClass('email-saving email-err').addClass('email-saved');
                    // setTimeout(function(){ elEmailTab.removeClass('email-saving email-saved nav-loaded-err') }, 1000);
                },
                (error) => {
                    // add loading/error classes and remove them after 3seconds
                    // elEmailTab.removeClass('email-saving email-saved').addClass('email-err');
                    // setTimeout(function(){ elEmailTab.removeClass('nav-loading nav-loaded nav-loaded-err') }, 1000);
                    if(error.status == 'error') {
                        this.messageTemplate.setErrors({
                            "msgBody": [ error.message ]
                        });
                    } else {
                        this.messageTemplate.setErrors(error.response.data.errors);
                    }
                }
            )
        },

        createCustomField(field) {
            // block action if campaign is running
            if (this.campaign.status === 'RUNNING'){
                swal({
                    title: "Campaign is RUNNING",
                    text: 'Paused this campaign to add custom fields.',
                    icon: 'error',
                }).then(() => {})

                return false;
            }

            axios.post('/campaigns/'+this.campaign.hashid+'/snippets/', {name: field})
                .then((response)=>{
                    if(response && response.data && response.data.status === 'success'){
                        this.$root.$emit('snippetAdded', {id:response.data.snippet.id, name:response.data.snippet.name});
                        this.newSnippetErrMsg = '';
                        $('#snippetAddModal').modal('hide');

                        swal("Field was successfully added!", {
                            icon: "success",
                        }).then(() => {})
                    }
                }).catch((error)=>{
                // display error feedback
                this.newSnippetErrMsg = error.response.data.message;
            });
        },

        /**
         * delete a snippet (custom field) on the database
         */
        deleteCustomField(field){
            // Need to find the custom field by the name. We should have an object of custom fields with {id,name}
            // block action if campaign is running
            console.log(field);
            let fieldName = this.titleCase(field);
            if(this.campaign.status === 'RUNNING'){
                swal({
                    title: "Campaign is RUNNING",
                    text: 'Paused this campaign to delete custom fields.',
                    icon: 'error',
                }).then(() => {})

                return false;
            }

            swal({
                title: "Delete " + fieldName + " field?",
                icon: "warning",
                text: "Are you sure you want to delete this field?",
                buttons: ['No, Go Back', 'Yes, Delete'],
                dangerMode: true,
            }).then((res) => {
                if (res) {
                    axios.delete('/campaigns/'+this.campaign.hashid+'/snippet-name/'+field)
                        .then((response)=>{
                            if(response && response.data && response.data.status === 'success'){
                                this.$root.$emit('snippetDeleted', field);
                                // animate using jquery then remove from list when hidden
                                // $('#snippetFieldItem'+customField.id).slideUp();
                            }
                        }).catch((error)=>{
                        // display error feedback
                    });
                }});
        },

        deleteMessageTemplate() {
            // block action if campaign is running
            if(this.campaign.status === 'RUNNING'){
                swal({
                    title: "Campaign is RUNNING",
                    text: 'Pause this campaign to delete email templates.',
                    icon: 'error',
                }).then(() => {})

                return false;
            }

            swal({
                title: "Delete " + this.emailLabel,
                icon: "warning",
                text: "Are you sure you want to delete this Linkedin Message Version?",
                buttons: ['No, Go Back', 'Yes, Delete'],
                dangerMode: true,
            }).then((res) => {
                if (res) {
                    this.isUpdating = true;
                    let deleteUrl = '/campaigns/'+this.campaign.hashid+'/linkedin-message-templates/'+this.messageTemplate.hashid;

                    return axios.delete(deleteUrl, this.messageTemplate).then((response) => {
                      // notify email-list component that we deleted an email
                      this.$root.$emit('templateDeleted', response.data.stages);

                      swal("LinkedIn Message Version Deleted", {
                          icon: "success",
                      }).then(
                          () => {
                              // if emails were displayed in "tabs" open the first email template after delete
                              if($('.campaignEmailsTab').length){
                                  setTimeout(()=>{
                                      $('.campaignEmailsTab').first().find('a').tab('show')
                                  }, 300)
                              }

                              // close panel
                              this.closePanel();
                          })
                    })
                }
            });
        },

        /**
         * A utility method that will return a formatted data for the "email-4" component
         */
        getDaySetting(day, email){
            return {
                label: day.label,
                from: email[day.label + '_from'],
                to: email[day.label + '_to']
            };
        },

        /**
         * Convert a string from snake case to title case
         * @param value
         * @returns {*}
         */
        titleCase(value) {
            return value.split('_').map(function(item) {
                return item.charAt(0).toUpperCase() + item.substring(1);
            }).join(' ');
        }
    }
}
</script>

<style>
.overlay-panel {
    padding: 20px 30px;
}
#template-slideout .panel-container {
    top: 7.858rem;
    bottom: 0;
    width: 100%;
    position: fixed;
    /*z-index: 1310;*/
    max-width: 100%;
    max-height: 100%;
    /*visibility: hidden;*/
    background: #fff;
    /*box-shadow: -10px 0 20px 0 rgba(66, 66, 66, .2);*/
}
.slidePanel-inner-section {
    border-bottom: 0px;
    border-top: 1px solid #e4eaec;
}
.slidePanel-actions {
    min-height: 0;
}
.slidePanel-actions .btn {
    line-height: 1.286rem;
    font-size: 1.286rem;
}
.slidePanel-inner-section:first-of-type {
    border-top: 0;
}
.slidePanel-inner-section .status-radio .list-inline > li {
    padding-right: 0;
    padding-left: 20px;
}
.slidePanel-inner-section .status-radio .list-inline > li label {
    font-size: 0;
    color: transparent;
}
.slidePanel-inner-section .status-radio .list-inline-item {
    display: inline-block;
}
.slidePanel-inner-section .section-title {
    font-size: 18px;
    margin-top: 10px;
}
.slidePanel-inner-section .section-description {
    font-size: 13px;
}
.my-dropdown-menu {
    position: absolute;
    transition: .25s;
    transform: translate3d(-126px, 36px, 0px);
    /*top: 100%;*/
    /*left: 0;*/
    top: 0px;
    left: 0px;
    z-index: 1200;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 5px 0 0;
    font-size: 1rem;
    color: #76838f;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #e4eaec;
    border-radius: .215rem;
    box-shadow: 0 3px 12px rgba(0, 0, 0, .05);
}
.swal-width-auto {
    width: auto;
}
.edit-subject {
    margin-bottom: 20px;
    font-weight: 300;
    font-size: 14px;
    font-family: Roboto, arial, Helvetica, sans-serif;
    color: #000;
}
#delivery-windows .checkbox-custom,
#send-test-email .checkbox-custom {
    margin-top: 0;
}
.tox-tinymce-inline {
    z-index: 1000;
}
</style>
