<template>
    <div class="platform-host-manager">
        <h3>Platform Host Management</h3>
        <p class="text-muted">Review and classify platform hosts. Each load shows 10 hosts with null is_platform status, ordered by subdomain count.</p>

        <div v-if="loading" class="text-center">
            <i class="fa fa-spinner fa-spin"></i> Loading...
        </div>

        <div v-else>
            <div class="panel panel-compact" v-for="platformHost in platformHosts" :key="platformHost.registrable">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h4 class="mb-2">
                                <a :href="'https://' + platformHost.registrable" target="_blank" class="text-primary">
                                    {{ platformHost.registrable }}
                                    <i class="fa fa-external-link fa-sm"></i>
                                </a>
                                <span class="text-muted ml-3">
                                    Subdomains: <strong>{{ platformHost.count_subdomains }}</strong> |
                                    Confidence: <strong>{{ platformHost.confidence }}</strong>
                                </span>
                            </h4>

                            <div v-if="platformHost.related_domains && platformHost.related_domains.length > 0">
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered table-compact">
                                        <thead>
                                            <tr>
                                                <th>Related Domain Name</th>
                                                <th>Merchant Name</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="domain in platformHost.related_domains" :key="platformHost.registrable + '-' + domain.id">
                                                <td>
                                                    <a :href="'https://' + domain.name" target="_blank" class="text-info">
                                                        {{ domain.name }}
                                                        <i class="fa fa-external-link fa-xs"></i>
                                                    </a>
                                                </td>
                                                <td>{{ domain.merchant_name || 'N/A' }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div v-else>
                                <p class="text-muted mb-0">No related domains found</p>
                            </div>
                        </div>

                        <div class="col-md-4 text-right">
                            <div class="btn-group-vertical btn-group-compact" role="group">
                                <button
                                    class="btn btn-success btn-sm"
                                    :disabled="platformHost.updating"
                                    @click="updatePlatformStatus(platformHost, true)"
                                >
                                    <i class="fa fa-check"></i> Mark as Platform
                                </button>
                                <button
                                    class="btn btn-danger btn-sm"
                                    :disabled="platformHost.updating"
                                    @click="updatePlatformStatus(platformHost, false)"
                                >
                                    <i class="fa fa-times"></i> Mark as Non-Platform
                                </button>
                            </div>

                            <div v-if="platformHost.updating" class="mt-1">
                                <small><i class="fa fa-spinner fa-spin"></i> Updating...</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="platformHosts.length === 0" class="text-center">
                <p class="text-muted">No platform hosts found with null is_platform status.</p>
            </div>

            <div class="text-center mt-4" v-if="hasMore">
                <button
                    class="btn btn-primary"
                    :disabled="loadingMore"
                    @click="loadMoreHosts"
                >
                    <i class="fa fa-spinner fa-spin" v-if="loadingMore"></i>
                    <i class="fa fa-plus" v-else></i>
                    Load More
                </button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'PlatformHostManager',

    props: {
        initialPlatformHosts: {
            type: Array,
            default: () => []
        }
    },

    data() {
        return {
            platformHosts: [],
            loading: false,
            loadingMore: false,
            hasMore: true
        }
    },

    mounted() {
        this.platformHosts = this.initialPlatformHosts.map(host => ({
            ...host,
            updating: false
        }));

        // Check if we have more data to load
        this.hasMore = this.platformHosts.length === 10;
    },

    methods: {
        updatePlatformStatus(platformHost, isPlatform) {
            platformHost.updating = true;

            axios.post('/admin/platform-hosts/update-status', {
                registrable: platformHost.registrable,
                is_platform: isPlatform
            })
            .then(response => {
                if (response.data.success) {
                    // Remove the host from the list since it's no longer null
                    const index = this.platformHosts.findIndex(h => h.registrable === platformHost.registrable);
                    if (index !== -1) {
                        this.platformHosts.splice(index, 1);
                    }

                    // Show success message
                    swal({
                        title: platformHost.registrable,
                        text: response.data.message || 'Platform status updated successfully',
                        icon: 'success'
                    });
                } else {
                    swal({
                        title: platformHost.registrable,
                        text: 'Failed to update platform status',
                        icon: 'error'
                    });
                }
            })
            .catch(error => {
                console.error('Error updating platform status:', error);
                swal({
                    title: 'Error',
                    text: 'An error occurred while updating platform status',
                    icon: 'error'
                });
            })
            .finally(() => {
                platformHost.updating = false;
            });
        },

        loadMoreHosts() {
            this.loadingMore = true;

            axios.post('/admin/platform-hosts/load-more', {
                offset: this.platformHosts.length
            })
            .then(response => {
                if (response.data.success) {
                    const newHosts = response.data.data.map(host => ({
                        ...host,
                        updating: false
                    }));

                    this.platformHosts.push(...newHosts);
                    this.hasMore = response.data.has_more;
                } else {
                    swal({
                        title: 'Error',
                        text: 'Failed to load more hosts',
                        icon: 'error'
                    });
                }
            })
            .catch(error => {
                console.error('Error loading more hosts:', error);
                swal({
                    title: 'Error',
                    text: 'An error occurred while loading more hosts',
                    icon: 'error'
                });
            })
            .finally(() => {
                this.loadingMore = false;
            });
        }
    }
}
</script>

<style scoped>
.platform-host-manager .panel {
    margin-bottom: 15px;
}

.panel-compact .panel-body {
    padding: 15px;
}

.btn-group-vertical .btn {
    margin-bottom: 3px;
}

.btn-group-compact .btn {
    padding: 6px 12px;
}

.table-compact {
    margin-bottom: 0;
}

.table-compact th,
.table-compact td {
    padding: 4px 8px;
    vertical-align: middle;
}

.table-compact th {
    background-color: #f8f9fa;
    font-weight: 600;
    font-size: 0.9em;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-0 {
    margin-bottom: 0;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

.fa-xs {
    font-size: 0.75em;
}

.fa-sm {
    font-size: 0.875em;
}

/* Link styling */
a.text-primary:hover {
    text-decoration: none;
    color: #0056b3 !important;
}

a.text-info:hover {
    text-decoration: none;
    color: #117a8b !important;
}

/* Compact spacing for domain links */
.table-compact a {
    display: inline-block;
}

.table-compact .fa-external-link {
    margin-left: 3px;
    opacity: 0.7;
}

.table-compact .fa-external-link:hover {
    opacity: 1;
}

/* Inline stats styling */
.ml-3 {
    margin-left: 1rem;
}

h4 .text-muted {
    font-size: 0.8em;
    font-weight: normal;
}
</style>
