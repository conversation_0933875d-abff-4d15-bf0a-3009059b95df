Vue.component('chatgpt-key', {
    props: ['user', 'agency'],

    /**
     * The component's data.
     */
    data() {
        return {
            api_key: '',
            api_model: '',
            isSaving: false,
            isAdded: false,
            isUpdated: false,
            isEditing: false,
            isChecking: false,
            chatGptErrors:[],
            models: [],
            typingTimer: null,
            typingWait: 500,
            showFull: false
        };
    },


    /**
     * The component has been created by Vue.
     */
    created() {
        this.api_key = this.agency.chat_gpt_key;
        this.api_model = this.agency.chat_gpt_model;

    },

    computed: {
        apiPreview() {
            let preview = "";
            let key = this.agency?.chat_gpt_key

            if(key) {
                let firstKey = key?.slice(0, 10);
                let lastKey = key?.slice(-10);
                let fillerCount = key.length - 20;
                let midKey = Array(fillerCount).join("*")

                preview = firstKey + midKey + lastKey
            }

            return preview;
        }
    },


    methods: {
        submitChatGptKey(actionTxt) {
            this.isSaving = true;
            let model = this.models?.length > 0 ? this.api_model : ""

            axios.put('/agency/setup/'+this.agency.hashid+'/keys/chatgpt', {
                'key': this.api_key,
                'model': model,
                '_method': "PUT"
            }).then((response)=>{
                this.chatGptErrors = [];
                $('#chatgptEditModal').modal('hide');

                if(response && response.data && response.data.status == 'success'){
                    this.agency.chat_gpt_key = this.api_key
                    this.agency.chat_gpt_model = this.api_model

                    swal('Success', {
                        icon: 'success',
                    }).then(() => {
                        if (actionTxt == 'Save') {
                            this.isAdded = true
                        } else {
                            this.isUpdated = true
                        }
                    });
                } else {
                    swal({
                        title: 'Warning',
                        text: 'An error happened while saving your changes',
                        icon: "warning",
                    }).then(() => {

                    });
                }

                this.isSaving = false;
            }).catch((error)=>{
                if(error?.response?.data?.errors) {
                    this.chatGptErrors = error?.response?.data?.errors;
                }

                this.isSaving = false;

                swal({
                    title: 'Unable to save changes',
                    // text: 'error',
                    icon: "error",
                }).then(() => {

                });
            });
        },

        saveChatGptKey() {
            const actionTxt = this.isEditing ? "Update" : "Add"
            swal({
                title: actionTxt + " ChatGPT settings?",
                icon: "warning",
                text: "",
                buttons: ['No, Cancel', 'Yes, '+actionTxt+' settings'],
            }).then((res) => {
                if (res) {
                    this.submitChatGptKey(actionTxt);
                }
            })
        },

        /*
         * Open modal to edit selected proxy
         */
        openEditModal(action) {
            if(action == 'add') {
                this.isEditing = false;
            } else {
                this.isEditing = true;
            }

            if(this.api_key) {
                this.getAvailableModels()
            }

            $('#chatgptEditModal').modal('show');
        },

        getAvailableModels() {
            this.isChecking = true
            axios.post('/chatgpt/key-test', {
              'api_key': this.api_key,
            })
                .then((response)=>{
                    this.isChecking = false

                    if(response && response.data && response.data.status == 'success'){
                        this.chatGptErrors = []
                        this.models = []

                        response?.data?.models?.map((model)=>{
                            this.models.push(model)
                        });

                        if(this.models?.length == 1 || !this.api_model) {
                            this.api_model = this.models[0]
                        }
                    }
                })
                .catch((error)=>{
                    this.isChecking = false

                    var keyErrMsg = error?.response?.data?.message
                    this.chatGptErrors = {
                        "key": [ keyErrMsg ]
                    }

                    this.models = []
                    this.api_model = ''
                });
        },

        testChatGptKey() {
            clearTimeout(this.typingTimer);
            this.typingTimer = setTimeout(()=>{
                this.getAvailableModels()
            }, this.typingWait);
        },

        toggleShow(shouldShow) {
            this.showFull = shouldShow
        }
    }
});
