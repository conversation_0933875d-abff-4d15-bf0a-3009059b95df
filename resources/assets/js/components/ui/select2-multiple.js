Vue.component('select2-multiple', {
    props: ['options', 'value'],
    template: `
    <select class="" style="width: 100%" multiple>
        <slot></slot>
    </select>
    `,

    mounted: function () {
        let vm = this;
        $(this.$el)
        // init select2
            .select2({ data: this.options, width: '100%' })
            .val(this.value)
            .trigger('change')
            // emit event on change.
            .on('change', function () {
                vm.$emit('input', $(this).val())
            })
    },
    watch: {
        value: function (value) {
            if (Array.from(value).sort().join(",") !== Array.from($(this.$el).val()).sort().join(","))
                $(this.$el).val(value).trigger('change');
        },
        // value: function (value) {
        //     // update value
        //     $(this.$el).val(value)
        // },
        // options: function (options) {
        //     // update options
        //     $(this.$el).select2({ data: options })
        // }
    },
    destroyed: function () {
        $(this.$el).off().select2('destroy');
    }
});