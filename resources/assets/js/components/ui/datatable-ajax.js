Vue.component('datatable-ajax', {
    props: ['dt_id', 'tabledata', 'tableheaders', 'tablescroll', 'tableaction', 'select_columns', 'formfilters', 'can_export'],

    template: `
        <div class="datatable-box">
        <table class="table table-hover table-striped dataTable dt-responsive nowrap w-full"></table>
        <select class="datatable-select-column" 
            multiple data-plugin="selectpicker" 
            data-style="btn-outline btn-default"
            data-selected-text-format="count"
            data-title="Choose columns"
            @change="selectColumnChanged"
            v-model="selectedColumns"
            v-if="select_columns"
        >
          <option v-for="(header, index) in headers" :value="index" v-if="header.class != 'never'">
            {{header.title}}
          </option>
        </select>
        </div>
    `,

    data() {
        return {
            headers: this.tableheaders,
            rows: [],
            showsDetails: false,
            dtHandle: null,
            selectedColumns: [],
            filters: this.formfilters,
            exportDisabled: 'disabled'
        }
    },

    created() {
        if ((typeof this.tableaction !== "undefined") && (this.tableaction != '')) {
            this.showsDetails = true;
        }

        // update pagination style
        setTimeout(function(){
            $('.dataTables_wrapper .pagination').addClass('pagination-gap');
        }, 1500)

        // get the selected columns (initially displayed columns)
        this.headers.forEach((head, index)=>{
            if(head.class != 'never' && head.select_checked){
                this.selectedColumns.push(index);
            }
        })
    },

    methods: {
        fillTable() {
            // Keeping the DataTable in sync.
            // New rows added, then redrawn!
            this.dtHandle.rows.add(this.rows);
            this.dtHandle.draw();
        },

        emptyTable() {
            // Datatable rows cleared.
            this.dtHandle.clear();
        },

        prepareRows(val) {
            let vm = this;
            this.rows = [];
            let itemVal = '';

            if (typeof val !== "undefined") {
                val.forEach(item => {
                    // Build an array with the fields defined in the headers.
                    let row = [];
                    this.headers.forEach(function (header) {
                        if (typeof header['field'] !== "undefined") {

                            if (header['type'] == 'boolean') {
                                itemVal = (item[header['field']] == true) ? '<i class="fa fa-check"></i> YES</span>' : '<i class="fa fa-times"></i>';
                            } else if (header['type'] == 'count') {
                                itemVal = item[header['field']].length;
                            } else {
                                itemVal = item[header['field']]
                            }

                            if (typeof itemVal === 'undefined')
                                itemVal = '';

                            row.push(itemVal);
                        }
                    });

                    this.rows.push(row);
                });
            }
        },

        getFilterByName(name){
            return this.formfilters[name];
        },

        /*
         * hide/show columns based on the selected column indexes
         */
        selectColumnChanged(){
            this.headers.forEach((head, index)=>{
                let is_visible = false;

                // check if header index is in the selectedColumns
                if(this.selectedColumns.includes(index)){
                    is_visible = true;
                };

                // then set column to visible or hide
                let column = this.dtHandle.column( index );
                column.visible( is_visible );
            })
        },

        /*
         * submit the export form hidden above the datatables
         */
        exportProspectCsv(formId){
            this.filters.source = 'contacts_page';
            this.filters.keywords = $('.datatable-box .dataTables_filter input[type="search"]').val();

            $('.prospectExportFilters').val(JSON.stringify(this.filters));
            $('#'+formId).submit();
        },

    },

    watch: {
        // Watch for changes in the table's data.
        tabledata(val, oldVal) {
            // this.prepareRows(val);
            // this.emptyTable();
            // this.fillTable();
            this.$emit('tableupdated');
        },

        // Watch for changes in the table's data.
        formfilters(val, oldVal) {

        },
    },

    mounted() {
        // disable/enable export button based on the current user role
        this.exportDisabled = this.can_export ? '' : 'disabled';

        let vm = this;

        // Set date format for datatable sorting
        // $.fn.dataTable.moment( 'DD-MM-YYYY' );
        // $.fn.dataTable.moment( 'DD/MM/YYYY' );
        // $.fn.dataTable.moment( 'DD-MM-YYYY, HH:mm' );

        // Instantiate the datatable and store the reference to the instance in the dtHandle element.
        this.dtHandle = $($(this.$el).find('table')).DataTable({
            columns: this.headers,
            // data: this.rows,

            "processing": true,
            "serverSide": true,
            "ajax": {
                url: "/prospects/datatable",
                type: "POST",
                // data: {
                //     _token: window.Spark.csrfToken,
                //     campaign: this.getFilterByName('campaign'),
                //     client: this.getFilterByName('client'),
                //     interested: this.filters.interested,
                //     status: this.filters.status,
                //     emails_sent: this.filters.emails_sent
                // },
                data:  ( d ) => {
                    d._token = window.Spark.csrfToken;
                    d.campaign = this.filters.campaign;
                    d.client = this.filters.client;
                    d.interested = this.filters.interested;
                    d.status = this.filters.status;
                    d.completed_steps = this.filters.completed_steps;
                },
                dataFilter: (reps) => {
                    return reps;
                },
                error:function(err){

                }
            },

            dom: "<'row mb-20'<'col-sm-3'f><'col-sm-3'l><'col-sm-6 text-right'B>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row pt-20'<'col-sm-5'i><'col-sm-7'p>>",

            buttons: [
                // {
                //     extend: 'csvHtml5',
                //     text: 'Export to CSV',
                //     className: 'btn btn-default'
                // },
                {
                    text: 'Export to CSV',
                    className: 'btn btn-default ' + this.exportDisabled,
                    action: ( e, dt, node, config ) => {
                        if(this.can_export){
                            this.exportProspectCsv('exportContactsCSV');
                        }
                    }
                }
            ],
            responsive: {
                details: {
                    type: 'column'
                }
            },
            pageLength: 25,
            paging: !this.tablescroll,
            scrollY:  (this.tablescroll) ? '50vh':null,
            scrollCollapse: this.tablescroll,
            lengthChange: true,
            searching: true,
            ordering: true,
            info: true,
            autoWidth: false,
            language: {
                paginate: {
                    previous: '&laquo;',
                    next: '&raquo;'
                }
            }
        });

        // Draw rows if we have initial data
        if (this.tabledata) {
            this.prepareRows(this.tabledata);
            this.fillTable();
        }

        if (this.showsDetails) {
            $(() => {
                $(vm.$el).find('table tbody').on('click', 'tr', function () {
                    vm.$emit(vm.tableaction, vm.dtHandle.row( this ).data()[0]);
                } );
            });
        }

        // if column select option is to display, configure the selecpicker
        if(this.select_columns){
            // update the select text format "items selected" into "columns displayed"
            $('.datatable-select-column').selectpicker({
                countSelectedText: function (numSelected, numTotal) {
                    return (numSelected == 1) ? "{0} column displayed" : "{0} columns displayed";
                },
                showTick: true,
                iconBase: 'icon wb-check',
                tickIcon: 'check-mark',
            })

            // hide the initially hidden columns
            this.selectColumnChanged();
        }

        // update the fields when snippet (custom fields) was deleted
        this.$root.$on('prospectFilterUpdate', (filter) => {
            this.dtHandle.ajax.reload();
        });

        // when prospect's state is update, reload datatables but keep the current page
        this.$root.$on('prospectStateUpdate', (filter) => {
            this.dtHandle.ajax.reload( null, false );
        });
    },
});
