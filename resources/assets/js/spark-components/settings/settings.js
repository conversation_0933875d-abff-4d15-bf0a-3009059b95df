var base = require('settings/settings');
var tabState = require('./../mixins/tab-state.js');

Vue.component('spark-settings', {
    mixins: [base, tabState],

    props: ['user', 'teams', 'agency'],

    mounted() {
        // check for 2nd lvl of tabs in integration
        // "/settings#/integrations/proxy-setting-tab"
        var hash = window.location.hash.substring(2);
        var parameters = hash.split('/');

        if (parameters[1]) {
            setTimeout(()=>{
                $('#settingsIntegrationTab a[href="#'+parameters[1]+'"]').tab('show')
            }, 300)
        }
    }
});
