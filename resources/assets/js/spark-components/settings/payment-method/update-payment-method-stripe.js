var base = require('settings/payment-method/update-payment-method-stripe');

Vue.component('spark-update-payment-method-stripe', {
    mixins: [base],

    props: ['from'],

    data() {
        return {
            addressForm: new SparkForm({
                address: '',
                address_line_2: '',
                city: '',
                state: '',
                zip: '',
                country: ''
            }),
            label: {
                title: 'Update Payment Details',
                btn: 'Update',
                btnbusy: 'Updating'
            },
            openedByUpgrade: false,
            openedByResearch: false,
            openedByLinkedin: false,
        }
    },

    mounted() {
        this.initializeBillingAddress();

        if(!this.user.pm_country && !this.user.pm_last_four){
            this.label.title = 'Add Payment Details';
            this.label.btn = 'Add';
            this.label.btnbusy = 'Adding';
        }

        /**
         * Open payment modal when required before upgrading to plans
         */
        this.$root.$on('openPaymentFromUpgrade', () => {
            this.openedByUpgrade = true;
        });

        if(this.from && this.from == 'email'){
            // $('#updatePaymentModal').modal('show');
        }

        this.$root.$on('openPaymentFromResearch', () => {
            this.openedByResearch = true;
        });

        this.$root.$on('openPaymentFromLinkedin', () => {
            this.openedByLinkedin = true;
        });

        if(this.from && this.from == 'research'){
            // $('#updatePaymentModal').modal('show');
        }
    },

    methods: {
        handlePaymentMethodUpdateSuccess() {
            Bus.$emit('paymentMethodUpdated');

            if (this.form.successful && (this.from && this.from == 'email')) {
                setTimeout(() => {
                    window.location = '/email-accounts/create';
                }, 1000);
            } else if (this.form.successful &&
                ((this.from && this.from == 'research') || this.openedByResearch)) {
                setTimeout(() => {
                    window.location = '/research-projects';
                }, 1000);
            } else if (this.form.successful &&
                (this.from && this.from == 'linkedin-data')) {
                setTimeout(() => {
                    window.location = '/linkedin-subscriptions/create';
                }, 1000);
            } else if (this.form.successful && this.openedByUpgrade) {
                setTimeout(() => {
                    window.location = '/settings?from=upgrade#/subscription';
                }, 1000);
            }
        },

        updateBillingAddress() {
            // this.form.busy = true;
            this.addressForm.busy = true;
            this.addressForm.errors.forget();
            this.addressForm.successful = false;

            Spark.put('/settings/billing/address', this.addressForm)
                .then(
                    (response) => {
                        this.addressForm.busy = false;
                        this.addressForm.successful = true;
                    },
                    (error) => {
                        this.addressForm.busy = false;
                    }
                )
        },

        /**
         * Initialize the billing address form for the billable entity.
         */
        initializeBillingAddress() {
            if (! Spark.collectsBillingAddress) {
                return;
            }

            this.addressForm.address = this.billable.billing_address;
            this.addressForm.address_line_2 = this.billable.billing_address_line_2;
            this.addressForm.city = this.billable.billing_city;
            this.addressForm.state = this.billable.billing_state;
            this.addressForm.zip = this.billable.billing_zip;
            this.addressForm.country = this.billable.billing_country || '';
        },
    }
});
