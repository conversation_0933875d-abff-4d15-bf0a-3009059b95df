module.exports = {
    props: ['user', 'team', 'billableType', 'clientSecret'],

    /**
     * The component's data.
     */
    data() {
        return {
            form: new SparkForm({
                payment_method: '',
            }),

            stripeError: '',
            stripeElementStyles: {
                base: {
                    color: '#76838f',
                    fontSize: '14px',
                    fontWeight: '100',
                    fontFamily: '"Roboto", sans-serif',
                    height: '36.0167px',
                    lineHeight: '22px',
                    '::placeholder': {
                        fontWeight: '600',
                        color: '#b9bbc1'
                    },
                },
                invalid: {
                    color: 'red',
                    ':focus': {
                        color: 'red',
                    },
                    '::placeholder': {
                        color: 'red',
                    },
                },
            },
            stripeElementClasses: {
                focus: 'focus',
                empty: 'empty',
                invalid: 'invalid',
            },
            cardElement: {},
        };
    },


    /**
     * Prepare the component.
     */
    mounted() {
        this.initializeBillingAddress();

        if (!this.$root.stripeElementMounted) {
            this.cardElement = elements.create('card', {
                hidePostalCode: true,
                style: this.stripeElementStyles,
                classes: this.stripeElementClasses,
                showIcon: true,
            });
            this.cardElement.mount('#stripe-card');

            let self = this;
            this.cardElement.on('change', function(event) {
                self.stripeError = '';
            });

            this.$root.stripeElementMounted = true;
        }
    },


    methods: {
        /**
         * Initialize the billing address form for the billable entity.
         */
        initializeBillingAddress() {
            if (! Spark.collectsBillingAddress) {
                return;
            }

            this.form.address = this.billable.billing_address;
            this.form.address_line_2 = this.billable.billing_address_line_2;
            this.form.city = this.billable.billing_city;
            this.form.state = this.billable.billing_state;
            this.form.zip = this.billable.billing_zip;
            this.form.country = this.billable.billing_country || 'US';
        },


        /**
         * Update the billable's card information.
         */
        update() {
            this.form.busy = true;
            this.stripeError = '';
            this.form.errors.forget();
            this.form.successful = false;

            let cardEmail = this.billable.email;
            let cardName = this.billable.name;

            stripe.confirmCardSetup ( this.clientSecret, {
                    payment_method: {
                        card: this.cardElement,
                        billing_details: {
                            name: cardName,
                            email: cardEmail
                        }
                    }
                }
            )
            .then((response) => {
                if (response.error) {
                    this.stripeError = response.error.message;
                    this.form.busy = false;
                } else {
                    this.form.successful = true;
                    this.form.busy = false;
                    this.sendUpdateToServer(response.setupIntent.payment_method);
                }
            });
        },


        /**
         * Send the credit card update information to the server.
         */
        sendUpdateToServer(paymentMethod) {
            // this.form.stripe_token = token;
            this.form.payment_method = paymentMethod;

            Spark.put(this.urlForUpdate, this.form)
                .then(() => {
                    Bus.$emit('updateUser');
                    Bus.$emit('updateTeam');

                    // update agency 'setup'
                    if (this.form.successful) {
                        axios.put('/agency/setup/'+this.user.agency.hashid+'/finish')
                            .then((response)=>{
                                // console.log(response);
                            }).catch((error)=>{

                        });
                    }

                    this.label.title = 'Update Payment Details';
                    this.label.btn = 'Update';
                    this.label.btnbusy = 'Updating';

                    this.cardElement.clear();

                    if ( ! Spark.collectsBillingAddress) {
                        this.form.zip = '';
                    }

                    this.handlePaymentMethodUpdateSuccess();
                });
        },

        handlePaymentMethodUpdateSuccess() {
            alert('Payment method updated!');
        }
    },


    computed: {
        /**
         * Get the billable entity's "billable" name.
         */
        billableName() {
            return this.billingUser ? this.user.name : this.team.owner.name;
        },


        /**
         * Get the URL for the payment method update.
         */
        urlForUpdate() {
            return this.billingUser
                            ? '/settings/payment-method'
                            : `/settings/${Spark.pluralTeamString}/${this.team.hashid}/payment-method`;
        },


        /**
         * Get the proper brand icon for the customer's credit card.
         */
        cardIcon() {
            if (! this.billable.pm_country) {
                return 'fa-cc-stripe';
            }

            switch (this.billable.pm_country) {
                case 'American Express':
                    return 'fa-cc-amex';
                case 'Diners Club':
                    return 'fa-cc-diners-club';
                case 'Discover':
                    return 'fa-cc-discover';
                case 'JCB':
                    return 'fa-cc-jcb';
                case 'MasterCard':
                    return 'fa-cc-mastercard';
                case 'Visa':
                    return 'fa-cc-visa';
                default:
                    return 'fa-cc-stripe';
            }
        },


        /**
         * Get the placeholder for the billable entity's credit card.
         */
        placeholder() {
            if (this.billable.pm_last_four) {
                return `************${this.billable.pm_last_four}`;
            }

            return 'Card number';
        }
    }
};
