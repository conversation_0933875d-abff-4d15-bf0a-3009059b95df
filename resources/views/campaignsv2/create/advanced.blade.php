<div class="col-md-12 pt-10" v-if="isAdvancedOpened">
    <div class="alert bg-blue-grey-100">
        <h4 class="mb-30 mt-20">
            <button type="button" class="setting-close btn btn-sm btn-default" 
                aria-label="Close"
                @click.prevent="toggleAdvanceSettings(false)"
            >
                <span aria-hidden="true">Close</span>
            </button>
            Advanced Settings
        </h4>
        <div class="row mb-0">
            <div class="mb-30 col-sm-12 col-md-12 col-lg-6">
                <label class="d-block clearfix">
                    Auto Forward Positive
                    <span class="float-left mr-10" data-toggle="tooltip" 
                        data-placement="top"
                        title="Automatically forward the email threads of positive contacts to this email address"
                    >
                        <i class="wb-info-circle"></i>
                    </span>
                </label>
                
                <input type="text" placeholder="Enter email address"
                    value="{{old('forward_positive')}}" 
                    name="forward_positive"
                    class="form-control {{ $errors->has('forward_positive') ? ' is-invalid' : '' }}"
                >

                @if ($errors->has('forward_positive'))
                    <div class="text-danger">
                        <span>{{ $errors->first('forward_positive') }}</span>
                    </div>
                @endif
            </div>

            <div class="mb-30 col-sm-12 col-md-12 col-lg-6">
                <label class="d-block clearfix">
                    Auto Forward Neutral
                    <span class="float-left mr-10" data-toggle="tooltip" 
                        data-placement="top"
                        title="Automatically forward the email threads of nuetral contacts to this email address"
                    >
                        <i class="wb-info-circle"></i>
                    </span>
                </label>
                
                <input type="text" placeholder="Enter email address"
                    value="{{old('forward_neutral')}}" 
                    name="forward_neutral"
                    class="form-control {{ $errors->has('forward_neutral') ? ' is-invalid' : '' }}"
                >

                @if ($errors->has('forward_neutral'))
                    <div class="text-danger">
                        <span>{{ $errors->first('forward_neutral') }}</span>
                    </div>
                @endif
            </div>

            <div class="mb-30 col-sm-12 col-md-5 col-lg-5">
                <label class="d-block clearfix">
                    Stop Colleagues
                    <span class="float-left mr-10" data-toggle="tooltip" data-placement="top"
                          title="When a contact replies, automatically stop other contacts at the same domain."
                    >
                        <i class="wb-info-circle"></i>
                    </span>
                </label>
                <span class="d-inline-block">
                    <span class="checkbox-custom bordered-dark checkbox-default d-inline-block m-0">
                        <input type="checkbox" value="1" 
                            name="stop_colleagues" 
                            id="stop_colleagues"
                            {{ old('stop_colleagues') == '1' ? 'checked' : '' }}
                        >
                        <label for="stop_colleagues">
                            Automatically stop colleagues when a contact replies
                        </label>
                    </span>
                </span>
            </div>

            <div class="mb-30 col-sm-12 col-md-1 col-lg-1"></div>

            <div class="mb-30 col-sm-12 col-md-5 col-lg-5">
                <label class="d-block clearfix">
                    Auto Archive
                    <span class="float-left mr-10" data-toggle="tooltip" data-placement="top"
                          title="These types of messages will automatically be moved to the 'Archive' folder. If this folder does not exist, it will be created."
                    >
                        <i class="wb-info-circle"></i>
                    </span>
                </label>
                <span class="d-inline-block">
                    <span class="checkbox-custom bordered-dark checkbox-default d-inline-block m-0">
                        <input type="checkbox" value="1" 
                            name="archive_bounces" 
                            id="archive_bounces"
                            {{ old('archive_bounces') == '1' ? 'checked' : '' }}
                        >
                        <label for="archive_bounces">
                            Bounced messages
                        </label>
                    </span>
                </span>
                <span class="d-inline-block ml-50">
                    <span class="checkbox-custom bordered-dark checkbox-default d-inline-block m-0">
                        <input type="checkbox" value="1" 
                            name="archive_autoreplies" 
                            id="archive_autoreplies"
                            {{ old('archive_autoreplies') == '1' ? 'checked' : '' }}
                        >
                        <label for="archive_autoreplies">
                            Autoreplies
                        </label>
                    </span>
                </span>
            </div>

            <div class="mb-30 col-sm-12 col-md-5 col-lg-5">
                <label class="d-block clearfix mb-0">
                    Start Campaign
                    <span class="float-left mr-10" data-toggle="tooltip" data-placement="top"
                        title="Schedule campaign start time."
                    >
                        <i class="wb-info-circle"></i>
                    </span>
                </label>
                <span class="d-inline-block">
                    <div class="d-inline-block">
                        <div class="radio-custom radio-default bordered-dark">
                            <input type="radio" id="startImmediately" name="start_option" value="auto" v-model="startOption">
                            <label for="startImmediately">
                                Immediately
                            </label>
                        </div>
                    </div>
                    <div class="d-inline-block ml-50">
                        <div class="radio-custom radio-default bordered-dark">
                            <input type="radio" id="startSchedule" name="start_option" value="schedule" v-model="startOption">
                            <label for="startSchedule">
                                <span v-show="startOption=='auto'">
                                    On scheduled time
                                </span>
                            </label>
                            <v-date-picker v-model="startTime" mode="dateTime" :minute-increment="10" v-show="startOption=='schedule'">
                                <template v-slot="{ inputValue, inputEvents }">
                                    <input
                                        class="px-2 py-1 border rounded focus:outline-none focus:border-blue-300"
                                        :value="inputValue"
                                        v-on="inputEvents"
                                        name="start_at"
                                    />
                                </template>
                            </v-date-picker>
                        </div>
                    </div>

                    @if ($errors->has('start_at'))
                        <div class="text-danger">
                            <small>{{ $errors->first('start_at') }}</small>
                        </div>
                    @endif
                </span>
            </div>

            <div class="mb-30 col-sm-12 col-md-1 col-lg-1"></div>

            <div class="mb-30 col-sm-12 col-md-5 col-lg-5">
                <label class="d-block clearfix mb-0">
                    Stop Campaign
                    <span class="float-left mr-10" data-toggle="tooltip" data-placement="top"
                        title="Schedule campaign stop time."
                    >
                        <i class="wb-info-circle"></i>
                    </span>
                </label>
                <span class="d-inline-block">
                    <div class="d-inline-block">
                        <div class="radio-custom radio-default bordered-dark">
                            <input type="radio" id="stopOnComplete" name="stop_option" value="auto" v-model="stopOption">
                            <label for="stopOnComplete">
                                When complete
                            </label>
                        </div>
                    </div>
                    <div class="d-inline-block ml-50">
                        <div class="radio-custom radio-default bordered-dark">
                            <input type="radio" id="stopSchedule" name="stop_option" value="schedule" v-model="stopOption">
                            <label for="stopSchedule">
                                <span v-show="stopOption=='auto'">
                                    On scheduled time
                                </span>
                            </label>
                            <v-date-picker v-model="stopTime" mode="dateTime" :minute-increment="10" v-show="stopOption=='schedule'">
                                <template v-slot="{ inputValue, inputEvents }">
                                    <input
                                        class="px-2 py-1 border rounded focus:outline-none focus:border-blue-300"
                                        :value="inputValue"
                                        v-on="inputEvents"
                                        name="stop_at"
                                    />
                                </template>
                            </v-date-picker>
                        </div>
                    </div>
                    @if ($errors->has('stop_at'))
                        <div class="text-danger">
                                <small>{{ $errors->first('stop_at') }}</small>
                        </div>
                    @endif
                </span>
            </div>

            <div class="mb-30 col-sm-12 col-md-5 col-lg-5">
                <label class="d-block clearfix">
                    Standby Mode
                    <span class="float-left mr-10" data-toggle="tooltip" data-placement="top"
                          title="Campaign can start and will remain in RUNNING state even without prospects."
                    >
                        <i class="wb-info-circle"></i>
                    </span>
                </label>
                <span class="d-inline-block">
                    <span class="checkbox-custom bordered-dark checkbox-default d-inline-block m-0">
                        <input type="checkbox" value="1"
                               name="is_on_standby"
                               id="is_on_standby"
                            {{ old('is_on_standby') == '1' ? 'checked' : '' }}
                        >
                        <label for="is_on_standby">
                            Enable campaign standby. Will never complete.
                        </label>
                    </span>
                </span>
            </div>
            <div class="mb-30 col-sm-12 col-md-1 col-lg-1"></div>
            <div class="mb-30 col-sm-12 col-md-5 col-lg-5"></div>
            {{--
            <div class="mb-30 col-sm-12 col-md-6 col-lg-6">
                <label class="d-block clearfix">
                    Webhook URL
                    <span class="float-left mr-10" data-toggle="tooltip" 
                        data-placement="top"
                        title="Webhooks allow campaign to notify you about changes on contact activities."
                    >
                        <i class="wb-info-circle"></i>
                    </span>
                </label>
                
                <input type="text" placeholder="Enter email address"
                    value="{{old('webhook_url')}}" 
                    name="webhook_url"
                    class="form-control {{ $errors->has('webhook_url') ? ' is-invalid' : '' }}"
                >

                @if ($errors->has('webhook_url'))
                    <div class="text-danger">
                        <span>{{ $errors->first('webhook_url') }}</span>
                    </div>
                @endif
            </div>
            <div class="mb-30 col-sm-12 col-md-6 col-lg-6">
                <label class="d-block clearfix mb-15">
                    Webhool Triggers
                    <span class="float-left mr-10" data-toggle="tooltip" data-placement="top"
                          title="Trigger webhook based on these activities"
                    >
                        <i class="wb-info-circle"></i>
                    </span>
                </label>
                <span class="d-inline-block">
                    <span class="checkbox-custom bordered-dark checkbox-default d-inline-block m-0">
                        <input type="checkbox" value="1" 
                            name="webhook_triggers_replied" 
                            id="webhook_triggers_replied"
                            {{ old('webhook_triggers_replied') == '1' ? 'checked' : '' }}
                        >
                        <label for="webhook_triggers_replied">
                            Contact Replied
                        </label>
                    </span>
                </span>
                <span class="d-inline-block ml-50">
                    <span class="checkbox-custom bordered-dark checkbox-default d-inline-block m-0">
                        <input type="checkbox" value="1" 
                            name="webhook_triggers_positive" 
                            id="webhook_triggers_positive"
                            {{ old('webhook_triggers_positive') == '1' ? 'checked' : '' }}
                        >
                        <label for="webhook_triggers_positive">
                            Contact Positive
                        </label>
                    </span>
                </span>
            </div>
            --}}

        </div>
    </div>
</div>