<div id="campaign-list" v-if="arrCampaigns.data.length">
    <div class="card card-shadow mb-5" v-for="(campaign, index) in arrCampaigns.data">
        <div class="card-block pb-15">
            <div class="row">
                <div class="col-lg-10 col-xl-10 col-md-9 col-sm-8 col-8">
                    <div class="row">
                        <div class="col-md-12 col-lg-5">
                            <h4 class="m-0">
                                <span class="d-block text-truncate">
                                    @if (Auth::user()->can('campaign.admin') || Auth::user()->can('support'))
                                        <a :href="'/campaigns/'+campaign.hashid" class="text-decoration-none blue-grey-700">
                                            @{{campaign.name}}
                                        </a>
                                    @else
                                        <a :href="'/stats/campaign/'+campaign.hashid" class="text-decoration-none blue-grey-700">
                                            @{{campaign.name}}
                                        </a>
                                    @endcan
                                </span>
                                <small class="d-block pt-5 text-truncate">
                                    <span class="mr-10 d-inline-block">
                                        <small v-if="campaign.status == 'PAUSED'" class="w-60 text-center badge badge-danger">paused</small>
                                        <small v-if="campaign.status == 'RUNNING'" class="w-60 text-center badge badge-success">running</small>
                                        <small v-if="campaign.status == 'DRAFT'" class="w-60 text-center badge badge-dark">draft</small>
                                        <small v-if="campaign.status == 'EDITED'" class="w-60 text-center badge badge-info">edited</small>
                                        <small v-if="campaign.status == 'COMPLETED'" class="w-60 text-center badge badge-primary">complete</small>
                                        <small v-if="campaign.status == 'STOPPED'" class="w-60 text-center badge badge-danger">stopped</small>
                                        <small v-if="campaign.status == 'ARCHIVED'" class="w-60 text-center badge badge-secondary">archived</small>
                                        <small v-if="campaign.prospects_missing_data_count > 0" class="w-80 text-center badge badge-warning">missing-data</small>
                                    </span>

                                    @if (Auth::user()->agency->isAgencyDashboard())
                                        <span class="mx-10">&bull;</span>
                                    @endif

                                    @can('support')
                                        <span class="text-capitalize">@{{campaign.agency.name}}</span>
                                    @else
                                        @if(Auth::user()->agency->isAgencyDashboard())
                                            <span class="text-capitalize" v-if="campaign.team">@{{campaign.team.name}}</span>
                                            <span class="text-capitalize" v-else>No Client</span>
                                        @endif
                                    @endcan
                                    <span class="mx-10">&bull;</span>
                                    <span v-if="campaign.email_accounts.length == 1">
                                        <span v-if="campaign.email_accounts[0].error == 'account.invalid'"
                                              data-toggle="tooltip" data-placement="top"
                                              title="Invalid email credentials, click to re-authenticate."
                                        >
                                            <a target="_blank" class="text-danger"
                                               :href="'/email-accounts/'+campaign.email_accounts[0].hashid+'/reauth'"
                                            >
                                                <i class="fa fa-exclamation text-danger"></i>
                                                @{{campaign.email_accounts[0].email_address}}
                                            </a>
                                        </span>
                                        <span v-else-if="campaign.email_accounts[0].error == 'account.reauth'"
                                              data-toggle="tooltip" data-placement="top"
                                              title="Re-authentication incomplete, click to re-authenticate."
                                        >
                                            <a target="_blank" class="text-danger"
                                               :href="'/email-accounts/'+campaign.email_accounts[0].hashid+'/reauth'"
                                            >
                                                <i class="fa fa-exclamation text-danger"></i>
                                                @{{campaign.email_accounts[0].email_address}}
                                            </a>
                                        </span>
                                        <span v-else-if="campaign.email_accounts[0].error == 'account.stopped' || campaign.email_accounts[0].error == 'sending.failed' || campaign.email_accounts[0].error == 'sync.timeout'"
                                              data-toggle="tooltip" data-placement="top"
                                              title="Email account stopped, click to view account details."
                                        >
                                            <a target="_blank" class="text-danger"
                                               :href="'/email-accounts/'+campaign.email_accounts[0].hashid+'/edit'"
                                            >
                                                <i class="fa fa-ban text-danger"></i>
                                                @{{campaign.email_accounts[0].email_address}}
                                            </a>
                                        </span>
                                        <span v-else>
                                            @{{campaign.email_accounts[0].email_address}}
                                        </span>
                                    </span>

                                    <span v-if="campaign.email_accounts.length > 1">
                                        @{{campaign.email_accounts[0].email_address}}
                                        <small class="text-muted font-size-12">
                                            (+@{{campaign.email_accounts.length-1}})
                                        </small>
                                        <span v-if="checkInvalidEmails(campaign.email_accounts)"
                                            data-toggle="tooltip" data-placement="top"
                                            title="One of the emails is not working."
                                        >
                                            <i class="fa fa-exclamation text-danger"></i>
                                        </span>
                                    </span>
                                    <span v-if="!campaign.email_accounts.length">No Email</span>
                                </small>
                            </h4>
                        </div>
                        <div class="col-md-12 col-lg-7 hidden-md-down text-center">
                            <h4 class="m-0 d-inline-block text-center px-10">
                                @{{campaign.prospects_count}}
                                <small class="d-block pt-5">Contacts</small>
                            </h4>
                            <h4 class="percent m-0 d-inline-block text-center px-10">
                                <span v-if="campaign.stats">
                                    @{{ campaign?.stats?.queue }}
                                </span>
                                <span v-else>0<small class="percent-sign">%</small></span>
                                <small class="d-block pt-5">Queued</small>
                            </h4>
                            <h4 class="percent m-0 d-inline-block text-center px-10 campaign-stat-replied">
                                <span v-if="campaign.stats">
                                    @{{ getActivityPercent(campaign.stats, 'replied').toFixed(0) }}<small class="percent-sign">%</small>
                                </span>
                                <span v-else>0<small class="percent-sign">%</small></span>
                                <small class="d-block pt-5">Replied</small>
                            </h4>
                            <h4 class="percent m-0 d-inline-block text-center px-10">
                                <span v-if="campaign.stats">
                                    @{{ getActivityPercent(campaign.stats, 'positive') }}<small class="percent-sign">%</small>
                                </span>
                                <span v-else>0<small class="percent-sign">%</small></span>
                                <small class="d-block pt-5">Positive</small>
                            </h4>
                            <h4 class="percent m-0 d-inline-block text-center px-10">
                                <span v-if="campaign.stats">
                                    @{{ getOpenRate(campaign) }}<small class="percent-sign">%</small>
                                </span>
                                <span v-else>0<small class="percent-sign">%</small></span>
                                <small class="d-block pt-5">Open Rate</small>
                            </h4>
                            <h4 class="percent m-0 d-inline-block text-center px-10 campaign-stat-bounced">
                                <span v-if="campaign.stats">
                                    @{{ getBouncedPercent(campaign) }}<small class="percent-sign">%</small>
                                </span>
                                <span v-else>0<small class="percent-sign">%</small></span>
                                <small class="d-block pt-5">Bounced</small>
                            </h4>
                            <h4 class="percent m-0 d-inline-block text-center px-10">
                                <span v-if="campaign.stats">
                                    @{{ getCompletedPercentage(campaign).toFixed(0) }}<small class="percent-sign">%</small>
                                </span>
                                <span v-else>0<small class="percent-sign">%</small></span>
                                <small class="d-block pt-5">Progress</small>
                            </h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-xl-2 col-lg-2 col-sm-4 col-4 text-right pt-3 pl-0">
                    @if (Auth::user()->can('campaign.admin') || Auth::user()->can('support'))
                        <a :href="'/campaigns/'+campaign.hashid" class="btn btn-default px-5"
                            data-toggle="tooltip" data-placement="top"
                            title="Click here to edit this campaign"
                        >
                            <i aria-hidden="true" class="icon wb-settings"></i>
                        </a>
                        <a :href="'/stats/campaign/'+campaign.hashid" class="btn btn-default px-5"
                            data-toggle="tooltip" data-placement="top"
                            title="Click here to view campaign's stats"
                        >
                            <i aria-hidden="true" class="icon wb-stats-bars"></i>
                        </a>

                        <button @click.prevent="campaignStatus('pause', campaign, 'Pause', 'Paused')"
                            v-if="campaign.status == 'RUNNING'"
                            class=" px-5 btn btn-yellow"
                        >
                            <i class="icon wb-pause" aria-hidden="true"></i>
                            <span class="hidden-lg-down w-50 d-inline-block text-left">Pause</span>
                        </button>
                        <button @click.prevent="campaignStatus('initialize', campaign, 'Start', 'Started')"
                            v-if="campaign.status == 'DRAFT' && campaign.setup_step == 3"
                            class=" px-5 btn btn-primary"
                        >
                            <i class="icon wb-play" aria-hidden="true"></i>
                            <span class="hidden-lg-down w-50 d-inline-block text-left">&nbsp; Start &nbsp;</span>
                        </button>
                        <button disabled class=" px-5 btn btn-secondary"
                            v-if="campaign.status == 'DRAFT' && campaign.setup_step < 3"
                        >
                            <i class="icon wb-play" aria-hidden="true"></i>
                            <span class="hidden-lg-down w-50 d-inline-block text-left">Underway</span>
                        </button>
                        <button @click.prevent="campaignStatus('resume', campaign, 'Resume', 'Resumed')"
                            v-if="campaign.status == 'PAUSED'"
                            class=" px-5 btn btn-success"
                        >
                            <i class="icon wb-play" aria-hidden="true"></i>
                            <span class="hidden-lg-down w-50 d-inline-block text-left">Resume</span>
                        </button>
                        <button @click.prevent="campaignStatus('archive', campaign, 'Archive', 'Archived')"
                            v-if="campaign.status == 'COMPLETED'"
                            class=" px-5 btn btn-blue"
                        >
                            <i class="icon wb-folder" aria-hidden="true"></i>
                            <span class="hidden-lg-down w-50 d-inline-block text-left">Archive</span>
                        </button>
                        <button v-if="campaign.status == 'ARCHIVED'" disabled
                            class=" px-5 btn btn-outline-secondary disabled cd"
                        >
                            <i class="icon wb-folder" aria-hidden="true"></i>
                            <span class="hidden-lg-down w-60 d-inline-block text-left">Archived</span>
                        </button>
                        <button v-if="campaign.status == 'STOPPED'"
                            @click.prevent="campaignStatus('resume', campaign, 'Resume', 'Resumed')"
                            class=" px-5 btn btn-success"
                        >
                            <i class="icon wb-play" aria-hidden="true"></i>
                            <span class="hidden-lg-down w-50 d-inline-block text-left">Resume</span>
                        </button>
                    @else
                        <a :href="'/stats/campaign/'+campaign.hashid" class="btn btn-default">
                            <i aria-hidden="true" class="icon wb-stats-bars hidden-md"></i>
                            <span class="hidden-lg-down">Stats</span>
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="py-30">
        <pagination :data="arrCampaigns" :limit="2" @pagination-change-page="getCampaigns"></pagination>
    </div>
</div>
<div v-else>
    <div class="panel">
        <div class="panel-body">
            <div class="alert alert-warning alert-icon">
                <i class="wb-alert-circle icon"></i>
                No campaigns yet.
                @can('campaign.admin')
                    Let's <a href="{{url('campaigns/create')}}">create one</a>.
                @endcan
            </div>
        </div>
    </div>
</div>
