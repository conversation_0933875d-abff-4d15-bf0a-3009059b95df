<div class="panel panel-bordered">
    <div class="panel-body">
        <div class="px-15">
            <div class="row mb-20">
                <div class="col-9 col-sm-9 col-lg-6 col-xl-7 col-md-7">
                    <h4 class="pt-5 mt-3 text-truncate">
                        Sending Configuration
                    </h4>
                </div>
            </div>
        </div>

        <div class="px-15">
            <div class="row pt-10">
                <div class="col-md-12">
                    <div class="row" v-if="email.warmup_status == 'off' || email.warmup_status == 'done'">
                        <div class="col-md-12 mb-20">
                            <div class="alert-icon alert alert-primary">
                                <i class="icon wb-pluse"></i>
                                <p class="mb-5">This account has a daily limit of <strong>@{{email.send_limit}}</strong> emails with a random send interval of <strong>@{{email.min_interval}}</strong>
                                to <strong>@{{email.max_interval}}</strong> seconds. It will send an average of <strong>@{{maxEmailsPerHour}}</strong> emails per hour.</p>
                            </div>
                        </div>

                        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6">
                            <label>Email Campaign Warm Up</label>
                            <div class="row" v-if="email.warmup_status == 'off'">
                                <div class="col-sm-6">
                                    <div class="checkbox-custom checkbox-primary mt-5">
                                        <input type="checkbox" id="warmupOffCheck"
                                            v-model="email.warmup_status"
                                            true-value="on"
                                            false-value="off"
                                            @click="warmStatusClicked"
                                        >
                                        <label for="warmupOffCheck">On / Off</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row" v-else>
                                <div class="col-md-12">
                                    <div class="py-5 alert alert-success">
                                        <p class="mb-3">
                                            <i class="icon wb-check-circle"></i> <strong>Complete</strong>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6">
                            <label>Daily Limit</label>
                            <div class="input-group">
                                <input v-model="email.send_limit"
                                    type="number"
                                    class="form-control"
                                    placeholder="60"
                                    :class="{'is-invalid': errors.send_limit}"
                                    @cannot('campaign.admin') disabled @endcan
                                >
                                <span class="input-group-addon">emails</span>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mt-20">
                            <label class="d-block clearfix">
                                Minimum Interval
                                <span class="float-right" data-toggle="tooltip"
                                    data-placement="left"
                                    id="intervalMinTooltip"
                                    title="Enter between 20-500 seconds and should be lower than the Max Interval"
                                >
                                    <i class="wb-info-circle"></i>
                                </span>
                            </label>
                            <div class="input-group">
                                <input v-model="email.min_interval"
                                    type="number"
                                    class="form-control px-5"
                                    placeholder="120"
                                    :class="{'is-invalid': errors.min_interval}"
                                    @input="checkThreshold('min')"
                                    @cannot('campaign.admin') disabled @endcan
                                >
                                <span class="input-group-addon">seconds</span>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mt-20">
                            <label class="d-block clearfix">
                                Max Interval
                                <span class="float-right" data-toggle="tooltip"
                                    data-placement="left"
                                    id="intervalMaxTooltip"
                                    title="Enter between 30-600 seconds and should be higher than the Minimum Interval"
                                >
                                    <i class="wb-info-circle"></i>
                                </span>
                            </label>
                            <div class="input-group">
                                <input v-model="email.max_interval"
                                    type="number"
                                    class="form-control px-5"
                                    placeholder="240"
                                    :class="{'is-invalid': errors.max_interval}"
                                    @cannot('campaign.admin') disabled @endcan
                                    @input="checkThreshold('max')"
                                >
                                <span class="input-group-addon">seconds</span>
                            </div>
                        </div>

                        <div class="col-md-12 text-center mt-10">
                            <span class="text-danger pr-5" v-if="errors.send_limit">
                                @{{errors.send_limit[0]}}
                            </span>
                            <span class="text-danger pr-5" v-if="errors.min_interval">
                                @{{errors.min_interval[0]}}
                            </span>
                            <span class="text-danger pr-5"
                                v-if="errors.max_interval && !errors.min_interval"
                            >
                                @{{errors.max_interval[0]}}
                            </span>
                        </div>
                    </div>

                    <div class="row" v-else>
                        <div class="col-md-12 mb-20">
                            <div class=" alert alert-success">
                                <p class="mb-10">
                                    <strong>Warmup Progress</strong>
                                    <strong class="pull-right">
                                        @{{warmup_config.days}} / 60 days
                                    </strong>
                                </p>

                                <div id="warmupBox">
                                    <div class="contextual-progress">
                                        <div class="progress progress-sm bg-grey-100 mb-20">
                                            <div class="progress-bar progress-bar-indicating active progress-bar-success"
                                                :style="'width: '+ warmup_progress +'%;'"
                                                role="progressbar"
                                            ></div>
                                        </div>
                                    </div>

                                    <div id="warmupSteps">
                                        <div class="warmstep warmstep1" data-toggle="tooltip" data-placement="top"
                                            title="0-2 days: daily limit of 15 emails with a random send interval of 450 to 500 seconds"
                                            :class="{'is-warmed': checkDaysWarmed(0, 2)}"
                                        >
                                            <i class="wb-medium-point"></i>
                                        </div>
                                        <div class="warmstep warmstep2" data-toggle="tooltip" data-placement="top"
                                            title="3-6 days: daily limit of 25 emails with a random send interval of 450 to 500 seconds"
                                            :class="{'is-warmed': checkDaysWarmed(3, 6)}"
                                        >
                                            <i class="wb-medium-point"></i>
                                        </div>
                                        <div class="warmstep warmstep3" data-toggle="tooltip" data-placement="top"
                                            title="7-9 days: daily limit of 40 emails with a random send interval of 350 to 400 seconds"
                                            :class="{'is-warmed': checkDaysWarmed(7, 9)}"
                                        >
                                            <i class="wb-medium-point"></i>
                                        </div>
                                        <div class="warmstep warmstep4" data-toggle="tooltip" data-placement="top"
                                            title="10-16 days: daily limit of 70 emails with a random send interval of 250 to 300 seconds"
                                            :class="{'is-warmed': checkDaysWarmed(10, 16)}"
                                        >
                                            <i class="wb-medium-point"></i>
                                        </div>
                                        <div class="warmstep warmstep5" data-toggle="tooltip" data-placement="top"
                                            title="17-30 days: daily limit of 100 emails with a random send interval of 200 to 250 seconds"
                                            :class="{'is-warmed': checkDaysWarmed(17, 30)}"
                                        >
                                            <i class="wb-medium-point"></i>
                                        </div>
                                        <div class="warmstep warmstep6" data-toggle="tooltip" data-placement="top"
                                            title="31-60 days: daily limit of 150 emails with a random send interval of 120 to 240 seconds"
                                            :class="{'is-warmed': checkDaysWarmed(31, 60)}"
                                        >
                                            <i class="wb-medium-point"></i>
                                        </div>
                                        <div class="warmstep warmstep7" data-toggle="tooltip" data-placement="top"
                                            title="After 60 days, email warm up is done!"
                                            :class="{'is-warmed': checkDaysWarmed(60, 65)}"
                                        >
                                            <i class="wb-medium-point"></i>
                                        </div>
                                    </div>
                                </div>

                                <p class="mb-0">
                                    This account has a daily limit of <strong>@{{warmup_config.send_limit}}</strong>
                                    emails with a random send interval of
                                    <strong>@{{warmup_config.min_interval}}</strong>
                                    to <strong>@{{warmup_config.max_interval}}</strong> seconds.
                                    It will send an average of <strong>@{{maxWarmEmailsPerHour}}</strong>
                                    emails per hour.
                                </p>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mb-20">
                            <label>Email Campaign Warm Up</label>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="checkbox-custom checkbox-primary mt-5">
                                        <input type="checkbox" id="inputUnchecked"
                                            v-model="email.warmup_status"
                                            true-value="on"
                                            false-value="off"
                                            @click="warmStatusClicked"
                                        >
                                        <label for="inputUnchecked">On / Off</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mb-20">
                            <label>Warmup Daily Limit</label>
                            <div class="input-group">
                                <input v-model="warmup_config.send_limit"
                                    type="number"
                                    class="form-control"
                                    placeholder="60"
                                    disabled
                                >
                                <span class="input-group-addon">emails</span>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mb-20">
                            <label class="d-block clearfix">
                                Warmup Minimum Interval
                            </label>
                            <div class="input-group">
                                <input v-model="warmup_config.min_interval"
                                    type="number"
                                    class="form-control px-5"
                                    placeholder="120"
                                    disabled
                                >
                                <span class="input-group-addon">seconds</span>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mb-20">
                            <label class="d-block clearfix">
                                Warmup Max Interval
                            </label>
                            <div class="input-group">
                                <input v-model="warmup_config.max_interval"
                                    type="number"
                                    class="form-control px-5"
                                    placeholder="240"
                                    disabled
                                >
                                <span class="input-group-addon">seconds</span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="clearfix text-center pt-20">
                                <button class="btn btn-primary mx-20 px-50" type="button"
                                    @click="saveEmailWarmupSettings($event)"
                                >
                                    <i class="icon wb-chevron-right"></i>
                                    Save Changes
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
