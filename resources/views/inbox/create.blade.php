@extends('layouts.app')

@section('page-title', 'Add Email Account')

@section('page-styles')
  <style>
      .swal-modal .swal-text,
      .swal-modal .swal-footer {
          text-align: center !important;
      }

      #campaignSignatureEditor .toolbar li[title="align"],
      #campaignSignatureEditor .toolbar li[title="list"],
      #campaignSignatureEditor .toolbar li[title="table"] {
          display: none !important;
      }

      .tooltip-inner {
          max-width: 400px !important;
      }

      #payment-details .card .card-block {
          padding: 7px;
      }
  </style>
@endsection

@section('scripts')
  <script src="https://js.stripe.com/v3/"></script>
  <script>
      const stripe = Stripe('{{ config('cashier.key') }}');

      const elements = stripe.elements({
          fonts: [
              {
                  cssSrc: 'https://fonts.googleapis.com/css2?family=Roboto:wght@300&display=swap',
              },
          ],
      });
  </script>
@endsection

@section('page-scripts')
  <script src="//rawcdn.githack.com/RickStrahl/jquery-resizable/master/dist/jquery-resizable.min.js"></script>
@endsection

@section('content-layout')

  @can('admin')
    <nylasemail-add
      default-team="0"
      is-client="false"
      :gmail-imap-enabled="{{ json_encode(config('app.enableGmailImap')) }}"
      inline-template
    >
  @else
    <nylasemail-add
      :default-team="{{Auth::user()->current_team_id ? Auth::user()->current_team_id : 0}}"
      :is-client="@can('agency-admin') false @else true @endcan"
      needs-payment="{{$needsPayment}}"
      on-free-plan="{{$onFreePlan}}"
      :user="{{Auth::user()}}"
      :gmail-imap-enabled="{{ json_encode(config('app.enableGmailImap')) }}"
      inline-template
    >
  @endcan

      <div class="page loader-wrapper" :class="{'loader-wrapper-loading': isLoading}">
        <div class="page-header page-header-bordered">
          <h1 class="page-title">Add Email <span class="hidden-sm-down">Account</span></h1>
          <div class="page-header-actions">
            <a href="{{ route('email-accounts.index') }}" class="btn btn-outline btn-default btn-round">
              <span class="hidden-sm-down">Cancel</span>
              <i class="icon wb-minus-circle" aria-hidden="true"></i>
            </a>

            @if(config('app.emailHelpUrl') && Auth::user()->agency->wavo_version == 3)
              <a href="{{ config('app.emailHelpUrl') }}" target="_new" class="btn btn-outline btn-primary btn-round">
                <span class="hidden-sm-down">Need Help</span>
                <i class="icon fa-phone-square" aria-hidden="true"></i>
              </a>
            @endif
          </div>
        </div>

        <div class="page-content container-fluid">
          <div>
            @csrf
            <div class="row">
              <div class="col-md-12">
                {{-- debug --}}
{{--                client remaining emails:  {{$clientRemainingEmails}}<br>--}}
{{--                client max emails: {{$clientMaxEmails}}<br>--}}
{{--                total accounts: {{$totalAccountCount}}<br>--}}
{{--                total agency emails: {{$totalAgencyEmails}}<br>--}}
{{--                max agency emails: {{$maxAgencyEmails}}<br>--}}
{{--                remaining agency emails: {{$remainingAgencyEmails}}<br>--}}
{{--                client or team member: {{$isClientOrTeamMember}}<br>--}}
                {{-- debug --}}

                {{-- Alerts and messages for agency admins, not clients or team members --}}
                @can ('agency-admin')
                  @if (!$teams->count())
                    <div class="alert-icon alert alert-warning mb-40">
                      <i class="icon wb-alert-circle"></i>
                      <p class="mb-0"><strong>Create your first Client</strong></p>
                      <p>No clients found. Please create a client that will own the campaigns</p>
                      <p>
                        <a class="btn btn-primary" href="{{url('/clients/create')}}">
                          <i class="icon wb-plus"></i>
                          Create Client
                        </a>
                      </p>
                    </div>
                  @endif

                  @if ($totalAgencyEmails && !$onFreePlan && Auth::user()->isBillable() && !empty($subscriptionPrice))
                    <div class="alert-icon alert alert-warning">
                      <i class="icon wb-alert-circle"></i>
                      Adding another email account will add
                      <strong>${{$subscriptionPrice}}/month</strong>
                      to your invoice
                    </div>
                  @endif

                  @if ($needsPayment && !$onFreePlan)
                    <div class="alert-icon alert alert-warning" v-if="!hasPaymentDetails">
                      <i class="icon wb-alert-circle"></i>
                      <p class="mb-0"><strong>Payment details required</strong></p>
                      <p class="mb-0">
                        Please add your payment details in order to add an email account.
                      </p>
                      @if (!empty($subscriptionPrice))
                        <p class="mb-0">
                          Each additional Email Account is <strong>${{$subscriptionPrice}}/month</strong>
                        </p>
                        <p>
                          If you're not 100% satisfied, let us know in the first 30 days for a full refund.
                        </p>
                      @endif
                      @include('billing.add-payment-method')
                    </div>

                  @elseif ($pendingInvoice)
                    <div class="alert-icon alert alert-warning">
                      <i class="icon wb-alert-circle"></i>
                      <p class="mb-0"><strong>Pending invoice</strong></p>
                      <p class="mb-0">
                        There is an invoice pending, from previous attempt of adding an Email Account.
                      </p>
                      <p class="mb-0">
                        Previous charge attempt failed with the following message:<br>
                        {{Auth::user()->agency->owner->failed_charge}}
                      </p>
                      <p>
                        <a class="btn btn-primary" href="{{ $pendingInvoice->hosted_invoice_url }}" target="_blank">
                          You can pay this invoice here
                        </a>
                      </p>
                    </div>
                  @elseif (is_null(Auth::user()->agency->current_billing_plan))
                    <div class="alert-icon alert alert-warning">
                      <i class="icon wb-alert-circle"></i>
                      <p class="mb-0"><strong>Subscription Inactive</strong></p>
                      <p class="mb-0">
                        You need to re-activate your subscription before adding an email account.
                      </p>
                    </div>
                  @endif
                @endcan
                {{-- End alerts and messages for agency admins --}}

                <div class="panel mb1 ">
                  <div class="panel-body">
                    {{-- Messages for client/team members to show their custom limits --}}
                    @if ($isClientOrTeamMember && !$pendingInvoice)
                      @if (Auth::user()->agency->wavo_version == 3)
                        <div class="alert alert-info alert-icon mb-40" v-if="hasPaymentDetails || onFreePlan">
                          <i class="icon wb-alert-circle"></i>

                          <p class="mb-0">
                            <strong>Remaining Email/LinkedIn accounts: {{$clientRemainingEmails}}</strong>
                          </p>
                          <p>
                            Your team can add <strong>{{$clientMaxEmails}}</strong> Email/LinkedIn accounts.
                            You have already added <strong>{{$totalAccountCount}}</strong> and you can add
                            <strong>{{$clientRemainingEmails}}</strong> more
                            {{$clientRemainingEmails == 1 ? 'account' : 'accounts'}}
                          </p>
                        </div>
                      @else
                        <div class="alert alert-info alert-icon mb-40" v-if="hasPaymentDetails">
                          <i class="icon wb-alert-circle"></i>

                          <p class="mb-0">
                            <strong>Remaining email accounts: {{$clientRemainingEmails}}</strong>
                          </p>
                          <p>
                            Your team can add <strong>{{$clientMaxEmails}}</strong> email accounts.
                            You have already added <strong>{{$clientEmailCount}}</strong> and you can add
                            <strong>{{$clientRemainingEmails}}</strong> more
                            {{$clientRemainingEmails == 1 ? 'email' : 'emails'}}
                          </p>
                        </div>
                      @endif
                    @endif

                    {{-- Messages for client/team members whose agency has billing issues --}}
                    @if ($needsPayment && Auth::user()->cannot('agency-admin') && !$isAgencyClient && !empty($subscriptionPrice))
                    {{-- team member with missing payment details --}}
                      <div class="alert-icon alert alert-warning">
                        <i class="icon wb-alert-circle"></i>
                        <p class="mb-0"><strong>No payment details found</strong></p>
                        <p>
                          Adding email account will cost
                          <strong>${{$subscriptionPrice}}/month</strong>.

                        </p>
                      </div>
                    @elseif ($pendingInvoice && Auth::user()->cannot('agency-admin') && !$isAgencyClient)
                    {{-- team member with failed invoice --}}
                      <div class="alert-icon alert alert-warning">
                        <i class="icon wb-alert-circle"></i>
                        <p class="mb-0">
                          Email Account creation is currently disabled due to billing issues.
                        </p>
                      </div>
                    @elseif (($pendingInvoice || $needsPayment) && Auth::user()->cannot('agency-admin') && $isAgencyClient)
                    {{-- client with billing issues --}}
                      <div class="alert-icon alert alert-warning">
                        <i class="icon wb-alert-circle"></i>
                        <p class="mb-0">
                          Email Account creation is currently disabled. Please contact us for support.
                        </p>
                      </div>
                    @endif
                    @if (is_null(Auth::user()->agency->current_billing_plan) && Auth::user()->cannot('agency-admin') && !$isAgencyClient)
                      <div class="alert-icon alert alert-warning">
                        <i class="icon wb-alert-circle"></i>
                        <p class="mb-0">
                          Email Account creation is currently disabled due to billing issues.
                        </p>
                      </div>
                    @elseif (is_null(Auth::user()->agency->current_billing_plan) && Auth::user()->cannot('agency-admin') && $isAgencyClient)
                      <div class="alert-icon alert alert-warning">
                        <i class="icon wb-alert-circle"></i>
                        <p class="mb-0">
                          Email Account creation is currently disabled. Please contact us for support.
                        </p>
                      </div>
                    @endif

                    @if ($isClientOrTeamMember && !$clientRemainingEmails)
                      <p class="text-center text-danger">
                        <i class="wb-alert-circle"></i> You have reached the maximum number of Email accounts that your team can add.
                      </p>
                    @elseif (!$isClientOrTeamMember && !$remainingAgencyEmails && Auth::user()->agency->wavo_version == 3)
                      @if (Auth::user()->agency->hide_billing && $onFreePlan)
                        <p class="text-center text-danger">
                          <i class="wb-alert-circle"></i> You have reached the limit of active Email accounts of the Free Plan. Please contact us to add more accounts.
                        </p>
                      @else
                        <p class="text-center text-danger">
                          <i class="wb-alert-circle"></i> You have reached the maximum number of Email accounts supported by your current subscription plan. Upgrade your plan to add more.
                        </p>
                      @endif
                    @else
                      @include('inbox.create-account-info')
                      @include('inbox.create-additional-info')
                      <div class="pt-20 text-center">
                        @if ($pendingInvoice || is_null(Auth::user()->agency->current_billing_plan))
                          <button class="btn btn-primary cd" type="button" disabled>
                            <i class="icon wb-chevron-right"></i>
                            Add Email Account
                          </button>
                        @else
                          @if($teams->count())
                            <button :disabled="isLoading || disableSubmit || (!hasPaymentDetails && !onFreePlan)"
                                    class="btn btn-primary" @click="saveEmailAccountSettings">
                              <span v-if="isLoading">
                                <i class="icon fa fa-spinner fa-spin"></i>
                                Adding...
                              </span>
                              <span v-else>
                                <i class="icon wb-chevron-right"></i>
                                Add Email Account
                              </span>
                            </button>
                          @else
                            <button class="btn btn-primary cd" type="button" disabled>
                              <i class="icon wb-chevron-right"></i>
                              Add Email Account
                            </button>
                          @endif
                        @endif
                      </div>
                    @endif
                  </div>
                </div>

              </div>
            </div>
          </div>

        </div>

        <div class="loader-box vertical-align text-center">
          <div class="loader vertical-align-middle loader-circle"></div>
        </div>
      </div>
    </nylasemail-add>
@endsection
