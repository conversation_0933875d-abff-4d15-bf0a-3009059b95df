@extends('layouts.app')

@section('page-title', 'Email Accounts')

@section('page-styles')
    <style>
		.campaign-panel-dropdown .btn {
		    padding-top: 5px;
			padding-bottom: 5px;
		}

		.deliverability-box {
			width: 30px;
			height: 22px;
			position: relative;
			display: block !important;
			margin: 0 auto !important;
		}

		.deliverability-pie {
			position: absolute;
			top: -5px;
		}

		.deliverability-pie .pie-progress-number {
			line-height: 10px;
		}

		.email-list-stats {
			gap: 20px;
		}

		@media (max-width: 1380px) {
		    .email-list-stats {
				gap: 15px;
			}
		}

		@media (max-width: 1300px) {
		    .email-list-stats {
				gap: 10px;
			}
		}

		@media (max-width: 1220px) {
		    .campaign-panel-stats {
		    	 padding: 5px;
		    }
		    .stats-count {
		    	font-size: 14px;
    			line-height: 14px;
		    }
			.campaign-panel-interest {
				 padding: 5px;
			}
			.campaign-panel-dropdown .btn {
			    padding: 5px 3px;
			    font-size: 13px;
			    line-height: 13px;
			    margin-top: 5px;
			}
		}

		@media (max-width: 991px) {
		    .campaign-panel .panel-body {
		        text-align: left;
		        padding-top: 5px;
		    }
		    .campaign-panel .panel-title {
		    	padding-bottom: 5px;
		    	display: inline-block;
    			padding-right: 0;
		    }
		    .campaign-panel .panel-title-meta {
				display: inline-block;
    			padding-bottom: 5px;
		    }
		    .campaign-panel-stats {
		    	background-color: #f3f7fa;
    			border: 1px solid #eee;
    			line-height: 13px;
    			margin-bottom: 5px;
    			width: auto;
		    }
		    .stats-count {
		    	display: inline-block;
		    	font-size: 13px;
		    	line-height: 13px;
		    	margin: 0px;
		    }
			.stats-label {
				display: inline-block;
				font-size: 13px;
				line-height: 13px;
				/*color: #37474f;*/
			}
			.campaign-panel-interest {
				padding: 5px 10px;
				width: auto;
			}
			.campaign-panel-interest .stats-count {
				font-size: 13px;
			    line-height: 13px;
			    margin: 0px;
			}
			.campaign-panel-dropdown .btn {
			    padding-top: 5px;
				padding-bottom: 5px;
				font-size: 11px;
			    line-height: 13px;
			    width: 100px;
			}
			.campaign-panel .panel-body .col-md-5.text-center {
				text-align: right !important;
			}
			.page-title {
				margin-bottom: 20px;
			}
		}
		@media (max-width: 767px) {
			.campaign-panel-dropdown .btn {
			    padding-top: 5px;
				padding-bottom: 5px;
				font-size: 11px;
			    line-height: 13px;
			    width: 100px;
			}
			.campaign-panel .panel-body .col-md-5.text-center {
				text-align: left !important;
			}
		}

		.warmup-chart .ct-series-c .ct-slice-donut-solid {
			fill: #ff666b !important; /*red*/
		}
		.warmup-chart .ct-series-b .ct-slice-donut-solid {
			fill: #ffcd17 !important; /*yellow*/
		}
		.warmup-chart .ct-series-a .ct-slice-donut-solid {
			fill: #11c26d !important; /*green*/
		}
    </style>
@endsection

@section('content-layout')
	<inbox-list
		:teams="{{$teams}}"
		:servers="{{json_encode(App\EmailAccount::EMAIL_SERVER_TYPES)}}"
		:agencies="{{$agencies}}"
		:agency="{{$agency}}"
		:user="user"
		app_env="{{ config('app.env') }}"
		:new_teamaccountemail="{{json_encode($newTeamAccountEmail)}}"
		inline-template
	>
		<div class="page loader-wrapper" :class="{'loader-wrapper-loading': isLoading}">
			<div class="page-header page-header-bordered">
		    	<h1 class="page-title">Email Accounts</h1>
		    	<div class="page-header-actions">
					<a href="{{ route('email-accounts.create') }}" class="btn btn-outline btn-primary btn-round">
						<span class="text hidden-sm-down">Add Email Account</span>
						<i class="icon wb-plus" aria-hidden="true"></i>
					</a>
				</div>
		    </div>


	        <div class="page-content container-fluid">
	        	@if (session('status'))
	                <div class="mb-20 alert alert-icon alert-dismissible alert-{{session('status') == 'success' ? 'success' : 'danger'}}">
	                	@if (session('status') == 'success')
	                		<i class="icon wb-check-circle"></i>
	                	@else
											<i class="icon wb-alert-circle"></i>
	                	@endif

	                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
	                        <span aria-hidden="true">&times;</span>
	                    </button>
	                    {{ session('msg') }}
	                </div>


						@elseif (request()->has('oauth_error'))
								<div class="mb-20 alert alert-icon alert-dismissible alert-danger">
									<i class="icon wb-alert-circle"></i>
									<button type="button" class="close" data-dismiss="alert" aria-label="Close">
										<span aria-hidden="true">&times;</span>
									</button>
									{{ request('oauth_error') }}
								</div>
						@endif

	          @if (session('ajaxMsg'))
	                <div class="mb-20 alert alert-dismissible alert-success">
	                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
	                        <span aria-hidden="true">&times;</span>
	                    </button>
	                    {{ session('ajaxMsg') }}
	                </div>
	          @endif

            @include('partials.journey-alert')

	          @include('inbox.list.list-filter')

	          <div class="row">
			      <div class="col-md-12" v-if="arrInboxes.data.length">

						<div class="card card-shadow mb-5" v-for="(email, index) in arrInboxes.data">
							<div class="card-block pb-15">
								<div class="row">
									<div class="col-lg-9 col-xl-9 col-md-9 col-sm-8 col-8">
										<div class="row">
											<div class="col-md-12 col-xl-6 col-lg-4">
												<h4 class="m-0">
													<span class="d-block text-truncate">
														<span v-if="email.error == 'account.invalid'"
															class="text-danger" data-toggle="tooltip"
															data-placement="top" title="Invalid email credentials, click to re-authenticate."
                                                      	>
															<a :href="'/email-accounts/'+email.hashid+'/reauth'"
																class="text-decoration-none text-danger"
															>
																<i class="fa fa-exclamation red-700"></i>
																@{{email.email_with_alias}}
															</a>
														</span>
														<span v-else-if="email.error == 'account.reauth'"
																	class="text-danger" data-toggle="tooltip"
																	data-placement="top" title="Re-authentication incomplete, click to re-authenticate."
														>
															<a :href="'/email-accounts/'+email.hashid+'/reauth'"
																 class="text-decoration-none text-danger"
															>
																<i class="fa fa-exclamation red-700"></i>
																@{{email.email_with_alias}}
															</a>
														</span>
														<span v-else-if="email.error == 'account.stopped' || email.error == 'sending.failed' || email.error == 'account.failed' || email.error == 'sync.timeout'"
															class="text-danger" data-toggle="tooltip"
															data-placement="top" title="Email account stopped. Please view account settings and re-authenticate if needed."
                                                      	>
														  	<a :href="'/email-accounts/'+email.hashid+'/edit'"
																class="text-decoration-none text-danger"
															>
																<i class="fa fa-ban red-700"></i>
																@{{email.email_with_alias}}
															</a>
														</span>
														<span v-else-if="email.error == 'account.connecting'">
															<a :href="'/email-accounts/'+email.hashid+'/edit'"
																class="text-decoration-none blue-grey-700"
																data-toggle="tooltip" data-placement="top"
																title="Checking SMTP status. Checking the account's SMTP status before it can send messages."
															>
																<i class="fa fa-circle-o-notch fa-spin grey-500" ></i>
																@{{email.email_with_alias}}
															</a>
														</span>
														<span v-else>
															<a :href="'/email-accounts/'+email.hashid+'/edit'"
																class="text-decoration-none blue-grey-700"
															>@{{email.email_with_alias}}</a>
														</span>
													</span>
													<small class="d-block pt-5 hidden-md-down text-capitalize">
														@can('support')
															@{{email.agency.name}} Agency
															<span class="mx-10">&bull;</span>
														@endcan
														@{{email.team.name}}
														<span class="mx-10">&bull;</span>
														@{{email.name}}
													</small>

													<small class="d-block pt-5 hidden-lg-up">
														<span class="d-inline-block">@{{ email.email_server_type_nice_name }}</span>
														<span class="mx-10">&bull;</span>
														<span class="d-inline-block">
															<span v-if="email.warmup_status == 'on' && email.email_warmup">
																@{{email.email_warmup.send_limit}}
															</span>
															<span v-else>
																@{{email.send_limit}}
															</span>
															Daily Limit
														</span>
														<span class="mx-10">&bull;</span>
														<span class="d-inline-block">@{{email.listed_campaigns_count}} Campaigns</span>
														<span class="mx-10">&bull;</span>
														<span class="d-inline-block" v-if="email?.sends_warmup_messages">
															@{{getDeliverabilityProgress(email)}}% Deliverability
														</span>
														<span class="d-inline-block grey-500" v-else>
															0% Deliverability
														</span>
													</small>
												</h4>
											</div>
											<div class="col-md-12 col-xl-6 col-lg-8 text-center hidden-md-down">
												<div class="d-flex justify-content-center email-list-stats">
													<h4 class="m-0 d-inline-block text-center w-160">
														<span class="">@{{ email.email_server_type_nice_name }}</span>
														<small class="d-block pt-5">Server Type</small>
													</h4>
													<h4 class="m-0 d-inline-block text-center">
														<span v-if="email.warmup_status == 'on' && email.email_warmup">
															@{{email.email_warmup.send_limit}}
														</span>
														<span v-else>
															@{{email.send_limit}}
														</span>
														<small class="d-block pt-5">Daily Limit</small>
													</h4>
													<h4 class="m-0 d-inline-block text-center">
														@{{email.listed_campaigns_count}}
														<small class="d-block pt-5">Campaigns</small>
													</h4>
													<h4 class="m-0 d-inline-block text-center">
                            <a :href="'/email-accounts/'+email.hashid+'/warmupinbox'"
                               class="text-decoration-none"
                               style="color: #37474f"
                               data-toggle="tooltip"
                               data-placement="top"
                               title="Calculated based on inbox warmup progress"
                               data-original-title="Calculated based on inbox warmup progress"
                               v-if="email && email.sends_warmup_messages"
                            >
														  <div class="deliverability-box w-30 h-22 d-inline-block">
                                <div class="deliverability-pie pie-progress pie-progress-xs w-30 h-30"
                                  data-plugin="pieProgress"
                                  data-barcolor="{{Environment::getPrimaryColorCode()}}"
                                  data-trackcolor="#FAD3D3"
                                  data-goal="100"
                                  :aria-valuenow="getDeliverabilityProgress(email)"
                                  data-size="100" data-barsize="7"
                                  role="progressbar"
                                >
                                  <div class="pie-progress-number font-size-10">
                                    @{{getDeliverabilityProgress(email)}}%
                                  </div>
                                </div>
														  </div>
                              <small class="d-block pt-5">Deliverability</small>
                            </a>
														<a :href="'/email-accounts/'+email.hashid+'/edit#/email_inbox_warmup'"
															class="d-block text-decoration-none"
                              style="color: #37474f"
															data-toggle="tooltip"
															data-placement="top"
															title="Enable inbox warmup in account settings"
                              data-original-title="Enable inbox warmup in account settings"
                              v-else
														>
															<small class="grey-500 font-size-10">
																<i class="wb-info-circle"></i> N/A
															</small>
														  <small class="d-block pt-5">Deliverability</small>
														</a>
													</h4>
												</div>
											</div>
										</div>
									</div>
									<div class="col-md-3 col-xl-3 col-lg-3 col-sm-4 col-4 text-right pt-3 pl-0">
										<div class="d-flex justify-content-end gap-5">
											<a :href="'/email-accounts/'+email.hashid+'/edit'" class="btn btn-default px-5" v-if="email.cancelled_at == null">
												<i aria-hidden="true" class="icon wb-settings hidden-lg"></i>
												<span class="hidden-md-down">Settings</span>
											</a>
											<a :href="'/email-accounts/'+email.hashid+'/reauth'" class="btn btn-danger" v-else>
												<i aria-hidden="true" class="icon wb-reload hidden-lg"></i>
												<span class="hidden-md-down">Re-Activate</span>
											</a>

											@if($agency->has_warmup || Auth::user()->can('support'))
											<a :href="'/email-accounts/'+email.hashid+'/warmupinbox'" class="btn btn-default"
												v-if="email?.sends_warmup_messages"
											>
												<i aria-hidden="true" class="icon wb-envelope-open  hidden-lg"></i>
												<span class="hidden-md-down">Warmup</span>
											</a>
											<a href="#" class="btn btn-default disabled" disabled
												v-else
											>
												<i aria-hidden="true" class="icon wb-envelope-open  hidden-lg"></i>
												<span class="hidden-md-down">Warmup</span>
											</a>
											@endif

											<a :href="'/email-accounts/'+email.hashid" class="btn btn-primary">
												<i aria-hidden="true" class="icon wb-envelope hidden-lg"></i>
												<span class="hidden-md-down">Inbox</span>
											</a>
										</div>
									</div>
								</div>
							</div>
						</div>



			        	<div class="text-center">
			        		<pagination :data="arrInboxes" :limit="2" @pagination-change-page="getEmailAccounts"></pagination>
			        	</div>

			        </div>

			        <div class="col-md-12" v-else>
			        	<div class="panel panel-body">
			        		<div class="alert alert-warning">
								<i class="wb-alert-circle mr-10"></i>
								No email accounts yet.
								Let's <a href="{{ route('email-accounts.create') }}">add one</a>.
							</div>
			        	</div>
			        </div>
			    </div>
			</div>

			<div class="loader-box vertical-align text-center">
		    	<div class="loader vertical-align-middle loader-circle"></div>
		    </div>
		</div>
	</inbox-list>
@endsection
