<div class="row mb-30 pt-20">
    <div class="col-lg-2 col-md-4 mb-10 col-sm-12">
		<input type="text" class="form-control" 
			v-model="filters.keywords" 
			placeholder="Search"
			@keyup="filterByKeywords()"
		>
	</div> 

    <div class="col-lg-2 col-md-4 mb-10 col-sm-6">
        <select class="form-control cap text-capitalize"
            v-model="filters.agency_id" @change="filterAgency()"
            data-plugin="selectpicker" data-style="btn-outline btn-default"
        >
            <option value="0">All Agencies</option>
            <option v-for="(agency, index) in arrAgencies" :value="agency.hashid">
                @{{agency.name}}
            </option>
        </select>
    </div>
	

	<div class="col-lg-2 col-md-4 mb-10 col-sm-6">
        <select class="form-control cap text-capitalize select-inboxclient"
            v-model="filters.team_id" @change="getEmailAccounts(1)" 
            data-plugin="selectpicker" data-style="btn-outline btn-default"
            :disabled="filters.agency_id == 0"
        >
            <option value="0">All Clients</option> 
            <option v-for="(team, index) in arrTeams" :value="team.hashid">@{{team.name}}</option>
        </select>
    </div>

	<div class="col-lg-6">
        <div class="row">
            <div class="col-md-3 mb-10 col-sm-6">
                <select class="form-control cap"
                    v-model="filters.status" @change="getEmailAccounts(1)"
                    data-plugin="selectpicker" data-style="btn-outline btn-default"
                >
                    <option value="active">Active</option>
                    <option value="inactive">Cancelled</option>
                </select>
            </div>

            <div class="col-md-3 mb-10 col-sm-6">
                <select class="form-control cap"
                    v-model="filters.server" @change="getEmailAccounts(1)"
                    data-plugin="selectpicker" data-style="btn-outline btn-default"
                >
                    <option value="ALL">All Server Types</option>
                    <option v-for="(server, index) in arrServers" :value="index">
                        @{{ server }}
                    </option> 
                </select>
            </div>

            <div class="col-md-3 mb-10 col-sm-6">
                <select class="form-control cap"
                    v-model="filters.integration_type" @change="getEmailAccounts(1)"
                    data-plugin="selectpicker" data-style="btn-outline btn-default"
                >
                    <option value="">All Email API</option>
                    <option value="ee">EmailEngine</option>
                    <option value="nylas">Nylas</option>
                </select>
            </div>

            <div class="col-md-3 mb-10 col-sm-6">
                <select name="" id="" class="form-control"
                    v-model="filters.sort_by" @change="getEmailAccounts(1)"
                    data-plugin="selectpicker" data-style="btn-outline btn-default"
                > 
                    <option value="email_address">Sort By Email Address</option>
                    <option value="listed_campaigns_count">Sort By Campaigns</option>
                </select> 
            </div>
        </div>
    </div>
</div>