<incoming-email-list
	:email_account="{{$account}}"
	:filter_type="incoming.type"
	:campaign_ids="incoming_filter.campaign_ids"
	inline-template
>
	<div class="loader-wrapper" :class="{'loader-wrapper-loading': isLoading}">
		
		<div>
            <div class="">
		        <div class="" v-if="arrIncomingEmails.total"> 

		        	<div class="card card-shadow mb-5" v-for="(message, index) in arrIncomingEmails.data">
						<div class="card-block pb-15">
							<div class="row">
								<div class="col-lg-10 col-xl-10 col-md-9 col-sm-8 col-8 cp"
									@click.prevent="openMessageModal(message)"
								>
									<h5 class="my-0 font-size-14"> 
										@{{message.from_name}}
										(@{{message.from}})
										<span class="badge badge-default" v-if="message.matched_subject"> 
											Matched Subject
										</span>
										<span class="badge badge-default" v-else-if="message.is_unsubscribe"> 
											Unsubscribe
										</span>
										<span class="badge badge-default" v-else-if="message.is_undeliverable"> 
											Undeliverable
										</span>
										<span class="badge badge-default" v-else-if="message.matched_domain"> 
											Matched Domain
										</span>
										<span class="badge badge-default" v-else>
											Not Matched
										</span>
									</h4>
									<div class="d-block pt-5 text-truncate">  
										@{{message.submitted_at}}
										<span class="mx-10">&bull;</span> 
										@{{message.subject}} - @{{message.snippet}}
									</div> 
								</div>
								<div class="col-md-3 col-xl-2 col-lg-2 col-sm-4 col-4 text-right pt-3"> 
									<button class=" px-5 btn btn-warning"
										@click.prevent="deleteMessage(message.hashid)"
									> 
										<i aria-hidden="true" class="icon wb-close"></i>
									</button>
									<button class=" px-5 btn btn-primary"
										@click.prevent="openMessageModal(message)"
									> 
										<i aria-hidden="true" class="icon wb-envelope-open"></i>
										<span class="hidden-lg-down pr-10 text-center d-inline-block">Open</span> 
									</button>
								</div>
							</div>
						</div> 
					</div>  
					  
		        	
		        	<div class="text-center">
		        		<pagination :data="arrIncomingEmails" :limit="2" @pagination-change-page="getIncomingEmails"></pagination>
		        	</div>

		        </div>

		        <div class="col-md-12" v-else>   
		        	<div class="panel panel-body"> 
		        		<div class="alert alert-warning"> 
							<i class="wb-alert-circle mr-10"></i>
							No unmatched email messages found.
						</div>
		        	</div>
		        </div>
		    </div>
		</div> 

		<div class="loader-box vertical-align text-center">
	    	<div class="loader vertical-align-middle loader-circle"></div>
	    </div> 

	    @include('inbox.incoming-emails.incoming-modal')
	    
	</div> 
</incoming-email-list>