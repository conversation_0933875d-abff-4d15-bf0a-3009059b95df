@extends('layouts.mailbox')

@section('page-title', 'Inbox - '.$campaign->name)

@section('page-styles') 
  <style>
    .thread-message-box {
      border-radius: 3px;
      box-shadow: 0 0 1px rgba(0,0,0,0.2);
    }
    .thread-message-head {
      border-bottom: 1px solid rgba(0,0,0,0.1);
      font-weight: bold;
    }
    .thread-message-body {
      font-size: 14px !important;
      font-family: "Roboto", sans-serif !important;
    }
    .thread-message-body .wv_ee_tr_op {
        display: none !important;
    }
    .mailContent {
      min-height: 600px;
    }
    .yahoo_quoted, .gmail_extra, 
    .gmail_quote, .unsrb, .quote {
      display: none !important;
    }
    .display-quoted .yahoo_quoted, .display-quoted .gmail_extra, 
    .display-quoted .gmail_quote, .display-quoted .unsrb, .display-quoted .quote {
      display: block !important;
    }

    .page-aside .list-group-item.active  { 
        background-color: #f3f7f9;
        font-weight: bold;
    }

    .active-interest label {
      color: #eb6709;
      font-weight: bold !important;
    }

    .page-aside .list-group-item {
      cursor: pointer;
    }
    #replyEditorWrap {
      position: relative;
    }
    #replyEditorBox {
      z-index: 10;
    }
    #replyEditorBtn {
      position: absolute;
      top: 3px;
      right: 3px;
      z-index: 10000;
    }
    .thread-interest-wrap {
      width: 110px;
      text-align: center;
    }
    .thread-interest-item {
      height: 22px;
      overflow: hidden;
    }
    .thread-interest-icon {
      width: 22px;
      height: 22px; 
      border: 1px solid rgba(0,0,0,0.3);
      display: inline-block;
      margin: 0 1px;
      border-radius: 5px;
      overflow: hidden;
      opacity: 0.3;
      vertical-align: top;
    } 
    .thread-interest-icon i {
      font-size: 12px;
      line-height: 12px;
    }
    .time {
      font-size: 12px;
      line-height: 12px;
      text-align: center;
      margin-bottom: 5px;
    }
    .thread-interest-icon:hover {
      opacity: 1;
    }
    .thread-interest-icon.active {
      opacity: 1 !important;
      border: 1px solid rgba(0,0,0,0.3);
      box-shadow: 0 0 5px rgba(0,0,0,0.2)
    }
    .resched-datepicker {
      border: none;
      padding: 4px;
      font-size: 12px;
      line-height: 12px;
      width: 90px;
      text-align: center;
    }
    .font13 {
      font-size: 13px;
      line-height: 13px;
    }
    
    .radio-interest { 
      background-color: transparent;
      border: 1px solid #e4eaec;
      padding: 5px 10px;
      font-size: 12px;
      line-height: 18px;
      border-radius: 5px;
    }
    .radio-interest.radio-custom input[type="radio"] {
      margin-left: 0px;
    }
    .radio-interest.radio-custom label {
      padding-left: 25px;
    }
    .radio-interest.radio-custom label::before {
      margin-left: 0px;
    }
    .radio-interest.radio-custom label::after {
      margin-left: 0px;
    }
    .radio-interest.active-interest {
      border-color: #526069;
    }
    .bg-grey-150 {
      background-color: #f4f4f4 !important;
    }
  </style>
@endsection


  
@section('content-layout')
<thread-list 
  :account="{{$account}}"
  inline-template
>
  <div class="page bg-white">
    <!-- Mailbox Sidebar -->
    <div class="page-aside">
      <div class="page-aside-switch">
        <i class="icon wb-chevron-left" aria-hidden="true"></i>
        <i class="icon wb-chevron-right" aria-hidden="true"></i>
      </div>
      <div class="page-aside-inner page-aside-scroll scrollable is-enabled scrollable-vertical" style="position: relative;">
        <div data-role="container" class="scrollable-container" style="height: 625px; width: 276px;">
          <div data-role="content" class="scrollable-content" style="width: 259px;">
            <div class="page-aside-section">
              <div class="list-group">
                <h5 class="page-aside-title clearfix">
                  INTEREST
                  <a href="#" 
                    class="btn btn-xs btn-default pull-right"
                    @click.prevent="getThreadsByInterest('')"
                  >All</a>
                </h5>

                <a class="list-group-item list-group-item-action"
                  :class="filter.interest == 'INTERESTED' ? 'active' : ''"
                  @click.prevent="getThreadsByInterest('INTERESTED')"
                >
                  <span class="badge badge-pill badge-default mt-5">
                    @{{statsCount['INTERESTED']}}
                  </span> 
                  <i aria-hidden="true" class="wb-medium-point green-600"></i> 
                  Interested
                </a>

                <a class="list-group-item" 
                  :class="filter.interest == 'MAYBE_LATER' ? 'active' : ''"
                  @click.prevent="getThreadsByInterest('MAYBE_LATER')"
                >
                  <span class="badge badge-pill badge-default mt-5">
                    @{{statsCount['MAYBE_LATER']}}
                  </span> 
                  <i aria-hidden="true" class="wb-medium-point yellow-600"></i>
                  Maybe Later
                </a>

                <a class="list-group-item" 
                  :class="filter.interest == 'NOT_INTERESTED' ? 'active' : ''"
                  @click.prevent="getThreadsByInterest('NOT_INTERESTED')"
                >
                  <span class="badge badge-pill badge-default mt-5">
                    @{{statsCount['NOT_INTERESTED']}}
                  </span> 
                  <i aria-hidden="true" class="wb-medium-point red-600"></i> 
                  Not Interested
                </a> 

                <a class="list-group-item" 
                  :class="filter.interest == 'null' ? 'active' : ''"
                  @click.prevent="getThreadsByInterest('null')"
                >
                  <span class="badge badge-pill badge-default mt-5">
                    @{{statsCount['UNMARKED']}}
                  </span> 
                  <i aria-hidden="true" class="wb-medium-point gray-600"></i> 
                  Unmarked
                </a> 
              </div>
            </div>
            <div class="page-aside-section mb-40">
              <h5 class="page-aside-title clearfix">
                STATUS  
                <a href="#" 
                  class="btn btn-xs btn-default pull-right"
                  @click.prevent="getThreadsByStatus('')"
                >All</a>
              </h5>
              <div class="list-group">
                <a class="list-group-item list-group-item-action" 
                  :class="filter.status == 'ACTIVE' ? 'active' : ''"
                  @click.prevent="getThreadsByStatus('ACTIVE')"
                >
                  <span class="badge badge-pill badge-default mt-5">
                    @{{statsCount['ACTIVE']}}
                  </span>
                  Active
                </a>
                <a class="list-group-item list-group-item-action" 
                  :class="filter.status == 'REPLIED' ? 'active' : ''"
                  @click.prevent="getThreadsByStatus('REPLIED')"
                >
                  <span class="badge badge-pill badge-default mt-5">
                    @{{statsCount['REPLIED']}}
                  </span>
                  Replied
                </a>
                <a class="list-group-item list-group-item-action" 
                  :class="filter.status == 'BLACKLIST' ? 'active' : ''"
                  @click.prevent="getThreadsByStatus('BLACKLIST')"
                >
                  <span class="badge badge-pill badge-default mt-5">
                    @{{statsCount['BLACKLIST']}}
                  </span>
                  Blacklist
                </a>
                <a class="list-group-item list-group-item-action" 
                  :class="filter.status == 'INVALID' ? 'active' : ''"
                  @click.prevent="getThreadsByStatus('INVALID')"
                >
                  <span class="badge badge-pill badge-default mt-5">
                    @{{statsCount['INVALID']}}
                  </span>
                  Invalid
                </a>
                <a class="list-group-item list-group-item-action" 
                  :class="filter.status == 'BOUNCED' ? 'active' : ''"
                  @click.prevent="getThreadsByStatus('BOUNCED')"
                >
                  <span class="badge badge-pill badge-default mt-5">
                    @{{statsCount['BOUNCED']}}
                  </span>
                  Bounced
                </a>
                <a class="list-group-item list-group-item-action" 
                  :class="filter.status == 'AUTOREPLIED' ? 'active' : ''"
                  @click.prevent="getThreadsByStatus('AUTOREPLIED')"
                >
                  <span class="badge badge-pill badge-default mt-5">
                    @{{statsCount['AUTOREPLIED']}}
                  </span>
                  Autoreplied
                </a>
                <a class="list-group-item list-group-item-action" 
                  :class="filter.status == 'TO-REVIEW' ? 'active' : ''"
                  @click.prevent="getThreadsByStatus('TO-REVIEW')"
                >
                  <span class="badge badge-pill badge-default mt-5">
                    @{{statsCount['TO-REVIEW']}}
                  </span>
                  To Review
                </a> 
                <a class="list-group-item list-group-item-action" 
                  :class="filter.status == 'TO-CHECK' ? 'active' : ''"
                  @click.prevent="getThreadsByStatus('TO-CHECK')"
                >
                  <span class="badge badge-pill badge-default mt-5">
                    @{{statsCount['TO-CHECK']}}
                  </span>
                  To Check
                </a> 
              </div>
            </div>
            <div class="clearfix mb-40"></div>
          </div>
        </div>
      <div class="scrollable-bar scrollable-bar-vertical is-disabled scrollable-bar-hide" draggable="false"><div class="scrollable-bar-handle"></div></div></div>
    </div>

    <!-- Mailbox Content -->
    <div class="page-main">

      <!-- Mailbox Header -->
      <div class="page-header">
        <h1 class="page-title">Mailbox : {{$account->email_address}}</h1>
        <div class="page-header-actions">
          <form @submit.prevent="getThreadsByKeywords()">
            <div class="input-search input-search-dark">
              <i class="input-search-icon wb-search" aria-hidden="true"></i>
              <input type="text" 
                class="form-control" 
                name=""  
                placeholder="Search..."
                v-model="filter.keywords"
              >
            </div>
          </form>
        </div>
      </div>

      <!-- Mailbox Content -->
      <div id="mailContent" class="page-content page-content-table" data-plugin="selectable">

        <!-- Actions -->
        <div class="page-content-actions">
          <div class="float-right filter">
            <select name="" id="mailBoxCampaignSelect" class="form-control" v-model="filter.campaign_id" @change="getThreadsByCampaign()">
              <option value="0">All Campaigns</option>
              @foreach($account->campaigns as $campaign)
                <option value="{{ $campaign->getRouteKey() }}">
                  {{ $campaign->name }}
                </option>
              @endforeach
            </select>
          </div>
          <div class="actions-main">
            <strong class="inline-block vertical-align-bottom py-10">
              @{{total}} threads found in "@{{mailBoxLabel}}"
            </strong>
            <span class="inline-block vertical-align-bottom py-5 mb-10 ml-20 badge badge-warning " 
              v-if="emailHistories.length"
            >
              This campaign also used other Mailbox.
              <a href="#" class="blue-grey-700">View all messages</a>
            </span>
          </div>
        </div>
        
        <div v-if="!boolIsThreadLoading && !threads.length">
          <div class="p-20">
            <div class="panel bg-indigo-100">
              <div class="panel-heading">
                <h4 class="panel-title text-center">No threads found.</h4>
              </div>
            </div> 
          </div>
        </div>

        <!-- Mailbox -->
        <table v-if="!boolIsThreadLoading && threads.length" id="mailboxTable" class="table" data-plugin="animateList" data-animate="fade" data-child="tr">
          <tbody>
            

            <tr id="mid_1" v-for="thread in threads" :key="thread.id"> 
              <td data-toggle="modal" 
                data-target="#threadMessagesModal" 
                @click="showThreadMessages(thread)"
              >
                <div class="content">
                  <div class="title">

                    @{{thread.prospect.full_name}} (@{{thread.prospect.email}})
                    <span class="badge badge-outline badge-sm"
                      :class="thread['status'] == 'REPLIED' ? 'badge-success' : 'badge-default'"
                    > 
                      @{{thread['status']}} 
                    </span>

                  </div>
                  <div class="abstract">
                    @{{thread.snippet.replace('&nbsp;', ' ')}} 
                  </div>
                </div>
              </td> 
              <td class="cell-150" >
                <div class="time">
                  @{{thread['email_messages'][thread['email_messages'].length-1]['submitted_at']}} 
                </div>
                
                <div class="thread-interest-wrap">
                  <div class="thread-interest-item">
                    <div class="thread-interest-icon interested"
                      :class="thread.interested == 'INTERESTED' ? 'active green-600' : ''"
                      @click.prevent="saveProspectInterest(thread, 'INTERESTED')"
                    >
                      <i class="fa fa-thumbs-o-up"></i>
                    </div>
                    <div class="thread-interest-icon maybe"
                      :class="thread.interested == 'MAYBE_LATER' ? 'active yellow-600' : ''"
                      @click.prevent="saveProspectInterest(thread, 'MAYBE_LATER')"
                    >
                      <i class="fa fa-clock-o"></i>
                    </div>
                    <div class="thread-interest-icon notinterested"
                      :class="thread.interested == 'NOT_INTERESTED' ? 'active orange-600' : ''" 
                      @click.prevent="saveProspectInterest(thread, 'NOT_INTERESTED')"
                    >
                      <i class="fa fa-thumbs-o-down"></i>
                    </div>
                  </div>
                </div>
              </td>
            </tr>  
             

          </tbody>
        </table>
        <!-- pagination -->
      </div>
    </div>

    <!-- prospect manual modal -->
    @include('inbox.thread-modal')
  </div> 
</thread-list>
@endsection
