<!-- Edit Proxy Modal -->
<div class="modal fade" id="proxyEditModal" aria-hidden="false"
    aria-labelledby="proxyEditModalLabel" role="dialog" tabindex="-1"
>
    <div class="modal-dialog modal-simple">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                <h4 class="modal-title" id="exampleFormModalLabel">Edit Proxy</h4>
            </div>
            <div class="modal-body form-icons form-icons-right">
                <div class="mb-20">
                    <label for="">Name</label>
                    <input type="text" v-model="selectedProxy.name" class="form-control">
                    <small class="text-xs text-danger" v-if="selectedProxyErrors.name">@{{selectedProxyErrors.name[0]}}</small>
                </div>
                <div class="mb-20">
                    <label for="">Proxy URL</label>
                    <input type="text" v-model="selectedProxy.url" class="form-control">
                    <small class="text-xs text-danger" v-if="selectedProxyErrors.url">@{{selectedProxyErrors.url[0]}}</small>
                </div>
                <div class="mb-20">
                    <label for="">Username</label>
                    <input type="text" v-model="selectedProxy.username" class="form-control">
                </div>
                <div class="mb-20">
                    <label for="">Password</label>
                    <input type="text" v-model="selectedProxy.password" class="form-control">
                </div>
                <div>
                    <button type="button" 
                        class="btn btn-primary"
                        @click="updateProxy"
                        :disabled="isUpdating"
                    >
                        Update
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>