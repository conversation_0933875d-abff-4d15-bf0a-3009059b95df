<spark-subscribe-stripe :user="user" :team="team"
                :plans="plans" :billable-type="billableType" inline-template>

    <div>
        <!-- Common Subscribe Form Contents -->
        @include('spark::settings.subscription.subscribe-common')

        <!-- Billing Information -->
        <div class="card card-shadow" v-show="selectedPlan">
            <div class="card-header card-header-transparent">
                <h4 class="card-title">Billing Information</h4>
            </div>

            <div class="card-block">
                <!-- Generic 500 Level Error Message / Stripe Threw Exception -->
                <div class="alert alert-danger" v-if="form.errors.has('form')">
                    We had trouble validating your card. It's possible your card provider is preventing
                    us from charging the card. Please contact your card provider or customer support.
                </div>

                <form role="form">
                    <!-- Billing Address Fields -->
                    @if (Spark::collectsBillingAddress())
                        <h5><i class="fa fa-btn fa-map-marker"></i> Billing Address</h5>

                        @include('spark::settings.subscription.subscribe-address')

                        <h5><i class="fa fa-btn fa-credit-card"></i> Credit Card</h5>
                    @endif

                    <div class="form-group-attached">
                        <!-- Cardholder's Name -->
                        <div class="form-group form-group-default">
                            <label for="name">Cardholder's Name</label>
                            <input type="text" class="form-control" name="name" v-model="cardForm.name">
                        </div>

                        <!-- Card Number -->
                        <div class="form-group form-group-default" :class="{'has-error': cardForm.errors.has('number')}">
                            <label for="number">Card Number</label>
                            <input type="text" class="form-control" name="number" data-stripe="number" v-model="cardForm.number">
                            <span class="help-block" v-show="cardForm.errors.has('number')">
                                @{{ cardForm.errors.get('number') }}
                            </span>
                        </div>

                        <div class="row clearfix">
                            <div class="col-sm-6">
                                <!-- Expiration -->
                                <div class="form-group form-group-default">
                                    <label>Expiration (MM-YYYY)</label>
                                    <div class="row clearfix">
                                        <div class="col-sm-6">
                                            <!-- Month -->
                                            <input type="text" class="form-control" name="month"
                                                   placeholder="MONTH" maxlength="2" data-stripe="exp-month" v-model="cardForm.month">
                                        </div>
                                        <div class="col-sm-6">
                                            <!-- Year -->
                                            <input type="text" class="form-control" name="year"
                                                   placeholder="YEAR" maxlength="4" data-stripe="exp-year" v-model="cardForm.year">

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <!-- Security Code -->
                                <div class="form-group form-group-default">
                                    <label for="number">Security Code (CVV)</label>
                                    <input type="text" class="form-control" name="cvc" data-stripe="cvc" v-model="cardForm.cvc">
                                </div>
                            </div>
                        </div>
                    </div>

                    <br>

                    <!-- ZIP Code -->
                    <div class="form-group form-group-default" v-if=" ! spark.collectsBillingAddress">
                        <label for="number">ZIP / Postal Code</label>
                        <input type="text" class="form-control" name="zip" v-model="form.zip">
                    </div>

                    <br>

                    <!-- Coupon -->
                    {{--<div class="form-group form-group-default" :class="{'has-error': form.errors.has('coupon')}">--}}
                        {{--<label>Coupon</label>--}}
                        {{--<input type="text" class="form-control" v-model="form.coupon">--}}
                        {{--<span class="help-block" v-show="form.errors.has('coupon')">--}}
                            {{--@{{ form.errors.get('coupon') }}--}}
                        {{--</span>--}}
                    {{--</div>--}}

                    <!-- Tax / Price Information -->
                    <div class="form-group form-group-default" v-if="spark.collectsEuropeanVat && countryCollectsVat && selectedPlan">
                        <label>&nbsp;</label>
                        <div class="alert alert-info" style="margin: 0;">
                            <strong>Tax:</strong> @{{ taxAmount(selectedPlan) | currency }}
                            <br><br>
                            <strong>Total Price Including Tax:</strong>
                            @{{ priceWithTax(selectedPlan) | currency }} / @{{ selectedPlan.interval | capitalize }}
                        </div>
                    </div>

                    <!-- Subscribe Button -->
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" @click.prevent="subscribe" :disabled="form.busy">
                            <span v-if="form.busy">
                                <i class="fa fa-btn fa-spinner fa-spin"></i>Subscribing
                            </span>

                            <span v-else>
                                Subscribe
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</spark-subscribe-stripe>
