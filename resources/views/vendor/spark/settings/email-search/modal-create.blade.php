<!-- Add email search Modal -->
<div class="modal fade" id="emailSearchAddModal" aria-hidden="false"
    aria-labelledby="emailSearchAddModalLabel" role="dialog" tabindex="-1"
>
    <div class="modal-dialog modal-simple">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                <h4 class="modal-title" id="exampleFormModalLabel">Add Email Search Integrations</h4>
            </div>
            <div class="modal-body form-icons form-icons-right">
                <div class="mb-20">
                    <label for="">Integration</label>
                    <select
                        id="apiSelect"
                        class="form-control"
                        v-model="newIntegration.source"
                    >
                        <option value="">Select Integration</option>
                        <option v-for="(source, index) in availableSources" :value="index">
                            @{{ source }}
                        </option>
                    </select>
                    <small class="text-xs text-danger" v-if="createErrors.source">@{{createErrors.source[0]}}</small>
                </div>
                <div class="mb-20">
                    <label for="">Api Key</label>
                    <input type="text" class="form-control" name="newIntegration-key" v-model="newIntegration.key" placeholder="API Key">
                    <small class="text-xs text-danger" v-if="createErrors.key">@{{createErrors.key[0]}}</small>
                </div>
                <div class="mb-20" v-show="newIntegration.source=='snov'">
                    <label for="">User ID</label>
                    <input type="text" class="form-control" name="newIntegration-client_id" v-model="newIntegration.client_id" placeholder="API User ID ">
                    <small class="text-xs text-danger" v-if="createErrors.client_id">@{{createErrors.client_id[0]}}</small>
                </div>
                <div>
                    <button type="button" 
                        class="btn btn-primary"
                        @click="saveNewIntegration"
                        :disabled="processing"
                    >
                        <i class="icon wb-chevron-right hidden-md"></i>
                        Submit
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>