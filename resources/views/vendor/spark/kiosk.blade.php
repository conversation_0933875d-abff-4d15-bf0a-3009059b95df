@extends('layouts.plain')

@section('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mousetrap/1.4.6/mousetrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.4.0/Chart.min.js"></script>
@endsection

@section('page-title', 'Kiosk')

@section('content')
<spark-kiosk :user="user" inline-template>
    <div class="row">
        <!-- Tabs -->
        <div class="col-md-4">
            <div class="card card-shadow">
                {{--<div class="card-header card-header-transparent">--}}
                    {{--Kiosk--}}
                {{--</div>--}}

                <div class="card-body spark-settings-tabs">
                    <ul class="nav spark-settings-stacked-tabs list-group" role="tablist">
                        <!-- Announcements Link -->
                        {{--<li role="presentation" class="active">--}}
                            {{--<a href="#announcements" aria-controls="announcements" role="tab" data-toggle="tab"--}}
                               {{--class="list-group-item list-group-item-action active">--}}
                                {{--<i class="fa fa-fw fa-btn fa-bullhorn"></i>Announcements--}}
                            {{--</a>--}}
                        {{--</li>--}}

                        <!-- Metrics Link -->
                        @if (Spark::developer(Auth::user()->email))
                            <li role="presentation">
                                <a href="#metrics" aria-controls="metrics" role="tab" data-toggle="tab"
                                   class="list-group-item list-group-item-action active">
                                    <i class="fa fa-fw fa-btn fa-bar-chart"></i>Metrics
                                </a>
                            </li>
                        @endif

                        <!-- Users Link -->
                        <li role="presentation">
                            <a href="#users" aria-controls="users" role="tab" data-toggle="tab"
                               class="list-group-item list-group-item-action">
                                <i class="fa fa-fw fa-btn fa-user"></i>Users
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Tab cards -->
        <div class="col-md-8">
            <div class="tab-content">
                <!-- Announcements -->
                {{--<div role="tabcard" class="tab-pane active" id="announcements">--}}
                    {{--@include('spark::kiosk.announcements')--}}
                {{--</div>--}}

                <!-- Metrics -->
                @if (Spark::developer(Auth::user()->email))
                    <div role="tabcard" class="tab-pane active" id="metrics">
                        @include('spark::kiosk.metrics')
                    </div>
                @endif

                <!-- User Management -->
                <div role="tabcard" class="tab-pane" id="users">
                    @include('spark::kiosk.users')
                </div>
            </div>
        </div>
    </div>
</spark-kiosk>
@endsection
