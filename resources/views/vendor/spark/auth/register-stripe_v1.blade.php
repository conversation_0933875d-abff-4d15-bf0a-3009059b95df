@extends('layouts.register_v1')

@section('scripts')
    <script src="https://js.stripe.com/v2/"></script>
@endsection

@section('content')
<spark-register-stripe inline-template>
    <div class="page-content vertical-align-middle animation-slide-top animation-duration-1 spark-screen">
        <div class="panel authbox authbox-lg mx-auto">
            <div class="panel-body p-0"> 
                <div class="pt-30 px-40">
                    <div class="brand mb-20">
                        {{-- 
                        @if (Environment::getLogoUrl())
                            <img class="brand-img" src="{{ Environment::getLogoUrl() }}" alt="logo">
                        @else
                            <h2 class="brand-text font-size-40">{{ Environment::getLogoTitle() }}</h2>
                        @endif
                        --}}

                        @if (Environment::getLogoUrlSecondary())
                            <img class="img-fluid img-logo" src="{{ Environment::getLogoUrlSecondary() }}" alt="logo"> 
                        @elseif (Environment::getLogoUrl())
                            <img class="img-fluid img-logo" src="{{ Environment::getLogoUrl() }}" alt="logo">
                        @else
                            <h2 class="brand-text font-size-18 d-none">{{ Environment::getLogoTitle() }}</h2>
                        @endif
                    </div>
                    <p class="font-size-16">Register to access your campaign data.</p>
                </div>

                <!-- Common Register Form Contents -->
                @include('spark::auth.register-common_v1')
            </div>
        </div>

        <footer class="page-copyright page-copyright-inverse">
            {{--<p>{{ Environment::getLogoTitle() }} © {{ date('Y') }}. All RIGHTS RESERVED.</p>--}}
            <p>Copyright © {{ date('Y') }} {{ Environment::getLogoTitle() }}. All Rights Reserved</p>
        </footer>

    </div>
</spark-register-stripe>
@endsection
