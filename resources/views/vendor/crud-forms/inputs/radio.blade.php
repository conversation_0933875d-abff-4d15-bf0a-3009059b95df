<?php

if (old($field['name'])) {
    $selectedOption = old($field['name']);
} else {
    $selectedOption = $entity->{$field['name']};
}

?>

<p class="all-caps bold">
    {{ $field['label'] }}
</p>

@foreach($relationshipOptions["{$field['relationship']}"] as $key=>$val)
<div class="radio-custom radio-success">
    <input type="radio"
           name="{{ $field['name'] }}"
           value="{{ $key }}"
           @if ($key == $selectedOption)
               checked="checked"
           @endif
           id="{{ $field['name'] . '_' . $key }}"
    >
    <label for="{{ $field['name'] . '_' . $key }}">{{ $val }}</label>
</div>
@endforeach