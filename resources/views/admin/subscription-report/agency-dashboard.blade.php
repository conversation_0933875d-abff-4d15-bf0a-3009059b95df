@extends('layouts.app')

@section('page-title', 'Agency Dashboard Subscriptions')

@section('content-layout')
  <div class="page">
    <div class="page-content">
      <h3>
        Agency Dashboard Subscriptions Report
          <a href="{{ route('admin.subscription-report.agency-dash').'?download=1' }}" class="btn btn-primary btn-outline ml-20">
            Download CSV
            <i aria-hidden="true" class="icon wb-download"></i>
          </a>

      </h3>
      <p>This report shows all users who have created an agency-dashboard subscription</p>
      <div class="row">
        <div class="col-md-12">
          <div class="panel">
            <div class="panel-body container-fluid">
              <table class="table table-striped table-bordered table-responsive">
                <thead>
                  <tr>
                    <td>name</td>
                    <td>owner</td>
                    <td>email</td>
                    <td>created_at</td>
                    <td>active</td>
                    <td>trialing</td>
                    <td>email_accounts</td>
                    <td>campaigns</td>
                    <td>users</td>
                  </tr>
                </thead>
                <tbody>
                  @foreach($agencies as $agency)
                    <tr>
                      <td>{{ $agency['name'] }}</td>
                      <td>{{ $agency['owner'] }}</td>
                      <td>{{ $agency['email'] }}</td>
                      <td>{{ $agency['created_at'] }}</td>
                      <td>{{ $agency['active'] }}</td>
                      <td>{{ $agency['trialing'] }}</td>
                      <td>{{ $agency['email_accounts'] }}</td>
                      <td>{{ $agency['campaigns'] }}</td>
                      <td>{{ $agency['users'] }}</td>
                    </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
@endsection
