@extends('layouts.app')

@section('page-title', 'Clients')

@section('page-styles') 
<style>
.active-user-label {
    display: inline-block;
} 

@media (max-width: 1300px) {
    .active-user-label {
        display: none;
    } 
}
</style>
@endsection

@section('page-scripts')
    
@endsection
  
@section('content-layout') 
    <client-list
        :agencies="{{$agencies}}"
        inline-template
    >
        <div class="page loader-wrapper" :class="{'loader-wrapper-loading': isLoading}">
            <div class="page-header page-header-bordered">
                <h1 class="page-title">Clients</h1>
                <div class="page-header-actions">
                    @can('agency-admin')
                        <a href="/clients/create" class="btn btn-outline btn-primary btn-round">
                            <span class="text hidden-sm-down">Add Client</span>
                            <i class="icon wb-plus" aria-hidden="true"></i>
                        </a>
                    @endcan
                </div>
            </div>

            <div class="page-content container-fluid">

                @if (session('status'))
                    <div class="mb-20 alert-icon alert-dismissible alert alert-{{session('status') == 'success' ? 'success' : 'danger'}}">
                        @if (session('status') == 'success')
                            <i class="icon wb-check-circle"></i>
                        @else
                            <i class="icon wb-close-circle"></i>
                        @endif
                        
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button> 
                        {{ session('msg') }}
                    </div>
                @endif  
                
                @include('clients.client-list-filter')
                
                <div v-if="arrClients.total">
                    <div class="card card-shadow mb-5" v-for="(client, index) in arrClients.data">
                        <div class="card-block pb-15">
                            <div class="row">
                                {{-- 
                                <div class="col-lg-9 col-xl-7 col-md-8 col-sm-8 col-8">
                                    <div class="row">
                                        <div class="col-md-12 col-xl-4 col-lg-5">
                                            <h4 class="m-0">
                                                <span class="d-block hidden-md-down pt-10"></span>
                                                <span class="d-block text-truncate">
                                                    <a :href="'/clients/'+client.hashid" class="text-decoration-none blue-grey-700">
                                                        @{{client.name}}
                                                    </a>
                                                </span> 
                                                @can('admin') 
                                                    <small class="d-block pt-5 hidden-md-down text-capitalize">
                                                        @{{client.agency.name}} Agency
                                                    </small>
                                                @endcan

                                                <small class="d-block pt-5 hidden-lg-up">
                                                    <span class="d-inline-block">
                                                        @{{client.email_accounts_count}} Email Accounts
                                                    </span>
                                                    <span class="mx-10">&bull;</span>
                                                    <span class="d-inline-block">
                                                        @{{client.not_archived_campaigns_count}} Campaigns
                                                    </span>
                                                    <span class="mx-10">&bull;</span>
                                                    <span class="d-inline-block">
                                                        @{{client.positive_prospects_count}} Positive Responses
                                                    </span>
                                                </small>
                                            </h4>
                                        </div>
                                        <div class="col-md-12 col-xl-8 col-lg-7 text-center hidden-md-down px-0">
                                            <h4 class="m-0 d-inline-block text-center px-10">
                                                @{{client.email_accounts_count}}
                                                <small class="d-block pt-5">Users</small>
                                            </h4>
                                            <h4 class="m-0 d-inline-block text-center px-10">
                                                @{{client.email_accounts_count}}
                                                <small class="d-block pt-5">Email<span class="hidden-lg-down"> Account</span>s</small>
                                            </h4>
                                            <h4 class="m-0 d-inline-block text-center px-10">
                                                @{{client.not_archived_campaigns_count}}
                                                <small class="d-block pt-5">Campaigns</small>
                                            </h4>
                                            <h4 class="m-0 d-inline-block text-center px-10">
                                                @{{client.positive_prospects_count}}
                                                <small class="d-block pt-5">Positive Responses</small>
                                            </h4>
                                        </div>
                                    </div>
                                </div>
                                --}}

                                <div class="col-xl-4 col-lg-4 col-md-8 col-sm-8 col-8">
                                    <h4 class="m-0">
                                        <span class="d-block hidden-md-down pt-10"></span>
                                        <span class="d-block text-truncate">
                                            <a :href="'/clients/'+client.hashid" class="text-decoration-none blue-grey-700">
                                                @{{client.name}}
                                            </a>
                                        </span> 
                                        @can('admin') 
                                            <small class="d-block pt-5 hidden-md-down text-capitalize">
                                                @{{client.agency.name}} Agency
                                            </small>
                                        @endcan

                                        <small class="d-block pt-5 hidden-lg-up">
                                            <span class="d-inline-block">
                                                @{{client.email_accounts_count}} Email Accounts
                                            </span>
                                            <span class="mx-10">&bull;</span>
                                            <span class="d-inline-block">
                                                @{{client.not_archived_campaigns_count}} Campaigns
                                            </span>
                                            <span class="mx-10">&bull;</span>
                                            <span class="d-inline-block">
                                                @{{client.positive_prospects_count}} Positive Responses
                                            </span>
                                        </small>
                                    </h4>
                                </div>

                                <div class="col-xl-5 col-lg-5 col-md-4 col-sm-4 col-4 hidden-md-down">
                                    <h4 class="m-0 d-inline-block text-center px-10">
                                        @{{client.users_count ? client.users_count - 1 : 0}}
                                        <small class="d-block pt-5">
                                            <span class="hidden-lg-down active-user-label">Active</span>
                                            Users
                                        </small>
                                    </h4>
                                    <h4 class="m-0 d-inline-block text-center px-10">
                                        @{{client.email_accounts_count}}
                                        <small class="d-block pt-5">Email<span class="hidden-lg-down"> Account</span>s</small>
                                    </h4>
                                    <h4 class="m-0 d-inline-block text-center px-10">
                                        @{{client.not_archived_campaigns_count}}
                                        <small class="d-block pt-5">Campaigns</small>
                                    </h4>
                                    <h4 class="m-0 d-inline-block text-center px-10">
                                        @{{client.positive_prospects_count}}
                                        <small class="d-block pt-5">
                                            Positive<span class="hidden-lg-down"> Response</span>s
                                        </small>
                                    </h4>
                                </div>
 
                                <div class="col-xl-3 col-lg-3 col-md-4 col-sm-4 col-4 text-right pt-3 pl-0">
                                    <a :href="'/clients/'+client.hashid" 
                                        class="btn btn-default"
                                        data-toggle="tooltip" data-placement="top"
                                        title="Client details"
                                    >
                                        <i aria-hidden="true" class="icon wb-settings"></i>
                                    </a>
                                    <a :href="'/clients/'+client.hashid+'/users'" 
                                        class="btn btn-primary hidden-md-down"
                                        data-toggle="tooltip" data-placement="top"
                                        title="Users"
                                    >
                                        <i aria-hidden="true" class="icon wb-users"></i>
                                    </a>
                                    <a :href="'/clients/'+client.hashid+'/suppressions'" 
                                        class="btn btn-primary hidden-md-down"
                                        data-toggle="tooltip" data-placement="top"
                                        title="Suppression"
                                    >
                                        <i aria-hidden="true" class="icon fa fa-user-times"></i>
                                    </a>
                                    <a :href="'/clients/'+client.hashid+'/domains'" 
                                        class="btn btn-primary hidden-md-down"
                                        data-toggle="tooltip" data-placement="top"
                                        title="Unsubscribe URLs"
                                    >
                                        <i aria-hidden="true" class="icon wb-link"></i>
                                    </a>
                                </div>
                            </div>
                        </div> 
                    </div>  

                    <div class="text-center">
                        <pagination :data="arrClients" :limit="2" @pagination-change-page="getClients"></pagination> 
                    </div>
                </div>

                <div v-else>   
                    <div class="panel panel-body">
                        <div class="alert alert-warning mb-0"> 
                            <i class="wb-alert-circle mr-10"></i>
                            @can('admin')
                                No clients found
                            @else
                                No clients yet, let's <a href="{{url('/clients/create')}}">add one</a>
                            @endcan
                        </div>
                    </div>
                </div>
                 

            </div>  
            
            <div class="loader-box vertical-align text-center">
                <div class="loader vertical-align-middle loader-circle"></div>
            </div>
        </div> 
    </client-list>
@endsection
