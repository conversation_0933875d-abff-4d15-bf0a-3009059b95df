@if (config('app.env') == 'production' && Auth::check() && !Auth::user()->isAdmin())
	
	<script>
		window['_fs_debug'] = false;
		window['_fs_host'] = 'fullstory.com';
		window['_fs_org'] = 'HA5D6';
		window['_fs_namespace'] = 'FS';
		(function(m,n,e,t,l,o,g,y){
		    if (e in m) {if(m.console && m.console.log) { m.console.log('FullStory namespace conflict. Please set window["_fs_namespace"].');} return;}
		    g=m[e]=function(a,b,s){g.q?g.q.push([a,b,s]):g._api(a,b,s);};g.q=[];
		    o=n.createElement(t);o.async=1;o.src='https://'+_fs_host+'/s/fs.js';
		    y=n.getElementsByTagName(t)[0];y.parentNode.insertBefore(o,y);
		    g.identify=function(i,v,s){g(l,{uid:i},s);if(v)g(l,v,s)};g.setUserVars=function(v,s){g(l,v,s)};g.event=function(i,v,s){g('event',{n:i,p:v},s)};
		    g.shutdown=function(){g("rec",!1)};g.restart=function(){g("rec",!0)};
		    g.consent=function(a){g("consent",!arguments.length||a)};
		    g.identifyAccount=function(i,v){o='account';v=v||{};v.acctId=i;g(o,v)};
		    g.clearUserCookie=function(){};
		})(window,document,window['_fs_namespace'],'script','user');
	</script>

	 
	<script>
		// This is an example script - don't forget to change it!
		FS.identify('{{Auth::user()->id}}', {
		  displayName: '{{Auth::user()->name}}',
		  email: '{{Auth::user()->email}}',
		  // TODO: Add your own custom user variables here, details at
		  // http://help.fullstory.com/develop-js/setuservars
		  reviewsWritten_int: 14,
		});
	</script>
	
@endif

