<!DOCTYPE html>
<html class="no-js css-menubar" lang="en">
<head>
    @if(Request::is('agency/setup*') || Request::is('settings*') || Request::is('register') || Request::is('email-accounts'))
        @include('layouts.includes.google_tag_head')
    @endif

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>
        @if (isset($htmlTitle))
            {{ $htmlTitle }} -
        @else
            @hasSection('page-title')
                @yield('page-title') -
            @endif
        @endif
        {{ Environment::getLogoTitle() }}
    </title>

    @include('layouts.includes.favicon')

    <!-- Stylesheets -->
    <link rel="stylesheet" href="{{ mix('css/styles.css') }}">

    <!-- Plugins -->
    <link rel="stylesheet" href="{{ mix('css/plugins.css') }}">

    <!-- Datatable -->
    @if(Request::is('stats/campaign*') || Request::is('contacts') || Request::is('prospects-demo'))
    <link rel="stylesheet" href="{{ mix('css/datatables.css') }}">
    @endif

    <!-- Skin Color -->
    <link rel="stylesheet" href="/css/skins/{{ Environment::getPrimaryColor() }}.min.css">

    <!-- Fonts -->
    <link rel="stylesheet" href="{{ mix('fonts/fonts.css') }}">
    <link rel='stylesheet' href='https://fonts.googleapis.com/css?family=Roboto:300,400,500,300italic'>

    <!-- Style overrides -->
    <link rel="stylesheet" href="{{ mix('css/custom.css') }}">

    <!-- Just hide menu icon for agency admin to fix crowded menu on medium/large screen -->
    @if ( Auth::user()->agency->is_leadsoft_enabled && Auth::user()->can('agency-admin') )
        <style>
            @media (max-width: 1350px) {
                .site-menu-item .site-menu-icon {
                    display: none;
                }
            }
            @media (max-width: 767px) {
                .site-menu-item .site-menu-icon {
                    display: inline-block;
                }
            }
        </style>
    @endif

    @yield('page-styles', '')

    <!--[if lt IE 9]>
    <script src="/js/html5shiv.min.js"></script>
    <![endif]-->

    <!--[if lt IE 10]>
    <script src="/js/media.match.min.js"></script>
    <script src="/js//respond.min.js"></script>
    <![endif]-->

    <!-- Scripts -->
    <script src="{{ mix('js/breakpoints.js') }}"></script>
    <script>
        Breakpoints();
    </script>

    @yield('scripts', '')

    <!-- Global Spark Object -->
    <script>
        window.Spark = <?php echo json_encode(array_merge(
            Spark::scriptVariables(), [
                'agency' => Environment::getAgency(),
                'primary_color' => Environment::getPrimaryColorCode(),
                'user_plans' => [
                  'user-dashboard',
                  config('app.billing.rippleAgencyPriceId'),
                  config('app.billing.crestAgencyPriceId'),
                  config('app.billing.tsunamiAgencyPriceId')
                ],
                'agency_plans' => ['agency-dashboard'],
            ]
        ));
        ?>
    </script>

{{--    @include('layouts.includes.fullstory_script')--}}
{{--    @include('layouts.includes.first_promoter')--}}
    @include('layouts.includes.crisp')
</head>
<!--  {{ !Auth::check() || !Auth::user()->showMenu() ? 'no-menu' : '' }} -->
<!-- class:: animsition -->
<body class=" site-navbar-small {{ !Auth::check() ? 'no-menu' : '' }} page-theme-{{Environment::getPrimaryColor()}}">

    @if(Request::is('agency/setup*') || Request::is('settings*') || Request::is('register') || Request::is('email-accounts'))
        @include('layouts.includes.google_tag_body')
    @endif

    <div id="spark-app" v-cloak>
        <!--[if lt IE 8]>
        <p class="browserupgrade">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
        <![endif]-->

        <!-- Navigation -->
        @if (Auth::check() && (Auth::user()->agency->stripe_id || Auth::user()->showMenu()))
            @include('layouts.nav.user')
        @else
            @include('layouts.nav.guest')
        @endif

        @yield('content-layout')

        <!-- Footer -->
        <footer class="site-footer">
            <div class="site-footer-legal">Copyright © {{ date('Y') }} {{ Environment::getLogoTitle() }}</div>
            <div class="site-footer-right">
                Built with <i class="red-600 wb wb-heart"></i> by {{ Environment::getLogoTitle() }}
            </div>
        </footer>

        <!-- Application Level Modals -->
        @if (Auth::check())
            {{--@include('spark::modals.notifications')--}}
            {{--@include('spark::modals.support')--}}
            @include('spark::modals.session-expired')
        @endif
    </div>

    <!-- Core Plugin Dependencies (eg bootstrap) -->
    <script src="{{ mix('js/core.js') }}"></script>

    <!-- Additional jQuery Plugins needed by template -->
    <script src="{{ mix('js/plugins.js') }}"></script>

    <script src="{{ asset('vendor/chartist/chartist.min.js') }}"></script>
{{--    <script src="{{ asset('vendor/chartist/chartist-plugin-tooltip.min.js') }}"></script>--}}
{{--    <script src="{{ asset('vendor/chartist/tooltip-updated/chartist-plugin-tooltip.min.js') }}"></script>--}}

    <!-- Additional datatables plugin -->
    @if(Request::is('stats/campaign*') || Request::is('contacts') || Request::is('prospects-demo') || Request::is('admin/*'))
    <script src="{{ mix('js/datatables.js') }}"></script>
    @endif

    <!-- Template setup scripts -->
    <script src="{{ mix('js/template.js') }}"></script>

    <!-- Template page scripts & Plugin wrappers-->
    <script src="{{ mix('js/page.js') }}"></script>

    <!-- Vue application scripts -->
    <script src="{{ mix('js/app.js') }}"></script>

    <!-- Page Run Scripts -->
    @section('page-scripts')
    @show

{{--    @include('layouts.includes.intercom_script')--}}
    @include('layouts.includes.customerly')
</body>
</html>
