@extends('layouts.app')

@section('page-title', 'Date Range Report')

@section('page-styles')
    <style>
        .report-campaign-list { overflow-y: scroll; max-height: 160px; height: 160px; margin-right: 10px; }
        .response-stats-label { position: relative; margin-bottom: -14px !important; }
        #bydayResponseChart .ct-chart-bar .ct-bar { stroke: #49de94; stroke-width: 10%; }
        /*#downloadBtn { position: absolute; top: -60px; right: 0px; }*/
        .posr { position: relative; }
        .checkbox-reports-box { border: 1px solid rgba(0,0,0,0.1) !important; border-radius: 3px; padding: 5px 10px; margin-bottom: 5px; }
        .checkbox-custom.checkbox-reports { margin-top: 0 !important; margin-bottom: 0 !important;  }
        .checkbox-custom.checkbox-reports label { display: block !important; }
        .checkbox-reports-box.selected { background-color: #f3f7f9; }
        #bydayResponseChart .ct-square:before { padding-bottom: 150px !important; }
        .flex-grow { display: flex; flex-grow: 1; }
        .ct-series-b .ct-line {
            stroke-width: 2px;
        }
        .ct-series-b .ct-point {
            stroke-width: 6px;
        }
    </style>

    <!-- include customize vc styles if not present in vc skins -->
    <!-- https://vcalendar.io/colors-dark-mode.html -->
    @if(in_array(Environment::getPrimaryColor(), ['black', 'brown', 'cyan', 'grey', 'wavo']))
        <link rel="stylesheet" href="/css/v-calendar/{{ Environment::getPrimaryColor() }}.min.css">
    @endif
@endsection

@section('content-layout')
<stats-date-range-report
    :campaigns="{{$campaigns}}"
    :agencies="{{$agencies}}"
    :teams="{{$teams}}"
    inline-template
>
    <div class="page">
        <div class="page-header page-header-bordered">
            <h1 class="page-title">
                Date Range Report
            </h1>
            <div class="page-header-actions">
            </div>
        </div>

        <div class="page-content container-fluid">

            <div class="posr">
                @include('partials.journey-alert')
                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-bordered">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-lg-6">
                                        <h4 class="font-size-18 m-0 clearfix">
                                            Select Campaigns
                                        </h4>

                                        <div class="row py-10 pr-10">
                                            <div class="col-lg-9">
                                                <input type="text" class="form-control"
                                                       v-model="filters.keyword"
                                                       placeholder="Filter by campaign name..."
                                                       @keyup="filterByKeyword"
                                                >
                                            </div>
                                            <div class="col-lg-3">
                                                <button type="button"
                                                        v-if="!selectedAllCampaigns"
                                                        class="btn btn-block btn-default btn-icon mr-20"
                                                        @click="selectAllCampaigns"
                                                >
                                                    <i class="icon wb-align-justify"></i>
                                                    <span class="hidden-lg-down">Select All</span>
                                                </button>
                                                <button type="button"
                                                        v-else
                                                        class="btn btn-block btn-default btn-icon mr-20"
                                                        @click="deselectAllCampaigns"
                                                >
                                                    <i class="icon wb-close"></i>
                                                    <span class="hidden-lg-down">Deselect All</span>
                                                </button>
                                            </div>
                                        </div>

                                        @can('support')
                                            <div class="py-10 pr-10">
                                                <select class="form-control cap text-capitalize"
                                                        v-model="filters.agency_id" @change="filterByAgency"
                                                        data-plugin="selectpicker" data-style="btn-outline btn-default"
                                                >
                                                    <option value="0">All Agencies</option>
                                                    <option v-for="(agency, index) in filters.agencies" :value="agency.id">
                                                        @{{agency.name}}
                                                    </option>
                                                </select>
                                            </div>
                                        @endcan

                                        @if ((Auth::user()->can('agency-admin') && Auth::user()->agency->isAgencyDashboard()) || Auth::user()->can('support'))
                                            <div class="py-10 pr-10">
                                                <select class="form-control select-campaignclient"
                                                        v-model="filters.team_id" data-plugin="selectpicker"
                                                        data-style="btn-outline btn-default" @change="filterByTeam"
                                                        :disabled="filters.agency_id == 0"
                                                >
                                                    @if(Auth::user()->can('agency-admin') || Auth::user()->can('support'))
                                                        <option value="0">All Clients</option>
                                                    @endif
                                                    <option v-for="(team, index) in filters.teams" :value="team.id">@{{team.name}}</option>
                                                </select>
                                            </div>
                                        @endif

                                        <div class="report-campaign-list">
                                            <div class="py-10 pr-10">
                                                <div class="checkbox-reports-box"
                                                     :class="{'d-none': campaign.is_hidden}"
                                                     v-for="(campaign, index) in reportCampaigns"
                                                     :key="campaign.hashid"
                                                >
                                                    <div class="checkbox-custom checkbox-primary checkbox-reports">
                                                        <input type="checkbox"
                                                               :id="'campaignCheck'+campaign.hashid"
                                                               v-model="campaignIds"
                                                               :value="campaign.hashid"
                                                        >
                                                        <label :for="'campaignCheck'+campaign.hashid">@{{campaign.name}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <h4 class="font-size-18 m-0 clearfix">
                                            Select Date Range
                                        </h4>
                                        <div class="py-10 pr-10 form-group">
                                            <div class="input-group">
                                                <v-date-picker
                                                    mode="date"
                                                    v-model="dateRange"
                                                    :color="spark.agency.color"
                                                    :columns="2"
                                                    class="flex-grow"
                                                    locale="en-US"
                                                    :first-day-of-week="2"
                                                    :min-date="new Date(2020, 5, 1)"
                                                    is-range
                                                >
                                                    <template v-slot="{ inputValue, inputEvents }">
                                                        <input type="text"
                                                               id="dateRange"
                                                               class="form-control"
                                                               :value="inputValue.start ? inputValue.start  + ' - ' + inputValue.end : ''"
                                                               v-on="inputEvents.start"
                                                        >
                                                    </template>
                                                </v-date-picker>
                                                <span class="input-group-btn">
                                                    <button type="button"
                                                            class="btn btn-default"
                                                            @click="dateRange = null"
                                                    >Clear</button>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="alert-icon alert alert-default mr-10">
                                            <i class="icon wb-alert-circle"></i>
                                            There might be a small difference on a day's stats as these reports are calculated based on the campaign timezone and time of tagging a reply as positive or negative.
                                        </div>
                                    </div>
                                    <div class="col-lg-6 text-right">
                                        <span v-if="!dayLabels.length">
                                            <button class="btn btn-primary" disabled>
                                                <span class="text hidden-sm-down">Download Report</span>
                                                <i class="icon wb-download" aria-hidden="true"></i>
                                            </button>
                                        </span>
                                        <span v-else>
                                            <form target="_blank" id="exportReportForm"
                                                action="{{url('reports/date-range/export')}}"
                                                method="POST" class="d-inline-block"
                                            >
                                                @csrf
                                                <input type="hidden" id="reportCampaignIds" name="campaign_ids">
                                                <input type="hidden" id="reportFrom" name="from">
                                                <input type="hidden" id="reportTo" name="to">
                                                <input type="hidden" id="reportDayLabels" name="day_labels">
                                                <input type="hidden" id="reportCampaignStats" name="campaign_stats">
                                                <input type="hidden" id="reportMessageStats" name="message_stats">

                                                <button class="btn btn-primary "
                                                    type="button" @click.prevent="downloadReportCSV"
                                                >
                                                    <span class="text hidden-sm-down">Download Report</span>
                                                    <i class="icon wb-download" aria-hidden="true"></i>
                                                </button>
                                            </form>
                                        </span>

                                        <button class="btn btn-primary" @click="report">
                                            Show Stats
                                        </button>
                                        <span id="error" class="error float-left mt-10" v-show="error != ''">
                                            @{{ error }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row" v-show="dayLabels.length > 0">
                    <div class="col-lg-6" v-for="(chart, index) in charts">
                        <div class="panel">
                            <div class="panel-heading">
                                <div class="panel-title font-size-14 blue-grey-700 font-weight-300 pb-0 pt-30">
                                    <div class="text-truncate">
                                        @{{ chart }}
                                    </div>
                                    <div v-if="totals[snake(chart)]">
                                        <div class="total font-size-20 font-weight-400 d-inline-block">
                                            @{{ totals[snake(chart)].total }}
                                        </div>
                                        <div class="counter counter-sm text-left d-inline-block ml-10">
                                            <div class="counter-number-group">
                                                <span :class="totals[snake(chart)].diffAmount >= 0 ? 'green-600' : 'red-600'" class="counter-icon mr-5">
                                                    <i :class="totals[snake(chart)].diffAmount >= 0 ? 'wb-graph-up' : 'wb-graph-down'"></i>
                                                </span>
                                                <span class="counter-number">@{{ totals[snake(chart)].diffText }}%</span>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="panel-body mb-0 pb-0 pt-20" :class="kebab(chart) + '-activity-box'">
                                <div v-if="isLoading">
                                    <div class="loadingEmailActivityChart">
                                        <div class="loader loader-circle"></div>
                                    </div>
                                </div>
                                <div :class="'ct-'+kebab(chart)+'-activity ct-'+kebab(chart)+'-activity-line'"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</stats-date-range-report>

@endsection
