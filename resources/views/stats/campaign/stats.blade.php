{{--<div class="row mb-20">--}}
{{--    <div class="col-md-12">--}}
{{--        <div class="btn-group" style="width: 100%">--}}
{{--            <button type="button" class="btn btn-primary btn-block text-left dropdown-toggle"--}}
{{--                id="campaignDropdown"--}}
{{--                data-toggle="dropdown" aria-expanded="false"--}}
{{--            >--}}
{{--                <strong class="d-inline-block" style="width: 98%;">@{{selected_email}}</strong>--}}
{{--            </button>--}}
{{--            <div class="dropdown-menu scrollable-menu" aria-labelledby="campaignDropdown" role="menu" style="width: 100%;">--}}
{{--                <a class="dropdown-item" href="javascript:void(0)" role="menuitem"--}}
{{--                    @click="filterStatsByEmail(null)"--}}
{{--                >--}}
{{--                    All Email Accounts--}}
{{--                </a>--}}
{{--                <a class="dropdown-item" href="javascript:void(0)" role="menuitem"--}}
{{--                    v-for="email in campaign.email_accounts"--}}
{{--                    @click="filterStatsByEmail(email)"--}}
{{--                >--}}
{{--                    @{{ email.email_address }}--}}
{{--                </a>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </div>--}}
{{--</div>--}}

<!-- Daily Tracking Link -->
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-end">
            <a href="/campaigns/{{ $campaign->hashid }}/daily-stats" class="btn btn-outline-primary">
                <i class="fa fa-clock-o mr-2"></i>
                View Daily Tracking
            </a>
        </div>
    </div>
</div>

<div class="row" data-plugin="matchHeight" data-by-row="true">
    <div class="col-xxl-12">
        <div class="row">
            <div class="col-lg-4 col-xl-4 mb-30 col-md-6">
                <div class="card-shadow h-full card p-30 flex-row justify-content-between mb-0">
                    <div class="counter counter-md text-left">
                        <div class="counter-number-group">
                            <span class="counter-number">{{ $stats->queue }}</span>
                            <span class="counter-number-related text-capitalize"></span>
                        </div>
                        <div class="counter-label text-capitalize font-size-16">
                            Queued
                        </div>
                    </div>
                    <div class="white pt-10">
                        <i aria-hidden="true" class="icon icon-circle icon-2x fa fa-address-card-o bg-{{Environment::getPrimaryColor()}}-600"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-xl-4 mb-30 col-md-6">
                <div class="card-shadow h-full card p-30 flex-row justify-content-between mb-0">
                    <div class="counter counter-md text-left">
                        <div class="counter-number-group">
                            <span class="counter-number">@{{ filter_email_delivery }}</span>
                            <span class="counter-number-related text-capitalize"></span>
                        </div>
                        <div class="counter-label text-capitalize font-size-16">
                            Contacted
                        </div>
                    </div>
                    <div class="white pt-10">
                        <i aria-hidden="true" class="icon icon-circle icon-2x fa fa-envelope-open-o bg-{{Environment::getPrimaryColor()}}-600"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-xl-4 mb-30 col-md-6">
                <div class="card-shadow h-full card p-30 flex-row justify-content-between mb-0">
                    <div class="counter counter-md text-left">
                        <div class="counter-number-group">
                            <span class="counter-number">
                                @{{ filter_email_replied }}
                                {{-- $campaign_responses --}}
                            </span>
                            <span class="counter-number-related text-capitalize"></span>
                        </div>
                        <div class="counter-label text-capitalize font-size-16">
                            Replied
                        </div>
                    </div>
                    <div class="white pt-10">
                        <i aria-hidden="true" class="icon icon-circle icon-2x fa fa-reply-all bg-{{Environment::getPrimaryColor()}}-600"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-xl-4 mb-30 col-md-6">
                <div class="card-shadow h-full card p-30 flex-row justify-content-between mb-0">
                    <div class="counter counter-md text-left">
                        <div class="counter-number-group">
                            <span class="counter-number">@{{sumPositive}}</span>
                            <span class="counter-number-related text-capitalize">Positive</span>
                        </div>
                        <div class="counter-label font-size-16">
                            @{{getStatPercent(sumPositive) + '% of Contacted'}}
                        </div>
                    </div>
                    <div class="white pt-10">
                        <i aria-hidden="true" class="icon icon-circle icon-2x fa fa-thumbs-o-up bg-green-600"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-xl-4 mb-30 col-md-6">
                <div class="card-shadow h-full card p-30 flex-row justify-content-between mb-0">
                    <div class="counter counter-md text-left">
                        <div class="counter-number-group">
                            <span class="counter-number">@{{sumNeutral}}</span>
                            <span class="counter-number-related text-capitalize">Neutral</span>
                        </div>
                        <div class="counter-label font-size-16">
                            @{{getStatPercent(sumNeutral) + '% of Contacted'}}
                        </div>
                    </div>
                    <div class="white pt-10">
                        <i aria-hidden="true" class="icon icon-circle icon-2x fa fa-clock-o  bg-yellow-600"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-xl-4 mb-30 col-md-6">
                <div class="card-shadow h-full card p-30 flex-row justify-content-between mb-0">
                    <div class="counter counter-md text-left">
                        <div class="counter-number-group">
                            <span class="counter-number">@{{sumNegative}}</span>
                            <span class="counter-number-related text-capitalize">Negative</span>
                        </div>
                        <div class="counter-label font-size-16">
                            @{{getStatPercent(sumNegative) + '% of Contacted'}}
                        </div>
                    </div>
                    <div class="white pt-10">
                        <i aria-hidden="true" class="icon icon-circle icon-2x fa fa-thumbs-o-down bg-red-600"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xxl-12">
        <div class="row">
            <!-- if(!$campaign->cleaned_at)  -->
            <div class="col-xxl-6 col-lg-6 col-md-6 col-sm-12 col-xs-12">
                <div class="card card-shadow" id="interest-bar-chart">
                    <div class="card-block p-30">
                        <h3 class="font-size-18 clearfix mt-5">
                            Positive Responses by Cadence Step
                        </h3>
                        <chartist
                                ref="chart1"
                                ratio="ct-major-seventh"
                                type="Bar"
                                :data="positiveCadenceData"
                                :options="cadenceBarOptions"
                        >
                        </chartist>
                    </div>
                </div>
            </div>

            <div class="col-xxl-6 col-lg-6 col-md-6 col-sm-12 col-xs-12">
                <div class="card card-shadow" id="unsubscribe-bar-chart">
                    <div class="card-block p-30">
                        <h3 class="font-size-18 clearfix mt-5">
                            Unsubscribes by Cadence Step
                        </h3>
                        <chartist
                                ref="chart2"
                                ratio="ct-major-seventh"
                                type="Bar"
                                :data="unsubscribeCadenceData"
                                :options="cadenceBarOptions">
                        </chartist>
                    </div>
                </div>
            </div>
            <!-- endif -->

            <div class="col-xxl-6 col-lg-6 col-md-6 col-sm-12 col-xs-12">
                <div class="card card-shadow" id="ampm-pie-chart">
                    <div class="card-block p-30">
                        <h3 class="font-size-18 clearfix mt-5">
                            Positive Responses by AM or PM
                        </h3>
                        <chartist
                                ref="chart3"
                                ratio="ct-major-seventh"
                                type="Pie"
                                :data="amPmData"
                                :options="am_pm_pie_options">
                        </chartist>
                        <ul class="list-inline text-center mt-10 mb-0">
                            <li class="list-inline-item mx-15">
                                <span class="icon wb-medium-point blue-600 mr-5"></span>
                                AM Responses: @{{amPmData.series[0]}}
                            </li>
                            <li class="list-inline-item mx-15">
                                <span class="icon wb-medium-point green-600 mr-5"></span>
                                PM Responses: @{{amPmData.series[1]}}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-xxl-6 col-lg-6 col-md-6 col-sm-12 col-xs-12">
                <div class="card card-shadow" id="cadence-steps-line-chart">
                    <div class="card-block pt-30 px-30 pb-25">
                        <h3 class="font-size-18 clearfix mt-5">
                            Cadence Status Snapshot
                        </h3>

                        <ul class="list-unstyled list-inline font-size-14">
                            <li class="list-inline-item mr-15">
                                <i class="icon wb-medium-point blue-200 mr-5" aria-hidden="true"></i>
                                Emails Delivered
                            </li>
                            <li class="list-inline-item mr-15">
                                <i class="icon wb-medium-point purple-200 mr-5" aria-hidden="true"></i>
                                Emails in Queue
                            </li>
                            <li class="list-inline-item mr-15">
                                <i class="icon wb-medium-point teal-200 mr-5" aria-hidden="true"></i>
                                Replies
                            </li>
                        </ul>

                        <chartist
                                ref="chart4"
                                ratio="ct-major-seventh"
                                type="Line"
                                :data="cadenceStepsData"
                                :options="lineAreaOptions">
                        </chartist>
                    </div>
                </div>
            </div>

            <div class="col-xxl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="card card-shadow" id="positives-by-day-bar-chart">
                    <div class="card-block p-30">
                        <h3 class="font-size-18 clearfix mt-5">
                            Positive Responses by Day Of Week
                        </h3>
                        <chartist
                                ref="chart5"
                                type="Bar"
                                :data="positivesByDayData"
                                :options="cadenceBarPerWeekOptions">
                        </chartist>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
