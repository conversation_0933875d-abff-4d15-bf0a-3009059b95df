@extends('layouts.app')

@section('page-title', 'Editor Test: TinyMCE')

@section('page-styles')
    <style>
        .edit-subject {
            color: #000;
            font-weight: 500;
            font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Oxygen,Ubuntu,Cantarell,'Open Sans','Helvetica Neue',sans-serif;
            line-height: 1.4;
        }
    </style>
@endsection

@section('content-layout')
    <editor-tiny inline-template>
        <div class="page loader-wrapper">
            <div class="page-header page-header-bordered">
                <h1 class="page-title">Editor Test: TinyMCE</h1>
            </div>
            <div class="page-content container-fluid pb-0">
                <div class="row">
                    <div class="col-md-12">
                        <label>Subject</label>
                        <span class="form-control edit-subject">
                            <tiny-editor
                                    v-model="mySubject"
                                    type="slim"
                                    :variables="vars"
                                    @update-field="checkEvent"
                            >
                            </tiny-editor>
                        </span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <label>Body</label>
                        <tiny-editor
                                v-model="myText"
                                type="simple"
                                :variables="vars"
                                @update-field="checkEvent"
                        >
                        </tiny-editor>
                    </div>
                </div>
{{--                <div class="row">--}}
{{--                    <div class="col-md-12">--}}
{{--                        <button @click="addText('first_name')">First Name</button>--}}
{{--                    </div>--}}
{{--                </div>--}}
            </div>
        </div>
    </editor-tiny>
@endsection

