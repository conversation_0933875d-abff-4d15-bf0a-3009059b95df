<div class="loader-wrapper":class="{'loader-wrapper-loading': isLoading}" >
    <div class="" style="position: relative;">
        <div data-role="container" class=" scrollable-container-mailbox w-full"
            style="height: 625px;"
        >
            <div data-role="content" class="tour-preview-box w-full" style=" ">
                <div id="mailContent" class="page-content page-content-table" data-plugin="selectable">
                    {{--<div class="div-1"></div>--}}

                    <div class="columnNonGroup">
                        <div class="columnContent">
                            <div class="columnTreeGrid scrollbar scrollbar-clear scrollbar-lg">
                                <div class="columnRowGroup columnRowGroupHeader">
                                    <div class="columnRow">
                                        <div class="columnCell columnFreeze w-350">
                                            <div class="py-10 pr-10">
                                                <div class="pl-10">
                                                    @if(Auth::user()->isAdmin() || Auth::user()->isSupport())
                                                        <div class="checkbox-custom checkbox-primary my-0 mr-10 d-inline-block">
                                                            <input type="checkbox" disabled>
                                                            <label for="emailCheckboxAllDisabled"></label>
                                                        </div>
                                                    @else
                                                        <div class="checkbox-custom checkbox-primary my-0 mr-10 d-inline-block">
                                                            <input type="checkbox" 
                                                                id="emailCheckboxAll"
                                                                v-model="emailCheckboxAll"
                                                                @change="toggleEmailCheckboxAll"
                                                            >
                                                            <label for="emailCheckboxAll"></label>
                                                        </div>
                                                    @endif
                                                    <label class="m-0 py-0 pl-10 pr-0 cp" for="emailCheckboxAll">
                                                        <strong>Company</strong>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="columnCell py-10 w-200">
                                            <strong class="pl-10">Actions</strong>
                                        </div>
                                        <div class="columnCell py-10 w-300">
                                            <strong>Contact Email</strong>
                                        </div>
                                        <div class="columnCell py-10 w-300" v-if="viewType == 'saved'">
                                            <strong>Full Name</strong>
                                        </div>
                                        <div class="columnCell py-10 w-300" v-if="viewType == 'saved'">
                                            <strong>Position</strong>
                                        </div>
                                        <div class="columnCell py-10 w-200">
                                            <strong>Links</strong>
                                        </div>
                                        <div class="columnCell py-10 w-250">
                                            <strong>Platform</strong>
                                        </div>
                                        <div class="columnCell py-10 w-250">
                                            <strong>Category</strong>
                                        </div>
                                        <div class="columnCell py-10 w-200">
                                            <strong>Location</strong>
                                        </div>
                                        <div class="columnCell py-10 w-250">
                                            <strong>Sales</strong>
                                        </div>
                                        <div class="columnCell py-10 w-150">
                                            <strong>Employees</strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="columnRowGroup columnRowGroupData"
                                    v-for="(domain, index) in domains"
                                    :class="{'columnRowGroupSelected': domain.isChecked}"
                                >
                                    <div class="columnRow align-items-center">
                                        <!-- company name and checkbox -->
                                        <div class="columnCell py-0 columnFreeze w-350"
                                        >
                                            <div class="py-10">
                                                @if(Auth::user()->isAdmin() || Auth::user()->isSupport())
                                                    <div class="checkbox-custom checkbox-primary my-0 mx-10 d-inline-block">
                                                        <input type="checkbox" disabled>
                                                        <label for="emailCheckboxAdmin"></label>
                                                    </div>
                                                @else
                                                    <div class="checkbox-custom checkbox-primary my-0 mx-10 d-inline-block"
                                                        v-if="domain.agency_domain && domain.agency_domain?.status != 'success'"
                                                    >
                                                        <input type="checkbox" disabled>
                                                        <label for="emailCheckboxFailed"></label>
                                                    </div>
                                                    <div class="checkbox-custom checkbox-primary my-0 mx-10 d-inline-block"
                                                        v-else
                                                    >
                                                        <input type="checkbox" 
                                                            :id="'checkboxItem_'+index"
                                                            v-model="domain.isChecked"
                                                            @change="toggleEmailCheckbox(domain)"
                                                        >
                                                        <label for="emailCheckboxAll"></label>
                                                    </div>
                                                @endif
                                            </div>

                                            <div class="p-10 cp"
                                                @click="showDomain(domain)"
                                            >
                                                <div class="d-flex align-items-center gap-5">
                                                    <img :src="domain.icon"
                                                        v-if="domain.icon"
                                                        alt="" class="companyThumbnail"
                                                        onerror="javascript:this.src='/img/icon-bg.png'"
                                                    >
                                                    <div v-else class="companyNameInitial">
                                                        @{{domain.merchant_name?.[0]}}
                                                    </div>
                                                    <div class="companyCellName">
                                                        <div class="companyCellName text-success">
                                                            @{{domain.merchant_name ?? domain.name}}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- action buttons -->
                                        <div class="columnCell companyCellData pr-10 py-10 w-200">
                                            <div class="pl-10">
                                                @include('data-search.companies.partials.list-action-buttons')
                                            </div>
                                        </div>

                                        <!-- contact email -->
                                        <div class="columnCell companyCellData pr-10 py-10 w-300">
                                            @{{domain.contact_email}}
                                            <small v-if="domain.agency_domain?.contacts_count > 1">
                                                (+@{{ domain.agency_domain.contacts_count - 1 }})
                                            </small>
                                        </div>

                                        <!-- contact name -->
                                        <div class="columnCell companyCellData pr-10 py-10 w-300 text-capitalize" v-if="viewType == 'saved'">
                                            @{{domain.contact_name || 'N/A'}}
                                        </div>

                                        <!-- contact position -->
                                        <div class="columnCell companyCellData pr-10 py-10 w-300 text-capitalize" v-if="viewType == 'saved'">
                                            @{{domain.contact_position || 'N/A'}}
                                        </div>

                                        <!-- social links -->
                                        <div class="columnCell companyCellData pr-10 py-10 w-200 d-flex gap-5 align-items-center">
                                            <a class="blue-grey-700" target="_new"
                                                :href="'//'+domain.name"
                                            >
                                                <i class="fa fa-link"></i>
                                            </a>
                                            <a class="blue-grey-700" target="_new"
                                                :href="domain?.contacts?.facebook" 
                                                v-if="domain?.contacts?.facebook"
                                            >
                                                <i class="fa fa-facebook-square"></i>
                                            </a>
                                            <a class="blue-grey-700" target="_new"
                                                :href="domain?.contacts?.instagram" 
                                                v-if="domain?.contacts?.instagram"
                                            >
                                                <i class="fa fa-instagram"></i>
                                            </a>
                                            <a class="blue-grey-700" target="_new"
                                                :href="domain?.contacts?.linkedin" 
                                                v-if="domain?.contacts?.linkedin"
                                            >
                                                <i class="fa fa-linkedin-square"></i>
                                            </a>
                                            <a class="blue-grey-700" target="_new"
                                                :href="domain?.contacts?.twitter" 
                                                v-if="domain?.contacts?.twitter"
                                            >
                                                <i class="fa fa-twitter-square"></i>
                                            </a>
                                            <a class="blue-grey-700" target="_new"
                                                :href="domain?.contacts?.tiktok" 
                                                v-if="domain?.contacts?.tiktok"
                                            >
                                                <svg style="height: 13px; margin-bottom: -3px;"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 448 512"
                                                >
                                                    <path d="M448 209.9a210.1 210.1 0 0 1 -122.8-39.3V349.4A162.6 162.6 0 1 1 185 188.3V278.2a74.6 74.6 0 1 0 52.2 71.2V0l88 0a121.2 121.2 0 0 0 1.9 22.2h0A122.2 122.2 0 0 0 381 102.4a121.4 121.4 0 0 0 67 20.1z"/>
                                                </svg>
                                            </a>
                                        </div>
                                        <div class="columnCell companyCellData pr-10 py-10 w-250">
                                            @{{getDomainPlatformName(domain)}}
                                        </div>
                                        <div class="columnCell companyCellData pr-10 py-10 w-250">
                                            @{{getDomainCategory(domain)}}
                                        </div>
                                        <div class="columnCell companyCellData pr-10 py-10 w-200">
                                            @{{getDomainLocation(domain)}}
                                        </div>
                                        <div class="columnCell companyCellData pr-10 py-10 w-250">
                                            @{{getDomainSale(domain)}}
                                        </div>
                                        <div class="columnCell companyCellData pr-10 py-10 w-150 text-capitalize">
                                            @{{getDomainEmployeeCount(domain)}}
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                    </div>

                    @include('data-search.companies.partials.select-all-popup')
                </div>
            </div>
        </div>
        <div class="scrollable-bar scrollable-bar-vertical is-disabled scrollable-bar-hide" draggable="false">
            <div class="scrollable-bar-handle"></div>
        </div>
    </div>

    <div class="loader-box vertical-align text-center">
        <div class="loader vertical-align-middle loader-circle"></div>
    </div>
</div>