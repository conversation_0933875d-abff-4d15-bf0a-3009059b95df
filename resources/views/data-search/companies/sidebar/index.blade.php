<div class="relative tour-filter-box">
    {{-- 
    <div class="search-aside-header tour-searchcreate-box animation-fade"
        v-if="selectedIds.length"
    >
        <div class="">
            <div class="mb-10">
                <button class="btn btn-block btn-primary btn-round mb-10"
                    v-if="viewType != 'saved'"
                    @click="showSaveSearch"
                >
                    Save All <i class="icon wb-plus"></i>
                </button>

                <button class="btn btn-block btn-primary btn-round mb-10"
                    v-if="viewType == 'saved'"
                    @click="showAddAllToList"
                >
                    Add to Lists <i class="icon wb-list"></i>
                </button>
                
                <button class="btn btn-block btn-primary btn-round mb-10"
                    v-if="viewType == 'saved'"
                    @click="showImportToCampaign"
                >
                    Import to Campaign <i class="icon fa-bullhorn"></i>
                </button>

                <button class="btn btn-block btn-outline btn-round mb-10"
                    @click="cancelSelectAll"
                >
                    Unselect All
                    <i class="icon wb-close-mini"></i>
                </button>
            </div>
        </div>
    </div>
    --}}

    @include('data-search.companies.sidebar.view-types')

    <div class="">
        <div data-role="container" class="tabbed-inbox-scrollable-filter scrollbar scrollbar-clear scrollbar-lg" style="height: 900px;">
            <div data-role="content" class="">
                <div class="search-aside-header">
                    <input type="text"
                        v-model="keywords"
                        placeholder="Filter by keywords"
                        class="form-control input-outline-default px-10"
                        @keyup="filterByKeywords()"
                    >
                    <div class="checkbox-custom checkbox-primary my-0 pt-10"
                        v-if="viewType == 'new'"
                    >
                        <input type="checkbox"
                            id="filter_suppresionlist"
                            v-model="exclude_suppression"
                            :checked="exclude_suppression"
                            true-value="yes"
                            false-value="no"
                            @change="filterToggle"
                            :disabled="disableFilters"
                        >
                        <label for="filter_suppresionlist">
                            Ignore suppression list
                        </label>
                    </div>
                </div>
                @include('data-search.companies.sidebar.lists')
                @include('data-search.companies.sidebar.searches')
                @include('data-search.companies.sidebar.filter-categories')
                @include('data-search.companies.sidebar.filter-platforms')
                @include('data-search.companies.sidebar.filter-others')
            </div>
        </div>
    </div>

    <div class="search-aside-footer">
        <div class="d-flex gap-5 justify-content-between align-items-center">
            <div class="flex-grow"  >
                <button class="btn btn-block btn-outline btn-sm"
                    :class="{
                        'disabled': !canClearFilters && !keywords,
                        'btn-primary': canClearFilters || keywords
                    }"
                    @click="resetFilters"
                >
                    Clear Filters
                </button>
            </div>
            <div class="flex-grow text-success text-center" >
                Credits:
                <strong>
                    @{{ credits }}
                </strong>
            </div>
        </div>
    </div>

    
</div>