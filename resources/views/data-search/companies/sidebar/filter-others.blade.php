<div class="search-aside-section"
    v-for="(filterType, filterIndex) in filterTypes"
>
    <div class="py-0">
        <div class="d-flex justify-content-between align-self-center gap-10 cp pt-15"
            :class="{
                'pb-15': (!filters[filterType]?.open && !filters[filterType]?.numSelected) || filters[filterType]?.open,
                'pb-0': !filters[filterType]?.open && filters[filterType]?.numSelected
            }"
            @click="filters[filterType].open = !filters[filterType]?.open"
        >
            <strong>
                @{{filters[filterType]?.name}}
                <span
                    v-if="filters[filterType]?.numSelected"
                    class="badge badge-secondary mr-5"
                >
                    @{{filters[filterType]?.numSelected}}
                </span>
            </strong>
            <div>
                <span >
                    <i class="fa fa-chevron-up" v-if="filters[filterType]?.open"></i>
                    <i class="fa fa-chevron-down" v-else></i>
                </span>
            </div>
        </div>

        <div class="filter-option-list tabbed-inbox-scrollable mb-15 scrollbar" v-if="filters[filterType]?.open">
            <div v-for="(filter, filterIndex) in filters[filterType]?.options" class="mt-1">
                <template>
                    <label type="button" class="filter-option-item d-block cp"
                        :class="filter.checked ? 'filter-option-active' : ''"
                        :for="filterType+'_'+filterIndex"
                    >
                        <div class="checkbox-custom checkbox-primary my-0">
                            <input type="checkbox"
                                :id="filterType+'_'+filterIndex"
                                v-model="filter.checked"
                                :checked="filter.checked"
                                @change="filterToggle"
                                :disabled="disableFilters"
                            >
                            <label :for="filterType+'_'+filterIndex">
                                @{{fixUnknown(filter.name ?? filter.code)}}
                            </label>
                        </div>
                    </label>
                </template>
            </div>
        </div>

        <div v-else>
            <div class="mb-15 scrollbar scrollbar-clear" v-if="filters[filterType]?.numSelected">
                <template
                    v-for="(filter, filterIndex) in filters[filterType]?.options"    
                >
                    <span
                        class="badge badge-secondary mr-1"
                        v-if="filter.checked"
                    >@{{fixUnknown(filter.name ?? filter.code)}}</span>
                </template>
            </div>
        </div>
    </div>
</div>


{{--
<div class="search-aside-section">
    <div class="py-15">
        <div class="d-flex justify-content-between align-self-center gap-10 cp">
            <strong>
                Technologies
                <span class="badge badge-secondary mr-5">2</span>
            </strong>

            <span>
                <!-- <i class="fa fa-chevron-down"></i> -->
                <i class="fa fa-chevron-up"></i>
            </span>
        </div>
    
        <!-- if open -->
        <div class="filter-option-list tabbed-inbox-scrollable mt-10">
            <div>
                <template>
                    <label type="button" class="filter-option-item filter-option-active">
                        <div class="checkbox-custom checkbox-primary my-0">
                            <input type="checkbox" checked>
                            <label>Test 1</label>
                        </div>
                    </label>
                </template>
            </div>
        </div>

        <!-- if close -->
        <div>
            <template >
                <span class="badge badge-secondary mr-1">Test 1</span>
            </template>
        </div>
    </div>
</div>
--}}