<div class="search-aside-section cp">
    <div class="py-15">
        <div class="d-flex justify-content-between align-self-center gap-10 cd">
            <strong @click="isSearchesOpen = !isSearchesOpen" class="cp w-120">
                Saved Filters
            </strong>
            <span class="cp">
                @if (Auth::user()->isAdmin() || Auth::user()->isSupport())
                    {{-- save filter with teamId? --}}
                @else
                    <span class="btn btn-outline btn-primary btn-xs btn-round mr-5" v-if="hasFilterSelected || keywords"
                        @click="showAddFilterForm">
                        Save Filters
                    </span>
                    <span class="btn btn-outline btn-default btn-disabled btn-xs btn-round mr-5" disabled v-else>
                        Save Filters
                    </span>
                @endif

                <span @click="isSearchesOpen = !isSearchesOpen">
                    <i class="fa fa-chevron-up" v-if="isSearchesOpen"></i>
                    <i class="fa fa-chevron-down" v-else></i>
                </span>
            </span>
        </div>

        <div class="mt-10" v-if="isSearchesOpen">
            <select class="form-control" data-plugin="select2" data-placeholder="Select previous search"
                data-allow-clear="true" @change="previousSearchSelected" v-model="previousSearchId"
                id="previousSearchSelect">
                <option value="">Select a saved filter</option>
                <option v-for="filter in agency_filters" :value="filter.id">
                    @{{ filter.name }}
                </option>
            </select>

            <p class="m-0 pt-10 text-sm search-helptext">
                Apply saved filter to search.
                <a href="#" @click.prevent="showAllFilters">View all</a>.
            </p>
        </div>
    </div>
</div>
