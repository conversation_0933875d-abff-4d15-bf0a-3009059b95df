<div class="search-aside-stats">
    <div class="py-20">
        <div class="d-flex gap-5 justify-content-between align-items-center">
            <!-- text-center border border-success bg-success py-5 flex-grow rounded -->
            <div class="btn flex-grow"
                :class="{
                    'btn-primary cd': viewType == 'total',
                    'btn-primary btn-outline cp': viewType != 'total'
                }"
                @click="displayTotalDomains"
            >
                <strong class="m-0" v-if="hasFilterSelected || keywords">
                    @{{ estimatedTotal.toLocaleString() }}
                </strong>
                <strong class="m-0" v-else>
                    @{{ totalDomains.toLocaleString() }}
                </strong>
                <div><small>Total</small></div>
            </div>
            <div class="btn flex-grow"
                :class="{
                    'btn-primary cd': viewType == 'new',
                    'btn-primary btn-outline cp': viewType != 'new'
                }"
                @click="displayNewDomains"
            >
                <strong class="m-0">
                    @{{ newCount.toLocaleString() }}
                </strong>
                <div><small>New</small></div>
            </div>
            <div class="btn flex-grow"
                :class="{
                    'btn-primary cd': viewType == 'saved',
                    'btn-primary btn-outline cp': viewType != 'saved'
                }"
                @click="displaySavedDomains"
            >
                <strong class="m-0">
                    @{{ savedDomains.total.toLocaleString() }}
                </strong>
                <div><small>Saved</small></div>
            </div>
        </div>
    </div>
</div>