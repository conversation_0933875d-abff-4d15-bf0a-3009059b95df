<div class="tabbed-inbox-filter-btn px-10 py-20">
    <h4 class="clearfix">
        <span class="d-inline-block mt-3">
            Searches
        </span>
    </h4>

    <div class="px-10">
        <select class="form-control"  
            data-placeholder="All searches"
            data-allow-clear="true"
            @change="searchFilterSelected"
            v-model="searchFilterId"
            id="searchFilterSelect"
        >
            <option value="">All searches</option>
            <option v-for="search in searches" :value="search.id">
                @{{search.name}}
            </option>
        </select> 
    </div>
</div>

<div class="tabbed-inbox-filter-btn px-10 py-20">
    <h4 class="clearfix">
        <span class="d-inline-block mt-3 pt-3">
            Activities
        </span>
        <button
            @click="emailActivities.isOpened = !emailActivities?.isOpened"
            class="btn btn-xs btn-default pull-right"
        >
            <i class="fa fa-angle-double-up" v-if="emailActivities?.isOpened"></i>
            <i class="fa fa-angle-double-down" v-else></i>
        </button>
    </h4>

    <div v-if="emailActivities?.isOpened">
        <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
            :class="{'btn-primary': emailActivities.not_imported == 1, 'btn-default': emailActivities.not_imported == 0}"
            for="emailFilterNotImported"
        >
            <div class="checkbox-custom checkbox-primary mt-5 mb-5">
                <input type="checkbox" 
                    id="emailFilterNotImported" 
                    v-model="emailActivities.not_imported"
                    @change="emailFilterToggle"
                    true-value="1"
                    false-value="0"
                >
                <label for="emailFilterNotImported">
                    Not Imported
                </label>
            </div>
        </label>
        <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
            :class="{'btn-primary': emailActivities.imported == 1, 'btn-default': emailActivities.imported == 0}"
            for="emailFilterImported"
        >
            <div class="checkbox-custom checkbox-primary mt-5 mb-5">
                <input type="checkbox" 
                    id="emailFilterImported" 
                    v-model="emailActivities.imported"
                    @change="emailFilterToggle"
                    true-value="1"
                    false-value="0"
                >
                <label for="emailFilterImported">
                    Imported
                </label>
            </div>
        </label>
        <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
            :class="{'btn-primary': emailActivities.not_contacted == 1, 'btn-default': emailActivities.not_contacted == 0}"
            for="emailFilterNotContacted"
        >
            <div class="checkbox-custom checkbox-primary mt-5 mb-5">
                <input type="checkbox" 
                    id="emailFilterNotContacted" 
                    v-model="emailActivities.not_contacted"
                    @change="emailFilterToggle"
                    true-value="1"
                    false-value="0"
                >
                <label for="emailFilterNotContacted">
                    Not Contacted
                </label>
            </div>
        </label>
        <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
            :class="{'btn-primary': emailActivities.contacted == 1, 'btn-default': emailActivities.contacted == 0}"
            for="emailFilterContacted"
        >
            <div class="checkbox-custom checkbox-primary mt-5 mb-5">
                <input type="checkbox" 
                    id="emailFilterContacted" 
                    v-model="emailActivities.contacted"
                    @change="emailFilterToggle"
                    true-value="1"
                    false-value="0"
                >
                <label for="emailFilterContacted">
                    Contacted
                </label>
            </div>
        </label>
        <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
            :class="{'btn-primary': emailActivities.not_replied == 1, 'btn-default': emailActivities.not_replied == 0}"
            for="emailFilterNotReplied"
        >
            <div class="checkbox-custom checkbox-primary mt-5 mb-5">
                <input type="checkbox" 
                    id="emailFilterNotReplied" 
                    v-model="emailActivities.not_replied"
                    @change="emailFilterToggle"
                    true-value="1"
                    false-value="0"
                >
                <label for="emailFilterNotReplied">
                    Not Replied
                </label>
            </div>
        </label>
        <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
            :class="{'btn-primary': emailActivities.replied == 1, 'btn-default': emailActivities.replied == 0}"
            for="emailFilterReplied"
        >
            <div class="checkbox-custom checkbox-primary mt-5 mb-5">
                <input type="checkbox" 
                    id="emailFilterReplied" 
                    v-model="emailActivities.replied"
                    @change="emailFilterToggle"
                    true-value="1"
                    false-value="0"
                >
                <label for="emailFilterReplied">
                    Replied
                </label>
            </div>
        </label>
    </div>
</div>

<div class="tabbed-inbox-filter-btn px-10 py-20" v-if="filters?.categories">
    <h4 class="clearfix">
        <span class="d-inline-block mt-5 pt-3">
            @{{filters?.categories?.name}}
        </span>
        <button
            @click="filters.categories.open = !filters?.categories?.open"
            class="btn btn-xs btn-default pull-right"
        >
            <i class="fa fa-angle-double-up" v-if="filters?.categories?.open"></i>
            <i class="fa fa-angle-double-down" v-else></i>
        </button>
    </h4>
    <div v-if="filters?.categories?.open">
        <div v-for="(level1, lvl1Index) in filters?.categories?.options">
            <template v-if="checkIfDisplayed(lvl1Index, level1.checked, 'categories')">
                <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
                    :class="level1.checked ? 'btn-primary' : 'btn-default'"
                    :for="'category_level_1_'+lvl1Index"
                >
                    <div class="checkbox-custom checkbox-primary my-3">
                        <input type="checkbox" 
                            :id="'category_level_1_'+lvl1Index" 
                            v-model="level1.checked" 
                            :checked="level1.checked"
                            @change="categoryFilterToggle"
                        >
                        <label :for="'category_level_1_'+lvl1Index">
                            @{{level1.name}}
                        </label>
                    </div>
                </label>

                <div v-if="level1.checked">
                    <div class="py-0 pl-20"
                        v-for="(level2, lvl2Index) in level1?.children"
                    >
                        <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
                            :class="level2.checked ? 'btn-primary' : 'btn-default'"
                            :for="'category_level_1_'+lvl1Index+'_2'+lvl2Index"
                        >
                            <div class="checkbox-custom checkbox-primary my-3">
                                <input type="checkbox" 
                                    :id="'category_level_1_'+lvl1Index+'_2'+lvl2Index" 
                                    v-model="level2.checked" 
                                    :checked="level2.checked"
                                    @change="emailFilterToggle"
                                >
                                <label :for="'category_level_1_'+lvl1Index+'_2'+lvl2Index">
                                    @{{level2.name}}
                                </label>
                            </div>
                        </label>

                        <div v-if="level2.checked">
                            <div class="py-0 pl-20"
                                v-for="(level3, lvl3Index) in level2?.children"
                            >
                                <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
                                    :class="level3.checked ? 'btn-primary' : 'btn-default'"
                                    :for="'category_level_1' + lvl1Index+ '_2' + lvl2Index + '_3' + lvl3Index"
                                >
                                    <div class="checkbox-custom checkbox-primary my-3">
                                        <input type="checkbox" 
                                            :id="'category_level_1' + lvl1Index+ '_2' + lvl2Index + '_3' + lvl3Index" 
                                            v-model="level3.checked" 
                                            :checked="level3.checked"
                                            @change="emailFilterToggle"
                                        >
                                        <label :for="'category_level_1' + lvl1Index+ '_2' + lvl2Index + '_3' + lvl3Index">
                                            @{{level3.name}}
                                        </label>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <div class="px-10 mt-10" v-if="filters?.categories?.options?.length">
            <button class="btn btn-xs btn-block btn-default text-center"
                v-if="filters?.categories?.showAll"
                @click="filters.categories.showAll = false"
            >Show Less</button>

            <button class="btn btn-xs btn-block btn-default text-center"
                v-if="!filters?.categories?.showAll"
                @click="filters.categories.showAll = true"
            >Show More</button>
        </div>
    </div>
</div>

<div class="tabbed-inbox-filter-btn px-10 py-20">
    <h4 class="clearfix">
        <span class="d-inline-block mt-3 pt-3">
            @{{filters?.platforms?.name}}
        </span>
        <button
            @click="filters.platforms.open = !filters.platforms.open"
            class="btn btn-xs btn-default pull-right"
        >
            <i class="fa fa-angle-double-up" v-if="filters?.platforms?.open"></i>
            <i class="fa fa-angle-double-down" v-else></i>
        </button>
    </h4>
    <div v-if="filters?.platforms?.open">
        <div v-for="(platform, platformIndex) in filters?.platforms?.options">
            <template v-if="checkIfDisplayed(platformIndex, platform.checked, 'platforms')">
                <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
                    :class="platform.checked ? 'btn-primary' : 'btn-default'"
                    :for="'platforms_'+platformIndex"
                >
                    <div class="checkbox-custom checkbox-primary my-3">
                        <input type="checkbox" 
                            :id="'platforms_'+platformIndex" 
                            v-model="platform.checked" 
                            :checked="platform.checked"
                            @change="emailFilterToggle"
                            :disabled="checkPlatformDisabled(platform)"
                        >
                        <label :for="'platforms_'+platformIndex">
                            @{{platform.name ?? platform.code}}
                        </label>
                    </div>
                </label>

                <div v-if="checkShopifySelected(platform)">
                    <div class="py-0 pl-20"
                        v-for="(plan, planIndex) in platform?.plans"
                    >
                        <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1">
                            <div class="checkbox-custom checkbox-primary my-3">
                                <input type="checkbox"
                                    v-model="plan.checked"
                                    :checked="plan.checked"
                                    @change="filterPlan('ev', plan)"
                                    :id="'plan_'+planIndex"
                                >
                                <label :for="'plan_'+planIndex">
                                    @{{plan.name}}
                                </label>
                            </div>
                        </label>
                    </div>
                </div>
            </template>
        </div>

        <div class="px-10 mt-10" v-if="filters?.platforms?.options?.length > 5">
            <button class="btn btn-xs btn-block btn-default text-center"
                v-if="filters?.platforms?.showAll"
                @click="filters.platforms.showAll = false"
            >Show Less</button>

            <button class="btn btn-xs btn-block btn-default text-center"
                v-if="!filters?.platforms?.showAll"
                @click="filters.platforms.showAll = true"
            >Show More</button>
        </div>
    </div>
</div>

<template v-for="(filterType, filterIndex) in filterTypes"
    v-if="filters[filterType] && filters[filterType]?.options?.length"
>
    <div class="tabbed-inbox-filter-btn px-10 py-20">
        <h4 class="clearfix">
            <span class="d-inline-block mt-3 pt-3">
                @{{filters[filterType]?.name}}
            </span>
            <button
                @click="filters[filterType].open = !filters[filterType]?.open"
                class="btn btn-xs btn-default pull-right"
            >
                <i class="fa fa-angle-double-up" v-if="filters[filterType]?.open"></i>
                <i class="fa fa-angle-double-down" v-else></i>
            </button>
        </h4>
        <div v-if="filters[filterType]?.open">
            <div v-for="(filter, filterIndex) in filters[filterType]?.options">
                <template v-if="checkIfDisplayed(filterIndex, filter.checked, filterType)">
                    <label type="button" class="btn btn-block btn-outline clearfix py-3 mb-1"
                        :class="filter.checked ? 'btn-primary' : 'btn-default'"
                        :for="filterType+'_'+filterIndex"
                    >
                        <div class="checkbox-custom checkbox-primary my-3">
                            <input type="checkbox" 
                                :id="filterType+'_'+filterIndex" 
                                v-model="filter.checked" 
                                :checked="filter.checked"
                                @change="emailFilterToggle"
                            >
                            <label :for="filterType+'_'+filterIndex">
                                @{{filter.name ?? filter.code}}
                            </label>
                        </div>
                    </label>
                </template>
            </div>

            <div class="px-10 mt-10" v-if="filters[filterType]?.options?.length > 5">
                <button class="btn btn-xs btn-block btn-default text-center"
                    v-if="filters[filterType]?.showAll"
                    @click="filters[filterType].showAll = false"
                >Show Less</button>

                <button class="btn btn-xs btn-block btn-default text-center"
                    v-if="!filters[filterType]?.showAll"
                    @click="filters[filterType].showAll = true"
                >Show More</button>
            </div>
        </div>
    </div>
</template>
