<div class="">
    <div class="p-10">
        <div class="row">
            <div class="col-sm-9">
                @if($type == 'paginate')
                <h4 class="mb-0 d-inline-block">
                    Search Preview:
                    <span v-if="hasFilters">
                        <span v-if="!isLoading">
                            @{{ domains.real_total?.toLocaleString() }}
                        </span>
                    </span>
                    <span v-else>@{{ totalDomains.toLocaleString() }}</span>
                    Companies
                </h4>
                @endif

                @if($type == 'cursor')
                  <div class="mb-0 d-inline-block">
                    <h4>
                    Search Preview
                      <template v-if="hasFilters">
                        <span v-if="isFetchingTotals || isLoading">
                          <i class="fa fa-spinner fa-spin"></i>
                        </span>
                        <span v-else>
                          @{{ estimatedTotal.toLocaleString() }}
                        </span>
                      </template>
                      <template v-else>
                        @{{ totalDomains.toLocaleString() }}
                      </template>
                      Companies
                      <span class="d-inline-block grey-500" data-toggle="tooltip" data-placement="top"
                            title="Estimated total. Final result count may be lower after filtering out previous searches, suppressed domains, and unreachable contacts. Credits are only used when valid, verified leads are found."
                      >
												<i class="wb-info-circle small"></i>
											</span>
                    </h4>
                    {{--<span v-if="shownTotal >= 2000">Preview limit reached</span>--}}
                  </div>
                @endif
            </div>
            <div class="col-sm-3 text-right">
                <h4 class="mb-0 pt-3 d-inline-block">
                    Credits:
                    <span v-if="credits">@{{credits.toLocaleString()}}</span>
                    <span v-else>0</span>
                </h4>

                @include('data-search.search.export')
            </div>
        </div>
    </div>
    <div class="">
        <div class="list-group list-group-dividered mr-10 mb-0">
            <div class="list-group-item px-20">
                <div class="row">
                    {{--
                    <div class="col-lg-3 col-md-4 col-sm-1 col-1">
                        <!--
                        <div class="checkbox-custom checkbox-primary my-0 mr-30 d-inline-block">
                            <input type="checkbox"
                                id="domainCheckboxAll"
                                v-model="domainCheckboxAll"
                                @change="toggleDomainCheckboxAll"
                            >
                            <label for="domainCheckboxAll"></label>
                        </div>
                        -->

                        <strong class="hidden-sm-down">Company</strong>
                    </div>
                    --}}

                    <div class="col-lg-4 col-md-5 col-sm-7 col-6">
                        {{-- <strong>Domain</strong> --}}
                        <strong>Company</strong>
                    </div>
                    <div class="col-lg-8 col-md-7 col-sm-5 col-6">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <strong>Category</strong>
                            </div>
                            <div class="col-lg-3 col-md-6 hidden-sm-down">
                                <strong>Location</strong>
                            </div>
                            <div class="col-lg-3 hidden-md-down">
                                <strong>Platform</strong>
                            </div>
                            <div class="col-lg-3 hidden-md-down">
                                <strong>Sales</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="list-group list-group-dividered mr-5 mb-10">
            @include("data-search.search.list-".$type)
        </div>
    </div>
</div>
