<update-phone-number :user="user" inline-template>
  <div class="card card-shadow p-10">
    <div class="card-header card-header-transparent">
      <h4 class="card-title">Phone Number</h4>
    </div>
    <div class="card-block">
      <div class="alert alert-info" v-if="noVerifiedPhone && freeCreditsEligibility">
          Verify your phone number to get 50 free credits!
      </div>
      <!-- Success Message -->
      <div class="alert alert-success" v-if="updatePhoneSuccess && !verifyPhoneSuccess">
          Your phone number has been updated!
      </div>
      <div class="alert alert-success" v-if="verifyPhoneSuccess">
          Your phone number has been verified!
      </div>

      <form role="form">
        <!-- Phone -->
        <div class="form-group form-group-default" :class="{'has-error': phoneError}">
          <vue-tel-input :value="phone" @input="getPhoneObject"></vue-tel-input>
          <div id="phone-error" class="error" for="phone" v-show="phoneError">
            @{{ phoneError }}
          </div>
        </div>
        <!-- Update Button -->
        <div class="form-group w-full text-center">
          <button type="submit" class="btn btn-primary"
                  @click.prevent="updatePhone"
                  :disabled="updating">
            <i class="icon wb-chevron-right"></i> Update Phone Number
          </button>
        </div>

        <!-- Verification Code -->
        <div class="form-group form-group-default" :class="{'has-error': verificationError}" v-if="verificationPending">
          <label>Verification Code</label>
          <input type="text" class="form-control" name="verificationCode" v-model="verificationCode">
          <div id="verification-code-error" class="error" for="verificationCode" v-if="verificationError">
            @{{ verificationError }} <a href="#" @click.prevent="resendVerificationCode">Resend Verification Code</a>
          </div>
          <div v-else class="help-block">
            We sent a verification code to your phone. Please enter it here.
            <a href="#" @click.prevent="resendVerificationCode">Resend Verification Code</a>
          </div>
        </div>
        <!-- Update Button -->
        <div class="form-group w-full text-center" v-if="verificationPending">
          <button type="submit" class="btn btn-primary"
                  @click.prevent="verifyPhone"
                  :disabled="updating">
            <i class="icon wb-chevron-right"></i> Verify Phone Number
          </button>
        </div>
      </form>
    </div>
  </div>
</update-phone-number>
