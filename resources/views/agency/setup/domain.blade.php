<h4 class="text-center mt-0 mb-30 font-weight-100 d-none">
    <i class="fa fa-link"></i> Confirm the URL that will be used by your Agency's Dashboard.
</h4>

<form class="form-horizontal pt-20">
    <!-- Agency Email -->
    <div class="row" :class="{'has-error': domain_form.errors.has('domain')}">
        <label class="col-12 d-none">
            <button class="btn btn-xs btn-primary float-right" type="button" 
                @click.prevent="defaultDomain(false)"  v-if="isDefaultDomain"
            >
                <i class="fa fa-link"></i> 
                Use Custom Domain
            </button>

            <button class="btn btn-xs btn-dark float-right" type="button" 
                @click.prevent="defaultDomain(true)"  v-else
            >
                <i class="fa fa-close"></i> 
                Use Default Domain
            </button>
            
            <span class="d-inline-block grey-800 d-nonw">
                Domain Name (without http) *    
            </span> 
        </label>
        <div class="col-md-8 col-sm-8">
            <div class="form-group" v-if="isDefaultDomain">
                <div class="input-group">
                    <span class="input-group-addon">https://</span>
                    <input type="text" class="form-control text-center setup-subdomain-input" 
                        placeholder="" v-model="appsubdomain" @input="domainUpdated"
                    >
                    <span class="input-group-addon w-p50">.@{{app_domain}}</span>
                </div>
            </div>

            <div class="form-group" v-else>
                <div class="input-group">
                    <span class="input-group-addon">https://</span>
                    <input type="text" class="form-control" placeholder="" v-model="domain_form.domain">
                </div>
            </div>

            {{--<input type="domain" class="form-control" name="domain" v-model="domain_form.domain" required>--}}
            
            <div id="domain-error" class="error" for="domain" v-show="domain_form.errors.has('domain')">
                @{{ domain_form.errors.get('domain') }}
            </div> 
        </div>  
        <div class="col-md-4 pl-0 col-sm-4">
            <button class="btn btn-primary btn-block px-0" type="button" 
                data-toggle="tooltip" data-placement="top"
                title="Click here to use Custom Domain"
                @click.prevent="defaultDomain(false)"  v-if="isDefaultDomain"
            >
                <i class="fa fa-globe"></i> 
                <span class="hidden-lg-down">Use </span> Custom <span class="hidden-md-down">Domain</span>
            </button>

            <button class="btn btn-default btn-block px-0" type="button" 
                data-toggle="tooltip" data-placement="top"
                title="Click here to use Wavo Subdomain (default)"
                @click.prevent="defaultDomain(true)"  v-else
            >
                <i class="fa fa-link"></i> 
                <span class="hidden-lg-down">Use </span> <span class="hidden-md-down">Wavo</span> Subdomain
            </button>
        </div>

        <div class="col-md-12">
            <div class="alert alert-icon alert-info mb-40">
                <i class="icon wb-info-circle"></i>
                <p>Please enter the domain where you'd like to access your dashboard instance.</p>
                <p>If using a custom domain, please create a CNAME record to {{ parse_url(config('app.url'))['host'] }}</p>
                <p>You will have to wait for the DNS update to propagate before you can access your dashboard instance using a custom domain.</p>
                <p>You can use the domain <strong>@{{ appsubdomain }}.{{ config('app.domain') }}</strong> temporarily or while DNS settings propagate and set up a custom domain later.</p>
            </div>
        </div>
    </div> 
</form>
 