@extends('layouts.app')

@section('page-title', 'Default Unsubscribe URLs')

@section('page-styles') 
    <style>  
		.swal-text,
		.swal-footer {
			text-align: center;
		}
    </style>
@endsection
  
@section('content-layout') 

	<nylasdomain-list
		team="null" 
		domain_type="default"
		app_domain="{{parse_url(config('app.url'))['host']}}"
		inline-template
	>
		<div class="page loader-wrapper" :class="{'loader-wrapper-loading': isLoading}">
		    <div class="page-header page-header-bordered">
		    	<h1 class="page-title text-capitalize">Default Unsubscribe URLs</h1> 
		    	<div class="page-header-actions">
					<a href="#" data-toggle="modal" data-target="#addDomainModal" 
						class="btn btn-outline btn-primary btn-round"
					>
						<span class="text hidden-sm-down">Add Default URL</span>
						<i class="icon wb-plus" aria-hidden="true"></i>
					</a> 
				</div>
		    </div>
		    <div class="page-content container-fluid"> 
		    	@if (session('status'))
	                <div class="mb-20 alert alert-dismissible alert-{{session('status') == 'success' ? 'success' : 'danger'}}">
	                	<button type="button" class="close" data-dismiss="alert" aria-label="Close">
	                        <span aria-hidden="true">&times;</span>
	                    </button> 
	                    {{ session('msg') }}
	                </div>
	            @endif 

	            @if (session('ajaxMsg'))
	                <div class="mb-20 alert alert-success">
	                    {{ session('ajaxMsg') }}
	                </div>
	            @endif 

	            @include('domains.list')
				 

		    </div>

		    <div class="loader-box vertical-align text-center">
		    	<div class="loader vertical-align-middle loader-circle"></div>
		    </div> 

		    <div class="modal fade" id="addDomainModal" tabindex="-1" role="dialog" 
		    	aria-labelledby="addDomainModalLabel" aria-hidden="true"
		    >
				<div class="modal-dialog" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title" id="exampleModalLabel">Add Custom Unsuscribe URLs</h4>
							<button type="button" class="close" data-dismiss="modal" aria-label="Close">
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						<div class="modal-body">
							<div class="mb-30">
								<label class="d-block">
									Domain
								</label>
								<textarea rows="5" v-model="newDomainData.domains" class="form-control mb-0"
	    							placeholder="*.customurl.com, *.unsubscribedomain.com" 
	    						></textarea>	
	    						<p class="text-help mb-5"> 
	    							Enter multiple URLs seperated by commas, spaces or new lines
	    						</p>
	    						<div class="alert-icon alert alert-info mt-20 mb-20">
	    							<i class="icon wb-info-circle"></i>	 
		    						<p class="mb-10"> 
		    							Configure a custom URL by creating a wildcard subdomain CNAME and pointing it to
		    							<strong>{{ parse_url(config('app.url'))['host'] }}</strong>
		    						</p>
		    						<p class="mb-10"> 
		    							Enter it above as <strong>*.customurl.com</strong> or 
		    							<strong>*.subdomain.customurl.com</strong>
		    						</p>
		    						<p class="mb-10"> 
		    							If this form fails, the wildcard subdomain is not resolving to 
		    							<strong>{{ parse_url(config('app.url'))['host'] }}</strong>
		    						</p>
	    						</div> 
							</div>

    						<div class="text-center">
								<button type="button" class="btn btn-primary"
									@click="addNewDomains"
									:disabled="isAddingDomains || !newDomainData.domains"
								> 
									<span v-if="isAddingDomains">
										<i class="fa fa-spinner fa-spin"></i> Adding...
									</span>
									<span v-else>
										<i class="icon wb-chevron-right"></i> Add
									</span> 
								</button>
							</div>
						</div> 
					</div>
				</div>
			</div>
 
		</div>
	</nylasdomain-list>	
 
@endsection
