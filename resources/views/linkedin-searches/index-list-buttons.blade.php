@canany(['linkedinsearch.edit', 'support'])
<!-- button if start searching -->
<form :action="'/linkedin-searches/'+linkedinSearch.hashid+'/search'"
    v-if="['DRAFT', 'STOPPED'].indexOf(linkedinSearch.status) > -1"
    class="d-inline-block m-0 p-0 float-right" method="POST"
    id='formStatusSearch'
>
    @csrf
    <button class="btn btn-default btn-block px-5"
        @click.prevent="submitSearch('Start', 'formStatusSearch', 'Start')"
    >
        <i class="icon wb-search" aria-hidden="true"></i>
        <span class="hidden-lg-down w-55 text-center d-inline-block">
            SEARCH
        </span>
     </button>
</form>

<!-- button if cancel search queued for searching -->
<form :action="'/linkedin-searches/'+linkedinSearch.hashid+'/cancel'"
    v-else-if="['WAITING'].indexOf(linkedinSearch.status) > -1"
    class="d-inline-block m-0 p-0 float-right" method="POST"
    id='formStatusCancel'
>
    @csrf
    <button class="btn btn-danger btn-block px-5"
        @click.prevent="submitSearch('Cancel waiting for', 'formStatusCancel', 'Cancel')"
    >
        <i class="icon fa fa-ban" aria-hidden="true"></i>
        <span class="hidden-lg-down w-55 text-center d-inline-block">
            CANCEL
        </span>
     </button>
</form>

<!-- button if cancel search queued for searching -->
<form :action="'/linkedin-searches/'+linkedinSearch.hashid+'/stop'"
    v-else-if="['SEARCHING'].indexOf(linkedinSearch.status) > -1"
    class="d-inline-block m-0 p-0 float-right" method="POST"
    id='formStatusStop'
>
    @csrf
    <button class="btn btn-danger btn-block px-5"
        @click.prevent="submitSearch('Stop', 'formStatusStop', 'Stop')"
    >
        <i class="icon wb-stop" aria-hidden="true"></i>
        <span class="hidden-lg-down w-55 text-center d-inline-block">
            STOP
        </span>
     </button>
</form>

<!-- button to start scraping profile if all search results are scraped -->
<form :action="'/linkedin-searches/'+linkedinSearch.hashid+'/run'"
    v-else-if="['SEARCHED', 'PAUSED'].indexOf(linkedinSearch.status) > -1"
    class="d-inline-block m-0 p-0 float-right" method="POST"
    id='formStatusRun'
>
    @csrf
    <button class="btn btn-primary btn-block px-5"
        @click.prevent="submitSearch('Run', 'formStatusRun', 'Run')"
    >
        <i class="icon wb-play" aria-hidden="true"></i>
        <span class="hidden-lg-down w-55 text-center d-inline-block">
            RUN
        </span>
     </button>
</form>

<!-- button to pause scraping profile -->
<form :action="'/linkedin-searches/'+linkedinSearch.hashid+'/pause'"
    v-else-if="['RUNNING'].indexOf(linkedinSearch.status) > -1"
    class="d-inline-block m-0 p-0 float-right" method="POST"
    id='formStatusPause'
>
    @csrf
    <button class="btn btn-yellow btn-block px-5"
        @click.prevent="submitSearch('Pause', 'formStatusPause', 'Pause')"
    >
        <i class="icon wb-pause" aria-hidden="true"></i>
        <span class="hidden-lg-down w-55 text-center d-inline-block">
            PAUSE
        </span>
     </button>
</form>

<button class=" px-5 btn btn-outline-secondary disabled cd float-right"
    v-else-if="['ARCHIVED'].indexOf(linkedinSearch.status) > -1" disabled
>
    <i aria-hidden="true" class="icon wb-folder"></i>
    <span class="hidden-lg-down w-55 text-center d-inline-block">
        @{{linkedinSearch.status}}
    </span>
</button>

<button class="btn btn-success btn-disabled px-5 cd float-right"
    v-else-if="['DONE'].indexOf(linkedinSearch.status) > -1" disabled
>
    <i aria-hidden="true" class="icon wb-check"></i>
    <span class="hidden-lg-down w-55 text-center d-inline-block">
        @{{linkedinSearch.status}}
    </span>
</button>
<a :href="'/linkedin-searches/'+linkedinSearch.hashid+'/edit'" class="btn btn-warning px-5 float-right"
    v-else-if="['INVALID'].indexOf(linkedinSearch.status) > -1"
>
    <i aria-hidden="true" class="icon wb-edit"></i>
    <span class="hidden-lg-down w-55 text-center d-inline-block">
        COOKIE
    </span>
</a>
@endcanany

@canany(['linkedinsearch.read', 'support'])
<a :href="'/linkedin-searches/'+linkedinSearch.hashid"
    class="btn btn-default px-5 float-right mr-5"
>
    <i aria-hidden="true" class="icon wb-settings"></i>
</a>
@endcanany
