@extends('layouts.app')

@section('page-title', 'Linkedin Searches')

@section('page-styles')
    <style>
        .w-55 {
            width: 55px !important;
        }
        @media (max-width: 992px) {
            .hidden-md-down {
                display: none !important;
            }
        }
    </style>
@endsection

@section('content-layout')
    <linkedin-search-list
        :teams="{{$teams}}"
        :agencies="{{$agencies}}"
        :linkedin-accounts="{{$linkedinAccounts}}"
        :user="{{Auth::user()}}"
        inline-template
    >
        <div class="page loader-wrapper" :class="{'loader-wrapper-loading': isLoading}">
            <div class="page-header page-header-bordered">
                <h1 class="page-title">Linkedin Searches</h1>
                <div class="page-header-actions">
                    @canany(['linkedinsearch.create', 'support'])
                    <a href="{{ route('linkedin-searches.create') }}" class="btn btn-outline btn-primary btn-round">
                        <span class="text hidden-sm-down">Create Search</span>
                        <i class="icon wb-plus" aria-hidden="true"></i>
                    </a>
                    @endcanany
                </div>
            </div>

            <div class="page-content container-fluid">

                @include('linkedin-searches.index-filter')

                <div v-if="linkedinSearches.total">
                    <div class="card card-shadow mb-5" v-for="(linkedinSearch, index) in linkedinSearches.data">
                        <div class="card-block pb-15">
                            <div class="row">
                                <div class="col-lg-10 col-xl-10 col-md-9 col-sm-8 col-8">
                                    <div class="row">
                                        <div class="col-md-12 col-xl-7 col-lg-7">
                                            <h4 class="m-0">
                                                <span class="d-block text-truncate">
                                                    <a :href="'/linkedin-searches/'+linkedinSearch.hashid" class="text-decoration-none blue-grey-700">
                                                        @{{linkedinSearch.name}}
                                                    </a>
                                                </span>
                                                <small class="d-block pt-5 hidden-md-down">
                                                    <span class="d-inline-block">
                                                        <span class="px-0 font-size-10 text-lowercase w-50 badge"
                                                            :class="getBadgeStatus(linkedinSearch.status)"
                                                        >
                                                            <span v-if="linkedinSearch.status == 'SEARCHED'">ready</span>
                                                            <span v-else>@{{linkedinSearch.status}}</span>
                                                        </span>
                                                    </span>
                                                    @if ($displayClientSelect)
                                                        <span class="mx-10">&bull;</span>
                                                        <span class="d-inline-block">
                                                            @{{linkedinSearch.team.name}}
                                                        </span>
                                                    @endif
                                                    <span class="mx-10">&bull;</span>
                                                    <span class="d-inline-block">
                                                        <span v-if="linkedinSearch.linkedin_account">
                                                            @{{linkedinSearch.linkedin_account.name}}
                                                        </span>
                                                        <span v-else>
                                                            <span v-if="linkedinSearch.type == 'recruiter'">
                                                                Recruiter Cookie
                                                            </span>
                                                            <span v-else>No Linkedin Account</span>
                                                        </span>
                                                    </span>
                                                </small>

                                                <small class="d-block pt-5 hidden-lg-up">
                                                    <span class="d-inline-block">
                                                        <span class="font-size-10 text-lowercase w-50 badge"
                                                            :class="getBadgeStatus(linkedinSearch.status)"
                                                        >
                                                            @{{linkedinSearch.status}}
                                                        </span>
                                                    </span>
                                                    <span class="mx-10">&bull;</span>
                                                    <span class="d-inline-block">
                                                        Total Results: @{{linkedinSearch.linkedin_profiles_count}}
                                                    </span>
                                                    <span class="mx-10">&bull;</span>
                                                    <span class="d-inline-block">
                                                        Success Rate: @{{getSuccessRate(linkedinSearch)}}
                                                    </span>
                                                    <span class="mx-10">&bull;</span>
                                                    <span class="d-inline-block">
                                                        Progress: @{{getProgressRate(linkedinSearch)}}
                                                    </span>
                                                </small>
                                            </h4>
                                        </div>
                                        <div class="px-0 col-md-12 col-xl-5 col-lg-5 text-center hidden-md-down">
                                            <h4 class="m-0 d-inline-block text-center px-10">
                                                @{{linkedinSearch.linkedin_profiles_count}}
                                                <small class="d-block pt-5">Total Results</small>
                                            </h4>
                                            <h4 class="m-0 d-inline-block text-center px-10">
                                                @{{getSuccessRate(linkedinSearch)}}
                                                <small class="d-block pt-5">Success Rate</small>
                                            </h4>
                                            <h4 class="m-0 d-inline-block text-center px-10">
                                                @{{getProgressRate(linkedinSearch)}}
                                                <small class="d-block pt-5">Progress</small>
                                            </h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-xl-2 col-lg-2 col-sm-4 col-4 text-right pt-3">
                                    @include('linkedin-searches.index-list-buttons')
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <pagination :data="linkedinSearches" :limit="2" @pagination-change-page="getLinkedinSearches"></pagination>
                    </div>
                </div>
                <div v-else>
                    <div class="panel panel-body">
                        <div class="alert alert-warning mb-0">
                            <i class="wb-alert-circle mr-10"></i>
                            No LinkedIn searches found.
                        </div>
                    </div>
                </div>
            </div>

            <div class="loader-box vertical-align text-center">
                <div class="loader vertical-align-middle loader-circle"></div>
            </div>
        </div>
    </linkedin-search-list>
@endsection
