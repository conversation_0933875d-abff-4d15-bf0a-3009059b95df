@extends('layouts.app')

@section('page-title', 'LinkedIn Search: ' . $linkedinSearch->name)

@section('page-styles')
    <style>
        .linkedin-thumbnails {
            width: 24px;
            height: 24px;
            margin-right: 2px;
        }
        .linkedin-thumbnails-initials {
            background: #E8E5DE;
            line-height: 25px;
            text-align: center;
            font-size: 11px;
            display: inline-block;
        }
        .prospecttable-item-firstname {
            padding-left: 35px;
            position: relative;
        }
        .prospecttable-item-firstname .linkedin-thumbnails {
            position: absolute;
            top: 5px;
            left: 10px;
        }
        .prospecttable-item-lastname {
            padding-right: 55px;
            position: relative;
        }
        .prospecttable-item-lastname a {
            /*position: absolute;
            top: 5px;
            right: 10px;*/
        }
        .profile-free-open {
            color: #B09B61;
            /*position: absolute;
            top: 5px;
            right: 10px;*/
        }
        .prospecttable-item-lastname.bg-orange-100 {
            /*padding-right: 50px;*/
        }
        .prospecttable-item-lastname.bg-orange-100 .name-format-warning {
            /*position: absolute;
            top: 6px;
            right: 27px;*/
        }
        .profile-meta-box {
            position: absolute;
            top: 5px;
            right: 10px;
            width: 43px;
            text-align: right;
        }
        .form-control-profile {
            border-color: rgba(0,0,0,0);
            height: 23px !important;
            padding: 0px 5px; 
            background-color: rgba(255,255,255,0.5) !important;
        }
        .form-control-profile:hover {
            border-color: default;
        }
        .form-control-profile.loading-input {
            background-repeat: no-repeat;
            background-position: 100% 50%;
            background-size: 20px;
            background-image: url({{ asset('/img/progress/progress-circle-complete.svg') }});
        }
        .select2-container {
            z-index: 99999;
        }
        .prospecttable-wrap.prospecttable-wrap-lg {
            padding-left: 300px !important;
        }
        .prospecttable-left.prospecttable-left-lg {
            width: 300px !important;
        }
        .email-verified-badge {
            z-index: 10;
            position: relative;
        }
        .font13 {
            font-size: 13px;
            line-height: 13px;
        }
        .head-icon-desc,
        .head-icon-asc {
            display: none;
        }

        .sort.sort-asc .head-icon-asc {
            display: inline-block;
        }
        .sort.sort-desc .head-icon-desc {
            display: inline-block;
        }
        .prospecttable-item.prospecttable-item-email {
            position: relative;
        }
        .duplicate-email-warning {
            position: absolute;
            right: 5px;
            top: 3px;
            background: #fff;
            padding: 3px;
        }
        .prospecttable-item.bg-pink-100 .duplicate-email-warning {
            background: #ffd9e6 !important;
        }
    </style>
@endsection

@section('content-layout') 
    <linkedin-search-show
        email_integration_count="{{$emailIntegrationCount}}"
        linkedin_search_id="{{ $linkedinSearch->hashid }}"
        :linkedin-search="{{$linkedinSearch}}"
        :campaigns="{{$campaigns}}"
        :teams="{{$teams}}"
        :can-import-no-email="{{json_encode($canImportNoEmail)}}"
        inline-template
    >
        <div class="page loader-wrapper" >
            <div class="page-header page-header-bordered">
                <h1 class="page-title">LinkedIn Search</h1>
                <p class="page-description m-0 text-capitalize">
                    {{$linkedinSearch->name}}
                </p>
                <div class="page-header-actions">
                    <div class="spark-settings-tabs">
                        <ul role="tablist" class="nav nav-fill spark-settings-stacked-tabs nav-pills nav-round">
                            <li role="presentation" class="nav-item">
                                <a href="#search_details" aria-controls="search_details" 
                                    role="tab" data-toggle="tab" 
                                    class="nav-link active" 
                                    aria-selected="true"
                                >
                                    <i class="hidden-md icon wb-list"></i> 
                                    <span class="hidden-sm-down text-uppercase">Details</span>
                                </a>
                            </li> 
                            <li role="presentation" class="nav-item">
                                <a href="#search_results" aria-controls="search_results" 
                                    role="tab" data-toggle="tab" 
                                    class="nav-link" aria-selected="false"
                                >
                                    <i class="hidden-md icon wb-users"></i> 
                                    <span class="hidden-sm-down text-uppercase">
                                        Results
                                        ({{ $linkedinSearch->linkedin_profiles_count }})
                                    </span>
                                </a>
                            </li>  
                            <li role="presentation" class="nav-item">
                                <a href="#search_activities" aria-controls="search_activities" 
                                    role="tab" data-toggle="tab" 
                                    class="nav-link" aria-selected="false"
                                >
                                    <i class="hidden-md icon wb-more-vertical"></i> 
                                    <span class="hidden-sm-down text-uppercase">
                                        Activity
                                    </span>
                                </a>
                            </li>  
                        </ul>
                    </div> 
                </div>  
            </div>

            <div class="page-content container-fluid">
                @if (session('status'))
                    <div class="mb-20 alert-dismissible alert alert-{{session('status') == 'success' ? 'success' : 'danger'}}">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button> 
                        {{ session('msg') }}
                    </div>
                @endif 

                @if (session('ajaxMsg'))
                    <div class="mb-20 alert alert-dismissible alert-success">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                        {{ session('ajaxMsg') }}
                    </div>
                @endif

                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @include('linkedin-searches.show.status')

                <div class="row">
                    <div class="col-md-12">
                        <div class="nav-tabs-horizontal" data-plugin="tabs"> 
                            <div class="tab-content">
                                <div class="tab-pane active" id="search_details" role="tabpanel">
                                    @include('linkedin-searches.show.details')
                                </div>
                                <div class="tab-pane" id="search_results" role="tabpanel">
                                    @include('linkedin-searches.show.results.list')
                                </div>
                                <div class="tab-pane" id="search_activities" role="tabpanel">
                                    @include('linkedin-searches.show.activity')
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div> 
        </div>
    </linkedin-search-show>
@endsection