<div class="alert-icon alert alert-success" v-if="isDashboardTrial">
  <i class="icon wb-check-circle"></i>
  <p class="text-capitalize font-weight-600 mb-0">FREE TRIAL</p>
  <p>
    You are currently within your free trial.
    <template v-if="dashboardSubscription.provider_plan != 'free-ec'">
      Your trial will expire on <strong>@{{moment_dt(dashboardSubscription.trial_ends_at)}}</strong>.
    </template>
  </p>
</div>
<div class="alert-icon alert alert-danger" v-if="dashboardSubscription.ends_at">
  <i class="icon wb-warning"></i>
  <p class="text-capitalize font-weight-600 mb-0">NO ACTIVE SUBSCRIPTION</p>
  <p>
    Your subscription has expired. Select a subscription plan to continue using Wavo services.
  </p>
</div>
<div class="alert-icon alert alert-danger" v-else-if="wavo3billing && !isDashboardTrial && onFreePlan">
  <i class="icon wb-warning"></i>
  <p class="text-capitalize font-weight-600 mb-0">NO ACTIVE SUBSCRIPTION</p>
  <p>
    Your Free trial has expired. Select a subscription plan to continue using Wavo services.
  </p>
</div>

<div v-if="(onFreePlan && !dashboardSubscription.ends_at) || showPlanSwitch" style="margin: 40px 0px 10px;">
  <div class="row">
    <div class="col-sm-12" v-if="onFreePlan && !dashboardSubscription.ends_at">
      <h5>
        Select a subscription plan to activate the full potential of the Wavo services.
      </h5>
    </div>

    <div class="col-sm-12" v-if="showPlanSwitch">
      <strong>Monthly</strong>
      <toggle-switch
        v-model="planSwitch"
        size="xl"
        style="display:inline-block; vertical-align: middle; margin: 0 10px;"
      >
      </toggle-switch>
      <strong>Yearly</strong> (2 months free)
    </div>

  </div>
</div>
  <div class="card card-shadow mb-3"
       v-for="plan in shownPlans"
       :class="!dashboardSubscription.ends_at && dashboardSubscription.provider_plan === plan.id ? 'border border-primary' : 'border'"
  >
    <div class="card-block pb-15">
      <div class="row">
        <div class="col-lg-9 col-xl-9 col-md-9 col-sm-8 col-8">
          <div class="row">
            <div class="col-md-12 col-xl-8 col-lg-8">
              <h4 class="m-0">
                <span class="d-block text-truncate">
                  @{{ plan.name }}
                  <small class="d-block pt-10 hidden-md-down">
                    <ul class="pl-15" v-if="onBigPlan && plan.slug=='predictable-lead-flow'">
                      <li v-for="feature in bigPlan.features">
                        @{{ feature }}
                      </li>
                    </ul>
                  </small>
                </span>

                <small class="d-block pt-5 hidden-lg-up">
                  @{{ plan.price | currency }} / @{{ plan.interval | capitalize }}
                </small>
              </h4>
            </div>
            <div class="col-md-12 col-xl-4 col-lg-4 text-center hidden-md-down">
              <h4 class="m-0 d-inline-block text-center px-10">
                @{{ plan.price | currency }}
                <small class="d-block pt-5">@{{ plan.interval | capitalize }}</small>
              </h4>
            </div>
          </div>
        </div>
        <div v-if="dashboardSubscription.provider_plan !== plan.id || dashboardSubscription.ends_at"
             class="col-md-3 col-xl-3 col-lg-3 col-sm-4 col-4 text-right pt-3"
        >
          {{-- If subscription is canceled show activate button, otherwise show switch plan button --}}
          <button class="btn btn-primary btn-block"
                  data-toggle="modal"
                  :data-target="dashboardSubscription.ends_at ? '#activateWavo3PlanModal' :'#switchDashboardPlanModal'"
                  @click="selectNewDashboardPlan(plan.id)"
          >
            <i class="icon wb-plus hidden-lg"></i>
            <span class="hidden-md-down">View</span>
            <span class="hidden-lg-down">Plan</span>
          </button>
        </div>
        <div v-if="dashboardSubscription.provider_plan === plan.id && !dashboardSubscription.ends_at && canCancel"
             class="col-md-3 col-xl-3 col-lg-3 col-sm-4 col-4 text-right pt-3"
        >
          <div class="row">
{{--            <div class="col-sm-6 pr-5">--}}
{{--              <button class="btn btn-primary btn-block"--}}
{{--                      data-toggle="modal"--}}
{{--                      :data-target="'#renewWavo3PlanModal'"--}}
{{--                      @click="selectNewDashboardPlan(plan.id)"--}}
{{--              >--}}
{{--                <i class="icon wb-plus hidden-lg"></i>--}}
{{--                <span class="hidden-md-down">Renew</span>--}}
{{--              </button>--}}
{{--            </div>--}}
            <div class="col-sm-12">
              <button class="btn btn-danger btn-block"
                      @click="confirmCancelDashboard"
                      :disabled="isCancellingAgency"
              >
                <span v-if="isCancellingAgency">
                  <i class="fa fa-spinner fa-spin hidden-lg"></i>
                  <span class="hidden-md-down">Cancelling</span>
                </span>
                <span v-else>
                  <i class="icon wb-close hidden-lg"></i>
                  <span class="hidden-md-down">Cancel</span>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
