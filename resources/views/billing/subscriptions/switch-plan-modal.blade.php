<!-- Modal -->
<agency-upgrade
	:user="user"
	:team="team"
	:card-summary="cardSummary"
  :plan="newDashboardPlan"
  :current-plan="dashboardPlan"
	from="{{request()->from}}"
	app_env="{{ config('app.env') }}"
	v-on:gotopayment="scrollToPayment('openPaymentFromUpgrade')"
	inline-template
>
	<div>
		<div class="modal fade" id="switchDashboardPlanModal" tabindex="-1" role="dialog" aria-labelledby="switchDashboardPlanModalLabel" aria-hidden="true">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title w-full" id="exampleModalLabel">
							<span class="pull-right" v-if="plan.interval == 'monthly'">
								$@{{ plan.price }} Monthly
							</span>
              <span class="pull-right" v-else>
                $@{{ plan.price }} Yearly
              </span>
							@{{ plan.name }} Plan Details
						</h4>

					</div>
					<div class="modal-body">
						<div class="px-40">
							<div class="row">
								<div class="col-md-12 mb-20">
                  <p v-if="isUpgrade">
                    <strong>@{{ plan.tagLine }}</strong>
                  </p>
                  <p v-else>
                    <strong>Downgrade to a lower cost plan with the following features:</strong>
                  </p>
									<ul class="list-unstyled">
										<li v-for="feature in plan.features">
											<i class="icon wb-check"></i>
											<span class="pl-10">@{{ feature }}</span>
										</li>
									</ul>
								</div>

								<div class="col-md-12 text-center mb-20" v-if="hasBillingDetails">
									<button type="button" class="btn btn-default" data-dismiss="modal" aria-label="Close">
										<i class="icon wb-close hidden-lg"></i>
										<span class="hidden-md-down">Close</span>
									</button>
									<button class="btn btn-primary"
										@click="switchPlan"
										v-if="!isUpgrading"
									>
										<i class="fa fa-cogs"></i>
                    <template v-if="isUpgrade">
										  Upgrade to @{{ plan.name }} Plan
                    </template>
                    <template v-else>
                      Switch to @{{ plan.name }} Plan
                    </template>
									</button>
									<button class="btn btn-primary"
										disabled
										v-if="isUpgrading"
									>
										<i class="fa fa-check" v-if="isUpgraded"></i>
										<i class="fa fa-spinner fa-spin" v-else></i>
										<template v-if="isUpgrade">
                      Upgrading to @{{ plan.name }} Plan
                    </template>
                    <template v-else>
                      Switching to @{{ plan.name }} Plan
                    </template>
									</button>
								</div>

								<div class="col-md-12" v-else>
									<div class="alert-icon alert alert-warning">
										<i class="icon wb-alert-circle"></i>
										<strong>No payment details found</strong>
										<p>We require your payment details when upgrading to @{{ plan.name }} Plan.</p>
										<p>
											<button class="btn btn-primary" data-dismiss="modal" aria-label="Add Payment Details" @click="$emit('gotopayment')">
												<i class="fa fa-payment"></i>
												Add Payment Details
											</button>
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</agency-upgrade>
