@extends('layouts.app')

@section('page-title', 'Reauthenticate LinkedIn Account')

@section('page-styles')
    <style>
        .select2-results__options {
            text-transform:capitalize;
        }
        .is-invalid-select2 .select2.select2-container
        .select2-selection.select2-selection--single {
            border-color: #ff4c52 !important;
        }
    </style>
@endsection

@section('content-layout')
<linkedin-account-reauth
    type="{{old('login_type', $linkedinAccount->login_type)}}"
    useragent="{{old('user_agent', $linkedinAccount->user_agent)}}"
    hashid="{{old('proxy', '0')}}"
    :account="{{$linkedinAccount}}"
    :proxies="{{$proxies}}"
    inline-template
>
    <div class="page loader-wrapper" >
        <div class="page-header page-header-bordered">
            <h1 class="page-title">Reauthenticate LinkedIn Account</h1>
            <p class="page-description m-0">
                <span class="text-capitalize">
                    {{$linkedinAccount->name}}
                </span>
                (https://www.linkedin.com{{$linkedinAccount->account_url}})
            </p>
            <div class="page-header-actions">
{{--                <a href="{{ route('linkedin-accounts.edit', $linkedinAccount) }}"  class="btn btn-outline btn-primary btn-round"  >--}}
{{--                    <span class="text hidden-sm-down">Edit</span>--}}
{{--                    <i class="icon wb-minus-circle" aria-hidden="true"></i> --}}
{{--                </a>  --}}
                <a href="{{ route('linkedin-accounts.show', $linkedinAccount) }}"  class="btn btn-outline btn-default btn-round"  >
                    <span class="text hidden-sm-down">Cancel</span>
                    <i class="icon wb-minus-circle" aria-hidden="true"></i>
                </a>
            </div>
        </div>

        <div class="page-content container-fluid">
            @if (session('status'))
                <div class="mb-20 alert-dismissible alert alert-{{session('status') == 'success' ? 'success' : 'danger'}}">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    {{ session('msg') }}
                </div>
            @endif

            @if (session('ajaxMsg'))
                <div class="mb-20 alert alert-dismissible alert-success">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    {{ session('ajaxMsg') }}
                </div>
            @endif

            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="row">
                <div class="col-md-12">
                    <div class="panel  ">
                        <div class="panel-body">
                            <form method="POST" action="{{ route('linkedin-accounts.reauth.update', $linkedinAccount)}}">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="login_type" value="cookie">

                                <div class="row">
                                    <div class="@if($displayClientSelect && $displayProxySelect) col-lg-4 @else col-lg-6 @endif mb-20">
                                        <label class="black">Account Name</label>
                                        <input type="text" value="{{old('name', $linkedinAccount->name)}}" name="name"
                                            class="form-control {{ $errors->has('name') ? ' is-invalid' : '' }}"
                                            placeholder="Enter account name..."
                                        >

                                        @if ($errors->has('name'))
                                            <div class="text-danger">
                                                <small>{{ $errors->first('name') }}</small>
                                            </div>
                                        @endif
                                    </div>

                                    @if($displayClientSelect)
                                    <div class="@if($displayProxySelect) col-lg-4 @else col-lg-6 @endif mb-20">
                                        <label class="black">Client</label>
                                        <input type="text" value="{{$linkedinAccount->team->name}}" class="form-control" disabled>
                                    </div>
                                    @endif

                                    @if($displayProxySelect)
                                    <div class="@if($displayClientSelect) col-lg-4 @else col-lg-6 @endif mb-20">
                                        <label class="black">Proxy</label>

                                        <div  v-if="arrProxies?.data?.length">
                                        <select  name="proxy" v-model="proxy_hashid" data-plugin="select2" data-style="btn-outline btn-default"
                                            class="form-control {{ $errors->has('proxy') ? ' is-invalid' : '' }}"
                                            data-placeholder="Select a proxy"
                                        >
                                            <option value="0">No Proxy</option>
                                            <option :value="proxy.hashid" v-for="proxy in arrProxies.data">
                                                @{{proxy.name}}
                                            </option>
                                        </select>
                                        </div>
                                        <div class="row" v-else>
                                            <div class="col-10 col-md-11 col-lg-10 col-xl-10">
                                                <input disabled readonly value="No Proxy" class="form-control">
                                                <input type="hidden" name="proxy" value="0">
                                            </div>
                                            <div class="col-2 col-md-1 col-lg-2 col-xl-2 pl-0">
                                                <a href="#" @click="openProxyAddModal" class="btn btn-outline btn-primary btn-block px-0">
                                                    <i class="icon wb-plus"></i>
                                                </a>
                                            </div>
                                        </div>

                                        @if ($errors->has('proxy'))
                                            <div class="text-danger">
                                                <small>{{ $errors->first('proxy') }}</small>
                                            </div>
                                        @endif
                                    </div>
                                    @endif

                                    <div class="col-lg-12 mb-20">
                                        <label class="black">Linkedin Cookie</label>
                                        <input type="text" name="cookie"
                                            value="{{old('cookie', $linkedinAccount->cookie)}}"
                                            class="form-control {{ $errors->has('cookie') ? ' is-invalid' : '' }}"
                                            placeholder="Enter search cookie..."
                                            :disabled="login_type == 'credential'"
                                        >

                                        @if ($errors->has('cookie'))
                                            <div class="text-danger">
                                                <small>{{ $errors->first('cookie') }}</small>
                                            </div>
                                        @else
                                            <div class="">
                                                <small>* Make sure this account is not already added to the system</small>
                                            </div>
                                        @endif
                                    </div>

                                    <div class="col-lg-12 mb-20">
                                        <label class="black">Browser User-Agent</label>

                                        <div class="form-group mb-0">
                                            <div class="input-group">
                                            <input type="text" value="{{old('user_agent', $linkedinAccount->user_agent)}}" name="user_agent" v-model="user_agent"
                                                class="form-control {{ $errors->has('user_agent') ? ' is-invalid' : '' }}"
                                                placeholder="Enter browser's User-Agent"
                                            >
                                                <span class="input-group-btn">
                                                    <button type="button" class="btn btn-default"
                                                        @click="fetchBrowserUserAgent"
                                                    >
                                                        <i class="icon fa-asterisk" class="" aria-hidden="true"></i>
                                                        <span class="hidden-md-down">Use current browser's User-Agent</span>
                                                    </button>
                                                </span>
                                            </div>
                                        </div>

                                        @if ($errors->has('user_agent'))
                                            <div class="text-danger">
                                                <small>{{ $errors->first('user_agent') }}</small>
                                            </div>
                                        @else
                                            <div class="">
                                                <small>* Retrieve the user agent from the browser where the cookie is obtained to avoid account disconnection.</small>
                                            </div>
                                        @endif
                                    </div>

                                    <div class="col-md-12 pt-10" v-if="isAdvancedOpened">
                                        <div class="alert bg-blue-grey-100">
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <h4>
                                                        How to get your LinkedIn login cookie
                                                    </h4>

                                                    <ol>
                                                        <li>
                                                            Open chrome, navigate to <a href="https://www.linkedin.com/" target="_new">https://www.linkedin.com</a> and log in
                                                        </li>
                                                        <li>
                                                            Open up the browser developer tools (Ctrl-Shift-I or right click -> inspect element)
                                                        </li>
                                                        <li>
                                                            Select the Application tab
                                                        </li>
                                                        <li>
                                                            Click the Cookies dropdown and select www.linkedin.com
                                                        </li>
                                                        <li>
                                                            Find and copy the <strong>li_at</strong> value
                                                        </li>
                                                    </ol>
                                                </div>
                                                <div class="col-lg-6">
                                                    <h4>
                                                        How to find your browser's User Agent
                                                    </h4>

                                                    <ol>
                                                        <li>
                                                            Open the developer tools in Google Chrome, Microsoft Edge, Mozilla Firefox, Safari or any other browser. You can use <code>F12</code> or <code>Ctrl+Shift+I</code> on Windows/Linux, or <code>Cmd+Option(⌘)+I</code> on macOS
                                                        </li>
                                                        <li>
                                                            Switch to the Console tab
                                                        </li>
                                                        <li>
                                                            Type <code>navigator.userAgent</code> in the console and press Enter (or Ctrl+Enter). The console will return a string which is your browser's user agent (copy without the quotes).
                                                        </li>
                                                    </ol>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-20 pt-20 col-sm-12 text-center">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="icon wb-chevron-right"></i>
                                            Reauthenticate
                                        </button>
                                    </div>


                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        @include('spark::settings.proxies.modal-add')
    </div>
</linkedin-account-reauth>
@endsection
