<div class="modal fade" id="challengeCodeModal" 
	tabindex="-1" 
	role="dialog" 
	aria-labelledby="challengeCodeModalLabel" 
	aria-hidden="true"
	data-backdrop="static"
    data-keyboard="false"
>
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title" id="exampleModalLabel">Submit Linkedin Verification PIN/Code</h4>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body form-icons form-icons-right"> 
				<div class="alert-icon alert alert-warning">
					<i class="icon wb-warning"></i>
					Do not close or refresh this window
				</div>
				<div class="mb-20">
					<label class="d-block">
						<span class="pull-right text-danger">
							Remaining Time: 
							@{{codeTimerRemaining}}s
						</span>
						Verification Code
					</label>
					<input type="text" v-model="challengeCode" class="form-control" 
						:class="{'is-invalid': challengeErrors.code}"
					>
					<span v-cloak class="text-danger d-block" v-if="challengeErrors.code" 
						v-for="err in challengeErrors.code"
					>@{{err}}</span>
				</div>
				

				<div class="pt-20 text-center"> 
					<button type="button" class="btn btn-primary"
						@click="submitChallengeCode"
						:disabled="isSubmittingCode"
					>
						<span v-if="isSubmittingCode">
							<i class="fa fa-spinner fa-spin"></i> Submitting...
						</span>
						<span v-else>
							<i class="icon wb-chevron-right"></i> Submit
						</span>  
					</button>
				</div>
			</div> 
		</div>
	</div>
</div>