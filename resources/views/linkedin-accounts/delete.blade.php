@extends('layouts.app')

@section('page-title', 'Delete LinkedIn Account: ' . $linkedinAccount->name)

@section('page-styles')
    <style>

    </style>
@endsection

@section('content-layout')
    <div class="page loader-wrapper" >
        <div class="page-header page-header-bordered">
            <h1 class="page-title">Delete LinkedIn Account</h1>
            <p class="page-description m-0">
                <span class="text-capitalize">
                    {{$linkedinAccount->name}}
                </span>
                (https://www.linkedin.com{{$linkedinAccount->account_url}})
            </p>
            <div class="page-header-actions">
                <a href="{{ route('linkedin-accounts.show', $linkedinAccount) }}"  class="btn btn-outline btn-default btn-round"  >
                    <span class="text hidden-sm-down">Cancel</span>
                    <i class="icon wb-minus-circle" aria-hidden="true"></i>
                </a>
            </div>
        </div>

        <div class="page-content container-fluid">
          @if($campaigns->count())
            <div class="panel panel-bordered">
              <div class="panel-body">
                <div class="alert-icon alert alert-danger">
                  <i class="icon wb-alert-circle"></i>
                  <p class="mb-5">
                    <strong>Cannot delete account "{{$linkedinAccount->name}}".</strong>
                  </p>
                  <p>
                    There are active campaigns for this LinkedIn account.
                  </p>
                </div>

                @foreach($campaigns as $campaign)
                  <div class="card card-shadow border mb-5">
                    <div class="card-block pb-15">
                      <div class="row">
                        <div class="col-lg-10 col-xl-10 col-md-9 col-sm-8 col-8">
                          <h4 class="m-0">
                            <small class="d-block pt-5"></small>
                            <span class="d-block text-truncate">
                            {{$campaign->name}}
                          </span>
                          </h4>
                        </div>
                        <div class="col-md-3 col-xl-2 col-lg-2 col-sm-4 col-4 text-right pt-3">
                          <a href="/campaigns/{{$campaign->hashid}}" class="btn btn-primary">
                            <i class="icon wb-settings"></i>
                            Settings
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                @endforeach
              </div>
            </div>
          @else
            @if (session('status'))
                <div class="mb-20 alert-dismissible alert alert-{{session('status') == 'success' ? 'success' : 'danger'}}">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    {{ session('msg') }}
                </div>
            @endif

            @if (session('ajaxMsg'))
                <div class="mb-20 alert alert-dismissible alert-success">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    {{ session('ajaxMsg') }}
                </div>
            @endif

            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="row">
                <div class="col-md-12">
                    <form method="POST" action="{{ route('linkedin-accounts.destroy', $linkedinAccount) }}">
                        @csrf
                        @method('DELETE')
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel  ">
                                    <div class="panel-body">
                                        <div class="alert alert-danger text-center">
                                            <h3 class="text-capitalize">Delete "{{$linkedinAccount->name}}" ?</h3>
                                            <p>Are you sure you want to delete this LinkedIn account?</p>
                                            <p class="m-0">
                                                <a href="{{ route('linkedin-accounts.show', $linkedinAccount) }}" class="btn btn-dark mr-20">
                                                    Cancel
                                                </a>
                                                <button type="submit" class="btn btn-danger">Yes, Delete</button>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
          @endif
        </div>
    </div>
@endsection
